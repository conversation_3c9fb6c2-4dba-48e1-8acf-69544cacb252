import { _decorator } from 'cc';
import { BundleName } from 'db://assets/bundles/Bundle';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/ui/UIMgr';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import List from '../common/components/list/List';
import { PKUI } from './PKUI';
const { ccclass, property } = _decorator;

@ccclass('PKHistoryUI')
export class PKHistoryUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(List)
    list: List | null = null;
    public static getUrl(): string { return "prefab/ui/PKHistoryUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomePK; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        this.list!.numItems = 20;
    }
    async closeUI() {
        UIMgr.closeUI(PKHistoryUI);
        //await UIMgr.openUI(PKUI)
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {

    }
}


