{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts"], "names": ["EventGroupContext", "Condition<PERSON><PERSON><PERSON>", "EventGroup", "Comparer", "ConditionFactory", "ActionFactory", "eEmitterCondition", "eBulletCondition", "eBulletAction", "eEmitterAction", "eConditionOp", "eCompareOp", "emitter_cond", "bullet_cond", "emitter_act", "bullet_act", "BulletSystem", "emitter", "bullet", "reset", "conditions", "evaluate", "context", "length", "result", "i", "condition", "conditionResult", "data", "op", "And", "Or", "eEventGroupStatus", "status", "_status", "constructor", "ctx", "<PERSON><PERSON><PERSON><PERSON>", "actions", "_triggerCount", "Idle", "buildConditionChain", "map", "actionData", "action", "create", "changeStatus", "start", "onCreateEventGroup", "Waiting", "stop", "onDestroyEventGroup", "Stopped", "canExecute", "tick", "dt", "Active", "tickActive", "newStatus", "for<PERSON>ach", "onLoad", "chain", "condData", "index", "push", "isAllFinished", "isCompleted", "onExecute", "triggerCount", "compare", "a", "b", "Equal", "NotEqual", "Greater", "GreaterEqual", "Less", "LessEqual", "Error", "type", "Emitter_Active", "EmitterCondition_Active", "Emitter_InitialDelay", "EmitterCondition_InitialDelay", "Emitter_Prewarm", "EmitterCondition_Prewarm", "Emitter_PrewarmDuration", "EmitterCondition_PrewarmDuration", "Emitter_Duration", "EmitterCondition_Duration", "Emitter_ElapsedTime", "EmitterCondition_ElapsedTime", "Emitter_Loop", "EmitterCondition_Loop", "Emitter_LoopInterval", "EmitterCondition_LoopInterval", "Emitter_EmitInterval", "EmitterCondition_EmitInterval", "Emitter_PerEmitCount", "EmitterCondition_PerEmitCount", "Emitter_PerEmitInterval", "EmitterCondition_PerEmitInterval", "Emitter_PerEmitOffsetX", "EmitterCondition_PerEmitOffsetX", "Emitter_Angle", "EmitterCondition_Angle", "Emitter_Count", "EmitterCondition_Count", "Bullet_Duration", "EmitterCondition_BulletDuration", "Bullet_Speed", "EmitterCondition_BulletSpeed", "Bullet_Acceleration", "EmitterCondition_BulletAcceleration", "Bullet_AccelerationAngle", "EmitterCondition_BulletAccelerationAngle", "Bullet_FacingMoveDir", "EmitterCondition_BulletFacingMoveDir", "Bullet_TrackingTarget", "EmitterCondition_BulletTrackingTarget", "Bullet_Destructive", "EmitterCondition_BulletDestructive", "Bullet_DestructiveOnHit", "EmitterCondition_BulletDestructiveOnHit", "Bullet_Scale", "EmitterCondition_BulletScale", "Bullet_ColorR", "EmitterCondition_BulletColorR", "Bullet_ColorG", "EmitterCondition_BulletColorG", "Bullet_ColorB", "EmitterCondition_BulletColorB", "Bullet_DefaultFacing", "EmitterCondition_BulletDefaultFacing", "BulletCondition_Duration", "Bullet_ElapsedTime", "BulletCondition_ElapsedTime", "Bullet_PosX", "BulletCondition_PosX", "Bullet_PosY", "BulletCondition_PosY", "BulletCondition_Speed", "Bullet_SpeedAngle", "BulletCondition_SpeedAngle", "BulletCondition_Acceleration", "BulletCondition_AccelerationAngle", "BulletCondition_Scale", "BulletCondition_ColorR", "BulletCondition_ColorG", "BulletCondition_ColorB", "BulletCondition_FacingMoveDir", "BulletCondition_Destructive", "BulletCondition_DestructiveOnHit", "EmitterAction_Active", "EmitterAction_InitialDelay", "EmitterAction_Prewarm", "EmitterAction_PrewarmDuration", "EmitterAction_Duration", "EmitterAction_ElapsedTime", "EmitterAction_Loop", "EmitterAction_LoopInterval", "EmitterAction_EmitInterval", "EmitterAction_PerEmitCount", "EmitterAction_PerEmitInterval", "EmitterAction_PerEmitOffsetX", "EmitterAction_Angle", "EmitterAction_Count", "EmitterAction_BulletDuration", "Bullet_Damage", "EmitterAction_BulletDamage", "EmitterAction_BulletSpeed", "EmitterAction_BulletSpeedAngle", "EmitterAction_BulletAcceleration", "EmitterAction_BulletAccelerationAngle", "EmitterAction_BulletScale", "EmitterAction_BulletColorR", "EmitterAction_BulletColorG", "EmitterAction_BulletColorB", "EmitterAction_BulletFacingMoveDir", "EmitterAction_BulletTrackingTarget", "EmitterAction_BulletDestructive", "EmitterAction_BulletDestructiveOnHit", "BulletAction_Duration", "BulletAction_ElapsedTime", "BulletAction_PosX", "BulletAction_PosY", "BulletAction_Speed", "BulletAction_SpeedAngle", "BulletAction_Acceleration", "BulletAction_AccelerationAngle", "BulletAction_Scale", "BulletAction_ColorR", "BulletAction_ColorG", "BulletAction_ColorB", "BulletAction_FacingMoveDir", "BulletAction_Destructive", "BulletAction_DestructiveOnHit"], "mappings": ";;;mLAcaA,iB,EAYPC,c,EA8BOC,U,EAsHAC,Q,EAsBPC,gB,EAgGAC,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlSGC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;AACnBC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,c,iBAAAA,c;;AAGfC,MAAAA,Y,iBAAAA,Y;AAAcC,MAAAA,U,iBAAAA,U;;AACXC,MAAAA,Y;;AACAC,MAAAA,W;;AACAC,MAAAA,W;;AACAC,MAAAA,U;;AACHC,MAAAA,Y,iBAAAA,Y;;;;;AAET;mCACahB,iB,GAAN,MAAMA,iBAAN,CAAwB;AAAA;AAAA,eAC3BiB,OAD2B,GACD,IADC;AAAA,eAE3BC,MAF2B,GAEH,IAFG;AAAA;;AAG3B;AAEAC,QAAAA,KAAK,GAAS;AACV,eAAKF,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACH;;AAR0B,O,GAW/B;;;AACMjB,MAAAA,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjBmB,UADiB,GACoB,EADpB;AAAA;;AAGjBC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AAC1C,cAAI,KAAKF,UAAL,CAAgBG,MAAhB,KAA2B,CAA/B,EAAkC,OAAO,IAAP;AAClC,cAAIC,MAAM,GAAG,KAAKJ,UAAL,CAAgB,CAAhB,EAAmBC,QAAnB,CAA4BC,OAA5B,CAAb;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKL,UAAL,CAAgBG,MAApC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,gBAAMC,SAAS,GAAG,KAAKN,UAAL,CAAgBK,CAAhB,CAAlB;AACA,gBAAME,eAAe,GAAGD,SAAS,CAACL,QAAV,CAAmBC,OAAnB,CAAxB;;AAEA,gBAAII,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaC,GAAvC,EAA4C;AACxCN,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH,aAFD,MAEO,IAAID,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaE,EAAvC,EAA2C;AAC9CP,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH;AACJ;;AAED,iBAAOH,MAAP;AACH;;AAnBgB,O,EAsBrB;;mCACYQ,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;4BAOC9B,U,GAAN,MAAMA,UAAN,CAAiB;AASV,YAAN+B,MAAM,GAAsB;AAC5B,iBAAO,KAAKC,OAAZ;AACH;;AAEDC,QAAAA,WAAW,CAACC,GAAD,EAAyBR,IAAzB,EAA+C;AAAA,eAZjDA,IAYiD;AAAA,eAV1DN,OAU0D;AAAA,eAT1De,cAS0D;AAAA,eAR1DC,OAQ0D;AAAA,eANlDC,aAMkD,GAN1B,CAM0B;AAAA,eALlDL,OAKkD,GALrBF,iBAAiB,CAACQ,IAKG;AACtD,eAAKlB,OAAL,GAAec,GAAf;AACA,eAAKR,IAAL,GAAYA,IAAZ;AACA,eAAKS,cAAL,GAAsB,KAAKI,mBAAL,CAAyBb,IAAI,CAACR,UAA9B,CAAtB;AACA,eAAKkB,OAAL,GAAeV,IAAI,CAACU,OAAL,CAAaI,GAAb,CAAiBC,UAAU,IAAI;AAC1C,gBAAIC,MAAM,GAAGvC,aAAa,CAACwC,MAAd,CAAqBF,UAArB,CAAb;AACA,mBAAOC,MAAP;AACH,WAHc,CAAf;AAIA,eAAKL,aAAL,GAAqB,CAArB;AAEA,eAAKO,YAAL,CAAkBd,iBAAiB,CAACQ,IAApC;AACH;;AAEDO,QAAAA,KAAK,GAAS;AACV;AAAA;AAAA,4CAAaC,kBAAb,CAAgC,IAAhC;AACA,eAAKF,YAAL,CAAkBd,iBAAiB,CAACiB,OAApC;AACH;;AAEDC,QAAAA,IAAI,GAAS;AACT;AACA;AAAA;AAAA,4CAAaC,mBAAb,CAAiC,IAAjC;AACA,eAAKL,YAAL,CAAkBd,iBAAiB,CAACoB,OAApC;AACH;;AAEDC,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKhB,cAAL,CAAoBhB,QAApB,CAA6B,KAAKC,OAAlC,CAAP;AACH;;AAEDgC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAEnB,kBAAQ,KAAKrB,OAAb;AACI,iBAAKF,iBAAiB,CAACQ,IAAvB;AACI;AACA;;AACJ,iBAAKR,iBAAiB,CAACiB,OAAvB;AACI;AACA,kBAAI,KAAKI,UAAL,EAAJ,EAAuB;AACnB;AACA,qBAAKP,YAAL,CAAkBd,iBAAiB,CAACwB,MAApC;AACH;;AACD;;AACJ,iBAAKxB,iBAAiB,CAACwB,MAAvB;AACI;AACA,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAKvB,iBAAiB,CAACoB,OAAvB;AACI;AACA;AAjBR;AAmBH;;AAEON,QAAAA,YAAY,CAACY,SAAD,EAA+B;AAC/C,cAAI,KAAKxB,OAAL,KAAiBwB,SAArB,EAAgC;AAEhC,eAAKxB,OAAL,GAAewB,SAAf;;AAEA,kBAAQ,KAAKxB,OAAb;AACI,iBAAKF,iBAAiB,CAACiB,OAAvB;AACI;AACA;;AACJ,iBAAKjB,iBAAiB,CAACwB,MAAvB;AACI;AACA,mBAAKlB,OAAL,CAAaqB,OAAb,CAAqBf,MAAM,IAAIA,MAAM,CAACgB,MAAP,CAAc,KAAKtC,OAAnB,CAA/B;AACA;;AACJ,iBAAKU,iBAAiB,CAACoB,OAAvB;AACI;;AACJ;AAAS;AAVb;AAYH;;AAEOX,QAAAA,mBAAmB,CAACrB,UAAD,EAAmD;AAC1E,cAAMyC,KAAK,GAAG,IAAI5D,cAAJ,EAAd;AACAmB,UAAAA,UAAU,CAACuC,OAAX,CAAmB,CAACG,QAAD,EAAWC,KAAX,KAAqB;AACpC,gBAAMrC,SAAS,GAAGtB,gBAAgB,CAACyC,MAAjB,CAAwBiB,QAAxB,CAAlB;;AACA,gBAAIpC,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACkC,MAAV,CAAiB,KAAKtC,OAAtB;AACAuC,cAAAA,KAAK,CAACzC,UAAN,CAAiB4C,IAAjB,CAAsBtC,SAAtB;AACH;AACJ,WAND;AAOA,iBAAOmC,KAAP;AACH;;AAEOJ,QAAAA,UAAU,CAACF,EAAD,EAAmB;AACjC,cAAIU,aAAa,GAAG,IAApB;;AAEA,eAAK,IAAMrB,MAAX,IAAqB,KAAKN,OAA1B,EAAmC;AAC/B,gBAAIM,MAAM,CAACsB,WAAP,EAAJ,EAA0B;AAC1BtB,YAAAA,MAAM,CAACuB,SAAP,CAAiB,KAAK7C,OAAtB,EAA+BiC,EAA/B;AACAU,YAAAA,aAAa,GAAG,KAAhB;AACH;;AAED,cAAIA,aAAJ,EAAmB;AACf,iBAAK1B,aAAL;;AACA,gBAAI,KAAKX,IAAL,CAAUwC,YAAV,GAAyB,CAAzB,IAA8B,KAAK7B,aAAL,GAAqB,KAAKX,IAAL,CAAUwC,YAAjE,EAA+E;AAC3E;AACA,mBAAKtB,YAAL,CAAkBd,iBAAiB,CAACiB,OAApC;AACH,aAHD,MAIK;AACD,mBAAKC,IAAL;AACH;AACJ;AACJ;;AAlHmB,O,GAqHxB;;;0BACa/C,Q,GAAN,MAAMA,QAAN,CAAe;AACJ,eAAPkE,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB1C,EAAvB,EAAgD;AAC1D,kBAAQA,EAAR;AACI,iBAAK;AAAA;AAAA,0CAAW2C,KAAhB;AACI,qBAAOF,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,QAAhB;AACI,qBAAOH,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWG,OAAhB;AACI,qBAAOJ,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,YAAhB;AACI,qBAAOL,CAAC,IAAIC,CAAZ;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,IAAhB;AACI,qBAAON,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWM,SAAhB;AACI,qBAAOP,CAAC,IAAIC,CAAZ;;AACJ;AACI,oBAAM,IAAIO,KAAJ,gCAAuCjD,EAAvC,CAAN;AAdR;AAgBH;;AAlBiB,O,GAqBtB;;;AACMzB,MAAAA,gB,GAAN,MAAMA,gBAAN,CAAuB;AACN,eAANyC,MAAM,CAACjB,IAAD,EAA4C;AACrD,kBAAQA,IAAI,CAACmD,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBC,cAAvB;AACI,qBAAO,IAAIpE,YAAY,CAACqE,uBAAjB,CAAyCrD,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsD,oBAAvB;AACI,qBAAO,IAAItE,YAAY,CAACuE,6BAAjB,CAA+CvD,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwD,eAAvB;AACI,qBAAO,IAAIxE,YAAY,CAACyE,wBAAjB,CAA0CzD,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB0D,uBAAvB;AACI,qBAAO,IAAI1E,YAAY,CAAC2E,gCAAjB,CAAkD3D,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB4D,gBAAvB;AACI,qBAAO,IAAI5E,YAAY,CAAC6E,yBAAjB,CAA2C7D,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB8D,mBAAvB;AACI,qBAAO,IAAI9E,YAAY,CAAC+E,4BAAjB,CAA8C/D,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBgE,YAAvB;AACI,qBAAO,IAAIhF,YAAY,CAACiF,qBAAjB,CAAuCjE,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBkE,oBAAvB;AACI,qBAAO,IAAIlF,YAAY,CAACmF,6BAAjB,CAA+CnE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBoE,oBAAvB;AACI,qBAAO,IAAIpF,YAAY,CAACqF,6BAAjB,CAA+CrE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsE,oBAAvB;AACI,qBAAO,IAAItF,YAAY,CAACuF,6BAAjB,CAA+CvE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwE,uBAAvB;AACI,qBAAO,IAAIxF,YAAY,CAACyF,gCAAjB,CAAkDzE,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB0E,sBAAvB;AACI,qBAAO,IAAI1F,YAAY,CAAC2F,+BAAjB,CAAiD3E,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB4E,aAAvB;AACI,qBAAO,IAAI5F,YAAY,CAAC6F,sBAAjB,CAAwC7E,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB8E,aAAvB;AACI,qBAAO,IAAI9F,YAAY,CAAC+F,sBAAjB,CAAwC/E,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBgF,eAAvB;AACI,qBAAO,IAAIhG,YAAY,CAACiG,+BAAjB,CAAiDjF,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBkF,YAAvB;AACI,qBAAO,IAAIlG,YAAY,CAACmG,4BAAjB,CAA8CnF,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBoF,mBAAvB;AACI,qBAAO,IAAIpG,YAAY,CAACqG,mCAAjB,CAAqDrF,IAArD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsF,wBAAvB;AACI,qBAAO,IAAItG,YAAY,CAACuG,wCAAjB,CAA0DvF,IAA1D,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwF,oBAAvB;AACI,qBAAO,IAAIxG,YAAY,CAACyG,oCAAjB,CAAsDzF,IAAtD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB0F,qBAAvB;AACI,qBAAO,IAAI1G,YAAY,CAAC2G,qCAAjB,CAAuD3F,IAAvD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB4F,kBAAvB;AACI,qBAAO,IAAI5G,YAAY,CAAC6G,kCAAjB,CAAoD7F,IAApD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB8F,uBAAvB;AACI,qBAAO,IAAI9G,YAAY,CAAC+G,uCAAjB,CAAyD/F,IAAzD,CAAP;AACJ;AACA;;AACA,iBAAK;AAAA;AAAA,wDAAkBgG,YAAvB;AACI,qBAAO,IAAIhH,YAAY,CAACiH,4BAAjB,CAA8CjG,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBkG,aAAvB;AACI,qBAAO,IAAIlH,YAAY,CAACmH,6BAAjB,CAA+CnG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBoG,aAAvB;AACI,qBAAO,IAAIpH,YAAY,CAACqH,6BAAjB,CAA+CrG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsG,aAAvB;AACI,qBAAO,IAAItH,YAAY,CAACuH,6BAAjB,CAA+CvG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwG,oBAAvB;AACI,qBAAO,IAAIxH,YAAY,CAACyH,oCAAjB,CAAsDzG,IAAtD,CAAP;AACJ;;AACA,iBAAK;AAAA;AAAA,sDAAiBgF,eAAtB;AACI,qBAAO,IAAI/F,WAAW,CAACyH,wBAAhB,CAAyC1G,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB2G,kBAAtB;AACI,qBAAO,IAAI1H,WAAW,CAAC2H,2BAAhB,CAA4C5G,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB6G,WAAtB;AACI,qBAAO,IAAI5H,WAAW,CAAC6H,oBAAhB,CAAqC9G,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB+G,WAAtB;AACI,qBAAO,IAAI9H,WAAW,CAAC+H,oBAAhB,CAAqChH,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBkF,YAAtB;AACI,qBAAO,IAAIjG,WAAW,CAACgI,qBAAhB,CAAsCjH,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBkH,iBAAtB;AACI,qBAAO,IAAIjI,WAAW,CAACkI,0BAAhB,CAA2CnH,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBoF,mBAAtB;AACI,qBAAO,IAAInG,WAAW,CAACmI,4BAAhB,CAA6CpH,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBsF,wBAAtB;AACI,qBAAO,IAAIrG,WAAW,CAACoI,iCAAhB,CAAkDrH,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBgG,YAAtB;AACI,qBAAO,IAAI/G,WAAW,CAACqI,qBAAhB,CAAsCtH,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBkG,aAAtB;AACI,qBAAO,IAAIjH,WAAW,CAACsI,sBAAhB,CAAuCvH,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBoG,aAAtB;AACI,qBAAO,IAAInH,WAAW,CAACuI,sBAAhB,CAAuCxH,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBsG,aAAtB;AACI,qBAAO,IAAIrH,WAAW,CAACwI,sBAAhB,CAAuCzH,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBwF,oBAAtB;AACI,qBAAO,IAAIvG,WAAW,CAACyI,6BAAhB,CAA8C1H,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB4F,kBAAtB;AACI,qBAAO,IAAI3G,WAAW,CAAC0I,2BAAhB,CAA4C3H,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB8F,uBAAtB;AACI,qBAAO,IAAI7G,WAAW,CAAC2I,gCAAhB,CAAiD5H,IAAjD,CAAP;;AACJ;AACI,oBAAM,IAAIkD,KAAJ,8BAAqClD,IAAI,CAACmD,IAA1C,CAAN;AAzFR;AA2FH;;AA7FkB,O;AAgGjB1E,MAAAA,a,GAAN,MAAMA,aAAN,CAAoB;AACH,eAANwC,MAAM,CAACjB,IAAD,EAAsC;AAC/C,kBAAQA,IAAI,CAACmD,IAAb;AACI,iBAAK;AAAA;AAAA,kDAAeC,cAApB;AACI,qBAAO,IAAIlE,WAAW,CAAC2I,oBAAhB,CAAqC7H,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAesD,oBAApB;AACI,qBAAO,IAAIpE,WAAW,CAAC4I,0BAAhB,CAA2C9H,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAewD,eAApB;AACI,qBAAO,IAAItE,WAAW,CAAC6I,qBAAhB,CAAsC/H,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe0D,uBAApB;AACI,qBAAO,IAAIxE,WAAW,CAAC8I,6BAAhB,CAA8ChI,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe4D,gBAApB;AACI,qBAAO,IAAI1E,WAAW,CAAC+I,sBAAhB,CAAuCjI,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe8D,mBAApB;AACI,qBAAO,IAAI5E,WAAW,CAACgJ,yBAAhB,CAA0ClI,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAegE,YAApB;AACI,qBAAO,IAAI9E,WAAW,CAACiJ,kBAAhB,CAAmCnI,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAekE,oBAApB;AACI,qBAAO,IAAIhF,WAAW,CAACkJ,0BAAhB,CAA2CpI,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeoE,oBAApB;AACI,qBAAO,IAAIlF,WAAW,CAACmJ,0BAAhB,CAA2CrI,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAesE,oBAApB;AACI,qBAAO,IAAIpF,WAAW,CAACoJ,0BAAhB,CAA2CtI,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAewE,uBAApB;AACI,qBAAO,IAAItF,WAAW,CAACqJ,6BAAhB,CAA8CvI,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe0E,sBAApB;AACI,qBAAO,IAAIxF,WAAW,CAACsJ,4BAAhB,CAA6CxI,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe4E,aAApB;AACI,qBAAO,IAAI1F,WAAW,CAACuJ,mBAAhB,CAAoCzI,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe8E,aAApB;AACI,qBAAO,IAAI5F,WAAW,CAACwJ,mBAAhB,CAAoC1I,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAegF,eAApB;AACI,qBAAO,IAAI9F,WAAW,CAACyJ,4BAAhB,CAA6C3I,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe4I,aAApB;AACI,qBAAO,IAAI1J,WAAW,CAAC2J,0BAAhB,CAA2C7I,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAekF,YAApB;AACI,qBAAO,IAAIhG,WAAW,CAAC4J,yBAAhB,CAA0C9I,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAekH,iBAApB;AACI,qBAAO,IAAIhI,WAAW,CAAC6J,8BAAhB,CAA+C/I,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeoF,mBAApB;AACI,qBAAO,IAAIlG,WAAW,CAAC8J,gCAAhB,CAAiDhJ,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAesF,wBAApB;AACI,qBAAO,IAAIpG,WAAW,CAAC+J,qCAAhB,CAAsDjJ,IAAtD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAegG,YAApB;AACI,qBAAO,IAAI9G,WAAW,CAACgK,yBAAhB,CAA0ClJ,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAekG,aAApB;AACI,qBAAO,IAAIhH,WAAW,CAACiK,0BAAhB,CAA2CnJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeoG,aAApB;AACI,qBAAO,IAAIlH,WAAW,CAACkK,0BAAhB,CAA2CpJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAesG,aAApB;AACI,qBAAO,IAAIpH,WAAW,CAACmK,0BAAhB,CAA2CrJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAewF,oBAApB;AACI,qBAAO,IAAItG,WAAW,CAACoK,iCAAhB,CAAkDtJ,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe0F,qBAApB;AACI,qBAAO,IAAIxG,WAAW,CAACqK,kCAAhB,CAAmDvJ,IAAnD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe4F,kBAApB;AACI,qBAAO,IAAI1G,WAAW,CAACsK,+BAAhB,CAAgDxJ,IAAhD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe8F,uBAApB;AACI,qBAAO,IAAI5G,WAAW,CAACuK,oCAAhB,CAAqDzJ,IAArD,CAAP;AACJ;;AACA,iBAAK;AAAA;AAAA,gDAAcgF,eAAnB;AACI,qBAAO,IAAI7F,UAAU,CAACuK,qBAAf,CAAqC1J,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc2G,kBAAnB;AACI,qBAAO,IAAIxH,UAAU,CAACwK,wBAAf,CAAwC3J,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc6G,WAAnB;AACI,qBAAO,IAAI1H,UAAU,CAACyK,iBAAf,CAAiC5J,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc+G,WAAnB;AACI,qBAAO,IAAI5H,UAAU,CAAC0K,iBAAf,CAAiC7J,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAckF,YAAnB;AACI,qBAAO,IAAI/F,UAAU,CAAC2K,kBAAf,CAAkC9J,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAckH,iBAAnB;AACI,qBAAO,IAAI/H,UAAU,CAAC4K,uBAAf,CAAuC/J,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcoF,mBAAnB;AACI,qBAAO,IAAIjG,UAAU,CAAC6K,yBAAf,CAAyChK,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcsF,wBAAnB;AACI,qBAAO,IAAInG,UAAU,CAAC8K,8BAAf,CAA8CjK,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcgG,YAAnB;AACI,qBAAO,IAAI7G,UAAU,CAAC+K,kBAAf,CAAkClK,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAckG,aAAnB;AACI,qBAAO,IAAI/G,UAAU,CAACgL,mBAAf,CAAmCnK,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcoG,aAAnB;AACI,qBAAO,IAAIjH,UAAU,CAACiL,mBAAf,CAAmCpK,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcsG,aAAnB;AACI,qBAAO,IAAInH,UAAU,CAACkL,mBAAf,CAAmCrK,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcwF,oBAAnB;AACI,qBAAO,IAAIrG,UAAU,CAACmL,0BAAf,CAA0CtK,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc4F,kBAAnB;AACI,qBAAO,IAAIzG,UAAU,CAACoL,wBAAf,CAAwCvK,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc8F,uBAAnB;AACI,qBAAO,IAAI3G,UAAU,CAACqL,6BAAf,CAA6CxK,IAA7C,CAAP;;AACJ;AACI,oBAAM,IAAIkD,KAAJ,2BAAkClD,IAAI,CAACmD,IAAvC,CAAN;AAzFR;AA2FH;;AA7Fe,O", "sourcesContent": ["import { Emitter } from \"./Emitter\";\r\nimport { Bullet } from \"./Bullet\";\r\nimport { eEmitterCondition, eBulletCondition } from \"../data/bullet/EventConditionType\";\r\nimport { eBulletAction, eEmitterAction } from \"../data/bullet/EventActionType\";\r\nimport { IEventCondition } from \"./conditions/IEventCondition\";\r\nimport { IEventAction } from \"./actions/IEventAction\";\r\nimport { eConditionOp, eCompareOp, EventGroupData, EventActionData, EventConditionData } from \"../data/bullet/EventGroupData\";\r\nimport * as emitter_cond from \"./conditions/EmitterEventConditions\";\r\nimport * as bullet_cond from \"./conditions/BulletEventConditions\";\r\nimport * as emitter_act from \"./actions/EmitterEventActions\";\r\nimport * as bullet_act from \"./actions/BulletEventActions\";\r\nimport { BulletSystem } from \"./BulletSystem\";\r\n\r\n// context for running condition & action\r\nexport class EventGroupContext {\r\n    emitter: Emitter | null = null;\r\n    bullet: Bullet | null = null;\r\n    // TODO: add level \r\n\r\n    reset(): void {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n    }\r\n}\r\n\r\n// Condition chain with operators\r\nclass ConditionChain {\r\n    conditions: Array<IEventCondition> = [];\r\n\r\n    evaluate(context: EventGroupContext): boolean {\r\n        if (this.conditions.length === 0) return true;\r\n        let result = this.conditions[0].evaluate(context);\r\n        \r\n        for (let i = 1; i < this.conditions.length; i++) {\r\n            const condition = this.conditions[i];\r\n            const conditionResult = condition.evaluate(context);\r\n            \r\n            if (condition.data.op === eConditionOp.And) {\r\n                result = result && conditionResult;\r\n            } else if (condition.data.op === eConditionOp.Or) {\r\n                result = result || conditionResult;\r\n            }\r\n        }\r\n        \r\n        return result;\r\n    }\r\n}\r\n\r\n// Updated EventGroup\r\nexport enum eEventGroupStatus {\r\n    Idle,       // not active\r\n    Waiting,    // waiting for conditions to be met\r\n    Active,     // conditions are met, now ticking actions\r\n    Stopped     // stopped\r\n}\r\n\r\nexport class EventGroup {\r\n    readonly data: EventGroupData;\r\n\r\n    context: EventGroupContext;\r\n    conditionChain: ConditionChain;\r\n    actions: IEventAction[];\r\n\r\n    private _triggerCount: number = 0;\r\n    private _status: eEventGroupStatus = eEventGroupStatus.Idle;\r\n    get status(): eEventGroupStatus {\r\n        return this._status;\r\n    }\r\n    \r\n    constructor(ctx: EventGroupContext, data: EventGroupData) {\r\n        this.context = ctx;\r\n        this.data = data;\r\n        this.conditionChain = this.buildConditionChain(data.conditions);\r\n        this.actions = data.actions.map(actionData => {\r\n            let action = ActionFactory.create(actionData);\r\n            return action;\r\n        });\r\n        this._triggerCount = 0;\r\n\r\n        this.changeStatus(eEventGroupStatus.Idle);\r\n    }\r\n\r\n    start(): void {\r\n        BulletSystem.onCreateEventGroup(this);\r\n        this.changeStatus(eEventGroupStatus.Waiting);\r\n    }\r\n\r\n    stop(): void {\r\n        // both stop and idle will do the trick\r\n        BulletSystem.onDestroyEventGroup(this);\r\n        this.changeStatus(eEventGroupStatus.Stopped); \r\n    }\r\n\r\n    canExecute(): boolean {\r\n        return this.conditionChain.evaluate(this.context);\r\n    }\r\n    \r\n    tick(dt: number): void {\r\n        \r\n        switch (this._status) {\r\n            case eEventGroupStatus.Idle:\r\n                // not active\r\n                break;\r\n            case eEventGroupStatus.Waiting:\r\n                // waiting for conditions to be met\r\n                if (this.canExecute()) {\r\n                    // TODO: 考虑这里检测增加时间间隔来减少消耗\r\n                    this.changeStatus(eEventGroupStatus.Active);\r\n                }\r\n                break;\r\n            case eEventGroupStatus.Active:\r\n                // conditions are met, now ticking actions\r\n                this.tickActive(dt);\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                // stopped\r\n                break;\r\n        }\r\n    }\r\n\r\n    private changeStatus(newStatus: eEventGroupStatus) {\r\n        if (this._status === newStatus) return;\r\n\r\n        this._status = newStatus;\r\n    \r\n        switch (this._status) {\r\n            case eEventGroupStatus.Waiting:\r\n                // reset actions by onLoad\r\n                break;\r\n            case eEventGroupStatus.Active:\r\n                // 启用时，重置action的初始参数\r\n                this.actions.forEach(action => action.onLoad(this.context));\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                break;\r\n            default: break;\r\n        }\r\n    }\r\n\r\n    private buildConditionChain(conditions: EventConditionData[]): ConditionChain {\r\n        const chain = new ConditionChain();\r\n        conditions.forEach((condData, index) => {\r\n            const condition = ConditionFactory.create(condData);\r\n            if (condition) {\r\n                condition.onLoad(this.context);\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n\r\n    private tickActive(dt: number): void {\r\n        let isAllFinished = true;\r\n\r\n        for (const action of this.actions) {\r\n            if (action.isCompleted()) continue;\r\n            action.onExecute(this.context, dt);\r\n            isAllFinished = false;\r\n        }\r\n        \r\n        if (isAllFinished) {\r\n            this._triggerCount++;\r\n            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {\r\n                // restart\r\n                this.changeStatus(eEventGroupStatus.Waiting);\r\n            }\r\n            else {\r\n                this.stop();\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// 提供一个静态函数帮助比较value\r\nexport class Comparer {\r\n    static compare(a: number, b: number, op: eCompareOp): boolean {\r\n        switch (op) {\r\n            case eCompareOp.Equal:\r\n                return a === b;\r\n            case eCompareOp.NotEqual:\r\n                return a !== b;\r\n            case eCompareOp.Greater:\r\n                return a > b;\r\n            case eCompareOp.GreaterEqual:\r\n                return a >= b;\r\n            case eCompareOp.Less:\r\n                return a < b;\r\n            case eCompareOp.LessEqual:\r\n                return a <= b;\r\n            default:\r\n                throw new Error(`Unknown compare operator: ${op}`);\r\n        }\r\n    }\r\n}\r\n\r\n// Factory pattern for conditions & actions\r\nclass ConditionFactory {\r\n    static create(data: EventConditionData): IEventCondition {\r\n        switch (data.type) {\r\n            case eEmitterCondition.Emitter_Active:\r\n                return new emitter_cond.EmitterCondition_Active(data);\r\n            case eEmitterCondition.Emitter_InitialDelay:\r\n                return new emitter_cond.EmitterCondition_InitialDelay(data);\r\n            case eEmitterCondition.Emitter_Prewarm:\r\n                return new emitter_cond.EmitterCondition_Prewarm(data);\r\n            case eEmitterCondition.Emitter_PrewarmDuration:\r\n                return new emitter_cond.EmitterCondition_PrewarmDuration(data);\r\n            case eEmitterCondition.Emitter_Duration:\r\n                return new emitter_cond.EmitterCondition_Duration(data);\r\n            case eEmitterCondition.Emitter_ElapsedTime:\r\n                return new emitter_cond.EmitterCondition_ElapsedTime(data);\r\n            case eEmitterCondition.Emitter_Loop:\r\n                return new emitter_cond.EmitterCondition_Loop(data);\r\n            case eEmitterCondition.Emitter_LoopInterval:\r\n                return new emitter_cond.EmitterCondition_LoopInterval(data);\r\n            case eEmitterCondition.Emitter_EmitInterval:\r\n                return new emitter_cond.EmitterCondition_EmitInterval(data);\r\n            case eEmitterCondition.Emitter_PerEmitCount:\r\n                return new emitter_cond.EmitterCondition_PerEmitCount(data);\r\n            case eEmitterCondition.Emitter_PerEmitInterval:\r\n                return new emitter_cond.EmitterCondition_PerEmitInterval(data);\r\n            case eEmitterCondition.Emitter_PerEmitOffsetX:\r\n                return new emitter_cond.EmitterCondition_PerEmitOffsetX(data);\r\n            case eEmitterCondition.Emitter_Angle:\r\n                return new emitter_cond.EmitterCondition_Angle(data);\r\n            case eEmitterCondition.Emitter_Count:\r\n                return new emitter_cond.EmitterCondition_Count(data);\r\n            case eEmitterCondition.Bullet_Duration:\r\n                return new emitter_cond.EmitterCondition_BulletDuration(data);\r\n            case eEmitterCondition.Bullet_Speed:\r\n                return new emitter_cond.EmitterCondition_BulletSpeed(data);\r\n            case eEmitterCondition.Bullet_Acceleration:\r\n                return new emitter_cond.EmitterCondition_BulletAcceleration(data);\r\n            case eEmitterCondition.Bullet_AccelerationAngle:\r\n                return new emitter_cond.EmitterCondition_BulletAccelerationAngle(data);\r\n            case eEmitterCondition.Bullet_FacingMoveDir:\r\n                return new emitter_cond.EmitterCondition_BulletFacingMoveDir(data);\r\n            case eEmitterCondition.Bullet_TrackingTarget:\r\n                return new emitter_cond.EmitterCondition_BulletTrackingTarget(data);\r\n            case eEmitterCondition.Bullet_Destructive:\r\n                return new emitter_cond.EmitterCondition_BulletDestructive(data);\r\n            case eEmitterCondition.Bullet_DestructiveOnHit:\r\n                return new emitter_cond.EmitterCondition_BulletDestructiveOnHit(data);\r\n            // case eEmitterCondition.Bullet_Sprite:\r\n            //     return new emitter_cond.EmitterCondition_BulletSprite(data);\r\n            case eEmitterCondition.Bullet_Scale:\r\n                return new emitter_cond.EmitterCondition_BulletScale(data);\r\n            case eEmitterCondition.Bullet_ColorR:\r\n                return new emitter_cond.EmitterCondition_BulletColorR(data);\r\n            case eEmitterCondition.Bullet_ColorG:\r\n                return new emitter_cond.EmitterCondition_BulletColorG(data);\r\n            case eEmitterCondition.Bullet_ColorB:\r\n                return new emitter_cond.EmitterCondition_BulletColorB(data);\r\n            case eEmitterCondition.Bullet_DefaultFacing:\r\n                return new emitter_cond.EmitterCondition_BulletDefaultFacing(data);\r\n            // ... bullet cases\r\n            case eBulletCondition.Bullet_Duration:\r\n                return new bullet_cond.BulletCondition_Duration(data);\r\n            case eBulletCondition.Bullet_ElapsedTime:\r\n                return new bullet_cond.BulletCondition_ElapsedTime(data);\r\n            case eBulletCondition.Bullet_PosX:\r\n                return new bullet_cond.BulletCondition_PosX(data);\r\n            case eBulletCondition.Bullet_PosY:\r\n                return new bullet_cond.BulletCondition_PosY(data);\r\n            case eBulletCondition.Bullet_Speed:\r\n                return new bullet_cond.BulletCondition_Speed(data);\r\n            case eBulletCondition.Bullet_SpeedAngle:\r\n                return new bullet_cond.BulletCondition_SpeedAngle(data);\r\n            case eBulletCondition.Bullet_Acceleration:\r\n                return new bullet_cond.BulletCondition_Acceleration(data);\r\n            case eBulletCondition.Bullet_AccelerationAngle:\r\n                return new bullet_cond.BulletCondition_AccelerationAngle(data);\r\n            case eBulletCondition.Bullet_Scale:\r\n                return new bullet_cond.BulletCondition_Scale(data);\r\n            case eBulletCondition.Bullet_ColorR:\r\n                return new bullet_cond.BulletCondition_ColorR(data);\r\n            case eBulletCondition.Bullet_ColorG:\r\n                return new bullet_cond.BulletCondition_ColorG(data);\r\n            case eBulletCondition.Bullet_ColorB:\r\n                return new bullet_cond.BulletCondition_ColorB(data);\r\n            case eBulletCondition.Bullet_FacingMoveDir:\r\n                return new bullet_cond.BulletCondition_FacingMoveDir(data);\r\n            case eBulletCondition.Bullet_Destructive:\r\n                return new bullet_cond.BulletCondition_Destructive(data);\r\n            case eBulletCondition.Bullet_DestructiveOnHit:\r\n                return new bullet_cond.BulletCondition_DestructiveOnHit(data);\r\n            default:\r\n                throw new Error(`Unknown condition type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\nclass ActionFactory {\r\n    static create(data: EventActionData): IEventAction {\r\n        switch (data.type) {\r\n            case eEmitterAction.Emitter_Active:\r\n                return new emitter_act.EmitterAction_Active(data);\r\n            case eEmitterAction.Emitter_InitialDelay:\r\n                return new emitter_act.EmitterAction_InitialDelay(data);\r\n            case eEmitterAction.Emitter_Prewarm:\r\n                return new emitter_act.EmitterAction_Prewarm(data);\r\n            case eEmitterAction.Emitter_PrewarmDuration:\r\n                return new emitter_act.EmitterAction_PrewarmDuration(data);\r\n            case eEmitterAction.Emitter_Duration:\r\n                return new emitter_act.EmitterAction_Duration(data);\r\n            case eEmitterAction.Emitter_ElapsedTime:\r\n                return new emitter_act.EmitterAction_ElapsedTime(data);\r\n            case eEmitterAction.Emitter_Loop:\r\n                return new emitter_act.EmitterAction_Loop(data);\r\n            case eEmitterAction.Emitter_LoopInterval:\r\n                return new emitter_act.EmitterAction_LoopInterval(data);\r\n            case eEmitterAction.Emitter_EmitInterval:\r\n                return new emitter_act.EmitterAction_EmitInterval(data);\r\n            case eEmitterAction.Emitter_PerEmitCount:\r\n                return new emitter_act.EmitterAction_PerEmitCount(data);\r\n            case eEmitterAction.Emitter_PerEmitInterval:\r\n                return new emitter_act.EmitterAction_PerEmitInterval(data);\r\n            case eEmitterAction.Emitter_PerEmitOffsetX:\r\n                return new emitter_act.EmitterAction_PerEmitOffsetX(data);\r\n            case eEmitterAction.Emitter_Angle:\r\n                return new emitter_act.EmitterAction_Angle(data);\r\n            case eEmitterAction.Emitter_Count:\r\n                return new emitter_act.EmitterAction_Count(data);\r\n            case eEmitterAction.Bullet_Duration:\r\n                return new emitter_act.EmitterAction_BulletDuration(data);\r\n            case eEmitterAction.Bullet_Damage:\r\n                return new emitter_act.EmitterAction_BulletDamage(data);\r\n            case eEmitterAction.Bullet_Speed:\r\n                return new emitter_act.EmitterAction_BulletSpeed(data);\r\n            case eEmitterAction.Bullet_SpeedAngle:\r\n                return new emitter_act.EmitterAction_BulletSpeedAngle(data);\r\n            case eEmitterAction.Bullet_Acceleration:\r\n                return new emitter_act.EmitterAction_BulletAcceleration(data);\r\n            case eEmitterAction.Bullet_AccelerationAngle:\r\n                return new emitter_act.EmitterAction_BulletAccelerationAngle(data);\r\n            case eEmitterAction.Bullet_Scale:\r\n                return new emitter_act.EmitterAction_BulletScale(data);\r\n            case eEmitterAction.Bullet_ColorR:\r\n                return new emitter_act.EmitterAction_BulletColorR(data);\r\n            case eEmitterAction.Bullet_ColorG:\r\n                return new emitter_act.EmitterAction_BulletColorG(data);\r\n            case eEmitterAction.Bullet_ColorB:\r\n                return new emitter_act.EmitterAction_BulletColorB(data);\r\n            case eEmitterAction.Bullet_FacingMoveDir:\r\n                return new emitter_act.EmitterAction_BulletFacingMoveDir(data);\r\n            case eEmitterAction.Bullet_TrackingTarget:\r\n                return new emitter_act.EmitterAction_BulletTrackingTarget(data);\r\n            case eEmitterAction.Bullet_Destructive:\r\n                return new emitter_act.EmitterAction_BulletDestructive(data);\r\n            case eEmitterAction.Bullet_DestructiveOnHit:\r\n                return new emitter_act.EmitterAction_BulletDestructiveOnHit(data);\r\n            // ... bullet cases\r\n            case eBulletAction.Bullet_Duration:\r\n                return new bullet_act.BulletAction_Duration(data);\r\n            case eBulletAction.Bullet_ElapsedTime:\r\n                return new bullet_act.BulletAction_ElapsedTime(data);\r\n            case eBulletAction.Bullet_PosX:\r\n                return new bullet_act.BulletAction_PosX(data);\r\n            case eBulletAction.Bullet_PosY:\r\n                return new bullet_act.BulletAction_PosY(data);\r\n            case eBulletAction.Bullet_Speed:\r\n                return new bullet_act.BulletAction_Speed(data);\r\n            case eBulletAction.Bullet_SpeedAngle:\r\n                return new bullet_act.BulletAction_SpeedAngle(data);\r\n            case eBulletAction.Bullet_Acceleration:\r\n                return new bullet_act.BulletAction_Acceleration(data);\r\n            case eBulletAction.Bullet_AccelerationAngle:\r\n                return new bullet_act.BulletAction_AccelerationAngle(data);\r\n            case eBulletAction.Bullet_Scale:\r\n                return new bullet_act.BulletAction_Scale(data);\r\n            case eBulletAction.Bullet_ColorR:\r\n                return new bullet_act.BulletAction_ColorR(data);\r\n            case eBulletAction.Bullet_ColorG:\r\n                return new bullet_act.BulletAction_ColorG(data);\r\n            case eBulletAction.Bullet_ColorB:\r\n                return new bullet_act.BulletAction_ColorB(data);\r\n            case eBulletAction.Bullet_FacingMoveDir:\r\n                return new bullet_act.BulletAction_FacingMoveDir(data);\r\n            case eBulletAction.Bullet_Destructive:\r\n                return new bullet_act.BulletAction_Destructive(data);\r\n            case eBulletAction.Bullet_DestructiveOnHit:\r\n                return new bullet_act.BulletAction_DestructiveOnHit(data);\r\n            default:\r\n                throw new Error(`Unknown action type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n"]}