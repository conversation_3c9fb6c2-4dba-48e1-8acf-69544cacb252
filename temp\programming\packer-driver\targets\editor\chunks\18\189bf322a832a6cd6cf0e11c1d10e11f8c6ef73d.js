System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Sprite, tween, Tween, <PERSON>de, <PERSON>ti<PERSON>, <PERSON>ll<PERSON>om<PERSON>, Buff<PERSON><PERSON>p, res, AttributeData, AttributeConst, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, PlaneBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/scripts/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillComp(extras) {
    _reporterNs.report("SkillComp", "./skill/SkillComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuffComp(extras) {
    _reporterNs.report("BuffComp", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuff(extras) {
    _reporterNs.report("Buff", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfbuiltin(extras) {
    _reporterNs.report("builtin", "db://assets/scripts/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfres(extras) {
    _reporterNs.report("res", "db://assets/scripts/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCircleCollider(extras) {
    _reporterNs.report("FCircleCollider", "db://assets/scripts/game/collider-system/FCircleCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "db://assets/scripts/game/collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFPolygonCollider(extras) {
    _reporterNs.report("FPolygonCollider", "db://assets/scripts/game/collider-system/FPolygonCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      Tween = _cc.Tween;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      Entity = _unresolved_2.default;
    }, function (_unresolved_3) {
      SkillComp = _unresolved_3.default;
    }, function (_unresolved_4) {
      BuffComp = _unresolved_4.default;
    }, function (_unresolved_5) {
      res = _unresolved_5.res;
    }, function (_unresolved_6) {
      AttributeData = _unresolved_6.AttributeData;
    }, function (_unresolved_7) {
      AttributeConst = _unresolved_7.AttributeConst;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Label', 'Sprite', 'tween', 'Tween', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", PlaneBase = (_dec = ccclass('PlaneBase'), _dec2 = property(Node), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(Label), _dec(_class = (_class2 = class PlaneBase extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "hpNode", _descriptor, this);

          _initializerDefineProperty(this, "hpBar", _descriptor2, this);

          // 血条
          _initializerDefineProperty(this, "hpAniSprite", _descriptor3, this);

          // 血条动画条
          _initializerDefineProperty(this, "hpfont", _descriptor4, this);

          // 血条文本
          this.enemy = true;
          // 是否为敌机
          this.isDead = false;
          // 是否死亡
          this.type = 0;
          // 敌人类型
          this.bDamageable = true;
          // 是否可以被造成伤害
          this.maxHp = 0;
          this.curHp = 0;
          this.collideComp = null;
          // 碰撞组件
          this._skillComp = null;
          this._buffComp = null;
          // TODO 临时做法，后续应该挪到 PlaneBase
          this._attributeData = new (_crd && AttributeData === void 0 ? (_reportPossibleCrUseOfAttributeData({
            error: Error()
          }), AttributeData) : AttributeData)();
        }

        init() {
          this._skillComp = new (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
            error: Error()
          }), SkillComp) : SkillComp)();
          this.addComp("skill", this._skillComp);
          this._buffComp = new (_crd && BuffComp === void 0 ? (_reportPossibleCrUseOfBuffComp({
            error: Error()
          }), BuffComp) : BuffComp)();
          this.addComp("buff", this._buffComp);
          super.init();
        }

        get skillComp() {
          return this._skillComp;
        }

        get buffComp() {
          return this._buffComp;
        }

        get attribute() {
          return this._attributeData;
        }

        set colliderEnabled(value) {
          if (this.collideComp) {
            this.collideComp.isEnable = value;
          }
        }

        get colliderEnabled() {
          return this.collideComp ? this.collideComp.isEnable : false;
        }

        CastSkill(skillID) {
          this.skillComp.Cast(this, skillID);
        }

        addHp(heal) {
          this.curHp = Math.min(this.maxHp, this.curHp + heal);
          this.updateHpUI();
          ;
        }

        hurt(damage) {
          if (this.isDead) {
            return;
          }

          this.cutHp(damage);
          this.playHurtAnim();

          if (this.curHp <= 0) {
            this.toDie();
          }
        }
        /**
         * 减少血量
         * @param {number} damage 受到的伤害值
         */


        cutHp(damage) {
          const newHp = this.curHp - damage;
          this.curHp = Math.max(0, newHp);
          this.updateHpUI();
        }

        toDie() {
          if (this.isDead) {
            return false;
          }

          this.isDead = true;
          this.colliderEnabled = false;
          return true;
        }
        /**
         * 更新血量显示
         */


        updateHpUI() {
          if (this.hpBar) {
            // 更新血条前景的填充范围
            this.hpBar.fillRange = this.curHp / this.maxHp;

            if (this.hpAniSprite) {
              // 计算血条动画时间
              const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);
              Tween.stopAllByTarget(this.hpAniSprite); // 血条中间部分的动画

              tween(this.hpAniSprite).to(duration, {
                fillRange: this.hpBar.fillRange
              }).call(() => {}).start();
            }
          } // 更新血量文字


          this.hpfont && (this.hpfont.string = this.curHp.toFixed(0));
        }

        playHurtAnim() {// 子类实现
        }

        ApplyBuffEffect(buff, effectData) {
          switch (effectData.type) {
            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.Kill:
              this.toDie();
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.Hurt:
              if (effectData.param.length >= 1) {
                this.hurt(effectData.param[0]);
              }

              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrMaxHPPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).MaxHP, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrMaxHPAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).MaxHP, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrHPRecoveryPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).HPRecovery, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrHPRecoveryAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).HPRecovery, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrHPRecoveryMaxHPPerAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).HPRecoveryRate, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrAttackPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).Attack, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrAttackAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).Attack, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrAttackBossPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).AttackBoss, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrAttackNormalPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).AttackNormal, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrFortunatePer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).Fortunate, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrFortunateAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).Fortunate, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrMissAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).MissRate, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrBulletHurtResistancePer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtResistance, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrBulletHurtResistanceAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtResistance, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrCollisionHurtResistancePer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).CollisionHurtResistance, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrCollisionHurtResistanceAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).CollisionHurtResistance, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrFinalScoreAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).FinalScoreRate, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrKillScoreAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).KillScoreRate, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrEnergyRecoveryPerAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).EnergyRecoveryRate, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrEnergyRecoveryAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).EnergyRecovery, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrPickRadiusPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).PickRadius, effectData, true);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrPickRadius:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).PickRadius, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrBombMax:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BombMax, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrBombHurtAdd:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BombHurt, effectData, false);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.AttrBombHurtPer:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BombHurt, effectData, true);
              break;

            default:
              break;
          }
        }

        ApplyBuffAttributeEffect(buff, key, effectData, isPer) {
          if (!buff) {
            return;
          }

          if (effectData.param.length < 1) {
            return;
          }

          this.attribute.addModify(buff.id, key, effectData.param[0], isPer);
        }

        RemoveBuffEffect(buff, effectData) {
          this.attribute.removeModify(buff.id);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "hpNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "hpBar", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpAniSprite", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "hpfont", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=189bf322a832a6cd6cf0e11c1d10e11f8c6ef73d.js.map