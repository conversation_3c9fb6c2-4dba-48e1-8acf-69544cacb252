[{"__type__": "cc.Prefab", "_name": "MailUI", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "MailUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}], "_prefab": {"__id__": 93}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53M5WozRhMEaveafwSBqnc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1334, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34dLLM7/hOh40aqxm4YvTM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0VlWWFtRN3Z4zUtHat82k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f17azGFA9A1q44j3yUjE0G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 17}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 1134}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54uHUTpehCSrbiKOgfkmdq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e24302db-92f1-490d-bfdc-18f650aecf7a@58cb7", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "e24302db-92f1-490d-bfdc-18f650aecf7a", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7cau4w3FGGo+o33Zqyf3U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90ZHpvGwpM2I1bFmtkqrmY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Top", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 18}, {"__id__": 32}], "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -86.22699999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ButtonClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "_prefab": {"__id__": 31}, "_lpos": {"__type__": "cc.Vec3", "x": 303.779, "y": 594.559, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0OgnivIBOcqjH/FPrFYWD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "关闭", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cLH1YyF1A8p39BdauVcB1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbCn5jKKRCh6riYSEk+8dX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1N+v4zNtMbKkWFhqtSVhr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70RvPsAJFElZ31o3AUOEFT"}, {"__type__": "13f7fOXLHFLK6LXmwAmgjNH", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 30}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "clickDefZoomScale": true, "audioUrl": "", "_N$string": "", "openContinuous": true, "continuousTime": 1, "openLongPress": false, "longPressTime": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6e1tjlgpLAYKe0L4ZVX7C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05seyuFD1HVock06gaG+zg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 588.098, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": {"__id__": 34}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dnY3cBvRPYJK8H0vpXmu9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": {"__id__": 36}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "邮件", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0YsaTEeBJWKgvkcw7hf1u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dqQ2qe41IuZlsYNSmLUwZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 39}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3S4bj0tNByLWEldg8QRoO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1nkeU2HNBwrZpFhT/USa5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "List", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 42}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": 3.2309999999999945, "y": 441.2539999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 41}, "_children": [{"__id__": 43}], "_active": true, "_components": [{"__id__": 63}, {"__id__": 65}, {"__id__": 67}], "_prefab": {"__id__": 69}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 44}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ListItem", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 43}, "_children": [{"__id__": 45}], "_active": true, "_components": [{"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 46}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 45}, "asset": {"__uuid__": "02c37c40-6959-458d-84f3-a169c87383c3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 47}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2duoP0ySNEmY484ccIirUm", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 48}, {"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_name"], "value": "MailCellUI"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 54}, "_contentSize": {"__type__": "cc.Size", "width": 666, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95ImUEYvpFfIVKZHsXG5S/"}, {"__type__": "b8d5d2T+bBINoz1NJmi12Ug", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 56}, "icon": null, "title": null, "selectedMode": 0, "selectedFlag": null, "selectedSpriteFrame": null, "adaptiveSize": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "darB9OqDRGvZo5eL4zpZcq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32zVKWF0dM8rkHeYxGhgSs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 59}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cFfzsWcBLuK+7r8klmT7w"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 61}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 1, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95hod933FBxL5n7s7vxJMg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0c1fh5OQZJ+L+Sf+nXGqlp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09pQej7RRJvodcvtbePcOC"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 66}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35DG1LoDdPuLRaezCquxo1"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 68}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b66Vb42pdFm6jFoe8Q53/h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efD61Qgp9Pw6WZK28p2sNp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 71}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84aT65YrhAUpwYmOu8VYEA"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 73}, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 43}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99Lv9/E+9FMouHGHpFrOJU"}, {"__type__": "1f720QMZiVFZIb6khvx7f27", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 75}, "templateType": 1, "tmpNode": {"__id__": 45}, "tmpPrefab": null, "_slideMode": 1, "pageDistance": 0.3, "pageChangeEvent": {"__id__": 76}, "_virtual": true, "cyclic": false, "lackCenter": false, "lackSlide": false, "_updateRate": 0, "frameByFrameRenderNum": 0, "renderEvent": {"__id__": 77}, "selectedMode": 0, "selectedEvent": {"__id__": 78}, "repeatEventSingle": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69JsaZzZdH37uXaPACYogk"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "df54eIR+M9E8Z45UuDhut+i", "handler": "onList<PERSON>ender", "customEventData": ""}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2lwhSoCNPUKKpf+bqga6K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 1134}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6lyVTDrJIV6nHTHrdlhkr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 83}, "_alignFlags": 45, "_target": null, "_left": 25, "_right": 25, "_top": 100, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 700, "_originalHeight": 1134, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccFbRMiidA8bXsJTRVLFJC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17M4lzZIBGRqQ1bRQ+iwRR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6jOgg9S9Mibgaenidf6ER"}, {"__type__": "df54eIR+M9E8Z45UuDhut+i", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 88}, "btnClose": {"__id__": 29}, "list": {"__id__": 74}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "da/DEpv5hOAa3kyFu1f3BQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 90}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1334, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7J8p71tRK7I+qKEZ/GnjD"}, {"__type__": "6bbfaY+KzRPAaPjjxBnsTv3", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 92}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "af8aDY3fVILqwPnNK4o6N/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 45}]}]