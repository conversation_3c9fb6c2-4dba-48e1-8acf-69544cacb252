{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/BulletEventActions.ts"], "names": ["BulletActionBase", "BulletAction_Duration", "BulletAction_ElapsedTime", "BulletAction_PosX", "BulletAction_PosY", "BulletAction_Speed", "BulletAction_SpeedAngle", "BulletAction_Acceleration", "BulletAction_AccelerationAngle", "BulletAction_Scale", "BulletAction_ColorR", "BulletAction_ColorG", "BulletAction_ColorB", "BulletAction_FacingMoveDir", "BulletAction_Destructive", "BulletAction_DestructiveOnHit", "EventActionBase", "resetStartValue", "context", "_startValue", "bullet", "prop", "duration", "value", "executeInternal", "elapsedTime", "node", "position", "x", "setPosition", "y", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "r", "g", "b", "canLerp", "isFacingMoveDir", "isDestructive", "isDestructiveOnHit"], "mappings": ";;;+CAGaA,gB,EAIAC,qB,EAUAC,wB,EAUAC,iB,EAWAC,iB,EAWAC,kB,EAUAC,uB,EAUAC,yB,EAUAC,8B,EAUAC,kB,EAUAC,mB,EAQAC,mB,EAQAC,mB,EAQAC,0B,EAUAC,wB,EAUAC,6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA/IJC,MAAAA,e,iBAAAA,e;;;;;;;kCAGIhB,gB,GAAN,MAAMA,gBAAN;AAAA;AAAA,8CAA+C,CAClD;AADkD,O;;uCAIzCC,qB,GAAN,MAAMA,qBAAN,SAAoCD,gBAApC,CAAqD;AAC9CiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBC,QAArB,CAA8BC,KAAjD;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBC,QAArB,CAA8BC,KAA9B,GAAsCA,KAAtC;AACH;;AAPuD,O;;0CAU/CrB,wB,GAAN,MAAMA,wBAAN,SAAuCF,gBAAvC,CAAwD;AACjDiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBK,WAAnC;AACH;;AAESD,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBK,WAAhB,GAA8BF,KAA9B;AACH;;AAP0D,O;;mCAUlDpB,iB,GAAN,MAAMA,iBAAN,SAAgCH,gBAAhC,CAAiD;AAC1CiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAArB,CAA8BC,CAAjD;AACH;;AAESJ,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvE,cAAMI,QAAQ,GAAGT,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAAtC;AACAT,UAAAA,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBG,WAArB,CAAiCN,KAAjC,EAAwCI,QAAQ,CAACG,CAAjD;AACH;;AARmD,O;;mCAW3C1B,iB,GAAN,MAAMA,iBAAN,SAAgCJ,gBAAhC,CAAiD;AAC1CiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAArB,CAA8BG,CAAjD;AACH;;AAESN,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvE,cAAMI,QAAQ,GAAGT,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAAtC;AACAT,UAAAA,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBG,WAArB,CAAiCF,QAAQ,CAACC,CAA1C,EAA6CL,KAA7C;AACH;;AARmD,O;;oCAW3ClB,kB,GAAN,MAAMA,kBAAN,SAAiCL,gBAAjC,CAAkD;AAC3CiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBU,KAArB,CAA2BR,KAA9C;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBU,KAArB,CAA2BR,KAA3B,GAAmCA,KAAnC;AACH;;AAPoD,O;;yCAU5CjB,uB,GAAN,MAAMA,uBAAN,SAAsCN,gBAAtC,CAAuD;AAChDiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBW,UAArB,CAAgCT,KAAnD;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBW,UAArB,CAAgCT,KAAhC,GAAwCA,KAAxC;AACH;;AAPyD,O;;2CAUjDhB,yB,GAAN,MAAMA,yBAAN,SAAwCP,gBAAxC,CAAyD;AAClDiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBY,YAArB,CAAkCV,KAArD;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBY,YAArB,CAAkCV,KAAlC,GAA0CA,KAA1C;AACH;;AAP2D,O;;gDAUnDf,8B,GAAN,MAAMA,8BAAN,SAA6CR,gBAA7C,CAA8D;AACvDiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBa,iBAArB,CAAuCX,KAA1D;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBa,iBAArB,CAAuCX,KAAvC,GAA+CA,KAA/C;AACH;;AAPgE,O;;oCAUxDd,kB,GAAN,MAAMA,kBAAN,SAAiCT,gBAAjC,CAAkD;AAC3CiB,QAAAA,eAAe,CAACC,OAAD,EAAmC;AACxD,eAAKC,WAAL,GAAmBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBc,KAArB,CAA2BZ,KAA9C;AACH;;AAESC,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBc,KAArB,CAA2BZ,KAA3B,GAAmCA,KAAnC;AACH;;AAPoD,O;;qCAU5Cb,mB,GAAN,MAAMA,mBAAN,SAAkCV,gBAAlC,CAAmD;AAC5CwB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvE,cAAIa,KAAK,GAAGlB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAAvC;AACAa,UAAAA,KAAK,CAACC,CAAN,GAAUd,KAAV;AACAL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAA3B,GAAmCa,KAAnC;AACH;;AALqD,O;;qCAQ7CzB,mB,GAAN,MAAMA,mBAAN,SAAkCX,gBAAlC,CAAmD;AAC5CwB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvE,cAAIa,KAAK,GAAGlB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAAvC;AACAa,UAAAA,KAAK,CAACE,CAAN,GAAUf,KAAV;AACAL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAA3B,GAAmCa,KAAnC;AACH;;AALqD,O;;qCAQ7CxB,mB,GAAN,MAAMA,mBAAN,SAAkCZ,gBAAlC,CAAmD;AAC5CwB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvE,cAAIa,KAAK,GAAGlB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAAvC;AACAa,UAAAA,KAAK,CAACG,CAAN,GAAUhB,KAAV;AACAL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBe,KAArB,CAA2Bb,KAA3B,GAAmCa,KAAnC;AACH;;AALqD,O;;4CAQ7CvB,0B,GAAN,MAAMA,0BAAN,SAAyCb,gBAAzC,CAA0D;AAC7DwC,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AAEShB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBoB,eAArB,CAAqClB,KAArC,GAA8CA,KAAK,KAAK,CAAxD;AACH;;AAP4D,O;;0CAUpDT,wB,GAAN,MAAMA,wBAAN,SAAuCd,gBAAvC,CAAwD;AAC3DwC,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AAEShB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBqB,aAArB,CAAmCnB,KAAnC,GAA4CA,KAAK,KAAK,CAAtD;AACH;;AAP0D,O;;+CAUlDR,6B,GAAN,MAAMA,6BAAN,SAA4Cf,gBAA5C,CAA6D;AAChEwC,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AAEShB,QAAAA,eAAe,CAACN,OAAD,EAA6BK,KAA7B,EAAkD;AACvEL,UAAAA,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBsB,kBAArB,CAAwCpB,KAAxC,GAAiDA,KAAK,KAAK,CAA3D;AACH;;AAP+D,O", "sourcesContent": ["import { EventActionBase } from \"./IEventAction\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\n\r\nexport class <PERSON>etActionBase extends EventActionBase {\r\n    // this was intentionally left blank\r\n}\r\n\r\nexport class BulletAction_Duration extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.duration.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.duration.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ElapsedTime extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.elapsedTime;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.elapsedTime = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_PosX extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.node.position.x;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        const position = context.bullet!.node.position;\r\n        context.bullet!.node.setPosition(value, position.y);\r\n    }\r\n}\r\n\r\nexport class BulletAction_PosY extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.node.position.y;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        const position = context.bullet!.node.position;\r\n        context.bullet!.node.setPosition(position.x, value);\r\n    }\r\n}\r\n\r\nexport class BulletAction_Speed extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.speed.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.speed.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_SpeedAngle extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.speedAngle.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.speedAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_Acceleration extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.acceleration.value;\r\n    }\r\n    \r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.acceleration.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_AccelerationAngle extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.accelerationAngle.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.accelerationAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_Scale extends BulletActionBase {\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = context.bullet!.prop.scale.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.scale.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorR extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        let color = context.bullet!.prop.color.value;\r\n        color.r = value;\r\n        context.bullet!.prop.color.value = color;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorG extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        let color = context.bullet!.prop.color.value;\r\n        color.g = value;\r\n        context.bullet!.prop.color.value = color;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorB extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        let color = context.bullet!.prop.color.value;\r\n        color.b = value;\r\n        context.bullet!.prop.color.value = color;\r\n    }\r\n}\r\n\r\nexport class BulletAction_FacingMoveDir extends BulletActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.isFacingMoveDir.value = (value === 1);\r\n    }\r\n}\r\n\r\nexport class BulletAction_Destructive extends BulletActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.isDestructive.value = (value === 1);\r\n    }\r\n}\r\n\r\nexport class BulletAction_DestructiveOnHit extends BulletActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    \r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.prop.isDestructiveOnHit.value = (value === 1);\r\n    }\r\n}"]}