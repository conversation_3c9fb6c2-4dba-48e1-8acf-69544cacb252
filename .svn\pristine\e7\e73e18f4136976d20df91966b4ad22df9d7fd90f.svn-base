import { _decorator, <PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, EventTouch, Label } from 'cc';

const { ccclass, property, executeInEditMode, menu, help, inspector } = _decorator;
@ccclass
//@menu('i18n:MAIN_MENU.component.ui/ButtonPlus')
@executeInEditMode
//@help('i18n:COMPONENT.help_url.button')
//@inspector('packages://buttonplus/inspector.js')
export class ButtonPlus extends Button {

    @property({ tooltip: "点击放大默认倍数" })
    clickDefZoomScale = true;
    @property({ tooltip: "音效路径", type: '', multiline: true, formerlySerializedAs: '_N$string' })
    audioUrl = '';
    @property({ tooltip: "屏蔽连续点击" })
    openContinuous = true;
    @property({ tooltip: "屏蔽时间, 单位:秒" })
    continuousTime = 0.5;


    // false表示可以点击
    continuous: boolean = false;
    // 定时器
    _continuousTimer:NodeJS.Timeout|null = null;

    private _pressed = false;


    // 长按触发
    @property({ tooltip: "是否开启长按事件" })
    openLongPress = false;
    // 触发时间
    @property({ tooltip: "长按时间" })
    longPressTime = 1;
    longPressFlag = false;

    private longPressTimer:NodeJS.Timeout|null = null;

    onEnable() {
        this.continuous = false;
        super.onEnable();
        // if (!CC_EDITOR) {
        // }
        if (this.clickDefZoomScale) {
            this.transition = 3
            this.zoomScale = 1.05
            this.duration = 0.1
        }
    }
    onDisable() {
        if (this._continuousTimer) {
            clearTimeout(this._continuousTimer);
            this._continuousTimer = null;
        }
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        super.onDisable();
    }

    /** 重写 */
    protected _onTouchBegan(event: EventTouch) {
        if (!this.interactable || !this.enabledInHierarchy) return;

        if (this.openLongPress && !this.longPressFlag) {    // 开启长按
            if (this.longPressTimer) clearTimeout(this.longPressTimer);
            this.longPressTimer = setTimeout(function (this:ButtonPlus) {
                // 还在触摸中 触发事件
                if (this._pressed) {
                    this.node.emit('longclickStart', this);
                    this.longPressFlag = true;
                }
            }.bind(this), this.longPressTime * 1000);
        }

        this._pressed = true;
        this._updateState();
        //event.stopPropagation();
        event.propagationStopped = true
    }
    protected _onTouchEnded(event: EventTouch) {
        if (!this.interactable || !this.enabledInHierarchy) return;
        if (this._pressed && this.longPressFlag) {
            this.node.emit('longclickEnd', this);
            this.longPressFlag = false;
        } else if (this._pressed && !this.continuous) {
            this.continuous = this.openContinuous ? true : false;
            EventHandler.emitEvents(this.clickEvents, event);
            this.node.emit('click', event);
            //SoundMgr.inst.playEffect(this.audioUrl)
            if (this.openContinuous) {
                this._continuousTimer = setTimeout(function (this:ButtonPlus) {
                    this.continuous = false;
                }.bind(this), this.continuousTime * 1000);
            }
        }
        this._pressed = false;
        this._updateState();
        event.propagationStopped = true
        //event.stopPropagation();
    }
    protected _onTouchCancel() {
        if (!this.interactable || !this.enabledInHierarchy) return;
        if (this._pressed && this.longPressFlag) {
            this.node.emit('longclickEnd', this);
            this.longPressFlag = false;
        }
        this._pressed = false;
        this._updateState();
    }
    /** 添加点击事件 */
    addClick(callback: Function, target: Object) {
        this.node.off('click');
        this.node.on('click', callback, target);

        if (this.node.getComponents(Button).length > 1) {
            console.error("Error! " + this.node.getComponentInChildren(Label)?.string + " ButtonPlus had Button Component");
        }
    }
    /** 添加一个长按事件 */
    addLongClick(startFunc: Function, endFunc: Function, target: Object) {
        this.node.off('longclickStart');
        this.node.off('longclickEnd');
        this.node.on('longclickStart', startFunc, target);
        this.node.on('longclickEnd', endFunc, target);
    }
}