import { _decorator, Component, Label, Node } from 'cc';
import { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';
import { DataEvent } from 'db://assets/bundles/common/script/event/DataEvent';
import { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';
import csproto, { cs } from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { PlaneUIEvent } from '../../../../event/PlaneUIEvent';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { DropDown } from '../../../common/components/dropdown/DropDown';
import { BagSortType, TabStatus } from '../../PlaneTypes';
const { ccclass, property } = _decorator;

interface MaterialSortInfo {
    item: csproto.cs.ICSItem; // 物品信息
    isMainMaterial: boolean; // 是否为主材料
    isSubMaterial: boolean; // 是否为子材料
    canSynthesize: boolean; // 是否可以合成
    isRelatedToSlotMain: boolean; // 是否与已点击的主材料相关
    combineQuality: number; // 合成优先级（品质越高，数值越大）
    materialQuality: number; // 材料自身的品质

}

@ccclass('SortTypeDropdown')
export class SortTypeDropdown extends Component {
    @property(DropDown)
    dropDown: DropDown | null = null;

    private _tabStatus: TabStatus = TabStatus.None;
    private _sortType: BagSortType = BagSortType.None;
    private _bagTabOptions: { key: BagSortType, label: string }[] = [
        { key: BagSortType.Quality, label: '按品质排序' },
        { key: BagSortType.Part, label: '按部位排序' },
    ];
    private _mergeTabOptions: { key: BagSortType, label: string }[] = [
        { key: BagSortType.Merge, label: '按合成排序' },
    ];

    onLoad() {
        this.getComponent(ButtonPlus)!.addClick(this.onDropDownOptionClick, this);
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChangeEvent, this);
        EventMgr.on(PlaneUIEvent.UpdateBagGrids, this.sortBag, this)
        EventMgr.on(DataEvent.ItemsRefresh, this.sortBag, this)
    }

    onTabChangeEvent(tabStatus: TabStatus) {
        if (tabStatus === this._tabStatus) {
            return;
        }
        this._tabStatus = tabStatus;
        let optionKeyList: string[]
        if (tabStatus === TabStatus.Bag) {
            optionKeyList = this._bagTabOptions.map(v => v.key)
        } else {
            optionKeyList = this._mergeTabOptions.map(v => v.key)
        }
        this.dropDown!.init(optionKeyList, this.onOptionRender.bind(this), this.onDropDownOptionClick.bind(this))
        this.sortBag();
    }

    onOptionRender(optNode: Node, optKey: string) {
        const label = optNode.getComponentInChildren(Label)!;
        let opt: { label: string, key: BagSortType };
        switch (this._tabStatus) {
            case TabStatus.Bag:
                opt = this._bagTabOptions.find(v => v.key == optKey)!
                label.string = opt.label;
                break;
            case TabStatus.Merge:
                opt = this._mergeTabOptions.find(v => v.key == optKey)!
                label.string = opt.label;
                break;
            default:
                //logError("PlaneUI", `onOptionRender error ${this._tabStatus}`)
                break;
        }
    }

    onDropDownOptionClick(optKey: string) {
        let opt: { label: string, key: BagSortType };
        switch (this._tabStatus) {
            case TabStatus.Bag:
                opt = this._bagTabOptions.find(v => v.key == optKey)!;
                break;
            case TabStatus.Merge:
                opt = this._mergeTabOptions.find(v => v.key == optKey)!;
                break;
            default:
                //logError("PlaneUI", `Dropdown onClickDropDownOption error ${this._tabStatus}`)
                return;
        }
        this.sortBag()
    }

    private sortBag() {
        let items = []
        switch (this._tabStatus) {
            case TabStatus.Bag:
                items = [...this.sortEquipsInBagTabStatus(), ...this.sortItems()]
                break;
            case TabStatus.Merge:
                items = this.sortEquipsInCombineTabStatus();
                break;
            default:
                //logError("PlaneUI", `Dropdown onDisPlayRefresh error ${this._tabStatus}`)
                return;
        }
        EventMgr.emit(PlaneUIEvent.SortTypeChange, this._tabStatus, items);
    }

    private sortItems() {
        let sorted: csproto.cs.ICSItem[] = []
        DataMgr.bag.items.forEach(v => {
            const cfg = MyApp.lubanMgr.table.TbResItem.get(v.item_id!)
            if (cfg) {
                sorted.push(v)
            }
        })
        sorted.sort((a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {
            return b.add_time! - a.add_time!
        })
        return sorted
    }

    private sortEquipsInBagTabStatus() {
        const tbEquip = MyApp.lubanMgr.table.TbResEquip;
        // 1. 找出所有空部位
        const emptySlots: csproto.cs.ICSEquipSlotInfo[] = DataMgr.equip.eqSlots.getEmptySlots();

        logDebug("PlaneUI", `sortEquipsInBagTabStatus item_total:${DataMgr.bag.items.length}, empty_slots:${emptySlots.join(',')}`);

        // 2. 将装备分为三部分：
        //    - emptySlotEquips: 对应空部位的装备（最高优先级）
        //    - unequippedEquips: 未装备的其他装备
        const emptySlotEquips: csproto.cs.ICSItem[] = [];
        const unequippedEquips: csproto.cs.ICSItem[] = [];

        for (const item of DataMgr.bag.items) {
            const cfg = tbEquip.get(item.item_id!);
            if (!cfg) continue;

            // 3. 检查是否对应空部位
            const isEmptyEquipClass = emptySlots.some(e => e.equip_class === cfg.equipClass);
            if (isEmptyEquipClass) {
                emptySlotEquips.push(item);
            } else {
                unequippedEquips.push(item);
            }
        }

        // 4. 排序函数
        const sortFn = (a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {
            const aCfg = tbEquip.get(a.item_id!)!;
            const bCfg = tbEquip.get(b.item_id!)!;

            if (this._sortType === BagSortType.Part) {
                // 按部位排序：先按部位类型，再按品质（从高到低）
                return aCfg.equipClass! - bCfg.equipClass! || bCfg.quality! - aCfg.quality!;
            } else {
                // 按品质排序：先按品质（从高到低），再按部位类型
                return bCfg.quality! - aCfg.quality! || aCfg.equipClass - bCfg.equipClass;
            }
        };

        // 4. 分别排序三部分
        const sortedEmptySlotEquips = emptySlotEquips.sort(sortFn);
        const sortedUnequippedEquips = unequippedEquips.sort(sortFn);


        // 5. 合并结果：空部位装备 →  其他装备
        const sorted = [...sortedEmptySlotEquips, ...sortedUnequippedEquips];

        // 6. 调试输出
        sorted.forEach((e, index) => {
            const cfg = tbEquip.get(e.item_id!)!;
            const equipType = sortedEmptySlotEquips.includes(e) ? "[空部位]" : "[已装备部位]"
            logDebug("PlaneUI", `${index + 1}. ${equipType} ID:${e.item_id} 部位:${cfg.equipClass} 品质:${cfg.quality} 数量:${e.count}`);
        });

        return sorted;
    }


    /**
    * 材料排序函数（支持主材料格子优先）
    * 功能：对背包中的材料进行智能排序
    * 排序规则：
    * 1. 如果主材料格子有放主材料，优先排该主材料相关的副材料
    * 2. 如果主材料格子没放主材料，按合成后品质排可合成主材料,然后也按品质排其他副材料
    * @returns 排序后的材料列表
    */
    sortEquipsInCombineTabStatus(): any[] {
        const items = [...DataMgr.bag.items, ...DataMgr.equip.eqSlots.getEquippedSlots().map(v => <cs.ICSItem>{
            item_id: v.equip_id,
            count: 1,
            guid: v.guid,
        })]
        const combineConfigs = DataMgr.equip.eqCombine.calculateAllCombinePossible(items)
        let materialInfos: MaterialSortInfo[] = []
        // 获取主材料格子上的材料
        const combineMainMaterial = DataMgr.equip.eqCombine.getByPos(0)?.item;

        items.forEach(v => {
            const info: MaterialSortInfo = {
                item: v,
                isMainMaterial: false,
                isSubMaterial: false,
                canSynthesize: false,
                combineQuality: 0,
                isRelatedToSlotMain: false,
                materialQuality: 0,
            }
            const itemQuality = MyApp.lubanTables.TbResItem.get(v.item_id!)?.quality
            if (!itemQuality) {
                const eqQuality = MyApp.lubanTables.TbResEquip.get(v.item_id!)?.quality
                if (!eqQuality) {
                    return
                }
                info.materialQuality = eqQuality
            } else {
                info.materialQuality = itemQuality
            }
            //info.isRelatedToSlotMain = combineMainMaterial?.item_id == v.item_id
            materialInfos.push(info)
        })

        combineConfigs.forEach(cfg => {
            const mainMaterial = cfg.consumeItems[0];
            const subMaterials = cfg.consumeItems.slice(1);
            materialInfos.forEach(v => {
                if (mainMaterial.id == v.item.item_id) {
                    v.isMainMaterial = true;
                    v.canSynthesize = true;
                    if (combineMainMaterial?.item_id == v.item.item_id) {
                        v.isRelatedToSlotMain = true;
                    }
                }
            });
            subMaterials.forEach(v => {
                materialInfos.forEach(m => {
                    if (DataMgr.equip.eqCombine.isSameMatType(v.id, m.item.item_id!)) {
                        m.isSubMaterial = true;
                        m.canSynthesize = true;
                        m.combineQuality = cfg.quality || 0;
                        if (combineMainMaterial?.item_id == mainMaterial.id) {
                            m.isRelatedToSlotMain = true;
                        }
                    }
                })
            })
        })

        materialInfos.sort((a, b) => {
            // 场景1: 主材料格子有材料
            if (combineMainMaterial) {
                // 1. 优先排与主材料格子相关的材料（包括主材料和副材料）
                if (a.isRelatedToSlotMain && !b.isRelatedToSlotMain) return -1;
                if (!a.isRelatedToSlotMain && b.isRelatedToSlotMain) return 1;

                // 2. 都是相关材料时，主材料优先于副材料
                if (a.isRelatedToSlotMain && b.isRelatedToSlotMain) {
                    if (a.isMainMaterial && !b.isMainMaterial) return -1;
                    if (!a.isMainMaterial && b.isMainMaterial) return 1;

                    // 都是副材料时，按合成后品质排序
                    if (b.combineQuality !== a.combineQuality) {
                        return b.combineQuality - a.combineQuality;
                    }
                }

                // 3. 非相关材料按自身品质排序
                if (b.materialQuality !== a.materialQuality) {
                    return b.materialQuality - a.materialQuality;
                }
            }
            // 场景2: 主材料格子没有材料
            else {
                // 1. 可合成材料优先于不可合成材料
                if (a.canSynthesize && !b.canSynthesize) return -1;
                if (!a.canSynthesize && b.canSynthesize) return 1;

                // 2. 都可合成时，按合成后品质排序（品质高的优先）
                if (a.canSynthesize && b.canSynthesize) {
                    if (b.combineQuality !== a.combineQuality) {
                        return b.combineQuality - a.combineQuality;
                    }

                    // 同一合成优先级时，主材料优先于副材料
                    if (a.isMainMaterial && !b.isMainMaterial) return -1;
                    if (!a.isMainMaterial && b.isMainMaterial) return 1;
                }

                // 3. 都不可合成时，按材料自身品质排序
                if (b.materialQuality !== a.materialQuality) {
                    return b.materialQuality - a.materialQuality;
                }
            }

            // 最后按ID排序确保稳定性
            return a.item!.item_id! - b.item!.item_id!;
        });

        // 调试输出
        logDebug("PlaneUI", `材料排序结果 - 主材料格子: ${combineMainMaterial ? `ID ${combineMainMaterial.item_id}` : '空'}`);
        materialInfos.forEach((material, index) => {
            const type = material.isMainMaterial ? '主材料' : '副材料';
            const status = material.canSynthesize ? `可合成(品质${material.combineQuality})` : '不可合成';
            const related = material.isRelatedToSlotMain ? '[格子相关]' : '';
            const quality = `品质${material.materialQuality}`;
            logDebug("PlaneUI", `${index + 1}. ID:${material.item?.item_id} 数量:${material.item.count} ${type} ${status} ${quality} ${related}`);
        });

        return materialInfos.map(v => v.item);
    }
}