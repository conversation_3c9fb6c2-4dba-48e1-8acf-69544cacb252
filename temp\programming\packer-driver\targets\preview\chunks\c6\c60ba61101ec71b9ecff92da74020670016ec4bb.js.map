{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/StageData.ts"], "names": ["StageData", "Tools", "id", "mainStage", "subStage", "type", "enemyNorIDs", "enemyNorInterval", "enemyNorRate", "loadJson", "data", "stringToNumber", "enemyGroupID", "Number", "delay", "hasOwnProperty"], "mappings": ";;;2EAOMA,S;;;;;;;;;;;;;;;;;AAJGC,MAAAA,K,iBAAAA,K;;;;;;;2BAIHD,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZE,EADY,GACP,CADO;AAAA,eAEZC,SAFY,GAEA,CAFA;AAAA,eAGZC,QAHY,GAGD,CAHC;AAAA,eAIZC,IAJY,GAIL,CAJK;AAAA,eAKZC,WALY,GAKW,EALX;AAAA,eAMZC,gBANY,GAMO,CANP;AAAA,eAOZC,YAPY,GAOY,EAPZ;AAAA;;AAUZC,QAAAA,QAAQ,CAACC,IAAD,EAAa;AACjB,eAAKP,SAAL,GAAiBO,IAAI,CAACP,SAAtB;AACA,eAAKC,QAAL,GAAgBM,IAAI,CAACN,QAArB;AACA,eAAKC,IAAL,GAAYK,IAAI,CAACL,IAAjB;AACA,eAAKC,WAAL,GAAmB;AAAA;AAAA,8BAAMK,cAAN,CAAqBD,IAAI,CAACE,YAA1B,EAAwC,GAAxC,CAAnB;AACA,eAAKL,gBAAL,GAAwBM,MAAM,CAACH,IAAI,CAACI,KAAN,CAA9B;;AAEA,cAAIJ,IAAI,CAACK,cAAL,CAAoB,cAApB,KAAuCL,IAAI,CAACF,YAAL,KAAsB,EAAjE,EAAqE;AACjE,iBAAKA,YAAL,GAAoB;AAAA;AAAA,gCAAMG,cAAN,CAAqBD,IAAI,CAACF,YAA1B,EAAwC,GAAxC,CAApB;AACH;AACJ;;AApBW,O", "sourcesContent": ["\r\n\r\nimport { error } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { Stage } from \"../../autogen/luban/schema\";\r\n\r\n\r\nclass StageData {\r\n    id = 0;\r\n    mainStage = 0;\r\n    subStage = 0;\r\n    type = 0;\r\n    enemyNorIDs:number[] = [];\r\n    enemyNorInterval = 0;\r\n    enemyNorRate:number[] = [];\r\n\r\n\r\n    loadJson(data:Stage) {\r\n        this.mainStage = data.mainStage;\r\n        this.subStage = data.subStage;\r\n        this.type = data.type;\r\n        this.enemyNorIDs = Tools.stringToNumber(data.enemyGroupID, ',');\r\n        this.enemyNorInterval = Number(data.delay);\r\n\r\n        if (data.hasOwnProperty('enemyNorRate') && data.enemyNorRate !== '') {\r\n            this.enemyNorRate = Tools.stringToNumber(data.enemyNorRate, ',');\r\n        }\r\n    }\r\n}\r\n\r\nexport { StageData};"]}