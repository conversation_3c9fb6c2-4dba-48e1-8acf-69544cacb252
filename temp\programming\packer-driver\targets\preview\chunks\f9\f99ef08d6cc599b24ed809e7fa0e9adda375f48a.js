System.register(["__unresolved_0", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, SingletonBase, GameDataManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  _export("GameDataManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }],
    execute: function () {
      _crd = true;

      _export("GameDataManager", GameDataManager = class GameDataManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this.curMainStage = 1;
          // 当前主关卡
          this.curSubStage = 1;
          // 当前子关卡
          this.reviveCount = 0;
          // 当前子关卡
          this.isHardMode = false;
        }

        // 是否为困难模式
        resetBattleData() {}

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=f99ef08d6cc599b24ed809e7fa0e9adda375f48a.js.map