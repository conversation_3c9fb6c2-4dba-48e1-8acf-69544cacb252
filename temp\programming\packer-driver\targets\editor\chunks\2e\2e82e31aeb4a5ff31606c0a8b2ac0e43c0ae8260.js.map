{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts"], "names": ["EventConditionBase", "constructor", "data", "_targetValue", "onLoad", "context", "targetValue", "eval", "evaluate"], "mappings": ";;;mBAWaA,kB;;;;;;;;;;;;;;;;;mBARb;;oCAQaA,kB,GAAN,MAAMA,kBAAN,CAAoD;AAKvDC,QAAAA,WAAW,CAACC,IAAD,EAA4B;AAAA,eAJ9BA,IAI8B;AAAA,eAF7BC,YAE6B,GAFN,CAEM;AACnC,eAAKD,IAAL,GAAYA,IAAZ;AACH;;AAEDE,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,eAAKF,YAAL,GAAoB,KAAKD,IAAL,CAAUI,WAAV,CAAsBC,IAAtB,EAApB;AACH;;AAEDC,QAAAA,QAAQ,CAACH,OAAD,EAAsC;AAC1C;AACA,iBAAO,IAAP;AACH;;AAhBsD,O", "sourcesContent": ["import { IEventConditionData } from \"../../data/bullet/EventGroupData\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\n\r\n// Base interfaces\r\nexport interface IEventCondition {\r\n    readonly data: IEventConditionData;\r\n\r\n    onLoad(context: EventGroupContext): void;\r\n    evaluate(context: EventGroupContext): boolean;\r\n}\r\n\r\nexport class EventConditionBase implements IEventCondition {\r\n    readonly data: IEventConditionData;\r\n\r\n    protected _targetValue: number = 0;\r\n\r\n    constructor(data: IEventConditionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    onLoad(context: EventGroupContext): void {\r\n        this._targetValue = this.data.targetValue.eval();\r\n    }\r\n\r\n    evaluate(context: EventGroupContext): boolean {\r\n        // Default implementation (can be overridden)\r\n        return true;\r\n    }\r\n}\r\n"]}