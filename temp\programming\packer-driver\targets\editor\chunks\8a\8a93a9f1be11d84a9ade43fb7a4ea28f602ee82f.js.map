{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts"], "names": ["WaveManager", "warn", "SingletonBase", "GameConst", "GameIns", "Tools", "enemyCreateAble", "_bEnemyCreateAble", "value", "constructor", "_waveNorDatasMap", "Map", "_enemyOver", "_bEnemyNorCreateAble", "_enemyActions", "_enemyActionIndex", "_enemyCreateTime", "_curEnemyAction", "_waveCreateTime", "_waveIndexOver", "_waveIndex", "_waveArr", "_waveNumArr", "_waveTimeArr", "_waveActionArr", "_waves", "_boss<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bossCreateTime", "_bossToAddArr", "_bShowBossWarning", "reset", "splice", "setEnemyActions", "actions", "gameStart", "getNorWaveDatas", "groupID", "get", "addWaveByLevel", "wave", "posX", "posY", "trigger", "push", "updateGameLogic", "deltaTime", "_updateCurAction", "_updateEnemy", "_updateBoss", "length", "action", "type", "enemyNorInterval", "enemyManager", "getNormalPlaneCount", "console", "enemyNorIDs", "_updateWaves", "_updateNorEnemys", "dtInMiliseconds", "i", "tick", "isCompleted", "_updateEnemyCreate", "currentEnemyCount", "EnemyPos", "x", "y", "bSetStartPos", "startPosX", "startPosY", "j", "enemyNum", "enemyInterval", "enemy", "enemyPosX", "posDX", "enemyPosY", "posDY", "addPlane", "enemyID", "firstShootDelay", "setFirstShoot<PERSON>elay", "setStandByTime", "initTrack", "trackGroups", "liveParam", "rotateSpeed", "waveID", "waveDatas", "log", "arrC<PERSON>ain", "groupInterval", "isEnemyOver", "bossData", "boss", "boss<PERSON><PERSON><PERSON>", "addBoss"], "mappings": ";;;oHAYqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZbC,MAAAA,I,OAAAA,I;;AACCC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AAEAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;yBAOYL,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AA4B7C,YAAfM,eAAe,GAAY;AAC3B,iBAAO,KAAKC,iBAAZ;AACH;;AAEkB,YAAfD,eAAe,CAACE,KAAD,EAAiB;AAChC,eAAKD,iBAAL,GAAyBC,KAAzB;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,kBADU,CAEV;;AAFU,eAnCNC,gBAmCM,GAnCuC,IAAIC,GAAJ,EAmCvC;AAnCiD;AAmCjD,eAlCNC,UAkCM,GAlCgB,KAkChB;AAlCsB;AAkCtB,eAhCNL,iBAgCM,GAhCuB,KAgCvB;AAhC6B;AAgC7B,eA/BNM,oBA+BM,GA/B0B,KA+B1B;AA/BgC;AA+BhC,eA7BNC,aA6BM,GA7B8B,IA6B9B;AA7BmC;AA6BnC,eA5BNC,iBA4BM,GA5BsB,CA4BtB;AA5BwB;AA4BxB,eA3BNC,gBA2BM,GA3BqB,CA2BrB;AA3BuB;AA2BvB,eA1BNC,eA0BM,GA1B8B,IA0B9B;AA1BmC;AA0BnC,eAzBNC,eAyBM,GAzBoB,CAyBpB;AAAA,eAxBNC,cAwBM,GAxBqB,EAwBrB;AAAA,eAtBNC,UAsBM,GAtBe,CAsBf;AAtBiB;AAsBjB,eArBNC,QAqBM,GArBkB,EAqBlB;AArBqB;AAqBrB,eApBNC,WAoBM,GApBkB,EAoBlB;AApBqB;AAoBrB,eAnBNC,YAmBM,GAnBmB,EAmBnB;AAnBsB;AAmBtB,eAlBNC,cAkBM,GAlBkB,EAkBlB;AAAA,eAjBNC,MAiBM,GAjBW,EAiBX;AAfd;AAec,eAdNC,gBAcM,GAdqB,CAcrB;AAAA,eAbNC,eAaM,GAboB,CAapB;AAAA,eAZNC,aAYM,GAZiB,EAYjB;AAAA,eAXNC,iBAWM,GAXuB,KAWvB;AAGb,SAvC+D,CAyChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAC,QAAAA,KAAK,GAAS;AACV,eAAKlB,UAAL,GAAkB,KAAlB;AACA,eAAKE,aAAL,GAAqB,EAArB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKT,iBAAL,GAAyB,KAAzB;AACA,eAAKM,oBAAL,GAA4B,KAA5B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKF,eAAL,GAAuB,CAAvB;;AACA,eAAKC,cAAL,CAAoBY,MAApB,CAA2B,CAA3B;;AACA,eAAKd,eAAL,GAAuB,IAAvB;AACA,eAAKI,QAAL,GAAgB,EAAhB;AACA,eAAKG,cAAL,GAAsB,EAAtB;AACA,eAAKF,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKE,MAAL,GAAc,EAAd;AACA,eAAKI,iBAAL,GAAyB,KAAzB;AACA,eAAKF,eAAL,GAAuB,CAAvB;AACH;;AAEDK,QAAAA,eAAe,CAACC,OAAD,EAA6B;AACxC,eAAKnB,aAAL,GAAqBmB,OAArB;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd,eAAK3B,iBAAL,GAAyB,IAAzB;AACA,eAAKM,oBAAL,GAA4B,IAA5B;AACA,eAAKQ,QAAL,GAAgB,EAAhB;AACA,eAAKG,cAAL,GAAsB,EAAtB;AACA,eAAKF,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB,CANc,CAQd;AACA;AACH;;AAEDY,QAAAA,eAAe,CAACC,OAAD,EAA2C;AACtD,iBAAO,KAAK1B,gBAAL,CAAsB2B,GAAtB,CAA0BD,OAA1B,CAAP;AACH;;AAGDE,QAAAA,cAAc,CAACC,IAAD,EAAaC,IAAb,EAA2BC,IAA3B,EAA8C;AACxDF,UAAAA,IAAI,CAACG,OAAL;;AACA,eAAKjB,MAAL,CAAYkB,IAAZ,CAAiBJ,IAAjB,EAFwD,CAIxD;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AACH;;AAGoB,cAAfK,eAAe,CAACC,SAAD,EAAoB;AACrC,eAAKC,gBAAL,CAAsBD,SAAtB;;AACA,eAAKE,YAAL,CAAkBF,SAAlB;;AACA,eAAKG,WAAL,CAAiBH,SAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,gBAAgB,CAACD,SAAD,EAA0B;AAC9C,cAAI,CAAC,KAAKjC,UAAV,EAAsB;AAAA;;AAClB;AACA;AACA;AAEA,gBAAI,KAAKG,iBAAL,KAA2B,6BAAKD,aAAL,yCAAoBmC,MAApB,KAA8B,CAAzD,CAAJ,EAAiE;AAC7D,mBAAKrC,UAAL,GAAkB,IAAlB;AACAX,cAAAA,IAAI,CAAC,YAAD,CAAJ;AACH,aAHD,MAGO,IAAI,KAAKK,eAAL,IAAwB,CAAC,KAAKW,eAAlC,EAAmD;AACtD,oBAAMiC,MAAM,GAAG,KAAKpC,aAAL,CAAoB,KAAKC,iBAAzB,CAAf;;AACA,sBAAQmC,MAAM,CAACC,IAAf;AACI,qBAAK,CAAL;AACI,uBAAKnC,gBAAL,IAAyB6B,SAAzB;;AACA,sBACI,KAAK7B,gBAAL,IAAyBkC,MAAM,CAACE,gBAAhC,IACC,KAAK/B,QAAL,CAAc4B,MAAd,KAAyB,CAAzB,IAA8B;AAAA;AAAA,0CAAQI,YAAR,CAAqBC,mBAArB,OAA+C,CAFlF,EAGE;AACE,yBAAKrC,eAAL,GAAuBiC,MAAvB;AACH;;AACD;;AACJ;AACI,sBAAIA,MAAM,CAACC,IAAP,IAAe,GAAnB,EAAwB;AACpBI,oBAAAA,OAAO,CAACtD,IAAR,CAAa,YAAb,EAA2BiD,MAAM,CAACC,IAAlC,EAAwCD,MAAM,CAACM,WAAP,CAAmB,CAAnB,CAAxC;AACA,yBAAK9B,gBAAL,GAAwBwB,MAAM,CAACE,gBAA/B;;AACA,yBAAKxB,aAAL,CAAmBe,IAAnB,CAAwBO,MAAxB;;AACA,yBAAKnC,iBAAL;AACH;;AAhBT;AAkBH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AAC8B,cAAZgC,YAAY,CAACF,SAAD,EAAoB;AAC1C;AACA,eAAKY,YAAL,CAAkBZ,SAAlB;;AAEA,cAAI,KAAK5B,eAAT,EAA0B;AACtB,gBAAI,CAAC,KAAKyC,gBAAL,CAAsBb,SAAtB,CAAL,EAAuC;AACnC,mBAAK5B,eAAL,GAAuB,IAAvB;AACA,mBAAKF,iBAAL;AACA,mBAAKC,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ;;AAEOyC,QAAAA,YAAY,CAACZ,SAAD,EAAoB;AACpC,gBAAMc,eAAe,GAAGd,SAAS,GAAG,IAApC;;AACA,eAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnC,MAAL,CAAYwB,MAAhC,EAAwCW,CAAC,EAAzC,EAA6C;AACzC,kBAAMrB,IAAI,GAAG,KAAKd,MAAL,CAAYmC,CAAZ,CAAb;AACArB,YAAAA,IAAI,CAACsB,IAAL,CAAUF,eAAV;;AACA,gBAAIpB,IAAI,CAACuB,WAAT,EAAsB;AAClB,mBAAKrC,MAAL,CAAYM,MAAZ,CAAmB6B,CAAnB,EAAsB,CAAtB;;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACoC,cAAlBG,kBAAkB,CAAClB,SAAD,EAAmC;AAC/D,eAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvC,QAAL,CAAc4B,MAAlC,EAA0CW,CAAC,EAA3C,EAA+C;AAC3C,kBAAMrB,IAAI,GAAG,KAAKlB,QAAL,CAAcuC,CAAd,CAAb;AACA,iBAAKrC,YAAL,CAAkBqC,CAAlB,KAAwBf,SAAxB;AACA,kBAAMmB,iBAAiB,GAAG,KAAK1C,WAAL,CAAiBsC,CAAjB,CAA1B;AACA,gBAAIpB,IAAI,GAAG;AAAA;AAAA,wCAAUyB,QAAV,CAAmBC,CAA9B;AACA,gBAAIzB,IAAI,GAAG;AAAA;AAAA,wCAAUwB,QAAV,CAAmBE,CAA9B;;AAEA,gBAAI5B,IAAI,CAAC6B,YAAT,EAAuB;AACnB5B,cAAAA,IAAI,IAAID,IAAI,CAAC8B,SAAb;AACA5B,cAAAA,IAAI,IAAIF,IAAI,CAAC+B,SAAb;AACH;;AAED,iBAAK,IAAIC,CAAC,GAAGP,iBAAb,EAAgCO,CAAC,GAAGhC,IAAI,CAACiC,QAAzC,EAAmDD,CAAC,EAApD,EAAwD;AACpD,kBAAIhC,IAAI,CAACkC,aAAL,IAAsBF,CAAC,GAAG,CAA1B,IAA+B,KAAKhD,YAAL,CAAkBqC,CAAlB,CAAnC,EAAyD;AACrD,qBAAKtC,WAAL,CAAiBsC,CAAjB;AACA,oBAAIc,KAAJ;AACA,sBAAMC,SAAS,GAAGnC,IAAI,GAAGD,IAAI,CAACqC,KAAL,IAAcL,CAAC,GAAG,CAAlB,CAAzB;AACA,sBAAMM,SAAS,GAAGpC,IAAI,GAAGF,IAAI,CAACuC,KAAL,IAAcP,CAAC,GAAG,CAAlB,CAAzB;;AAEA,wBAAQhC,IAAI,CAACY,IAAb;AACI,uBAAK,CAAL;AACIuB,oBAAAA,KAAK,GAAG,MAAM;AAAA;AAAA,4CAAQrB,YAAR,CAAqB0B,QAArB,CAA8BxC,IAAI,CAACyC,OAAnC,CAAd;;AACA,wBAAIN,KAAJ,EAAW;AACP,0BAAIH,CAAC,GAAGhC,IAAI,CAAC0C,eAAL,CAAqBhC,MAA7B,EAAqC;AACjCyB,wBAAAA,KAAK,CAACQ,kBAAN,CAAyB3C,IAAI,CAAC0C,eAAL,CAAqBV,CAArB,CAAzB;AACH;;AACDG,sBAAAA,KAAK,CAACS,cAAN,CAAqB,CAArB;AACAT,sBAAAA,KAAK,CAACU,SAAN,CACI7C,IAAI,CAAC8C,WADT,EAEI9C,IAAI,CAAC+C,SAFT,EAGIX,SAHJ,EAIIE,SAJJ,EAKItC,IAAI,CAACgD,WALT;AAOH;;AACD;AAhBR;AAkBH;AACJ;;AAED,gBAAIhD,IAAI,CAACiC,QAAL,IAAiB,KAAKlD,WAAL,CAAiBsC,CAAjB,CAArB,EAA0C;AACtC,mBAAKvC,QAAL,CAAcU,MAAd,CAAqB6B,CAArB,EAAwB,CAAxB;;AACA,mBAAKtC,WAAL,CAAiBS,MAAjB,CAAwB6B,CAAxB,EAA2B,CAA3B;;AACA,mBAAKrC,YAAL,CAAkBQ,MAAlB,CAAyB6B,CAAzB,EAA4B,CAA5B;;AACA,mBAAKpC,cAAL,CAAoBO,MAApB,CAA2B6B,CAA3B,EAA8B,CAA9B;;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACYF,QAAAA,gBAAgB,CAACb,SAAD,EAA6B;AACjD,cAAI,KAAKhC,oBAAT,EAA+B;AAC3B,gBAAI,KAAKO,UAAL,IAAmB,KAAKH,eAAL,CAAsBuC,WAAtB,CAAkCP,MAAzD,EAAiE;AAC7D,mBAAK7B,UAAL,GAAkB,CAAlB;AACA,qBAAO,KAAP;AACH;;AAED,kBAAMoE,MAAM,GAAG,KAAKvE,eAAL,CAAsBuC,WAAtB,CAAkC,KAAKpC,UAAvC,CAAf;AACA,iBAAKF,eAAL,IAAwB2B,SAAxB;AAEA,kBAAM4C,SAAS,GAAG,KAAKtD,eAAL,CAAqBqD,MAArB,CAAlB;;AACA,gBAAI,CAACC,SAAL,EAAgB;AACZ,qBAAO,KAAP;AACH;;AACDlC,YAAAA,OAAO,CAACmC,GAAR,CAAa,eAAcF,MAAO,qBAAoBC,SAAS,CAACxC,MAAO,EAAvE;;AACA,iBAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6B,SAAS,CAAExC,MAA/B,EAAuCW,CAAC,EAAxC,EAA4C;AACxC,oBAAMrB,IAAI,GAAGkD,SAAS,CAAE7B,CAAF,CAAtB;;AACA,kBACI,CAAC;AAAA;AAAA,kCAAM+B,UAAN,CAAiB,KAAKxE,cAAtB,EAAsCyC,CAAtC,CAAD,IACA,KAAK1C,eAAL,IAAwBqB,IAAI,CAACqD,aAFjC,EAGE;AACE,qBAAKvE,QAAL,CAAcsB,IAAd,CAAmBJ,IAAnB;;AACA,qBAAKjB,WAAL,CAAiBqB,IAAjB,CAAsB,CAAtB;;AACA,qBAAKpB,YAAL,CAAkBoB,IAAlB,CAAuB,CAAvB;;AACA,qBAAKnB,cAAL,CAAoBmB,IAApB,CAAyB,KAAK1B,eAA9B;;AACA,qBAAKE,cAAL,CAAoBwB,IAApB,CAAyBiB,CAAzB;AACH;AACJ;;AAED,gBAAI,KAAKzC,cAAL,CAAoB8B,MAApB,IAA8BwC,SAAS,CAAExC,MAA7C,EAAqD;AACjD,mBAAK9B,cAAL,CAAoBY,MAApB,CAA2B,CAA3B;;AACA,mBAAKb,eAAL,GAAuB,CAAvB;AACA,mBAAKE,UAAL;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACY4B,QAAAA,WAAW,CAACH,SAAD,EAA0B;AACzC,cAAI,KAAKjB,aAAL,CAAmBqB,MAAnB,GAA4B,CAA5B,IAAiC;AAAA;AAAA,kCAAQI,YAAR,CAAqBwC,WAArB,EAArC,EAAwE;AACpE,iBAAKlE,eAAL,IAAwBkB,SAAxB;;AACA,gBAAI,KAAKlB,eAAL,GAAuB,KAAKD,gBAAhC,EAAkD;AAC9C,oBAAMoE,QAAQ,GAAG,KAAKlE,aAAL,CAAmB,CAAnB,CAAjB;AACA,oBAAMmE,IAAI,GAAG;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,OAApB,CAA4BH,QAAQ,CAACtC,WAAT,CAAqB,CAArB,CAA5B,CAAb;;AACA,mBAAK5B,aAAL,CAAmBG,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACI8D,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKjF,UAAL,IAAmB,KAAKS,QAAL,CAAc4B,MAAd,KAAyB,CAA5C,IAAiD,KAAKrB,aAAL,CAAmBqB,MAAnB,KAA8B,CAAtF;AACH;;AAhT+D,O", "sourcesContent": ["import {warn } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyWave } from \"../data/EnemyWave\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { StageData } from \"../data/StageData\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n\r\nexport default class WaveManager extends SingletonBase<WaveManager> {\r\n    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();// 波次配表数据\r\n    private _enemyOver: boolean = false;//是否加载所有敌机\r\n\r\n    private _bEnemyCreateAble: boolean = false;//是否可以创建敌机\r\n    private _bEnemyNorCreateAble: boolean = false;//是否可以创建普通敌机\r\n\r\n    private _enemyActions: StageData[] | null = null;//小阶段所有数据列表\r\n    private _enemyActionIndex: number = 0;//当前小阶段索引\r\n    private _enemyCreateTime: number = 0;//当前小阶段时间\r\n    private _curEnemyAction: StageData | null = null;//当前波次数据\r\n    private _waveCreateTime: number = 0;\r\n    private _waveIndexOver: number[] = [];\r\n\r\n    private _waveIndex: number = 0;//当前波次的索引\r\n    private _waveArr: EnemyWave[] = [];//当前波次的所有敌机数据\r\n    private _waveNumArr: number[] = [];//当前波次已创建的敌机数量\r\n    private _waveTimeArr: number[] = [];//当前波次计时\r\n    private _waveActionArr: any[] = [];\r\n    private _waves: Wave[] = [];\r\n\r\n    //boss\r\n    private _bossCreateDelay: number = 0;\r\n    private _bossCreateTime: number = 0;\r\n    private _bossToAddArr: any[] = [];\r\n    private _bShowBossWarning: boolean = false;\r\n\r\n\r\n    get enemyCreateAble(): boolean {\r\n        return this._bEnemyCreateAble;\r\n    }\r\n\r\n    set enemyCreateAble(value: boolean) {\r\n        this._bEnemyCreateAble = value;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        // this.initConfig();\r\n    }\r\n\r\n    // initConfig() {\r\n    //     let waveDatas = MyApp.lubanTables.TbWave.getDataList();\r\n    //     for (let waveData of waveDatas) {\r\n    //         const wave = new EnemyWave();\r\n    //         wave.loadJson(waveData);\r\n    //         const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];\r\n    //         group.push(wave);\r\n    //         this._waveNorDatasMap.set(wave.enemyGroupID, group);\r\n    //     }\r\n    // }\r\n\r\n    reset(): void {\r\n        this._enemyOver = false;\r\n        this._enemyActions = [];\r\n        this._enemyActionIndex = 0;\r\n        this._enemyCreateTime = 0;\r\n        this._bEnemyCreateAble = false;\r\n        this._bEnemyNorCreateAble = false;\r\n        this._waveIndex = 0;\r\n        this._waveCreateTime = 0;\r\n        this._waveIndexOver.splice(0);\r\n        this._curEnemyAction = null;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n        this._waves = [];\r\n        this._bShowBossWarning = false;\r\n        this._bossCreateTime = 0;\r\n    }\r\n\r\n    setEnemyActions(actions: StageData[]): void {\r\n        this._enemyActions = actions;\r\n    }\r\n\r\n    gameStart(): void {\r\n        this._bEnemyCreateAble = true;\r\n        this._bEnemyNorCreateAble = true;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n\r\n        // 这里不能清掉，清掉后存在问题: 时序上是先addWaveByLevel，后gameStart\r\n        // this._waves = [];\r\n    }\r\n\r\n    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {\r\n        return this._waveNorDatasMap.get(groupID);\r\n    }\r\n\r\n    \r\n    addWaveByLevel(wave: Wave, posX: number, posY: number):void {\r\n        wave.trigger();\r\n        this._waves.push(wave);\r\n\r\n        // const enemyWave = EnemyWave.fromLevelWave(wave, posX, posY);\r\n        // this._waveArr.push(enemyWave)\r\n        // this._waveNumArr.push(0)\r\n        // this._waveTimeArr.push(0)\r\n        // this._waveActionArr.push(this._curEnemyAction!)\r\n\r\n        // console.log(`ybgg addWaveByLevel enemyWave:${enemyWave}`)\r\n        \r\n        // const group = this._waveNorDatasMap.get(enemyWave.enemyGroupID)\r\n        // if (group == null) {\r\n        //     this._waveNorDatasMap.set(enemyWave.enemyGroupID, [enemyWave]);\r\n        // } else {\r\n        //     group.push(enemyWave);\r\n        // }\r\n    }\r\n\r\n\r\n    async updateGameLogic(deltaTime: number) {\r\n        this._updateCurAction(deltaTime);\r\n        this._updateEnemy(deltaTime);\r\n        this._updateBoss(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 更新当前敌人行为\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateCurAction(deltaTime: number): void {\r\n        if (!this._enemyOver) {\r\n            // if (this._waves.length <= 0) {\r\n            //     this._enemyOver = true;\r\n            // }\r\n\r\n            if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {\r\n                this._enemyOver = true;\r\n                warn(\"enemy over\");\r\n            } else if (this.enemyCreateAble && !this._curEnemyAction) {\r\n                const action = this._enemyActions![this._enemyActionIndex];\r\n                switch (action.type) {\r\n                    case 0:\r\n                        this._enemyCreateTime += deltaTime;\r\n                        if (\r\n                            this._enemyCreateTime >= action.enemyNorInterval ||\r\n                            (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)\r\n                        ) {\r\n                            this._curEnemyAction = action;\r\n                        }\r\n                        break;\r\n                    default:\r\n                        if (action.type >= 100) {\r\n                            console.warn(\"Boss stage\", action.type, action.enemyNorIDs[0]);\r\n                            this._bossCreateDelay = action.enemyNorInterval;\r\n                            this._bossToAddArr.push(action);\r\n                            this._enemyActionIndex++;\r\n                        }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemy(deltaTime: number) {\r\n        // await this._updateEnemyCreate(deltaTime);\r\n        this._updateWaves(deltaTime);\r\n\r\n        if (this._curEnemyAction) {\r\n            if (!this._updateNorEnemys(deltaTime)) {\r\n                this._curEnemyAction = null;\r\n                this._enemyActionIndex++;\r\n                this._enemyCreateTime = 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _updateWaves(deltaTime: number) {\r\n        const dtInMiliseconds = deltaTime * 1000;\r\n        for (let i = 0; i < this._waves.length; i++) {\r\n            const wave = this._waves[i];\r\n            wave.tick(dtInMiliseconds);\r\n            if (wave.isCompleted) {\r\n                this._waves.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemyCreate(deltaTime: number): Promise<void> {\r\n        for (let i = 0; i < this._waveArr.length; i++) {\r\n            const wave = this._waveArr[i];\r\n            this._waveTimeArr[i] += deltaTime;\r\n            const currentEnemyCount = this._waveNumArr[i];\r\n            let posX = GameConst.EnemyPos.x;\r\n            let posY = GameConst.EnemyPos.y;\r\n\r\n            if (wave.bSetStartPos) {\r\n                posX += wave.startPosX;\r\n                posY += wave.startPosY;\r\n            }\r\n\r\n            for (let j = currentEnemyCount; j < wave.enemyNum; j++) {\r\n                if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {\r\n                    this._waveNumArr[i]++;\r\n                    let enemy:EnemyPlane|null;\r\n                    const enemyPosX = posX + wave.posDX * (j + 1);\r\n                    const enemyPosY = posY + wave.posDY * (j + 1);\r\n\r\n                    switch (wave.type) {\r\n                        case 0:\r\n                            enemy = await GameIns.enemyManager.addPlane(wave.enemyID);\r\n                            if (enemy) {\r\n                                if (j < wave.firstShootDelay.length) {\r\n                                    enemy.setFirstShootDelay(wave.firstShootDelay[j]);\r\n                                }\r\n                                enemy.setStandByTime(0);\r\n                                enemy.initTrack(\r\n                                    wave.trackGroups,\r\n                                    wave.liveParam,\r\n                                    enemyPosX,\r\n                                    enemyPosY,\r\n                                    wave.rotateSpeed\r\n                                );\r\n                            }\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (wave.enemyNum <= this._waveNumArr[i]) {\r\n                this._waveArr.splice(i, 1);\r\n                this._waveNumArr.splice(i, 1);\r\n                this._waveTimeArr.splice(i, 1);\r\n                this._waveActionArr.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新普通敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateNorEnemys(deltaTime: number): boolean {\r\n        if (this._bEnemyNorCreateAble) {\r\n            if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {\r\n                this._waveIndex = 0;\r\n                return false;\r\n            }\r\n\r\n            const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];\r\n            this._waveCreateTime += deltaTime;\r\n\r\n            const waveDatas = this.getNorWaveDatas(waveID);\r\n            if (!waveDatas) {\r\n                return false;\r\n            }\r\n            console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);\r\n            for (let i = 0; i < waveDatas!.length; i++) {\r\n                const wave = waveDatas![i];\r\n                if (\r\n                    !Tools.arrContain(this._waveIndexOver, i) &&\r\n                    this._waveCreateTime >= wave.groupInterval\r\n                ) {\r\n                    this._waveArr.push(wave);\r\n                    this._waveNumArr.push(0);\r\n                    this._waveTimeArr.push(0);\r\n                    this._waveActionArr.push(this._curEnemyAction);\r\n                    this._waveIndexOver.push(i);\r\n                }\r\n            }\r\n\r\n            if (this._waveIndexOver.length >= waveDatas!.length) {\r\n                this._waveIndexOver.splice(0);\r\n                this._waveCreateTime = 0;\r\n                this._waveIndex++;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 更新 Boss 生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateBoss(deltaTime: number): void {\r\n        if (this._bossToAddArr.length > 0 && GameIns.enemyManager.isEnemyOver()){\r\n            this._bossCreateTime += deltaTime;\r\n            if (this._bossCreateTime > this._bossCreateDelay) {\r\n                const bossData = this._bossToAddArr[0];\r\n                const boss = GameIns.bossManager.addBoss(bossData.enemyNorIDs[0]);\r\n                this._bossToAddArr.splice(0, 1);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 检查敌人是否全部结束\r\n     * @returns 是否所有敌人都已结束\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;\r\n    }\r\n}"]}