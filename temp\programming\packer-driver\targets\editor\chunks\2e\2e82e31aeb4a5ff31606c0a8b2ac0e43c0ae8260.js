System.register(["__unresolved_0"], function (_export, _context) {
  "use strict";

  var _reporterNs, EventConditionBase, _crd;

  function _reportPossibleCrUseOfIEventConditionData(extras) {
    _reporterNs.report("IEventConditionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  _export("EventConditionBase", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }],
    execute: function () {
      _crd = true; // Base interfaces

      _export("EventConditionBase", EventConditionBase = class EventConditionBase {
        constructor(data) {
          this.data = void 0;
          this._targetValue = 0;
          this.data = data;
        }

        onLoad(context) {
          this._targetValue = this.data.targetValue.eval();
        }

        evaluate(context) {
          // Default implementation (can be overridden)
          return true;
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=2e82e31aeb4a5ff31606c0a8b2ac0e43c0ae8260.js.map