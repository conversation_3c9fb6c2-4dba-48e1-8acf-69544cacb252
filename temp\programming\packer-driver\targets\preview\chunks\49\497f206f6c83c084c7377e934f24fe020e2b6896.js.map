{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts"], "names": ["_decorator", "Component", "UITransform", "v2", "Vec2", "Vec3", "GameIns", "Tools", "ccclass", "property", "TrackComponent", "TRACK_LOOP_FOREVER", "_target", "_trackAble", "_formIndex", "_bForm", "_initPos", "ZERO", "_posX", "_posY", "_prePosX", "_prePosY", "_trackType", "_isTrackLeave", "_curTrack", "_curSpeed", "_curAccelerate", "_curTrackInterval", "_trackLoop", "_trackGroups", "_tracks", "_speeds", "_accelerates", "_trackIntervals", "_trackGroupIndex", "_trackIndex", "_trackTime", "_trackOffX", "_trackOffY", "_bTrackOver", "_bTrackOverDie", "_trackStayTime", "_bMoving", "_liveTime", "_leaveAct", "_leaveSpeed", "_leaveAccelerate", "_liveCount", "_rushTime", "_rushDuration", "_trackChangeCall", "_trackStartCall", "_trackGroupStartCall", "_trackGroupOverCall", "_trackOverCall", "_trackLeaveCall", "_trackFinish", "fpsAble", "init", "target", "trackGroups", "trackParams", "pos", "reset", "x", "y", "_setTargetPos", "length", "updateGameLogic", "deltaTime", "_checkLiveAble", "_updateMove", "startTrack", "_refreshTrackDatas", "_refreshCurTrackData", "resetTrack", "setTrackGroupIndex", "index", "group", "loopNum", "trackIDs", "map", "id", "enemyManager", "getTrackDataForID", "speeds", "accelerates", "trackIntervals", "formIndex", "_setTargetFormIndex", "type", "<PERSON><PERSON><PERSON><PERSON>", "track", "refreshTrackOffset", "speed", "bezierX", "getBezier", "startX", "control1X", "control2X", "endX", "bezierY", "startY", "control1Y", "control2Y", "endY", "setPos", "dir", "getDir", "_setTargetDestDir", "_setTargetDestAngle", "getDegreeForDir", "straight", "getStraight", "add", "node", "position", "straightForDir", "getStraightForDir", "worldPos", "mainPlaneManager", "mainPlane", "getComponent", "convertToWorldSpaceAR", "localPos", "isTrackOver", "offsetX", "offsetY", "deltaX", "deltaY", "isPlaneOutScreen", "getAngle", "_gotoNextTrack", "_trackOver", "checkLiveAble", "_gotoLeaveTrack", "setTrackAble", "isAble", "trackFinish", "is<PERSON><PERSON><PERSON>", "trackAble", "trackOffsetX", "trackOffsetY", "isMoving", "setTrackChangeCall", "callback", "setTrackStartCall", "setTrackGroupStartCall", "setTrackGroupOverCall", "setTrackOverCall", "setTrackLeaveCall", "_setTargetDir", "setDir", "setDestDir", "angle", "setDestAngle", "setFormIndex"], "mappings": ";;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAC9CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,c,WADpBF,OAAO,CAAC,gBAAD,C,gBAAR,MACqBE,cADrB,SAC4CT,SAD5C,CACsD;AAAA;AAAA;AAAA,eAClDU,kBADkD,GAC7B,GAD6B;AAAA,eAElDC,OAFkD,GAEnC,IAFmC;AAAA,eAGlDC,UAHkD,GAG5B,KAH4B;AAAA,eAIlDC,UAJkD,GAI7B,CAAC,GAJ4B;AAAA,eAKlDC,MALkD,GAKhC,KALgC;AAAA,eAMlDC,QANkD,GAMjCZ,IAAI,CAACa,IAN4B;AAAA,eAOlDC,KAPkD,GAOlC,CAPkC;AAAA,eAQlDC,KARkD,GAQlC,CARkC;AAAA,eASlDC,QATkD,GAS/B,CAT+B;AAAA,eAUlDC,QAVkD,GAU/B,CAV+B;AAAA,eAWlDC,UAXkD,GAW7B,CAAC,CAX4B;AAAA,eAYlDC,aAZkD,GAYzB,KAZyB;AAAA,eAalDC,SAbkD,GAajC,IAbiC;AAAA,eAclDC,SAdkD,GAc9B,CAd8B;AAAA,eAelDC,cAfkD,GAezB,CAfyB;AAAA,eAgBlDC,iBAhBkD,GAgBtB,CAhBsB;AAAA,eAiBlDC,UAjBkD,GAiB7B,CAjB6B;AAAA,eAkBlDC,YAlBkD,GAkB5B,EAlB4B;AAAA,eAmBlDC,OAnBkD,GAmBjC,EAnBiC;AAAA,eAoBlDC,OApBkD,GAoB9B,EApB8B;AAAA,eAqBlDC,YArBkD,GAqBzB,EArByB;AAAA,eAsBlDC,eAtBkD,GAsBtB,EAtBsB;AAAA,eAuBlDC,gBAvBkD,GAuBvB,CAvBuB;AAAA,eAwBlDC,WAxBkD,GAwB5B,CAxB4B;AAAA,eAyBlDC,UAzBkD,GAyB7B,CAzB6B;AAAA,eA0BlDC,UA1BkD,GA0B7B,CA1B6B;AAAA,eA2BlDC,UA3BkD,GA2B7B,CA3B6B;AAAA,eA4BlDC,WA5BkD,GA4B3B,KA5B2B;AAAA,eA6BlDC,cA7BkD,GA6BxB,KA7BwB;AAAA,eA8BlDC,cA9BkD,GA8BzB,CA9ByB;AAAA,eA+BlDC,QA/BkD,GA+B9B,KA/B8B;AAAA,eAgClDC,SAhCkD,GAgC9B,CAAC,CAhC6B;AAAA,eAiClDC,SAjCkD,GAiC9B,CAAC,CAjC6B;AAAA,eAkClDC,WAlCkD,GAkC5B,CAlC4B;AAAA,eAmClDC,gBAnCkD,GAmCvB,CAnCuB;AAAA,eAoClDC,UApCkD,GAoC7B,CApC6B;AAAA,eAqClDC,SArCkD,GAqC9B,CArC8B;AAAA,eAsClDC,aAtCkD,GAsC1B,GAtC0B;AAAA,eAuClDC,gBAvCkD,GAuCd,IAvCc;AAAA,eAwClDC,eAxCkD,GAwCf,IAxCe;AAAA,eAyClDC,oBAzCkD,GAyCV,IAzCU;AAAA,eA0ClDC,mBA1CkD,GA0CX,IA1CW;AAAA,eA2ClDC,cA3CkD,GA2ChB,IA3CgB;AAAA,eA4ClDC,eA5CkD,GA4Cf,IA5Ce;AAAA,eA6ClDC,YA7CkD,GA6C1B,KA7C0B;AAAA,eA8ClDC,OA9CkD,GA8C/B,IA9C+B;AAAA;;AAgDlD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,MAAD,EAAcC,WAAd,EAAkCC,WAAlC,EAAsDC,GAAtD,EAAgE;AAChE,eAAKC,KAAL;AACA,eAAKnD,OAAL,GAAe+C,MAAf;AACA,eAAK3C,QAAL,GAAgB8C,GAAhB;AACA,eAAK5C,KAAL,GAAa4C,GAAG,CAACE,CAAjB;AACA,eAAK7C,KAAL,GAAa2C,GAAG,CAACG,CAAjB;;AACA,eAAKC,aAAL,CAAmB,KAAKhD,KAAxB,EAA+B,KAAKC,KAApC;;AAEA,cAAIyC,WAAW,CAACO,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,iBAAKtC,YAAL,GAAoB+B,WAApB;;AACA,gBAAIC,WAAW,IAAIA,WAAW,CAACM,MAAZ,KAAuB,CAA1C,EAA6C;AACzC,mBAAK3B,cAAL,GAAsB,KAAtB;AACA,mBAAKG,SAAL,GAAiBkB,WAAW,CAAC,CAAD,CAA5B;AACA,mBAAKjB,SAAL,GAAiBiB,WAAW,CAAC,CAAD,CAA5B;AACA,mBAAKhB,WAAL,GAAmBgB,WAAW,CAAC,CAAD,CAA9B;AACH,aALD,MAKO;AACH,mBAAKrB,cAAL,GAAsB,IAAtB;AACH;AACJ;AACJ;;AAEDuB,QAAAA,KAAK,GAAG;AACJ,eAAKlD,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAC,GAAnB;AACA,eAAKS,aAAL,GAAqB,KAArB;AACA,eAAKD,UAAL,GAAkB,CAAC,CAAnB;AACA,eAAKE,SAAL,GAAiB,IAAjB;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,OAAL,GAAe,EAAf;AACA,eAAKC,OAAL,GAAe,EAAf;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,eAAL,GAAuB,EAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,WAAL,GAAmB,KAAnB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,QAAL,GAAgB,KAAhB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKS,YAAL,GAAoB,KAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIY,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,KAAKxD,UAAT,EAAqB;AACjB,iBAAKyD,cAAL,CAAoBD,SAApB;;AACA,iBAAKE,WAAL,CAAiBF,SAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACIG,QAAAA,UAAU,GAAG;AACT,cAAI,KAAK3C,YAAL,IAAqB,KAAKA,YAAL,CAAkBsC,MAAlB,GAA2B,CAApD,EAAuD;AACnD,iBAAKM,kBAAL;;AACA,iBAAKC,oBAAL,CAA0B,IAA1B;;AACA,iBAAKhC,QAAL,GAAgB,IAAhB;AACH;AACJ;AAED;AACJ;AACA;;;AACIiC,QAAAA,UAAU,GAAG;AACT,eAAKzC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKjB,KAAL,GAAa,KAAKF,QAAL,CAAcgD,CAA3B;AACA,eAAK7C,KAAL,GAAa,KAAKH,QAAL,CAAciD,CAA3B;;AACA,eAAKC,aAAL,CAAmB,KAAKhD,KAAxB,EAA+B,KAAKC,KAApC;;AACA,eAAKqD,UAAL;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,kBAAkB,CAACC,KAAD,EAAgB;AAC9B,eAAK3C,gBAAL,GAAwB2C,KAAxB;AACA,eAAK1C,WAAL,GAAmB,CAAnB;AACA,eAAKqC,UAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,KAAK5C,YAAT,EAAuB;AACnB,gBAAMiD,KAAK,GAAG,KAAKjD,YAAL,CAAkB,KAAKK,gBAAvB,CAAd;AACA,iBAAKN,UAAL,GAAkBkD,KAAK,CAACC,OAAxB;AACA,iBAAKjD,OAAL,GAAegD,KAAK,CAACE,QAAN,CAAeC,GAAf,CAAoBC,EAAD,IAAgB;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,iBAArB,CAAuCF,EAAvC,CAAnC,CAAf;AACA,iBAAKnD,OAAL,GAAe,CAAC,GAAG+C,KAAK,CAACO,MAAV,CAAf;AACA,iBAAKrD,YAAL,GAAoB,CAAC,GAAG8C,KAAK,CAACQ,WAAV,CAApB;AACA,iBAAKrD,eAAL,GAAuB,CAAC,GAAG6C,KAAK,CAACS,cAAV,CAAvB;AACA,iBAAKxE,MAAL,GAAc,KAAd;;AAEA,gBAAI,KAAKD,UAAL,KAAoBgE,KAAK,CAACU,SAA9B,EAAyC;AACrC,mBAAK1E,UAAL,GAAkBgE,KAAK,CAACU,SAAxB;;AACA,mBAAKC,mBAAL,CAAyB,KAAK3E,UAA9B;;AACA,mBAAKC,MAAL,GAAc,IAAd;AACH;;AAED,gBAAI,KAAKqC,oBAAT,EAA+B;AAC3B,mBAAKA,oBAAL,CAA0B,KAAKlB,gBAA/B,EAAiD,KAAKZ,UAAtD,EAAkEwD,KAAK,CAACY,IAAxE;AACH;;AAED,iBAAKpE,UAAL,GAAkBwD,KAAK,CAACY,IAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIhB,QAAAA,oBAAoB,CAACiB,OAAD,EAA2B;AAAA,cAA1BA,OAA0B;AAA1BA,YAAAA,OAA0B,GAAP,KAAO;AAAA;;AAC3C,cAAMC,KAAK,GAAG,KAAK9D,OAAL,CAAa,KAAKK,WAAlB,CAAd;AACA,eAAKV,SAAL,GAAiB,KAAKM,OAAL,CAAa,KAAKI,WAAlB,CAAjB;AACA,eAAKX,SAAL,GAAiBoE,KAAjB;AACA,eAAKjE,iBAAL,GAAyB,KAAKM,eAAL,CAAqB,KAAKE,WAA1B,CAAzB;AACA,eAAKZ,aAAL,GAAqB,KAArB;AACA,eAAKiC,YAAL,GAAoB,KAApB;;AAEA,cAAIoC,KAAK,CAACF,IAAN,KAAe,EAAnB,EAAuB;AACnB,iBAAKlD,cAAL,GAAsB,KAAtB;AACH;;AAED,cAAImD,OAAJ,EAAa;AACT,iBAAKE,kBAAL;AACH;;AAED,cAAI,KAAK1C,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqB,KAAK3B,SAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI+C,QAAAA,WAAW,CAACF,SAAD,EAAoB;AAC3B,cAAI,KAAK3B,QAAT,EAAmB;AACf,iBAAKtB,QAAL,GAAgB,KAAKF,KAArB;AACA,iBAAKG,QAAL,GAAgB,KAAKF,KAArB;AAEA,gBAAMyE,KAAK,GAAG,KAAKpE,SAAnB;AACA,gBAAMsE,KAAK,GAAG,KAAKrE,SAAnB;;AAEA,oBAAQmE,KAAK,CAACF,IAAd;AACI,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,oBAAMK,OAAO,GAAG;AAAA;AAAA,oCAAMC,SAAN,CAAgBJ,KAAK,CAACK,MAAtB,EAA8BL,KAAK,CAACM,SAApC,EAA+CN,KAAK,CAACO,SAArD,EAAgEP,KAAK,CAACQ,IAAtE,EAA4E,KAAKhE,UAAjF,CAAhB;AACA,oBAAMiE,OAAO,GAAG;AAAA;AAAA,oCAAML,SAAN,CAAgBJ,KAAK,CAACU,MAAtB,EAA8BV,KAAK,CAACW,SAApC,EAA+CX,KAAK,CAACY,SAArD,EAAgEZ,KAAK,CAACa,IAAtE,EAA4E,KAAKrE,UAAjF,CAAhB;AACA,qBAAKsE,MAAL,CAAYX,OAAO,GAAG,KAAK1D,UAA3B,EAAuCgE,OAAO,GAAG,KAAK/D,UAAtD;;AACA,oBAAI,KAAKF,UAAL,GAAkB,CAAtB,EAAyB;AACrB,sBAAMuE,GAAG,GAAG;AAAA;AAAA,sCAAMC,MAAN,CAAa,KAAKxF,QAAlB,EAA4B,KAAKC,QAAjC,EAA2C,KAAKH,KAAhD,EAAuD,KAAKC,KAA5D,CAAZ;;AACA,uBAAK0F,iBAAL,CAAuBF,GAAvB;;AACA,uBAAKG,mBAAL,CAAyB,CAAC;AAAA;AAAA,sCAAMC,eAAN,CAAsBJ,GAAtB,CAA1B;AACH;;AACD;;AAEJ,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,oBAAMK,QAAQ,GAAG;AAAA;AAAA,oCAAMC,WAAN,CACb,IAAI7G,IAAJ,CAAS,KAAKgB,QAAd,EAAwB,KAAKC,QAA7B,CADa,EAEb,IAAIjB,IAAJ,CAASwF,KAAK,CAACQ,IAAf,EAAqBR,KAAK,CAACa,IAA3B,EAAiCS,GAAjC,CAAqC,IAAI9G,IAAJ,CAAS,KAAKiC,UAAd,EAA0B,KAAKC,UAA/B,CAArC,CAFa,EAGbwD,KAHa,EAIbzB,SAJa,CAAjB;AAMA,qBAAKqC,MAAL,CAAYM,QAAQ,CAAChD,CAArB,EAAwBgD,QAAQ,CAAC/C,CAAjC;;AACA,oBAAI,KAAK7B,UAAL,KAAoB,CAAxB,EAA2B;AACvB,sBAAMuE,IAAG,GAAG;AAAA;AAAA,sCAAMC,MAAN,CAAa,KAAKxF,QAAlB,EAA4B,KAAKC,QAAjC,EAA2C,KAAKH,KAAhD,EAAuD,KAAKC,KAA5D,CAAZ;;AACA,uBAAK0F,iBAAL,CAAuBF,IAAvB;;AACA,uBAAKG,mBAAL,CAAyB,CAAC;AAAA;AAAA,sCAAMC,eAAN,CAAsBJ,IAAtB,CAA1B;AACH;;AACD;;AAEJ,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,qBAAKD,MAAL,CAAY,KAAKS,IAAL,CAAUC,QAAV,CAAmBpD,CAA/B,EAAkC,KAAKmD,IAAL,CAAUC,QAAV,CAAmBnD,CAArD;AACA;;AAEJ,mBAAK,EAAL;AACI,oBAAMoD,cAAc,GAAG;AAAA;AAAA,oCAAMC,iBAAN,CACnB,IAAIlH,IAAJ,CAAS,KAAKgB,QAAd,EAAwB,KAAKD,KAA7B,CADmB,EAEnB,KAAKP,OAAL,CAAagG,MAAb,EAFmB,EAGnBd,KAHmB,EAInBzB,SAJmB,CAAvB;AAMA,qBAAKqC,MAAL,CAAYW,cAAc,CAACrD,CAA3B,EAA8BqD,cAAc,CAACpD,CAA7C;;AACA,oBAAI,KAAK7B,UAAL,GAAkB,KAAKT,iBAA3B,EAA8C;AAC1C,uBAAKqB,SAAL,IAAkBqB,SAAlB;;AACA,sBAAI,KAAKrB,SAAL,GAAiB,KAAKC,aAA1B,EAAyC;AACrC,yBAAKD,SAAL,GAAiB,CAAjB;AACA,wBAAMuE,QAAQ,GAAG;AAAA;AAAA,4CAAQC,gBAAR,CAA0BC,SAA1B,CAAqCN,IAArC,CAA2CO,YAA3C,CAAwDxH,WAAxD,EAAsEyH,qBAAtE,CAA4FtH,IAAI,CAACY,IAAjG,EAAuGiG,GAAvG,CAA2G,IAAI7G,IAAJ,CAAS,KAAKmB,SAAL,CAAe4E,IAAxB,EAA8B,KAAK5E,SAAL,CAAeiF,IAA7C,CAA3G,CAAjB;AACA,wBAAMmB,QAAQ,GAAG,KAAKT,IAAL,CAAWO,YAAX,CAAwBxH,WAAxB,EAAsCyH,qBAAtC,CAA4DtH,IAAI,CAACY,IAAjE,CAAjB;;AACA,wBAAM0F,KAAG,GAAG;AAAA;AAAA,wCAAMC,MAAN,CAAagB,QAAQ,CAAC5D,CAAtB,EAAyB4D,QAAQ,CAAC3D,CAAlC,EAAqCsD,QAAQ,CAACvD,CAA9C,EAAiDuD,QAAQ,CAACtD,CAA1D,CAAZ;;AACA,yBAAK4C,iBAAL,CAAuBF,KAAvB;;AACA,yBAAKG,mBAAL,CAAyB,CAAC;AAAA;AAAA,wCAAMC,eAAN,CAAsBJ,KAAtB,CAA1B;AACH;AACJ;;AACD;AArDR;;AAwDA,iBAAKzC,aAAL,CAAmB,KAAKhD,KAAxB,EAA+B,KAAKC,KAApC;;AAEA,gBAAI0G,WAAW,GAAG,KAAlB;;AACA,oBAAQjC,KAAK,CAACF,IAAd;AACI,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,qBAAKtD,UAAL,IAAmB0D,KAAnB;;AACA,oBAAI,KAAK1D,UAAL,GAAkB,CAAtB,EAAyB;AACrByF,kBAAAA,WAAW,GAAG,IAAd;AACH;;AACD;;AAEJ,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,qBAAKzF,UAAL,GAAkB,CAAlB;AACA,oBAAM0F,OAAO,GAAG,KAAK5G,KAAL,IAAc0E,KAAK,CAACQ,IAAN,GAAa,KAAK/D,UAAhC,CAAhB;AACA,oBAAM0F,OAAO,GAAG,KAAK5G,KAAL,IAAcyE,KAAK,CAACa,IAAN,GAAa,KAAKnE,UAAhC,CAAhB;AACA,oBAAM0F,MAAM,GAAGpC,KAAK,CAACQ,IAAN,GAAaR,KAAK,CAACK,MAAlC;AACA,oBAAMgC,MAAM,GAAGrC,KAAK,CAACa,IAAN,GAAab,KAAK,CAACU,MAAlC;;AACA,oBAAIwB,OAAO,GAAGE,MAAV,IAAoB,CAApB,IAAyBD,OAAO,GAAGE,MAAV,IAAoB,CAAjD,EAAoD;AAChDJ,kBAAAA,WAAW,GAAG,IAAd;AACH;;AACD;;AAEJ,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,oBAAI,KAAKrE,YAAT,EAAuB;AACnBqE,kBAAAA,WAAW,GAAG,IAAd;AACH;;AACD;;AAEJ,mBAAK,EAAL;AACI,qBAAKzF,UAAL,IAAmBiC,SAAnB;;AACA,oBAAI,KAAKjC,UAAL,GAAkB,KAAKT,iBAAvB,IAA4C;AAAA;AAAA,oCAAMuG,gBAAN,CAAuB/H,EAAE,CAAC,KAAKgH,IAAL,CAAUC,QAAV,CAAmBpD,CAApB,EAAsB,KAAKmD,IAAL,CAAUC,QAAV,CAAmBnD,CAAzC,CAAzB,CAAhD,EAAuH;AACnH,uBAAK4C,iBAAL,CAAuB,KAAKjG,OAAL,CAAagG,MAAb,EAAvB;;AACA,uBAAKE,mBAAL,CAAyB,KAAKlG,OAAL,CAAauH,QAAb,EAAzB;;AACAN,kBAAAA,WAAW,GAAG,KAAKtG,aAAL,GAAqB,IAAnC;AACA,uBAAKI,iBAAL,GAAyB,CAAzB;AACH;;AACD;AApCR;;AAuCA,gBAAIkG,WAAJ,EAAiB;AACb,kBAAI,KAAKlG,iBAAL,GAAyB,CAA7B,EAAgC;AAC5B,qBAAKe,QAAL,GAAgB,KAAhB;AACA,qBAAKD,cAAL,GAAsB,CAAtB;AACH,eAHD,MAGO,IAAI,KAAKlB,aAAT,EAAwB;AAC3B,oBAAI,KAAKgC,eAAT,EAA0B;AACtB,uBAAKA,eAAL;;AACA,uBAAKA,eAAL,GAAuB,IAAvB;AACH;AACJ,eALM,MAKA;AACH,qBAAK6E,cAAL;AACH;AACJ;AACJ,WAtHD,MAsHO;AACH,iBAAK3F,cAAL,IAAuB4B,SAAvB;;AACA,gBAAI,CAAC,KAAK9B,WAAN,IAAqB,KAAKE,cAAL,GAAsB,KAAKd,iBAApD,EAAuE;AACnE,mBAAKyG,cAAL;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIA,QAAAA,cAAc,GAAG;AACb,eAAKjG,WAAL;;AACA,cAAI,KAAKA,WAAL,IAAoB,KAAKL,OAAL,CAAaqC,MAAjC,IAA2C,KAAKkE,UAAL,EAA/C,EAAkE,CAEjE,CAFD,MAEO;AACH,iBAAK3D,oBAAL;;AACA,iBAAKrC,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKM,SAAL,CAAeyE,MAA9C;AACA,iBAAK3D,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKK,SAAL,CAAe8E,MAA9C;AACA,iBAAKlE,UAAL,GAAkB,CAAlB;;AACA,gBAAI,KAAKc,gBAAT,EAA2B;AACvB,mBAAKA,gBAAL,CAAsB,KAAKf,WAA3B;;AACA,mBAAKe,gBAAL,GAAwB,IAAxB;AACH;;AACD,iBAAKnC,MAAL,GAAc,KAAd;AACA,iBAAK2B,QAAL,GAAgB,IAAhB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACI4B,QAAAA,cAAc,CAACD,SAAD,EAA6B;AACvC,eAAKtB,UAAL,IAAmBsB,SAAnB;;AACA,cAAI,KAAK1B,SAAL,IAAkB,CAAlB,IAAuB,KAAKI,UAAL,GAAkB,KAAKJ,SAA9C,IAA2D,KAAK/B,OAAL,CAAa0H,aAAxE,IAAyF,KAAK1H,OAAL,CAAa0H,aAAb,EAA7F,EAA2H;AACvH,gBAAI,KAAK1F,SAAL,KAAmB,CAAnB,IAAyB,KAAKA,SAAL,GAAiB,CAAjB,IAAsB,KAAK2F,eAAL,EAAnD,EAA4E;AACxE,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,eAAe,GAAG;AACd,eAAK/G,SAAL,GAAiB;AAAA;AAAA,kCAAQ2D,YAAR,CAAqBC,iBAArB,CAAuC,KAAKxC,SAA5C,CAAjB;AACA,eAAKnB,SAAL,GAAiB,KAAKoB,WAAtB;AACA,eAAKlB,iBAAL,GAAyB,CAAzB;AACA,eAAKU,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKM,SAAL,CAAeyE,MAA9C;AACA,eAAK3D,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKK,SAAL,CAAe8E,MAA9C;AACA,eAAKlE,UAAL,GAAkB,CAAlB;AACA,eAAKM,QAAL,GAAgB,IAAhB;AACA,eAAKnB,aAAL,GAAqB,IAArB;AACH;AAED;AACJ;AACA;AACA;;;AACI8G,QAAAA,UAAU,GAAY;AAClB,cAAI,KAAKzG,UAAL,GAAkB,KAAKjB,kBAA3B,EAA+C;AAC3C,iBAAKiB,UAAL;;AACA,gBAAI,KAAKA,UAAL,IAAmB,CAAvB,EAA0B;AACtB,kBAAI,KAAKyB,mBAAT,EAA8B;AAC1B,qBAAKA,mBAAL,CAAyB,KAAKnB,gBAA9B;AACH;;AACD,mBAAKA,gBAAL;;AACA,kBAAI,KAAKA,gBAAL,IAAyB,KAAKL,YAAL,CAAkBsC,MAA/C,EAAuD;AACnD,qBAAK5B,WAAL,GAAmB,IAAnB;;AACA,oBAAI,KAAKC,cAAT,EAAyB;AACrB,sBAAI,KAAKc,cAAT,EAAyB;AACrB,yBAAKA,cAAL;;AACA,yBAAKA,cAAL,GAAsB,IAAtB;AACH;AACJ,iBALD,MAKO;AACH,uBAAKZ,QAAL,GAAgB,KAAhB;AACH;;AACD,uBAAO,IAAP;AACH;;AACD,mBAAK+B,kBAAL;AACH;AACJ;;AACD,eAAKtC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;;AACA,eAAKsC,oBAAL;;AACA,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACI8D,QAAAA,YAAY,CAACC,MAAD,EAAkB;AAC1B,cAAI,KAAK5G,YAAL,IAAqB,KAAKA,YAAL,CAAkBsC,MAAlB,GAA2B,CAApD,EAAuD;AACnD,iBAAKtD,UAAL,GAAkB4H,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACmB,YAAXC,WAAW,GAAG;AACd,iBAAO,KAAKlF,YAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACmB,YAAXkF,WAAW,CAACC,QAAD,EAAW;AACtB,eAAKnF,YAAL,GAAoBmF,QAApB;AACH;AAED;AACJ;AACA;AACA;;;AACiB,YAATC,SAAS,GAAG;AACZ,iBAAO,KAAK/H,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIgH,QAAAA,WAAW,GAAG;AACV,iBAAO,KAAKtF,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACoB,YAAZsG,YAAY,GAAG;AACf,iBAAO,KAAKxG,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACoB,YAAZyG,YAAY,GAAG;AACf,iBAAO,KAAKxG,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACgB,YAARyG,QAAQ,GAAG;AACX,iBAAO,KAAKrG,QAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIgE,QAAAA,MAAM,CAAC1C,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAK/C,KAAL,GAAa8C,CAAb;AACA,eAAK7C,KAAL,GAAa8C,CAAb;AACH;AAED;AACJ;AACA;;;AACI4B,QAAAA,kBAAkB,GAAG;AACjB,eAAKxD,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKM,SAAL,CAAeyE,MAA9C;AACA,eAAK3D,UAAL,GAAkB,KAAKnB,KAAL,GAAa,KAAKK,SAAL,CAAe8E,MAA9C;AACH;AAED;AACJ;AACA;AACA;;;AACI0C,QAAAA,kBAAkB,CAACC,QAAD,EAAqB;AACnC,eAAK/F,gBAAL,GAAwB+F,QAAxB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,iBAAiB,CAACD,QAAD,EAAqB;AAClC,eAAK9F,eAAL,GAAuB8F,QAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,sBAAsB,CAACF,QAAD,EAAqB;AACvC,eAAK7F,oBAAL,GAA4B6F,QAA5B;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,qBAAqB,CAACH,QAAD,EAAqB;AACtC,eAAK5F,mBAAL,GAA2B4F,QAA3B;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,gBAAgB,CAACJ,QAAD,EAAqB;AACjC,eAAK3F,cAAL,GAAsB2F,QAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,iBAAiB,CAACL,QAAD,EAAqB;AAClC,eAAK1F,eAAL,GAAuB0F,QAAvB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACI/E,QAAAA,aAAa,CAACF,CAAD,EAAYC,CAAZ,EAAuB;AAChC,cAAI,KAAKrD,OAAL,CAAa8F,MAAjB,EAAyB;AACrB,iBAAK9F,OAAL,CAAa8F,MAAb,CAAoB1C,CAApB,EAAuBC,CAAvB,EAA0B,IAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIsF,QAAAA,aAAa,CAAC5C,GAAD,EAAY;AACrB,cAAI,KAAK/F,OAAL,CAAa4I,MAAjB,EAAyB;AACrB,iBAAK5I,OAAL,CAAa4I,MAAb,CAAoB7C,GAApB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,iBAAiB,CAACF,GAAD,EAAY;AACzB,cAAI,KAAK/F,OAAL,CAAa6I,UAAjB,EAA6B;AACzB,iBAAK7I,OAAL,CAAa6I,UAAb,CAAwB9C,GAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,mBAAmB,CAAC4C,KAAD,EAAgB;AAC/B,cAAI,KAAK9I,OAAL,CAAa+I,YAAjB,EAA+B;AAC3B,iBAAK/I,OAAL,CAAa+I,YAAb,CAA0BD,KAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIjE,QAAAA,mBAAmB,CAACZ,KAAD,EAAgB;AAC/B,cAAI,KAAKjE,OAAL,CAAagJ,YAAjB,EAA+B;AAC3B,iBAAKhJ,OAAL,CAAagJ,YAAb,CAA0B/E,KAA1B;AACH;AACJ;;AAxlBiD,O", "sourcesContent": ["import { _decorator, Component, UITransform, v2, Vec2, Vec3 } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport { Tools } from '../../utils/Tools';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('TrackComponent')\r\nexport default class TrackComponent extends Component {\r\n    TRACK_LOOP_FOREVER = 999;\r\n    _target: any = null;\r\n    _trackAble: boolean = false;\r\n    _formIndex: number = -100;\r\n    _bForm: boolean = false;\r\n    _initPos: Vec2 = Vec2.ZERO;\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n    _prePosX: number = 0;\r\n    _prePosY: number = 0;\r\n    _trackType: number = -1;\r\n    _isTrackLeave: boolean = false;\r\n    _curTrack: any = null;\r\n    _curSpeed: number = 0;\r\n    _curAccelerate: number = 0;\r\n    _curTrackInterval: number = 0;\r\n    _trackLoop: number = 0;\r\n    _trackGroups: any[] = [];\r\n    _tracks: any[] = [];\r\n    _speeds: number[] = [];\r\n    _accelerates: number[] = [];\r\n    _trackIntervals: number[] = [];\r\n    _trackGroupIndex: number = 0;\r\n    _trackIndex: number = 0;\r\n    _trackTime: number = 0;\r\n    _trackOffX: number = 0;\r\n    _trackOffY: number = 0;\r\n    _bTrackOver: boolean = false;\r\n    _bTrackOverDie: boolean = false;\r\n    _trackStayTime: number = 0;\r\n    _bMoving: boolean = false;\r\n    _liveTime: number = -1;\r\n    _leaveAct: number = -1;\r\n    _leaveSpeed: number = 0;\r\n    _leaveAccelerate: number = 0;\r\n    _liveCount: number = 0;\r\n    _rushTime: number = 0;\r\n    _rushDuration: number = 0.2;\r\n    _trackChangeCall: Function | null = null;\r\n    _trackStartCall: Function | null = null;\r\n    _trackGroupStartCall: Function | null = null;\r\n    _trackGroupOverCall: Function | null = null;\r\n    _trackOverCall: Function | null = null;\r\n    _trackLeaveCall: Function | null = null;\r\n    _trackFinish: boolean = false;\r\n    fpsAble: boolean = true;\r\n\r\n    /**\r\n     * 初始化轨迹组件\r\n     * @param target 目标对象\r\n     * @param trackGroups 轨迹组\r\n     * @param trackParams 轨迹参数\r\n     * @param startX 起始 X 坐标\r\n     * @param startY 起始 Y 坐标\r\n     */\r\n    init(target: any, trackGroups: any[], trackParams: any[], pos:Vec2) {\r\n        this.reset();\r\n        this._target = target;\r\n        this._initPos = pos;\r\n        this._posX = pos.x;\r\n        this._posY = pos.y;\r\n        this._setTargetPos(this._posX, this._posY);\r\n\r\n        if (trackGroups.length > 0) {\r\n            this._trackGroups = trackGroups;\r\n            if (trackParams && trackParams.length === 3) {\r\n                this._bTrackOverDie = false;\r\n                this._liveTime = trackParams[0];\r\n                this._leaveAct = trackParams[1];\r\n                this._leaveSpeed = trackParams[2];\r\n            } else {\r\n                this._bTrackOverDie = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    reset() {\r\n        this._trackAble = false;\r\n        this._formIndex = -100;\r\n        this._isTrackLeave = false;\r\n        this._trackType = -1;\r\n        this._curTrack = null;\r\n        this._curSpeed = 0;\r\n        this._curAccelerate = 0;\r\n        this._curTrackInterval = 0;\r\n        this._trackLoop = 0;\r\n        this._trackGroups = [];\r\n        this._tracks = [];\r\n        this._speeds = [];\r\n        this._accelerates = [];\r\n        this._trackIntervals = [];\r\n        this._trackGroupIndex = 0;\r\n        this._trackIndex = 0;\r\n        this._trackTime = 0;\r\n        this._trackOffX = 0;\r\n        this._trackOffY = 0;\r\n        this._bTrackOver = false;\r\n        this._bTrackOverDie = false;\r\n        this._trackStayTime = 0;\r\n        this._bMoving = false;\r\n        this._liveTime = -1;\r\n        this._leaveAct = -1;\r\n        this._leaveSpeed = 0;\r\n        this._leaveAccelerate = 0;\r\n        this._liveCount = 0;\r\n        this._trackFinish = false;\r\n    }\r\n\r\n    /**\r\n     * 更新轨迹逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (this._trackAble) {\r\n            this._checkLiveAble(deltaTime);\r\n            this._updateMove(deltaTime);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始轨迹\r\n     */\r\n    startTrack() {\r\n        if (this._trackGroups && this._trackGroups.length > 0) {\r\n            this._refreshTrackDatas();\r\n            this._refreshCurTrackData(true);\r\n            this._bMoving = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 重置轨迹\r\n     */\r\n    resetTrack() {\r\n        this._trackGroupIndex = 0;\r\n        this._trackIndex = 0;\r\n        this._posX = this._initPos.x;\r\n        this._posY = this._initPos.y;\r\n        this._setTargetPos(this._posX, this._posY);\r\n        this.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹组索引\r\n     * @param index 轨迹组索引\r\n     */\r\n    setTrackGroupIndex(index: number) {\r\n        this._trackGroupIndex = index;\r\n        this._trackIndex = 0;\r\n        this.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 刷新轨迹数据\r\n     */\r\n    _refreshTrackDatas() {\r\n        if (this._trackGroups) {\r\n            const group = this._trackGroups[this._trackGroupIndex];\r\n            this._trackLoop = group.loopNum;\r\n            this._tracks = group.trackIDs.map((id: number) => GameIns.enemyManager.getTrackDataForID(id));\r\n            this._speeds = [...group.speeds];\r\n            this._accelerates = [...group.accelerates];\r\n            this._trackIntervals = [...group.trackIntervals];\r\n            this._bForm = false;\r\n\r\n            if (this._formIndex !== group.formIndex) {\r\n                this._formIndex = group.formIndex;\r\n                this._setTargetFormIndex(this._formIndex);\r\n                this._bForm = true;\r\n            }\r\n\r\n            if (this._trackGroupStartCall) {\r\n                this._trackGroupStartCall(this._trackGroupIndex, this._trackType, group.type);\r\n            }\r\n\r\n            this._trackType = group.type;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新当前轨迹数据\r\n     * @param isFirst 是否为首次刷新\r\n     */\r\n    _refreshCurTrackData(isFirst: boolean = false) {\r\n        const track = this._tracks[this._trackIndex];\r\n        this._curSpeed = this._speeds[this._trackIndex];\r\n        this._curTrack = track;\r\n        this._curTrackInterval = this._trackIntervals[this._trackIndex];\r\n        this._isTrackLeave = false;\r\n        this._trackFinish = false;\r\n\r\n        if (track.type === 11) {\r\n            this._bTrackOverDie = false;\r\n        }\r\n\r\n        if (isFirst) {\r\n            this.refreshTrackOffset();\r\n        }\r\n\r\n        if (this._trackStartCall) {\r\n            this._trackStartCall(this._curTrack);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新移动逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    _updateMove(deltaTime: number) {\r\n        if (this._bMoving) {\r\n            this._prePosX = this._posX;\r\n            this._prePosY = this._posY;\r\n\r\n            const track = this._curTrack;\r\n            const speed = this._curSpeed;\r\n\r\n            switch (track.type) {\r\n                case 0:\r\n                case 4:\r\n                    const bezierX = Tools.getBezier(track.startX, track.control1X, track.control2X, track.endX, this._trackTime);\r\n                    const bezierY = Tools.getBezier(track.startY, track.control1Y, track.control2Y, track.endY, this._trackTime);\r\n                    this.setPos(bezierX + this._trackOffX, bezierY + this._trackOffY);\r\n                    if (this._trackTime > 0) {\r\n                        const dir = Tools.getDir(this._prePosX, this._prePosY, this._posX, this._posY);\r\n                        this._setTargetDestDir(dir);\r\n                        this._setTargetDestAngle(-Tools.getDegreeForDir(dir));\r\n                    }\r\n                    break;\r\n\r\n                case 1:\r\n                case 5:\r\n                    const straight = Tools.getStraight(\r\n                        new Vec2(this._prePosX, this._prePosY),\r\n                        new Vec2(track.endX, track.endY).add(new Vec2(this._trackOffX, this._trackOffY)),\r\n                        speed,\r\n                        deltaTime\r\n                    );\r\n                    this.setPos(straight.x, straight.y);\r\n                    if (this._trackTime === 0) {\r\n                        const dir = Tools.getDir(this._prePosX, this._prePosY, this._posX, this._posY);\r\n                        this._setTargetDestDir(dir);\r\n                        this._setTargetDestAngle(-Tools.getDegreeForDir(dir));\r\n                    }\r\n                    break;\r\n\r\n                case 2:\r\n                case 3:\r\n                    this.setPos(this.node.position.x, this.node.position.y);\r\n                    break;\r\n\r\n                case 11:\r\n                    const straightForDir = Tools.getStraightForDir(\r\n                        new Vec2(this._prePosX, this._posY),\r\n                        this._target.getDir(),\r\n                        speed,\r\n                        deltaTime\r\n                    );\r\n                    this.setPos(straightForDir.x, straightForDir.y);\r\n                    if (this._trackTime < this._curTrackInterval) {\r\n                        this._rushTime += deltaTime;\r\n                        if (this._rushTime > this._rushDuration) {\r\n                            this._rushTime = 0;\r\n                            const worldPos = GameIns.mainPlaneManager!.mainPlane!.node!.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO).add(new Vec3(this._curTrack.endX, this._curTrack.endY));\r\n                            const localPos = this.node!.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);\r\n                            const dir = Tools.getDir(localPos.x, localPos.y, worldPos.x, worldPos.y);\r\n                            this._setTargetDestDir(dir);\r\n                            this._setTargetDestAngle(-Tools.getDegreeForDir(dir));\r\n                        }\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            this._setTargetPos(this._posX, this._posY);\r\n\r\n            let isTrackOver = false;\r\n            switch (track.type) {\r\n                case 0:\r\n                case 4:\r\n                    this._trackTime += speed;\r\n                    if (this._trackTime > 1) {\r\n                        isTrackOver = true;\r\n                    }\r\n                    break;\r\n\r\n                case 1:\r\n                case 5:\r\n                    this._trackTime = 1;\r\n                    const offsetX = this._posX - (track.endX + this._trackOffX);\r\n                    const offsetY = this._posY - (track.endY + this._trackOffY);\r\n                    const deltaX = track.endX - track.startX;\r\n                    const deltaY = track.endY - track.startY;\r\n                    if (offsetX * deltaX >= 0 && offsetY * deltaY >= 0) {\r\n                        isTrackOver = true;\r\n                    }\r\n                    break;\r\n\r\n                case 2:\r\n                case 3:\r\n                    if (this._trackFinish) {\r\n                        isTrackOver = true;\r\n                    }\r\n                    break;\r\n\r\n                case 11:\r\n                    this._trackTime += deltaTime;\r\n                    if (this._trackTime > this._curTrackInterval && Tools.isPlaneOutScreen(v2(this.node.position.x,this.node.position.y))) {\r\n                        this._setTargetDestDir(this._target.getDir());\r\n                        this._setTargetDestAngle(this._target.getAngle());\r\n                        isTrackOver = this._isTrackLeave = true;\r\n                        this._curTrackInterval = 0;\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            if (isTrackOver) {\r\n                if (this._curTrackInterval > 0) {\r\n                    this._bMoving = false;\r\n                    this._trackStayTime = 0;\r\n                } else if (this._isTrackLeave) {\r\n                    if (this._trackLeaveCall) {\r\n                        this._trackLeaveCall();\r\n                        this._trackLeaveCall = null;\r\n                    }\r\n                } else {\r\n                    this._gotoNextTrack();\r\n                }\r\n            }\r\n        } else {\r\n            this._trackStayTime += deltaTime;\r\n            if (!this._bTrackOver && this._trackStayTime > this._curTrackInterval) {\r\n                this._gotoNextTrack();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 跳转到下一轨迹\r\n     */\r\n    _gotoNextTrack() {\r\n        this._trackIndex++;\r\n        if (this._trackIndex >= this._tracks.length && this._trackOver()) {\r\n            \r\n        } else {\r\n            this._refreshCurTrackData();\r\n            this._trackOffX = this._posX - this._curTrack.startX;\r\n            this._trackOffY = this._posY - this._curTrack.startY;\r\n            this._trackTime = 0;\r\n            if (this._trackChangeCall) {\r\n                this._trackChangeCall(this._trackIndex);\r\n                this._trackChangeCall = null;\r\n            }\r\n            this._bForm = false;\r\n            this._bMoving = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否存活\r\n     * @param deltaTime 帧间隔时间\r\n     * @returns {boolean} 是否存活\r\n     */\r\n    _checkLiveAble(deltaTime: number): boolean {\r\n        this._liveCount += deltaTime;\r\n        if (this._liveTime >= 0 && this._liveCount > this._liveTime && this._target.checkLiveAble && this._target.checkLiveAble()) {\r\n            if (this._leaveAct === 0 || (this._leaveAct > 0 && this._gotoLeaveTrack())) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 跳转到离开轨迹\r\n     */\r\n    _gotoLeaveTrack() {\r\n        this._curTrack = GameIns.enemyManager.getTrackDataForID(this._leaveAct);\r\n        this._curSpeed = this._leaveSpeed;\r\n        this._curTrackInterval = 0;\r\n        this._trackOffX = this._posX - this._curTrack.startX;\r\n        this._trackOffY = this._posY - this._curTrack.startY;\r\n        this._trackTime = 0;\r\n        this._bMoving = true;\r\n        this._isTrackLeave = true;\r\n    }\r\n\r\n    /**\r\n     * 轨迹结束\r\n     * @returns {boolean} 是否结束\r\n     */\r\n    _trackOver(): boolean {\r\n        if (this._trackLoop < this.TRACK_LOOP_FOREVER) {\r\n            this._trackLoop--;\r\n            if (this._trackLoop <= 0) {\r\n                if (this._trackGroupOverCall) {\r\n                    this._trackGroupOverCall(this._trackGroupIndex);\r\n                }\r\n                this._trackGroupIndex++;\r\n                if (this._trackGroupIndex >= this._trackGroups.length) {\r\n                    this._bTrackOver = true;\r\n                    if (this._bTrackOverDie) {\r\n                        if (this._trackOverCall) {\r\n                            this._trackOverCall();\r\n                            this._trackOverCall = null;\r\n                        }\r\n                    } else {\r\n                        this._bMoving = false;\r\n                    }\r\n                    return true;\r\n                }\r\n                this._refreshTrackDatas();\r\n            }\r\n        }\r\n        this._trackIndex = 0;\r\n        this._trackTime = 0;\r\n        this._refreshCurTrackData();\r\n        return false;\r\n    }\r\n\r\n    /**\r\n * 设置轨迹是否可用\r\n * @param {boolean} isAble 是否可用\r\n */\r\n    setTrackAble(isAble: boolean) {\r\n        if (this._trackGroups && this._trackGroups.length > 0) {\r\n            this._trackAble = isAble;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取轨迹是否完成\r\n     * @returns {boolean} 是否完成\r\n     */\r\n    get trackFinish() {\r\n        return this._trackFinish;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹是否完成\r\n     * @param {boolean} isFinish 是否完成\r\n     */\r\n    set trackFinish(isFinish) {\r\n        this._trackFinish = isFinish;\r\n    }\r\n\r\n    /**\r\n     * 获取轨迹是否可用\r\n     * @returns {boolean} 是否可用\r\n     */\r\n    get trackAble() {\r\n        return this._trackAble;\r\n    }\r\n\r\n    /**\r\n     * 检查轨迹是否结束\r\n     * @returns {boolean} 是否结束\r\n     */\r\n    isTrackOver() {\r\n        return this._bTrackOver;\r\n    }\r\n\r\n    /**\r\n     * 获取轨迹的 X 偏移量\r\n     * @returns {number} X 偏移量\r\n     */\r\n    get trackOffsetX() {\r\n        return this._trackOffX;\r\n    }\r\n\r\n    /**\r\n     * 获取轨迹的 Y 偏移量\r\n     * @returns {number} Y 偏移量\r\n     */\r\n    get trackOffsetY() {\r\n        return this._trackOffY;\r\n    }\r\n\r\n    /**\r\n     * 检查是否正在移动\r\n     * @returns {boolean} 是否正在移动\r\n     */\r\n    get isMoving() {\r\n        return this._bMoving;\r\n    }\r\n\r\n    /**\r\n     * 设置目标位置\r\n     * @param {number} x X 坐标\r\n     * @param {number} y Y 坐标\r\n     */\r\n    setPos(x: number, y: number) {\r\n        this._posX = x;\r\n        this._posY = y;\r\n    }\r\n\r\n    /**\r\n     * 刷新轨迹偏移量\r\n     */\r\n    refreshTrackOffset() {\r\n        this._trackOffX = this._posX - this._curTrack.startX;\r\n        this._trackOffY = this._posY - this._curTrack.startY;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹变化回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackChangeCall(callback: Function) {\r\n        this._trackChangeCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹开始回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackStartCall(callback: Function) {\r\n        this._trackStartCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹组开始回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackGroupStartCall(callback: Function) {\r\n        this._trackGroupStartCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹组结束回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackGroupOverCall(callback: Function) {\r\n        this._trackGroupOverCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹结束回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackOverCall(callback: Function) {\r\n        this._trackOverCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹离开回调\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setTrackLeaveCall(callback: Function) {\r\n        this._trackLeaveCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置目标位置\r\n     * @param {number} x X 坐标\r\n     * @param {number} y Y 坐标\r\n     */\r\n    _setTargetPos(x: number, y: number) {\r\n        if (this._target.setPos) {\r\n            this._target.setPos(x, y, true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置目标方向\r\n     * @param {Vec2} dir 方向\r\n     */\r\n    _setTargetDir(dir: Vec2) {\r\n        if (this._target.setDir) {\r\n            this._target.setDir(dir);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置目标目标方向\r\n     * @param {Vec2} dir 目标方向\r\n     */\r\n    _setTargetDestDir(dir: Vec2) {\r\n        if (this._target.setDestDir) {\r\n            this._target.setDestDir(dir);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置目标目标角度\r\n     * @param {number} angle 目标角度\r\n     */\r\n    _setTargetDestAngle(angle: number) {\r\n        if (this._target.setDestAngle) {\r\n            this._target.setDestAngle(angle);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置目标形态索引\r\n     * @param {number} index 形态索引\r\n     */\r\n    _setTargetFormIndex(index: number) {\r\n        if (this._target.setFormIndex) {\r\n            this._target.setFormIndex(index);\r\n        }\r\n    }\r\n}"]}