[{"__type__": "cc.SceneAsset", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 330}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 331}, "_id": "401efd7e-bd20-4537-a13a-f25e6238c2a9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 45}, {"__id__": 267}, {"__id__": 281}, {"__id__": 305}], "_active": true, "_components": [{"__id__": 314}, {"__id__": 315}, {"__id__": 316}, {"__id__": 317}, {"__id__": 318}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 667, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46ihxbAfVPT4CTuCqzBBFA"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "_active": true, "_components": [{"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5d8PZJe/FICbhko69M+Hhu"}, {"__type__": "cc.Node", "_name": "backgrounds", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e0R8IVA2hLp7KHKgP8kN0a"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "68YgRTOwhA1Jel93i/NTeh", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 11}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -27, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 15}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9fAave96tPCLeMFeLz8OCz", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 1253, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 19}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 20}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "29/r4DOvlGcYLqNfXUhl7V", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 21}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 25}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1czNXoCxVIgq1bF4AG+F0F", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 26}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 3813, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 29}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 28}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 30}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3bu0QuQRpKmLwvd0WX6PKB", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 5093, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 34}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 35}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eb7DjyjatAP5+wLl7kUsUu", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 6373, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1AkOsh8lAKIRkVFe8fxGK"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7floMBAfJJ57eUh1MQqeia"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42GyugGglEIZ809rOk/Q2k"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9aJvrhqJFMsYcoT1p7kKIi"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0eUTZlHhpHGrNHK8XUa7hM"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_id": "50lWxsfopMJbfogSKL+3L3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c7cr3UHz9ID5OL86RVgF3A"}, {"__type__": "cc.Node", "_name": "FloorLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 46}, {"__id__": 82}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62oMpiqNVJeLfnCzN4mnAI"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 79}, {"__id__": 80}], "_active": true, "_components": [{"__id__": 81}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beXxFejCNLxKe60F1Oin10"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2chtXhv+1Gwrs39Rt1zaG9"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f3/XrOIzlKBLoxEvHqVW4e"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 50}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a9GZmOyYBDULaCpd3mZnlU"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 49}, "_children": [{"__id__": 51}, {"__id__": 65}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 317.412, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "61Tm5OvxxKhokCaEqoqbnk"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 50}, "_prefab": {"__id__": 52}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 53}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "477nuOtLpL6r2PHD17y6BC", "prefabRootNode": null, "mountedChildren": [{"__id__": 54}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 55}, "nodes": [{"__id__": 56}, {"__id__": 59}, {"__id__": 62}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 51}, "_prefab": {"__id__": 57}, "__editorExtras__": {"mountedRoot": {"__id__": 51}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 56}, "asset": {"__uuid__": "e549918d-a699-468c-9d07-2b1c9dbc1a7a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 58}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "03aF+0L2xDMpZ31Y+wLjku", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 51}, "_prefab": {"__id__": 60}, "__editorExtras__": {"mountedRoot": {"__id__": 51}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "db54204b-56fb-485e-bc0c-66149c7951c3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 61}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f1r2IdyUxBNpB+O2vFEb6s", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 51}, "_prefab": {"__id__": 63}, "__editorExtras__": {"mountedRoot": {"__id__": 51}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 62}, "asset": {"__uuid__": "e3ae5be9-b8ca-450f-a8f7-edd149148782", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 64}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c0Fzv1NzFNFryJ5AmocScG", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 50}, "_prefab": {"__id__": 66}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 65}, "asset": {"__uuid__": "23436676-d303-4acf-82f3-4a44d352b69f", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 67}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8eSdhZK3NMIppIkVt36DPU", "prefabRootNode": null, "mountedChildren": [{"__id__": 68}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 69}, "nodes": [{"__id__": 70}, {"__id__": 73}, {"__id__": 76}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 65}, "_prefab": {"__id__": 71}, "__editorExtras__": {"mountedRoot": {"__id__": 65}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 70}, "asset": {"__uuid__": "b37ff484-6217-472b-bf28-09319f8a31bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 72}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0frmuHLxtBxY/MSAzUmG2C", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 65}, "_prefab": {"__id__": 74}, "__editorExtras__": {"mountedRoot": {"__id__": 65}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "5b101290-4eae-4e19-b77b-6674f856e767", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 75}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "27ED2z8S9BLIO+USiqPoi3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 65}, "_prefab": {"__id__": 77}, "__editorExtras__": {"mountedRoot": {"__id__": 65}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "475c7890-0252-4203-8578-e5928cc7c2e8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 78}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "22j8zgdX9BRanpyQF3t8J4", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8cXzyHT89AqpFqzDauryP/"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "41gkqHYxZMnqvL+ZXsZzjq"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_id": "08zVxPGJdGD40fUnTOXpg5"}, {"__type__": "cc.Node", "_name": "layer_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 83}, {"__id__": 84}, {"__id__": 263}, {"__id__": 264}, {"__id__": 265}], "_active": true, "_components": [{"__id__": 266}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35zuJjzB1ANayJAqJ1UW7u"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "17ou4Z52xO/q07whnPvcry"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [{"__id__": 85}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "097GoeVEdNVYHQp6trQR66"}, {"__type__": "cc.Node", "_name": "scroll_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}, {"__id__": 89}, {"__id__": 92}, {"__id__": 95}, {"__id__": 98}, {"__id__": 101}, {"__id__": 104}, {"__id__": 107}, {"__id__": 110}, {"__id__": 113}, {"__id__": 116}, {"__id__": 119}, {"__id__": 122}, {"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 134}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}, {"__id__": 149}, {"__id__": 152}, {"__id__": 155}, {"__id__": 158}, {"__id__": 161}, {"__id__": 164}, {"__id__": 167}, {"__id__": 170}, {"__id__": 173}, {"__id__": 176}, {"__id__": 179}, {"__id__": 182}, {"__id__": 185}, {"__id__": 188}, {"__id__": 191}, {"__id__": 194}, {"__id__": 197}, {"__id__": 200}, {"__id__": 203}, {"__id__": 206}, {"__id__": 209}, {"__id__": 212}, {"__id__": 215}, {"__id__": 218}, {"__id__": 221}, {"__id__": 224}, {"__id__": 227}, {"__id__": 230}, {"__id__": 233}, {"__id__": 236}, {"__id__": 239}, {"__id__": 242}, {"__id__": 245}, {"__id__": 248}, {"__id__": 251}, {"__id__": 254}, {"__id__": 257}, {"__id__": 260}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4b4c0T8t9Gbq/wPhYl2tgm"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 87}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 86}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 88}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ddhJ8fajJEGpdmRCt8nvlI", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 90}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 89}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 91}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "acA3PkbDJIFo30oxY77JMR", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 93}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 92}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 94}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "caNwhoispDbps6lj4ARwH+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 96}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 95}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 97}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "895SO5fiZHLZXfrMD4rjBP", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 99}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 98}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 100}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eb/qHEFkhGzpPst8eGRb3w", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 102}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 101}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 103}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d5Xnhb2EBFqZ3fC2ev0viJ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 105}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 104}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 106}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "793pA3wTFPba/jTEAMzX+I", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 108}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 107}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 109}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ebUxAk6YlALYTd5ciJAmtW", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 111}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 112}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0buX8h32VO84cRgTNRTBVE", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 114}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 113}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 115}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e2ZIAGRTtEgaXmmMif7LvJ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 117}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 116}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 118}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eekOpbZOZLeIzwAFGLUex5", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 120}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 121}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ddtMX/ZpRMH6x7Q1QACoGc", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 123}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 122}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 124}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dfJbyE6dJHTJEIeXBBAkL3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 126}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 125}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 127}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4an5ny4kNJ6bhypVoMsqo1", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 129}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 130}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e7r5DAdzRNPI+d7rgNp3vG", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 132}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 131}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 133}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d4Uj965fBBxYLOFZgD3BaE", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 135}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 136}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e091NFQ0hL8K+voL8GDHO4", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 138}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 139}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a1o6wtkB1I8Ia5WiAyhSak", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 141}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 140}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 142}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e87LQyaltC8p7Duo1CGnAg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 144}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 143}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 145}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e3+UT3XNNHu7pyIU/+oZXL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 147}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 146}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 148}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eb5Hb7uuJLD7RvfaW0msMy", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 150}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 149}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 151}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "92osddFIJEMrRI+v7s6Evg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 153}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 152}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 154}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d7p8hgtTZCFrHA7B/OaI8Z", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 156}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 155}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 157}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dbsN84BHpHf6T+VXUg82E+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 159}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 158}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 160}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ac2duSkNRINKjB+gulPu6q", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 162}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 163}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b3mebE6ItMWrsiZKuSxhTE", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 165}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 164}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 166}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6afw/CVmBAIrjHUTZBLFR/", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 168}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 167}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 169}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "96dPP6N2VJHbDwAoZHPc+m", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 171}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 170}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 172}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "85CBSo5oNEbIle5PIiUn8q", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 174}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 173}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 175}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d8KS0jRpdAu6C8W9atp6pu", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 177}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 176}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 178}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e3S2nPnWFFmLk7G9orLw/l", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 180}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 179}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 181}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "898JbNNaVN24tV4EH5plU1", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 183}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 184}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "336/cMvfZGlJfuXtY6a6G2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 186}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 187}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b2Zdai8B5JTK8l7qv1UYah", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 189}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 188}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 190}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "caLtimEgBJxLubbsiW0R79", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 192}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 193}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "364iDMwxRNXp8uA+NWJgHA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 195}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 194}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 196}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f0hDCDFmJAEKdGNL0PaRAg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 198}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 197}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 199}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b9Myq4c5ZEMJ7TGHfKEv8H", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 201}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 200}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 202}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eeGg/N+MJC07yQxiIBoavM", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 204}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 203}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 205}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "78fMXDlS5N/6uY+pOrq5wb", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 207}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 206}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 208}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4aRSFrV6RObL0Usf2rJwiF", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 210}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 209}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 211}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "34Q4KqwR5DBqkJ1JJhRTBQ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 213}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 212}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 214}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "fezBy8wCJD27FU/E73+bxm", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 216}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 217}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d3J3/Nin9DHbrws9JnvNai", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 219}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 218}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 220}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "19MGRs96RMrp518KdoQ24m", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 222}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 221}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 223}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "25Ffs2IVNJJ6XKH8h5X75X", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 225}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 224}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 226}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "43CkavRhhCEqo9jYLUeFjj", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 228}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 227}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 229}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "fe/qteguBHe5gbN+yOHc38", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 231}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 230}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 232}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "89Am8p2IJG/JFRZ6btopgn", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 234}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 233}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 235}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8frZbz8M1IS5NHIQByuYjt", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 237}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 236}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 238}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0cL5hzYbhJJond851SZI4O", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 240}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 239}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 241}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8bmfaIe69PXK63cLz+mNXt", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 243}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 242}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 244}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f5g+3P00dDm788d9O/161B", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 246}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 245}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 247}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dfhEMqTHtAdJpH5LMhzYbz", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 249}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 250}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d9BhdBVFlJFpDa6MyGtiGj", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 252}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 251}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 253}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b8niZJbvBFNIYXCU22ntwh", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 255}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 254}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 256}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e73ms3tyxCjoAuyazvTx6n", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 258}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 257}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 259}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "48FWgErHhPiqZd5sGRqMjA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 261}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 260}, "asset": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 262}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "265KlauSxCnLo1+DCtB7Du", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a9iEcu+4NIL7xZuJFyDZP8"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "734KBlcSFH/p/yKe8EM/Sa"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e9rd5Z5JJIZJspQb1UDWqp"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_id": "75fsiBAfVDXIHX0Oqz04ix"}, {"__type__": "cc.Node", "_name": "MainPlane", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 268}, {"__id__": 270}, {"__id__": 275}], "_active": true, "_components": [{"__id__": 280}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5Llji/3pG8ZUsk+/AnCts"}, {"__type__": "cc.Node", "_name": "enemy", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": true, "_components": [{"__id__": 269}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22ge7J4Q5BcrDOrtqtJWcy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0e4ol/pP9AdL9y81NxK1IW"}, {"__type__": "cc.Node", "_name": "Plane 128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [{"__id__": 271}], "_active": true, "_components": [{"__id__": 274}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbYdiDAGtJNILT2mAHeQHg"}, {"__type__": "cc.Node", "_name": "128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 270}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -146.727, "y": -381.491, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1innlyJpPfbCEAHtzu7nR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 126, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcvUM8lEVG7r+Duly9xTUi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bcbad599-6805-464f-b852-c6330a6cc136@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b9z9YmlO1ORJddzAvkDa26"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "69CWNsBiBF8qLjUsjYBXVD"}, {"__type__": "cc.Node", "_name": "Plane 150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [{"__id__": 276}], "_active": true, "_components": [{"__id__": 279}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fSyYswbxJMYFuKxLm+oIa"}, {"__type__": "cc.Node", "_name": "150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 275}, "_children": [], "_active": true, "_components": [{"__id__": 277}, {"__id__": 278}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 49.887, "y": -384.426, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97bvio7axI36wm/98rDd6y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4c8v2GIyBL7p+TWWFkd8E0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "13a76ed5-7bc0-444c-b47f-8beab0558280@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c3kMW+vo1DD4E5mvwzvbme"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dfuqEV4hA5JIX88WPdF7X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dVZOPnxBB55oxOGHxThGs"}, {"__type__": "cc.Node", "_name": "SkyLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 282}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3kr0ELyxNI6WeWVx+sGNb"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 281}, "_children": [{"__id__": 283}, {"__id__": 299}, {"__id__": 300}, {"__id__": 302}, {"__id__": 303}], "_active": true, "_components": [{"__id__": 304}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "67YdBOYOBLio50buqX7V3B"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [{"__id__": 284}, {"__id__": 287}, {"__id__": 290}, {"__id__": 293}, {"__id__": 296}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1cr/yN3WhN8ZwWkxagp12K"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 283}, "_prefab": {"__id__": 285}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 284}, "asset": {"__uuid__": "b37ff484-6217-472b-bf28-09319f8a31bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 286}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "33p9bL78FEI4MgmnrQzMLo", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 283}, "_prefab": {"__id__": 288}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 287}, "asset": {"__uuid__": "b37ff484-6217-472b-bf28-09319f8a31bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 289}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "90GuNASFpCuabT3u3Zyg9M", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 283}, "_prefab": {"__id__": 291}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 290}, "asset": {"__uuid__": "5b101290-4eae-4e19-b77b-6674f856e767", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 292}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1dfFUgCllJAZoVZnp9105J", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 283}, "_prefab": {"__id__": 294}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 293}, "asset": {"__uuid__": "5b101290-4eae-4e19-b77b-6674f856e767", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 295}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3eZmllS4RF1pdJoEBwN4mA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 283}, "_prefab": {"__id__": 297}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 296}, "asset": {"__uuid__": "475c7890-0252-4203-8578-e5928cc7c2e8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 298}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4a5MW+yZZJ47KrOf6ikcD3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "34ZbHLqZ1ALbTJvFYftwAR"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [{"__id__": 301}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "04C+/NAX5EKpl1k+1Mu3BP"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 300}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6dg9lfINEELXMRmt4zrZc"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40pkAjKStLvZeA3/lBWFln"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9f8GiJppZFBpg16QY50I1h"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": null, "_id": "61eNUjh+9DuJzmaa0JouZB"}, {"__type__": "cc.Node", "_name": "DrawNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 306}, {"__id__": 309}], "_active": true, "_components": [{"__id__": 312}, {"__id__": 313}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33t5ymWlpLkbT5idbqGQ7X"}, {"__type__": "cc.Node", "_name": "draw<PERSON>iew", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 305}, "_children": [], "_active": true, "_components": [{"__id__": 307}, {"__id__": 308}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65LbppaIRGz7EVQ8Ktul+X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1Cf74eydN0Yy5qbbxInaR"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "75zN+z8lVGqLYHx2jxa0qd"}, {"__type__": "cc.Node", "_name": "drawMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 305}, "_children": [], "_active": true, "_components": [{"__id__": 310}, {"__id__": 311}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9btiDQ+VZN95aFsLmdMyGC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9eg7PH/u9Il7l5kqhTVfqD"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "f7nWk9GTBEfo/KF0FlisGD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cewjtt/5IRbxIivL8bKq5"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "23xs7GFb1G6bRSeyAo5w9K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "68a25Vb5mhGApMaV59XFjQ0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "progress": 0, "label": "", "_id": "61Fg6c5BVE0I4TKZPyCZ+2"}, {"__type__": "a4bf2J2KGJJV7RbX1jwoQWJ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "levelname": "level2", "totalTime": 60, "backgroundLayer": {"__id__": 319}, "floorLayers": [{"__id__": 320}, {"__id__": 325}], "skyLayers": [{"__id__": 327}], "_id": "f2rN/MimJBS6HE7SikeuYU"}, {"__type__": "LevelEditorBackgroundLayer", "remark": "背景层", "zIndex": 0, "node": {"__id__": 6}, "speed": 120, "type": 1, "scrollLayers": [], "randomLayers": [], "backgrounds": [{"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}]}, {"__type__": "LevelEditorLayer", "remark": "地面随机地形", "zIndex": 0, "node": {"__id__": 46}, "speed": 120, "type": 2, "scrollLayers": [], "randomLayers": [{"__id__": 321}]}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 322}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": [{"__id__": 323}, {"__id__": 324}]}, {"__type__": "LevelEditorRandTerrainUI", "weight": 60, "terrainElement": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorRandTerrainUI", "weight": 40, "terrainElement": {"__uuid__": "23436676-d303-4acf-82f3-4a44d352b69f", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorLayer", "remark": "天空层", "zIndex": 1, "node": {"__id__": 82}, "speed": 150, "type": 3, "scrollLayers": [{"__id__": 326}], "randomLayers": []}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefab": {"__uuid__": "f1201696-4b12-4329-91e4-c46bbbe604b8", "__expectedType__": "cc.Prefab"}, "weight": 100}, {"__type__": "LevelEditorLayer", "remark": "", "zIndex": 0, "node": {"__id__": 282}, "speed": 70, "type": 2, "scrollLayers": [], "randomLayers": [{"__id__": 328}]}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 329}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": []}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "401efd7e-bd20-4537-a13a-f25e6238c2a9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 51}, {"__id__": 56}, {"__id__": 59}, {"__id__": 62}, {"__id__": 65}, {"__id__": 70}, {"__id__": 73}, {"__id__": 76}, {"__id__": 86}, {"__id__": 89}, {"__id__": 92}, {"__id__": 95}, {"__id__": 98}, {"__id__": 101}, {"__id__": 104}, {"__id__": 107}, {"__id__": 110}, {"__id__": 113}, {"__id__": 116}, {"__id__": 119}, {"__id__": 122}, {"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 134}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}, {"__id__": 149}, {"__id__": 152}, {"__id__": 155}, {"__id__": 158}, {"__id__": 161}, {"__id__": 164}, {"__id__": 167}, {"__id__": 170}, {"__id__": 173}, {"__id__": 176}, {"__id__": 179}, {"__id__": 182}, {"__id__": 185}, {"__id__": 188}, {"__id__": 191}, {"__id__": 194}, {"__id__": 197}, {"__id__": 200}, {"__id__": 203}, {"__id__": 206}, {"__id__": 209}, {"__id__": 212}, {"__id__": 215}, {"__id__": 218}, {"__id__": 221}, {"__id__": 224}, {"__id__": 227}, {"__id__": 230}, {"__id__": 233}, {"__id__": 236}, {"__id__": 239}, {"__id__": 242}, {"__id__": 245}, {"__id__": 248}, {"__id__": 251}, {"__id__": 254}, {"__id__": 257}, {"__id__": 260}, {"__id__": 284}, {"__id__": 287}, {"__id__": 290}, {"__id__": 293}, {"__id__": 296}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 332}, "shadows": {"__id__": 333}, "_skybox": {"__id__": 334}, "fog": {"__id__": 335}, "octree": {"__id__": 336}, "skin": {"__id__": 337}, "lightProbeInfo": {"__id__": 338}, "postSettings": {"__id__": 339}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]