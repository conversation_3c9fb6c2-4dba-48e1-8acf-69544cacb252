System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Node, UITransform, view, LevelLayerUI, MyApp, _dec, _class, _dec2, _class3, _dec3, _class5, _crd, ccclass, property, BackgroundsNodeName, LevelLayer, LevelBackgroundLayer, LevelBaseUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayerUI(extras) {
    _reporterNs.report("LevelLayerUI", "./LevelLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
      view = _cc.view;
    }, function (_unresolved_2) {
      LevelLayerUI = _unresolved_2.LevelLayerUI;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Node', 'Prefab', 'UITransform', 'view']);

      ({
        ccclass,
        property
      } = _decorator);
      BackgroundsNodeName = "backgrounds";
      LevelLayer = (_dec = ccclass('LevelLayer'), _dec(_class = class LevelLayer {
        constructor() {
          this.node = null;
          this.speed = 0;
        }

      }) || _class);
      LevelBackgroundLayer = (_dec2 = ccclass('LevelBackgroundLayer'), _dec2(_class3 = class LevelBackgroundLayer extends LevelLayer {
        constructor() {
          super(...arguments);
          this.backgrounds = [];
          this.backgroundsNode = null;
        }

      }) || _class3);

      _export("LevelBaseUI", LevelBaseUI = (_dec3 = ccclass('LevelBaseUI'), _dec3(_class5 = class LevelBaseUI extends Component {
        constructor() {
          super(...arguments);
          this._curLevelIndex = -1;
          // 当前关卡索引
          this._totalTime = 10;
          // 当前关卡的时长
          this._preLevelHeight = 0;
          // 上一关的关卡高度
          this._preLevelOffsetY = 0;
          // 上一关的关卡偏移量
          this._backgroundLayerNode = null;
          this._floorLayersNode = null;
          this._skyLayersNode = null;
          this._backgroundLayer = null;
          this._floorLayers = [];
          this._skyLayers = [];
          this._lastLevelUpdate = null;
        }

        get floorLayers() {
          return this._floorLayers;
        }

        get skyLayers() {
          return this._skyLayers;
        }

        get backgroundLayer() {
          if (!this._backgroundLayer) {
            throw new Error("backgroundLayer is not initialized");
          }

          return this._backgroundLayer;
        }

        get TotalTime() {
          return this._totalTime;
        }

        getLevelTotalHeightByIndex(index) {
          var totalHeight = 0;

          if (this._backgroundLayerNode) {
            var levelNode = this._backgroundLayerNode.getChildByName("level_" + index);

            if (levelNode) {
              var preBgNode = levelNode.getChildByName("layer_0");

              if (preBgNode) {
                var backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);

                if (backgroundsNode) {
                  backgroundsNode.children.forEach(bg => {
                    var height = bg.getComponent(UITransform).contentSize.height;
                    totalHeight += height;
                  });
                }
              }
            }
          }

          return totalHeight;
        }

        onLoad() {}

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        levelPrefab(levelData, levelInfo, bFristLevel) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (bFristLevel === void 0) {
              bFristLevel = false;
            }

            _this._backgroundLayerNode = _this._getOrAddNode(_this.node, "BackgroundLayer");
            _this._floorLayersNode = _this._getOrAddNode(_this.node, "FloorLayers");
            _this._skyLayersNode = _this._getOrAddNode(_this.node, "SkyLayers");

            if (bFristLevel) {
              yield _this._initByLevelData(levelData, levelInfo);
            } else {
              _this._initByLevelData(levelData, levelInfo);
            } // 如果是最后一关，设置无限循环滚动逻辑

            /*if (levelInfo.levelIndex + 1 >= levelInfo.levelCount) {
                this._setupInfiniteScroll();
                this._setupLastLevelUpdate(); 
            }*/

          })();
        }

        switchLevel(speed, time, levelIndex) {
          this.backgroundLayer.speed = speed;
          this._totalTime = time; // 释放上一关资源

          this._removeNode(this._backgroundLayerNode, "level_" + this._curLevelIndex);

          this._removeNode(this._floorLayersNode, "level_" + this._curLevelIndex);

          this._removeNode(this._skyLayersNode, "level_" + this._curLevelIndex);

          this._curLevelIndex = levelIndex;
        }

        _removeNode(parentNode, name) {
          var node = parentNode.getChildByName(name);

          if (node) {
            node.removeFromParent();
          }
        }

        _initByLevelData(data, levelInfo) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var levelBackground = _this2._getOrAddNode(_this2._backgroundLayerNode, "level_" + levelInfo.levelIndex);

            var levelFloor = _this2._getOrAddNode(_this2._floorLayersNode, "level_" + levelInfo.levelIndex);

            var levelSky = _this2._getOrAddNode(_this2._skyLayersNode, "level_" + levelInfo.levelIndex);

            yield _this2._initBackgroundLayer(levelBackground, data, levelInfo);

            _this2._initLayers(levelFloor, _this2.floorLayers, data.floorLayers);

            _this2._initLayers(levelSky, _this2.skyLayers, data.skyLayers);
          })();
        }

        _initLayers(parentNode, layers, dataLayers) {
          dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = this._addLayer(parentNode, "layer_" + i).node;
            levelLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).initByLevelData(layer, this._preLevelOffsetY);
            layers.push(levelLayer);
          });
        }

        _initBackgroundLayer(parentNode, data, levelInfo) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            if (data.backgroundLayer.backgrounds.length > 0) {
              if (_this3._backgroundLayer === null) {
                _this3._backgroundLayer = new LevelBackgroundLayer();
                _this3._backgroundLayer.backgrounds = [];
              }

              _this3._backgroundLayer.speed = data.backgroundLayer.speed;
              var bgCount = Math.ceil(data.totalTime * _this3._backgroundLayer.speed / 1334);
              var loadPromises = data.backgroundLayer.backgrounds.map(backgroundLayer => {
                return new Promise((resolve, reject) => {
                  var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, backgroundLayer);
                  (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                    if (err) {
                      console.error('LevelBaseUI', "initByLevelData load background prefab err", err);
                      reject(err);
                      return;
                    }

                    _this3._backgroundLayer.backgrounds.push(prefab);

                    resolve();
                  });
                });
              });
              yield Promise.all(loadPromises); // 节点设置偏移

              var offsetY = 0;
              _this3._preLevelHeight = 0;

              if (_this3.backgroundLayer) {
                var levelNode = _this3._backgroundLayerNode;
                var childrenCount = levelNode.children.length; // 获取上一关的背景层

                if (childrenCount > 1) {
                  var preLevel = _this3._backgroundLayerNode.children[childrenCount - 2];

                  if (preLevel) {
                    var preBgNode = preLevel.getChildByName("layer_0");

                    if (preBgNode) {
                      offsetY = preBgNode.getPosition().y;
                      var backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);

                      if (backgroundsNode) {
                        backgroundsNode.children.forEach(bg => {
                          var height = bg.getComponent(UITransform).contentSize.height;
                          _this3._preLevelHeight += height;
                        });
                      }
                    }
                  }
                }
              }

              _this3._preLevelOffsetY = _this3._preLevelHeight + offsetY;
              console.log('LevelBaseUI', "_initBackgroundLayer _preLevelHeight", _this3._preLevelHeight, "offsetY", offsetY);
              _this3.backgroundLayer.node = _this3._addLayer(parentNode, "layer_0").node;
              _this3.backgroundLayer.backgroundsNode = _this3._getOrAddNode(_this3.backgroundLayer.node, BackgroundsNodeName);

              _this3.backgroundLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
                error: Error()
              }), LevelLayerUI) : LevelLayerUI).initByLevelData(data.backgroundLayer, _this3._preLevelOffsetY);

              _this3.backgroundLayer.backgroundsNode.setSiblingIndex(0);

              var pos = 0;

              while (_this3._backgroundLayer.backgrounds.length > 0 && bgCount > _this3._backgroundLayer.backgroundsNode.children.length) {
                var bg = instantiate(_this3._backgroundLayer.backgrounds[_this3._backgroundLayer.backgroundsNode.children.length % _this3._backgroundLayer.backgrounds.length]);
                var height = bg.getComponent(UITransform).contentSize.height;
                bg.setPosition(0, pos, 0);
                pos += height;

                _this3._backgroundLayer.backgroundsNode.addChild(bg);
              }
            }
          })();
        }

        _addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
            error: Error()
          }), LevelLayerUI) : LevelLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        tick(deltaTime) {
          this._backgroundLayerNode.children.forEach(node => {
            node.children.forEach(child => {
              var layerUI = child.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
                error: Error()
              }), LevelLayerUI) : LevelLayerUI);

              if (layerUI) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              }
            });
          });

          this.floorLayers.forEach(layer => {
            var _layer$node;

            var layerUI = (_layer$node = layer.node) == null ? void 0 : _layer$node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI);

            if (layerUI) {
              if (layerUI.TrackBackground) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              } else {
                layerUI.tick(deltaTime, layer.speed);
              }
            }
          });
          this.skyLayers.forEach(layer => {
            var _layer$node2;

            var layerUI = (_layer$node2 = layer.node) == null ? void 0 : _layer$node2.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI);

            if (layerUI) {
              if (layerUI.TrackBackground) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              } else {
                layerUI.tick(deltaTime, layer.speed);
              }
            }
          });
        }

        _setupInfiniteScroll() {
          if (!this._backgroundLayerNode) return;
          this.schedule(() => {
            var lastLevelNode = this._backgroundLayerNode.getChildByName("level_" + this._curLevelIndex);

            if (!lastLevelNode) return;
            var lastBgNode = lastLevelNode.getChildByName("layer_" + this._curLevelIndex);
            if (!lastBgNode) return;
            var bgPosY = lastBgNode.getPosition().y;
            var screenHeight = view.getVisibleSize().height; // 当原始节点完全滚出屏幕时，重置其位置到副本下方，并移除副本

            if (bgPosY < -screenHeight * 2) {
              lastBgNode.setPosition(0, bgPosY + screenHeight * 2, 0);

              var lastFloorNode = this._floorLayersNode.getChildByName("floor_" + this._curLevelIndex);

              if (lastFloorNode) {
                lastFloorNode.setPosition(0, bgPosY + screenHeight * 2, 0);
              }

              var lastSkyNode = this._skyLayersNode.getChildByName("sky_" + this._curLevelIndex);

              if (lastSkyNode) {
                lastSkyNode.setPosition(0, bgPosY + screenHeight * 2, 0);
              }
            }
          }, 0.1);
        }

        _setupLastLevelUpdate() {
          this._lastLevelUpdate = () => {
            this.tick(0.016);
          };

          this.schedule(this._lastLevelUpdate, 0.016);
        }

        onDestroy() {
          this.unschedule(this._setupInfiniteScroll);

          if (this._lastLevelUpdate) {
            this.unschedule(this._lastLevelUpdate);
          }
        }

      }) || _class5));

      _crd = false;
    }
  };
});
//# sourceMappingURL=0070a8c1917374c8ca16a9b7e1c823a6819eebdb.js.map