{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts"], "names": ["GameResourceList", "MainPlane", "PrefabBoss", "FrameAnim", "Bullet", "EnemyPlane", "HurtEffect", "HurtNum", "Hurt0", "EmitterPrefabPath", "font_hurtNum", "atlas_mainPlane", "atlas_enemyBullet", "atlas_mainBullet", "atlas_hurtEffects", "atlas_enemyBullet1", "atlas_package_enemy1", "atlas_package_turret1", "atlas_boss_unit", "texture_map_mask", "spine_boss_smoke", "spine_mainfire", "GameMap_1", "key"], "mappings": ";;;;;;;;;AAAIA,MAAAA,gB,GAAmB;AAEnBC,QAAAA,SAAS,EAAE,6BAFQ;AAGnBC,QAAAA,UAAU,EAAE,wBAHO;AAInBC,QAAAA,SAAS,EAAE,mBAJQ;AAKnBC,QAAAA,MAAM,EAAE,gBALW;AAMnBC,QAAAA,UAAU,EAAE,0BANO;AAOnBC,QAAAA,UAAU,EAAE,oBAPO;AAQnBC,QAAAA,OAAO,EAAE,iBARU;AASnBC,QAAAA,KAAK,EAAE,qBATY;AAUnBC,QAAAA,iBAAiB,EAAE,kBAVA;AAYnBC,QAAAA,YAAY,EAAE,cAZK;AAcnBC,QAAAA,eAAe,EAAE,4CAdE;AAgBnBC,QAAAA,iBAAiB,EAAE,2BAhBA;AAiBnBC,QAAAA,gBAAgB,EAAE,8BAjBC;AAkBnBC,QAAAA,iBAAiB,EAAC,gCAlBC;AAmBnBC,QAAAA,kBAAkB,EAAE,8BAnBD;AAoBnBC,QAAAA,oBAAoB,EAAE,gCApBH;AAqBnBC,QAAAA,qBAAqB,EAAE,iCArBJ;AAsBnBC,QAAAA,eAAe,EAAC,wBAtBG;AAwBnBC,QAAAA,gBAAgB,EAAC,gCAxBE;AA0BnBC,QAAAA,gBAAgB,EAAC,uBA1BE;AA2BnBC,QAAAA,cAAc,EAAC,yCA3BI;AA6BnBC,QAAAA,SAAS,EAAC;AA7BS,O,EAgCvB;;AACA,OAAC,MAAM;AACC,aAAK,MAAMC,GAAX,IAAkBvB,gBAAlB,EAAoC;AACpCA,UAAAA,gBAAgB,CAACuB,GAAD,CAAhB,GAA0D,QAAOvB,gBAAgB,CAACuB,GAAD,CAAuC,EAAxH;AACH;AACJ,OAJD;;yBAMevB,gB", "sourcesContent": ["let GameResourceList = {\r\n\r\n    MainPlane: \"prefabs/mainPlane/MainPlane\",\r\n    PrefabBoss: \"prefabs/boss/BossPlane\",\r\n    FrameAnim: \"prefabs/FrameAnim\",\r\n    Bullet: \"prefabs/Bullet\",\r\n    EnemyPlane: \"prefabs/enemy/EnemyPlane\",\r\n    HurtEffect: \"prefabs/HurtEffect\",\r\n    HurtNum: \"prefabs/HurtNum\",\r\n    Hurt0: \"prefabs/effect/Hurt\",\r\n    EmitterPrefabPath: \"prefabs/emitter/\",\r\n\r\n    font_hurtNum: \"font/hurtNum\",\r\n\r\n    atlas_mainPlane: \"texture/mainPlane/package_mainPlane_trans_\",\r\n\r\n    atlas_enemyBullet: \"texture/enemy/enemyBullet\",\r\n    atlas_mainBullet: \"texture/mainPlane/mainBullet\",\r\n    atlas_hurtEffects:\"texture/hurtEffect/hurtEffects\",\r\n    atlas_enemyBullet1: \"texture/enemy/1/enemyBullet1\",\r\n    atlas_package_enemy1: \"texture/enemy/1/package_enemy1\",\r\n    atlas_package_turret1: \"texture/enemy/1/package_turret1\",\r\n    atlas_boss_unit:\"texture/boss/boss_unit\",\r\n\r\n    texture_map_mask:\"texture/mask/mask1/spriteFrame\",\r\n\r\n    spine_boss_smoke:\"spine/skel_boss_smoke\",\r\n    spine_mainfire:\"spine/mainPlane/firePoint/skel_mainfire\",\r\n\r\n    GameMap_1:\"normal/chapter_1/GameMap_1\"\r\n};\r\n\r\n// Add \"game/\" prefix to all values\r\n(() => {\r\n        for (const key in GameResourceList) {\r\n        GameResourceList[key as keyof typeof GameResourceList] = `game/${GameResourceList[key as keyof typeof GameResourceList]}`;\r\n    }\r\n})();\r\n\r\nexport default GameResourceList;"]}