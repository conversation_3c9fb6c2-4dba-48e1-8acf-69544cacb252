System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _dec, _class, _class2, _crd, ccclass, UIAnimMethods;

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'SpriteAtlas', 'tween', 'Node']);

      ({
        ccclass
      } = _decorator);

      _export("default", UIAnimMethods = (_dec = ccclass('UIAnimMethods'), _dec(_class = (_class2 = class UIAnimMethods {
        // 帧数转换为时间

        /**
         * 根据帧数计算时间
         * @param {number} from 起始帧
         * @param {number} to 结束帧
         * @returns {number} 时间（秒）
         */
        static fromTo(from, to) {
          return to < from ? 0 : (to - from) * this.frameToTime;
        }

      }, _class2.frameToTime = 1 / 30, _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=3719438ff916823b28f76e0f2744dc07ec3532d5.js.map