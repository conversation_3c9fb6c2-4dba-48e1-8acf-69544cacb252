System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, UITransform, _dec, _class, _class2, _crd, ccclass, property, EnemyEffectLayer;

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Prefab', 'NodePool', 'Vec3', 'sp', 'SpriteFrame', 'instantiate', 'UIOpacity', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyEffectLayer = (_dec = ccclass('EnemyEffectLayer'), _dec(_class = (_class2 = class EnemyEffectLayer extends Component {
        constructor() {
          super(...arguments);
          this.hurtEffectLayer = null;
          this.hurtNumLayer = null;
        }

        onLoad() {
          EnemyEffectLayer.me = this;
        }

        start() {
          this.hurtEffectLayer = new Node();
          this.hurtEffectLayer.addComponent(UITransform);
          this.hurtEffectLayer.parent = this.node;
          this.hurtEffectLayer.setPosition(0, 0);
          this.hurtNumLayer = new Node();
          this.hurtNumLayer.addComponent(UITransform);
          this.hurtNumLayer.parent = this.node;
          this.hurtNumLayer.setPosition(0, 0);
        }

      }, _class2.me = void 0, _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=61ea81908ddf56b42be6c42179a50acb60d148ad.js.map