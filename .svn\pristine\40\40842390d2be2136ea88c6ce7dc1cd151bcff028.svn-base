"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// 临时在当前模块增加编辑器内的模块为搜索路径，为了能够正常 require 到 cc 模块，后续版本将优化调用方式
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() {
}
;
function unload() {
}
;
exports.methods = {
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    async saveToPrefab(component_uuid, nodeName, prefabUuid) {
        // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
        const scene = cc_1.director.getScene();
        const target = scene.getChildByPath(`Canvas/${nodeName}`);
        if (!target) {
            console.error("node not found:", nodeName);
            return;
        }
        cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
        // const json = cce.Utils.serialize(target);
        // console.log('Prefab JSON:', json);
        // Editor.Message.request('asset-db', 'save-asset', prefabUuid, json);
    },
    async createNewEmitter(prefabName, prefabPath) {
        const scene = cc_1.director.getScene();
        const target = scene.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }
        let emitterNode = new cc_1.Node(prefabName);
        emitterNode.parent = target;
        emitterNode.setPosition(new cc_1.Vec3(0, 0, 0));
        emitterNode.addComponent('cc.UITransform'); // Ensure it has a transform component
        emitterNode.addComponent('Emitter');
        const nodeUuid = emitterNode.uuid;
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
    },
    movePlayer(direction) {
        const scene = cc_1.director.getScene();
        const target = scene.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }
        const playerNode = target.getChildByName('Player');
        if (!playerNode) {
            console.error("Player node not found");
            return;
        }
        playerNode.setPosition(playerNode.position.add(direction));
    },
    movePlayerUp() {
        this.movePlayer(new cc_1.Vec3(0, 10, 0));
    },
    movePlayerDown() {
        this.movePlayer(new cc_1.Vec3(0, -10, 0));
    },
    movePlayerLeft() {
        this.movePlayer(new cc_1.Vec3(-10, 0, 0));
    },
    movePlayerRight() {
        this.movePlayer(new cc_1.Vec3(10, 0, 0));
    }
};
//# sourceMappingURL=data:application/json;base64,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