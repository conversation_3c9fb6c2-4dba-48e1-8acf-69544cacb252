System.register([], function (_export, _context) {
  "use strict";

  var MainPlaneData, _crd;

  _export("MainPlaneData", void 0);

  return {
    setters: [],
    execute: function () {
      _crd = true;

      _export("MainPlaneData", MainPlaneData = class MainPlaneData {
        constructor() {
          this.hp = 0;
          this.maxhp = 0;
          this.screenLv = 0;
          this.die = false;
          this.relifeNum = 0;
          this.lifeNum = 0;
          this.atkAddRatio = 0;
          this.intensifyAtk = [];
          this.revive = false;
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=7488200ff64035440d127a60058d0eb6fc1e1e4d.js.map