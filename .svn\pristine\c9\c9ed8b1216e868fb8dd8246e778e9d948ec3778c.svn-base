import { MyApp } from "db://assets/scripts/MyApp";
import { error } from "cc";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { ResEnemy } from "../../autogen/luban/schema";

export class EnemyData extends AttributeData{
    _planeId: number = 0;//飞机id
    
    config:ResEnemy | null = null;//飞机静态配置
    
    get planeId(){
        return this._planeId;
    }

    set planeId(value){
        if(value!=this._planeId){
            this._planeId = value;
            this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;
            this.updateData();
        }
    }

    get recourseSpine() {
        if (!this.config){
            return "";
        }
    }

    updateData(){
        if (!this.config){
            error("enemyPlane lv config is null, cannot update attributes.");
            return;
        }
        this.setBaseAttribute(AttributeConst.MaxHP,this.config.hp);
        this.setBaseAttribute(AttributeConst.Attack,this.config.atk);
    }
}