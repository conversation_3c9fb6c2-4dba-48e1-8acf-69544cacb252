System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, v2, Tools, TrackGroup, BossBaseData, BossData, BossAttackPointData, BossAttackActionData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "./EnemyWave", _context.meta, extras);
  }

  _export({
    BossBaseData: void 0,
    BossData: void 0,
    BossAttackPointData: void 0,
    BossAttackActionData: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }, function (_unresolved_3) {
      TrackGroup = _unresolved_3.TrackGroup;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['color', 'Color', 'v2', 'Vec2']);

      /**
       * Boss 基础数据类
       */
      _export("BossBaseData", BossBaseData = class BossBaseData {
        constructor() {
          this.id = 0;
          this.atlas = [];
          this.exp = 0;
          this.collideArr = [];
          this.attack = 0;
          this.collideAttack = 0;
          this.transformAudio = "";
          this.blastParam = [];
          this.blastShake = [];
          this.appearParam = [];
          this.onlyLoot = [];
          this.lootArr = [];
          this.lootParam0 = [];
          this.lootParam1 = [];
        }

        /**
         * 从 JSON 数据加载 Boss 基础数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("id")) this.id = parseInt(data.id);
          if (data.hasOwnProperty("atlas")) this.atlas = data.atlas.split(";");
          if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
          if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;

          if (data.hasOwnProperty("cs") && data.cs !== "") {
            const csArray = data.cs.split(";");

            for (const cs of csArray) {
              if (cs !== "") this.collideArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(cs, ",");
            }
          }

          if (data.hasOwnProperty("bla") && data.bla !== "") {
            const blaArray = data.bla.split(";");

            for (const bla of blaArray) {
              if (bla !== "") this.blastParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(bla, ",");
            }
          }

          if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");

            for (const sk of skArray) {
              if (sk !== "") this.blastShake.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToPoint(sk, ","));
            }
          }

          if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
          if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);
          if (data.hasOwnProperty("app")) this.appearParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.app, ",");
          if (data.hasOwnProperty("fl") && data.fl !== "") this.onlyLoot = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.fl, ",");
          if (data.hasOwnProperty("loot")) this.lootArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.loot, ",");
          if (data.hasOwnProperty("lp0")) this.lootParam0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lp0, ",");
          if (data.hasOwnProperty("lp1")) this.lootParam1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lp1, ",");
        }

      });
      /**
       * Boss 数据类
       */


      _export("BossData", BossData = class BossData extends BossBaseData {
        constructor(...args) {
          super(...args);
          this.subId = 0;
          this.units = [];
          this.unitsOrder = [];
          this.hpParam = [];
          this.appearParam = [];
          this.transformAudio = "";
          this.blastType = 0;
          this.bombHurt = 0;
          this.leave = 0;
          this.nextBoss = [];
          this.wayPointXs = [];
          this.wayPointYs = [];
          this.wayPointIntervals = [];
          this.speeds = [];
          this.attackIntervals = [];
          this.snakeParam = [];
          this.trackGroups = [];
          this.attackActions = [];
          this.attackPoints = [];
          this.dieFallDelay = 0;
          this.blastCount = 0;
          this.va = [];
          this.dashTrack = [];
          this.freeTrackArr = [];
          this.enemyId = 0;
          this.enemyRotate = 0;
          this.enemyPos = [];
          this.enemyTrackGroup1 = [];
          this.enemyTrackGroup2 = [];
        }

        /**
         * 从 JSON 数据加载 Boss 数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("bId")) this.id = parseInt(data.bId);
          if (data.hasOwnProperty("sId")) this.subId = parseInt(data.sId);
          if (data.hasOwnProperty("us")) this.units = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.us, ",");
          if (data.hasOwnProperty("rid") && data.rid !== "") this.nextBoss = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.rid, ",");
          if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
          if (data.hasOwnProperty("leave")) this.leave = parseInt(data.leave);
          if (data.hasOwnProperty("va")) this.va = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.va, ",");

          if (data.hasOwnProperty("ua")) {
            const uaArray = data.ua.split(";");

            for (const ua of uaArray) {
              if (ua !== "") this.unitsOrder = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(ua, ",");
            }
          }

          if (data.hasOwnProperty("hpp")) this.hpParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hpp, ",");
          if (data.hasOwnProperty("app")) this.appearParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.app, ",");
          if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;
          if (data.hasOwnProperty("bla")) this.blastType = Number(data.bla);
          if (data.hasOwnProperty("bh")) this.bombHurt = Number(data.bh);
          if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
          if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);

          if (data.hasOwnProperty("dh")) {
            const dhArray = data.dh.split(";");

            for (const dh of dhArray) {
              if (dh !== "") this.dashTrack = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(dh, ",");
            }
          }

          if (data.hasOwnProperty("ea") && data.ea !== "") {
            this.freeTrackArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.ea, ",");
          }

          if (data.hasOwnProperty("way") && data.way !== "") {
            const wayArray = data.way.split(";");

            for (const way of wayArray) {
              if (way !== "") {
                const point = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(way, ",");
                this.wayPointXs.push(point.x);
                this.wayPointYs.push(point.y);
              }
            }
          }

          if (data.hasOwnProperty("wi") && data.wi !== "") {
            this.wayPointIntervals = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.wi, ",");
          }

          if (data.hasOwnProperty("sp") && data.sp !== "") {
            this.speeds = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.sp, ",");
          }

          if (data.hasOwnProperty("ai") && data.ai !== "") {
            this.attackIntervals = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.ai, ",");
          }

          if (data.hasOwnProperty("ra") && data.ra !== "") {
            const raArray = data.ra.split(";");

            for (let i = 0; i < raArray.length; i++) {
              if (raArray[i] !== "") {
                const attackAction = new BossAttackActionData();
                attackAction.loadJson(raArray[i]);
                this.attackActions.push(attackAction);
              }
            }
          } // 解析攻击点数据


          let attackPointIndex = 0;

          while (true) {
            const attackPointKey = "a" + attackPointIndex++;
            if (!data.hasOwnProperty(attackPointKey) || data[attackPointKey] === "") break;
            const attackPointData = new BossAttackPointData();
            attackPointData.loadJson(data[attackPointKey]);
            this.attackPoints.push(attackPointData);
          } // 解析爆炸参数


          if (data.hasOwnProperty("blp") && data.blp !== "") {
            const blpArray = data.blp.split(";");

            for (let i = 0; i < blpArray.length; i++) {
              if (blpArray[i] !== "") {
                this.blastParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(blpArray[i], ",");
                this.blastCount++;
              }
            }
          } // 解析爆炸震动参数


          if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");

            for (let i = 0; i < skArray.length; i++) {
              if (skArray[i] !== "") {
                this.blastShake.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(skArray[i], ","));
              }
            }
          } // 解析死亡掉落延迟


          if (data.hasOwnProperty("ft")) {
            this.dieFallDelay = Number(data.ft);
          } // 解析唯一掉落


          if (data.hasOwnProperty("fl") && data.fl !== "") {
            this.onlyLoot = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.fl, ",");
          } // 解析掉落数组


          if (data.hasOwnProperty("loot")) {
            this.lootArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.loot, ",");
          } // 解析掉落参数 0


          if (data.hasOwnProperty("lp0")) {
            this.lootParam0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.lp0, ",");
          } // 解析掉落参数 1


          if (data.hasOwnProperty("lp1")) {
            this.lootParam1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.lp1, ",");
          } // 解析敌人 ID


          if (data.hasOwnProperty("eid")) {
            this.enemyId = Number(data.eid);
          } // 解析敌人旋转角度


          if (data.hasOwnProperty("erotate")) {
            this.enemyRotate = Number(data.erotate);
          } // 解析敌人位置


          if (data.hasOwnProperty("epos")) {
            const eposArray = data.epos.split("#");

            for (let i = 0; i < eposArray.length; i++) {
              const position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(eposArray[i], ",");

              if (position.length === 2) {
                this.enemyPos.push(v2(position[0], position[1]));
              }
            }
          } // 解析敌人轨迹组 1


          if (data.hasOwnProperty("etrack1")) {
            const etrack1Array = data.etrack1.split("#");

            for (let i = 0; i < etrack1Array.length; i++) {
              if (etrack1Array[i] !== "" && etrack1Array[i].split(";").length > 1) {
                const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
                  error: Error()
                }), TrackGroup) : TrackGroup)();
                trackGroup.loadJson(etrack1Array[i]);
                this.enemyTrackGroup1.push(trackGroup);
              }
            }
          } // 解析敌人轨迹组 2


          if (data.hasOwnProperty("etrack2")) {
            const etrack2Array = data.etrack2.split("#");

            for (let i = 0; i < etrack2Array.length; i++) {
              if (etrack2Array[i] !== "" && etrack2Array[i].split(";").length > 1) {
                const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
                  error: Error()
                }), TrackGroup) : TrackGroup)();
                trackGroup.loadJson(etrack2Array[i]);
                this.enemyTrackGroup2.push(trackGroup);
              }
            }
          }
        }

      });
      /**
       * Boss 攻击点数据类
       */


      _export("BossAttackPointData", BossAttackPointData = class BossAttackPointData {
        constructor() {
          this.bAvailable = true;
          this.atkType = 0;
          this.atkUnitId = 0;
          this.atkAnim = [];
          this.x = 0;
          this.y = 0;
          this.shootInterval = [];
          this.bulletIDs = [];
          this.bulletNums = [];
          this.bulletIntervals = [];
          this.bulletAttackRates = [];
          this.attackOverDelay = [];
          this.waveIds = [];
        }

        /**
         * 从 JSON 数据加载攻击点数据
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = data.split("#");

          if (parts.length < 3) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("BossAttackPointData error:", data);
            return;
          }

          this.atkType = parseInt(parts[0]);
          this.atkUnitId = parseInt(parts[1]);
          const animParts = parts[2].split(";");

          for (const anim of animParts) {
            if (anim !== "") {
              this.atkAnim = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(anim, ",");
            }
          }

          switch (this.atkType) {
            case 0:
              // 普通攻击
              const attackParts = parts[3].split(";");

              try {
                if (attackParts.length <= 1) {
                  this.bAvailable = false;
                  return;
                }

                const position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(attackParts[0], ",");
                this.x = position.x;
                this.y = position.y;

                for (let i = 1; i < attackParts.length; i++) {
                  if (attackParts[i] !== "") {
                    const attackData = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToNumber(attackParts[i], ",");
                    this.shootInterval.push(attackData[0]);
                    this.bulletIDs.push(attackData[1]);
                    this.bulletNums.push(attackData[2]);
                    this.bulletIntervals.push(attackData[3]);
                    this.bulletAttackRates.push(attackData[4] / 100);
                    this.attackOverDelay.push(attackData[5]);
                  }
                }
              } catch (error) {
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).error("BossAttackPointData error:", data);
              }

              break;

            case 1:
              // 波次攻击
              const waveParts = parts[3].split(";");

              try {
                if (waveParts.length <= 1) {
                  this.bAvailable = false;
                  return;
                }

                const wavePosition = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(waveParts[0], ",");
                this.x = wavePosition.x;
                this.y = wavePosition.y;
                this.waveIds = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(waveParts[1], ",");

                if (waveParts.length > 2) {
                  this.attackOverDelay.push(parseInt(waveParts[2]));
                }
              } catch (error) {
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).error("BossAttackPointData error:", data);
              }

              break;

            default:
              (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).error("Unknown attack type:", this.atkType);
              break;
          }
        }

      });
      /**
       * Boss 攻击动作数据类
       */


      _export("BossAttackActionData", BossAttackActionData = class BossAttackActionData {
        constructor() {
          this.bAtkMove = false;
          // 是否移动攻击
          this.atkActId = 0;
          // 攻击动作 ID
          this.atkPointId = [];
        }

        // 攻击点 ID 列表

        /**
         * 从 JSON 数据加载攻击动作数据
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data, ",");

          try {
            if (parts.length > 1) {
              this.bAtkMove = parts[0] === 1;
              this.atkActId = parts[1];

              for (let i = 2; i < parts.length; i++) {
                this.atkPointId.push(parts[i]);
              }
            }
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("BossAttackActionData error:", data);
          }
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=a8b012d3e059b916f7a5fedf461dffc4fdf64c9e.js.map