{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/wave/Wave.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "Vec2", "WaveData", "eSpawnOrder", "GameIns", "ccclass", "property", "executeInEditMode", "WaveTrack", "WaveTrackGroup", "Wave", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_nextSpawnIndex", "_spawnQueue", "play", "value", "isCompleted", "_reset", "waveData", "planeList", "trigger", "spawnOrder", "Random", "sort", "Math", "random", "tick", "dtInMiliseconds", "spawn", "length", "spawnSingle", "spawnInterval", "index", "spawnPos", "spawnAngle", "eval", "spawnSpeed", "createPlane", "planeId", "pos", "angle", "speed", "enemy", "enemyManager", "addPlane", "console", "log", "setStandByTime", "setPos", "x", "y", "initMove", "initDelayDestroy", "delayDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AACVC,MAAAA,O,iBAAAA,O;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CX,U;;2BAGpCY,S,WADZH,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,2BARb,MACaU,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZJ,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAAC,CAACE,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAKpBC,I,aAFZL,OAAO,CAAC,MAAD,C,WACPE,iBAAiB,E,WAEbD,QAAQ,CAACT,SAAD,C,WAORS,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAACR,OAAD,C,WAGRQ,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAACR,OAAD,C,WAERQ,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAACR,OAAD,C,WAERQ,QAAQ,CAAEL,IAAF,C,WAGRK,QAAQ,CAAC,CAACG,cAAD,CAAD,C,WAGRH,QAAQ,CAAC,CAACR,OAAD,CAAD,C,WAGRQ,QAAQ,CAAC;AAACK,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,+CAlCb,MAEaD,IAFb,SAE0BV,SAF1B,CAEoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAmChC;AACJ;AACA;AArCoC,eAsCxBY,YAtCwB,GAsCA,KAtCA;AAAA,eAyCxBC,gBAzCwB,GAyCG,CAzCH;AAAA,eA0CxBC,cA1CwB,GA0CC,CA1CD;AAAA,eA2CxBC,eA3CwB,GA2CE,CA3CF;AAAA,eA4CxBC,WA5CwB,GA4CA,EA5CA;AAAA;;AAEjB,YAAJC,IAAI,CAACC,KAAD,EAAiB,CAC/B;;AACc,YAAJD,IAAI,GAAW;AACtB,iBAAO,KAAP;AACH;;AAiCD;AACsB,YAAXE,WAAW,GAAG;AAAE,iBAAO,KAAKP,YAAZ;AAA2B;;AAM9CQ,QAAAA,MAAM,GAAG;AACb,eAAKR,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,GAAmB,KAAKK,QAAL,CAAcC,SAAjC;AACH;;AAEDC,QAAAA,OAAO,GAAG;AACN,eAAKH,MAAL,GADM,CAEN;;;AACA,cAAI,KAAKC,QAAL,CAAcG,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,iBAAKT,WAAL,GAAmB,KAAKA,WAAL,CAAiBU,IAAjB,CAAsB,MAAMC,IAAI,CAACC,MAAL,KAAgB,GAA5C,CAAnB;AACH;AACJ,SA5D+B,CA8DhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAKlB,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyBiB,eAAzB;;AACA,cAAI,KAAKjB,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,gBAAI,CAAC,KAAKiB,KAAL,EAAL,EAAmB;AACf,mBAAKnB,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ;;AAEOmB,QAAAA,KAAK,GAAY;AACrB,cAAI,KAAKhB,eAAL,IAAwB,KAAKC,WAAL,CAAiBgB,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKC,WAAL,CAAiB,KAAKlB,eAAL,EAAjB;AACA,eAAKD,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKQ,QAAL,CAAca,aAA5D;AACA,iBAAO,IAAP;AACH;;AAEOD,QAAAA,WAAW,CAACE,KAAD,EAAsB;AACrC,cAAIA,KAAK,IAAI,KAAKnB,WAAL,CAAiBgB,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAII,QAAQ,GAAG,KAAKf,QAAL,CAAce,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKhB,QAAL,CAAcgB,UAAd,CAAyBC,IAAzB,EAAjB;AACA,cAAIC,UAAU,GAAG,KAAKlB,QAAL,CAAckB,UAA/B;AAEA,eAAKC,WAAL,CAAiB,KAAKxB,WAAL,CAAiBmB,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD,EAAgEE,UAAhE;AACH;;AAEaC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4CC,KAA5C,EAA2D;AAAA;;AAAA;AAChF,gBAAIC,KAAK,SAAS;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,QAArB,CAA8BN,OAA9B,CAAlB;;AACA,gBAAII,KAAJ,EAAW;AACP;AACAG,cAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BR,OAA3B,EAAoCC,GAApC,EAAyCC,KAAzC,EAAgDC,KAAhD;AACAC,cAAAA,KAAK,CAACK,cAAN,CAAqB,CAArB;AACAL,cAAAA,KAAK,CAACM,MAAN,CAAaT,GAAG,CAACU,CAAjB,EAAoBV,GAAG,CAACW,CAAxB;AACAR,cAAAA,KAAK,CAACS,QAAN,CAAeV,KAAf,EAAsBD,KAAtB;AACAE,cAAAA,KAAK,CAACU,gBAAN,CAAuB,KAAI,CAAClC,QAAL,CAAcmC,YAArC;AACH;AAT+E;AAUnF;;AA1G+B,O;;;;;iBASV,C;;;;;;;iBAEP,C;;;;;;;iBAGE,C;;;;;;;iBAEE,C;;;;;;;iBAEO,C;;;;;;;iBAEL,C;;;;;;;iBAEQ,C;;;;;;;iBAEL,IAAIvD,IAAJ,E;;;;;;;iBAGe,E;;;;;;;iBAGJ,E;;;;;;;iBAGL;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/scripts/Game/GameIns';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property(CCBoolean)\r\n    public set play(value: boolean) {\r\n    }\r\n    public get play():boolean {\r\n        return false;\r\n    }\r\n\r\n    @property(CCInteger)\r\n    public enemyGroupID = 0;\r\n    @property(CCFloat)\r\n    public delay = 0;\r\n\r\n    @property(CCInteger)\r\n    public planeID = 0;\r\n    @property(CCInteger)\r\n    public planeType = 0;\r\n    @property(CCFloat)\r\n    public interval: number = 0;\r\n    @property(CCInteger)\r\n    public num: number = 0;\r\n    @property(CCFloat)\r\n    public rotateSpeed: number = 0;\r\n    @property (Vec2)\r\n    public startPos: Vec2 = new Vec2();\r\n\r\n    @property([WaveTrackGroup])\r\n    public trackGroups: WaveTrackGroup[] = [];\r\n\r\n    @property([CCFloat])\r\n    public firstShootDelay: number[] = [];\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n\r\n    private _reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue = this.waveData.planeList;\r\n    }\r\n\r\n    trigger() {\r\n        this._reset();\r\n        // shuffle spawn queue\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._spawnQueue = this._spawnQueue.sort(() => Math.random() - 0.5);\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n            if (!this.spawn()) {\r\n                this._isCompleted = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawn(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingle(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval;\r\n        return true;\r\n    }\r\n\r\n    private spawnSingle(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        let spawnSpeed = this.waveData.spawnSpeed;\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle, spawnSpeed);\r\n    }\r\n\r\n    private async createPlane(planeId: number, pos: Vec2, angle: number, speed: number) {\r\n        let enemy = await GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setStandByTime(0);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(speed, angle);\r\n            enemy.initDelayDestroy(this.waveData.delayDestroy);\r\n        }\r\n    }\r\n\r\n}"]}