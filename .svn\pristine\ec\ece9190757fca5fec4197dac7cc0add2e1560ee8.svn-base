import { _decorator, Label, Node } from 'cc';

import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logDebug } from '../../../../../scripts/utils/Logger';
import { BaseUI, UILayer, UIOpt } from "db://assets/scripts/ui/UIMgr";
import { BundleName } from '../../../../Bundle';
import List from '../common/components/list/List';
import ListItem from '../common/components/list/ListItem';
const { ccclass, property } = _decorator;

@ccclass('PlaneCombineResultUI')
export class PlaneCombineResultUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/PlaneCombineResultUI"; }
    public static getLayer(): UILayer { return UILayer.PopUp }
    public static getBundleName(): string { return BundleName.HomePlane }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: true }
    }
    @property(List)
    list: List | null = null;
    @property(Node)
    singleResult: Node | null = null;
    private _results: csproto.cs.ICSEquipCombineResultOne[] = [];


    protected onLoad(): void {
    }

    async onShow(results: csproto.cs.ICSEquipCombineResultOne[]): Promise<void> {
        this._results = results
        if (results.length > 1) {
            this.list!.node.active = true;
            this.singleResult!.active = false;
            this.list!.numItems = results.length;
            return
        }
        this.list!.node.active = false;
        this.singleResult!.active = true;
        const equipInfo = MyApp.lubanTables.TbResEquip.get(this._results[0]!.equip_id!)
        if (equipInfo) {
            this.singleResult!.getComponentInChildren(Label)!.string = equipInfo.name + "(品质:" + equipInfo.quality + ")";
        } else {
            this.singleResult!.getComponentInChildren(Label)!.string = "未知";
        }
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

    onListRender(item: Node, index: number) {
        logDebug("PlaneUI", `index:${index} id:${item.getComponent(ListItem)!.listId}`)
        const equipInfo = MyApp.lubanTables.TbResEquip.get(this._results[index]!.equip_id!)
        if (equipInfo) {
            item.getComponentInChildren(Label)!.string = equipInfo.name + "(品质:" + equipInfo.quality + ")";
        } else {
            item.getComponentInChildren(Label)!.string = "未知";
        }
    }
}
