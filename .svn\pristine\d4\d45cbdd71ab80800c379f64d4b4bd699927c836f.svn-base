import { ResMainPlane, ResMainPlaneLv } from "db://assets/scripts/autogen/luban/schema";
import { AttributeData } from "../base/AttributeData";
import { MyApp } from "db://assets/scripts/MyApp";
import { AttributeConst } from "../../const/AttributeConst";
import { error } from "cc";

export class PlaneData extends AttributeData{
    id: number = 0;//唯一id
    _planeId: number = 0;//飞机id
    _level:number = 0//英雄等级
    
    config:ResMainPlane | null = null;//飞机静态配置
    lvConfig:ResMainPlaneLv | null = null;//飞机等级配置
    get level(){
        return this._level;
    }

    set level(value){
        if(value!=this._level){
            this._level = value;
            this.lvConfig = MyApp.lubanMgr.table.TbResMainPlaneLv.get(this._planeId*1000+this._level)!;
            this.updateData();
        }
    }

    get planeId(){
        return this._planeId;
    }

    set planeId(value){
        if(value!=this._planeId){
            this._planeId = value;
            this.config = MyApp.lubanMgr.table.TbResMainPlane.get(this._planeId*100)!;
        }
    }

    get recourseSpine() {
        if (!this.config){
            return "";
        }
    }

    updateData(){
        if (!this.lvConfig){
            error("Plane lv config is null, cannot update attributes.");
            return;
        }
        this.setBaseAttribute(AttributeConst.MaxHP,this.lvConfig.hp);
        this.setBaseAttribute(AttributeConst.Attack,this.lvConfig.atk);
        this.setBaseAttribute(AttributeConst.AttackBoss,this.lvConfig.atk);
        this.setBaseAttribute(AttributeConst.AttackNormal,this.lvConfig.atk);

        //根据飞机基础配置表，获取基础属性
        this.getAttributeList();
    }

    getAttributeList(){
        // 获取装备，技能，buff等属性
    }
}