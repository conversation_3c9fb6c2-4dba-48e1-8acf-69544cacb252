{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "emitter-editor", "version": "1.0.0", "author": "Cocos Creator", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "tsc", "watch": "tsc -w"}, "description": "i18n:emitter-editor.description", "main": "./dist/main.js", "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "contributions": {"inspector": {"section": {"node": {"EmitterEditor": "./dist/contributions/inspector/emitter-editor.js", "Emitter": "./dist/contributions/inspector/emitter.js"}}}, "scene": {"script": "./dist/scene.js"}, "messages": {"movePlayerUp": {"methods": ["movePlayerUp"]}, "movePlayerDown": {"methods": ["movePlayerDown"]}, "movePlayerLeft": {"methods": ["movePlayerLeft"]}, "movePlayerRight": {"methods": ["movePlayerRight"]}}, "shortcuts": [{"message": "movePlayerUp", "win": "shift+w", "mac": "cmd+w"}, {"message": "movePlayerDown", "win": "shift+s", "mac": "cmd+s"}, {"message": "movePlayerLeft", "win": "shift+a", "mac": "cmd+a"}, {"message": "movePlayerRight", "win": "shift+d", "mac": "cmd+d"}]}}