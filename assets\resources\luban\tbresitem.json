[{"id": 80000101, "name": "测试道具", "icon": "icon1", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80100101, "name": "周末宝箱", "icon": "icon2", "quality": 3, "quality_sub": 0, "use_type": 1, "effect_id": 1, "effect_param1": 6666, "effect_param2": 0, "max_stack_num": 0}, {"id": 80203101, "name": "合成材料1", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203201, "name": "合成材料2", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220199, "name": "升级材料1", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220299, "name": "升级材料2", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220399, "name": "升级材料3", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220499, "name": "升级材料4", "icon": "", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 89999999, "name": "金币", "icon": "icon_coin", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 2, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89999998, "name": "钻石", "icon": "icon_daimond", "quality": 3, "quality_sub": 0, "use_type": 2, "effect_id": 3, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89999997, "name": "体力", "icon": "icon_energy", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 5, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89990011, "name": "金币袋子666", "icon": "icon_coinpackage", "quality": 3, "quality_sub": 0, "use_type": 1, "effect_id": 2, "effect_param1": 666, "effect_param2": 0, "max_stack_num": 9999}]