
import { _decorator, Label } from 'cc';

import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { MyApp } from 'db://assets/scripts/MyApp';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/ui/UIMgr";
import { BundleName } from '../../../../Bundle';
import { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';
import { EventMgr } from '../../event/EventManager';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { OpenEquipInfoUISource } from './PlaneTypes';

const { ccclass, property } = _decorator;

@ccclass('PlaneEquipInfoUI')
export class PlaneEquipInfoUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/PlaneEquipInfoUI"; }
    public static getLayer(): UILayer { return UILayer.PopUp }
    public static getBundleName(): string { return BundleName.HomePlane; }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: true }
    }
    private _planeEquipInfo: csproto.cs.ICSItem | null = null;

    @property(ButtonPlus)
    replaceEquipBtn: ButtonPlus | null = null;
    @property(ButtonPlus)
    unEquipBtn: ButtonPlus | null = null;
    @property(ButtonPlus)
    levelUpEquipBtn: ButtonPlus | null = null;
    @property(ButtonPlus)
    multiLevelUpEquipBtn: ButtonPlus | null = null;

    protected onLoad(): void {
        this.levelUpEquipBtn!.addClick(this.onClickLevelUpEquip, this)
        this.multiLevelUpEquipBtn!.addClick(this.onClickMultiLevelUpEquip, this)
        this.unEquipBtn!.addClick(this.onClickUnEquip, this)
        this.replaceEquipBtn!.addClick(this.onClickReplaceEquip, this)
    }

    async onShow(planeEquipInfo: csproto.cs.ICSItem, source: OpenEquipInfoUISource): Promise<void> {
        logDebug("PlaneUI", `onShow planeEquipInfo:${planeEquipInfo} source:${source}`)
        const tbEquip = MyApp.lubanMgr.table.TbResEquip
        if (source == OpenEquipInfoUISource.DisPlay) {
            this.replaceEquipBtn!.node.active = false;
            this.unEquipBtn!.node.parent!.active = true
        } else {
            this.replaceEquipBtn!.node.active = true
            this.unEquipBtn!.node!.parent!.active = false
            const slot = DataMgr.equip.eqSlots.getEmptySlotByClass(tbEquip.get(planeEquipInfo.item_id!)?.equipClass ?? 0)
            if (!slot) {
                this.replaceEquipBtn!.getComponentInChildren(Label)!.string = "替换"
            } else {
                this.replaceEquipBtn!.getComponentInChildren(Label)!.string = "装备"
            }
        }
        this.getComponentInChildren(Label)!.string = MyApp.lubanTables.TbResEquip.get(planeEquipInfo.item_id!)?.name || ""
        this._planeEquipInfo = planeEquipInfo;
    }

    private onClickReplaceEquip() {
        DataMgr.equip.eqSlots.equip(this._planeEquipInfo!)
    }

    private onClickUnEquip() {
        DataMgr.equip.eqSlots.unequip(this._planeEquipInfo!.guid!)
        UIMgr.hideUI(PlaneEquipInfoUI)
    }

    private onClickLevelUpEquip() {
        //EventMgr.emit(PlaneUIEvent.LevelUpEquip)
    }
    private onClickMultiLevelUpEquip() {
        //EventMgr.emit(PlaneUIEvent.MultiLevelUpEquip)
    }

    async onHide(...args: any[]): Promise<void> { }
    async onClose(...args: any[]): Promise<void> {
        EventMgr.targetOff(this)
    }
    protected update(dt: number): void {
    }

}

