'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const path_1 = __importDefault(require("path"));
exports.template = `
<ui-prop type="dump" class="progress"></ui-prop>
<ui-prop type="dump" class="levelPrefab"></ui-prop>
<ui-prop type="dump" class="save"></ui-prop>
<ui-prop type="dump" class="play"></ui-prop>
<ui-prop type="dump" class="levelPrefabUUID"></ui-prop>
<ui-prop >
    <ui-button class="btn-save">保存(ctrl+t)</ui-button>
    <ui-button class="btn-new">新建关卡</ui-button>
    <ui-button class="btn-play">播放</ui-button>
    <ui-button class="btn-begin">起始(ctrl+←)</ui-button>
    <ui-button class="btn-end">末端(ctrl+→)</ui-button>
</ui-prop>
`;
exports.$ = {
    progress: '.progress',
    levelPrefab: '.levelPrefab',
    save: '.save',
    levelPrefabUUID: '.levelPrefabUUID',
    btnSave: '.btn-save',
    btnNew: '.btn-new',
    btnPlay: '.btn-play',
    play: '.play',
    btnBegin: '.btn-begin',
    btnEnd: '.btn-end',
};
function update(dump) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump;
    this.$.progress.render(dump.value.progress);
    this.$.levelPrefab.render(dump.value.levelPrefab); // 这个是 levelPrefab 的 uuid，需要转成 JsonAsset 才能用
    this.$.save.dump = dump.value.save;
    this.$.play.dump = dump.value.play;
    this.$.levelPrefabUUID.dump = dump.value.levelPrefabUUID;
    // this.$.save.render(dump.value.save);
}
function ready() {
    this.$.btnSave.addEventListener('confirm', () => {
        console.log("panel save level");
        // @ts-ignore
        this.dump.value.save.value = true;
        this.$.save.dispatch('change-dump');
    });
    this.$.btnNew.addEventListener('confirm', async () => {
        // @ts-ignore
        console.log("panel new level:", Editor.Project.path);
        // Editor.Panel.open('level-editor.newlevel')
        // @ts-ignore
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "game", "level");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Level', extensions: ['json'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        console.log("panel new level name:", name);
        const filePath = `db://assets/resources/game/level/${name}`;
        // @ts-ignore
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', filePath, "{}");
        console.log("panel new level create asset rsp:", createRsp);
        // @ts-ignore
        this.dump.value.levelPrefabUUID.value = createRsp === null || createRsp === void 0 ? void 0 : createRsp.uuid;
        this.$.levelPrefabUUID.dispatch('change-dump');
        // Editor.Message.send('level-editor', 'new-level', name)
    });
    this.$.btnPlay.addEventListener('confirm', () => {
        console.log("panel play level");
        // @ts-ignore
        this.dump.value.play.value = true;
        this.$.play.dispatch('change-dump');
    });
    this.$.btnBegin.addEventListener('confirm', () => {
        console.log("panel levelStrart");
        // @ts-ignore
        this.dump.value.progress.value = 0;
        this.$.progress.dispatch('change-dump');
    });
    this.$.btnEnd.addEventListener('confirm', () => {
        console.log("panel levelEnd");
        // @ts-ignore
        this.dump.value.progress.value = 1;
        this.$.progress.dispatch('change-dump');
    });
}
//# sourceMappingURL=data:application/json;base64,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