System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, eEmitterCondition, eBulletCondition, eBulletAction, eEmitterAction, eConditionOp, eCompareOp, emitter_cond, bullet_cond, emitter_act, bullet_act, BulletSystem, EventGroupContext, ConditionChain, EventGroup, Comparer, ConditionFactory, ActionFactory, _crd, eEventGroupStatus;

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterCondition(extras) {
    _reporterNs.report("eEmitterCondition", "../data/bullet/EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletCondition(extras) {
    _reporterNs.report("eBulletCondition", "../data/bullet/EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletAction(extras) {
    _reporterNs.report("eBulletAction", "../data/bullet/EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterAction(extras) {
    _reporterNs.report("eEmitterAction", "../data/bullet/EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "./conditions/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "./actions/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionOp(extras) {
    _reporterNs.report("eConditionOp", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionData(extras) {
    _reporterNs.report("EventActionData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventConditionData(extras) {
    _reporterNs.report("EventConditionData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/scripts/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  _export({
    EventGroupContext: void 0,
    EventGroup: void 0,
    Comparer: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      eEmitterCondition = _unresolved_2.eEmitterCondition;
      eBulletCondition = _unresolved_2.eBulletCondition;
    }, function (_unresolved_3) {
      eBulletAction = _unresolved_3.eBulletAction;
      eEmitterAction = _unresolved_3.eEmitterAction;
    }, function (_unresolved_4) {
      eConditionOp = _unresolved_4.eConditionOp;
      eCompareOp = _unresolved_4.eCompareOp;
    }, function (_unresolved_5) {
      emitter_cond = _unresolved_5;
    }, function (_unresolved_6) {
      bullet_cond = _unresolved_6;
    }, function (_unresolved_7) {
      emitter_act = _unresolved_7;
    }, function (_unresolved_8) {
      bullet_act = _unresolved_8;
    }, function (_unresolved_9) {
      BulletSystem = _unresolved_9.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "269cc0mLCdIDJz1bc2aNgzv", "EventGroup", undefined);

      // context for running condition & action
      _export("EventGroupContext", EventGroupContext = class EventGroupContext {
        constructor() {
          this.emitter = null;
          this.bullet = null;
          this.playerPlane = null;
        }

        // TODO: add level 
        reset() {
          this.emitter = null;
          this.bullet = null;
        }

      }); // Condition chain with operators


      ConditionChain = class ConditionChain {
        constructor() {
          this.conditions = [];
        }

        evaluate(context) {
          if (this.conditions.length === 0) return true;
          let result = this.conditions[0].evaluate(context);

          for (let i = 1; i < this.conditions.length; i++) {
            const condition = this.conditions[i];
            const conditionResult = condition.evaluate(context);

            if (condition.data.op === (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
              error: Error()
            }), eConditionOp) : eConditionOp).And) {
              result = result && conditionResult;
            } else if (condition.data.op === (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
              error: Error()
            }), eConditionOp) : eConditionOp).Or) {
              result = result || conditionResult;
            }
          }

          return result;
        }

      }; // Updated EventGroup

      _export("eEventGroupStatus", eEventGroupStatus = /*#__PURE__*/function (eEventGroupStatus) {
        eEventGroupStatus[eEventGroupStatus["Idle"] = 0] = "Idle";
        eEventGroupStatus[eEventGroupStatus["Waiting"] = 1] = "Waiting";
        eEventGroupStatus[eEventGroupStatus["Active"] = 2] = "Active";
        eEventGroupStatus[eEventGroupStatus["Stopped"] = 3] = "Stopped";
        return eEventGroupStatus;
      }({}));

      _export("EventGroup", EventGroup = class EventGroup {
        get status() {
          return this._status;
        }

        constructor(ctx, data) {
          this.data = void 0;
          this.context = void 0;
          this.conditionChain = void 0;
          this.actions = void 0;
          this._isStarted = false;
          this._triggerCount = 0;
          this._status = eEventGroupStatus.Idle;
          this.context = ctx;
          this.data = data;
          this.conditionChain = this.buildConditionChain(data.conditions);
          this.actions = data.actions.map(actionData => {
            let action = ActionFactory.create(actionData);
            return action;
          });
          this._triggerCount = 0;
          this.changeStatus(eEventGroupStatus.Idle);
        }

        tryStart() {
          if (this._isStarted || this._triggerCount >= this.data.triggerCount) return false;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateEventGroup(this);
          this.changeStatus(eEventGroupStatus.Waiting);
          this._isStarted = true;
          return true;
        }

        tryStop() {
          if (!this._isStarted) return false;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onDestroyEventGroup(this);
          this.changeStatus(eEventGroupStatus.Stopped);
          this._isStarted = false;
          return true;
        }

        reset() {
          this._triggerCount = 0;
          this.changeStatus(eEventGroupStatus.Idle);
        }

        canExecute() {
          return this.conditionChain.evaluate(this.context);
        }

        tick(dt) {
          switch (this._status) {
            case eEventGroupStatus.Idle:
              // not active
              break;

            case eEventGroupStatus.Waiting:
              // waiting for conditions to be met
              if (this.canExecute()) {
                // TODO: 考虑这里检测增加时间间隔来减少消耗
                this.changeStatus(eEventGroupStatus.Active);
              }

              break;

            case eEventGroupStatus.Active:
              // conditions are met, now ticking actions
              this.tickActive(dt);
              break;

            case eEventGroupStatus.Stopped:
              // stopped
              break;
          }
        }

        changeStatus(newStatus) {
          if (this._status === newStatus) return;
          this._status = newStatus;

          switch (this._status) {
            case eEventGroupStatus.Waiting:
              // reset actions by onLoad
              break;

            case eEventGroupStatus.Active:
              // 启用时，重置action的初始参数
              this.actions.forEach(action => action.onLoad(this.context));
              break;

            case eEventGroupStatus.Stopped:
              break;

            default:
              break;
          }
        }

        buildConditionChain(conditions) {
          const chain = new ConditionChain();
          conditions.forEach((condData, index) => {
            const condition = ConditionFactory.create(condData);

            if (condition) {
              condition.onLoad(this.context);
              chain.conditions.push(condition);
            }
          });
          return chain;
        }

        tickActive(dt) {
          let isAllFinished = true;

          for (const action of this.actions) {
            if (action.isCompleted()) continue;
            action.onExecute(this.context, dt);
            isAllFinished = false;
          }

          if (isAllFinished) {
            this._triggerCount++;

            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {
              // restart
              this.changeStatus(eEventGroupStatus.Waiting);
            } else {
              this.tryStop();
            }
          }
        }

      }); // 提供一个静态函数帮助比较value


      _export("Comparer", Comparer = class Comparer {
        static compare(a, b, op) {
          switch (op) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return a === b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return a !== b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Greater:
              return a > b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).GreaterEqual:
              return a >= b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Less:
              return a < b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).LessEqual:
              return a <= b;

            default:
              throw new Error(`Unknown compare operator: ${op}`);
          }
        }

      }); // Factory pattern for conditions & actions


      ConditionFactory = class ConditionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Active:
              return new emitter_cond.EmitterCondition_Active(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_InitialDelay:
              return new emitter_cond.EmitterCondition_InitialDelay(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Prewarm:
              return new emitter_cond.EmitterCondition_Prewarm(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_PrewarmDuration:
              return new emitter_cond.EmitterCondition_PrewarmDuration(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Duration:
              return new emitter_cond.EmitterCondition_Duration(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_ElapsedTime:
              return new emitter_cond.EmitterCondition_ElapsedTime(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Loop:
              return new emitter_cond.EmitterCondition_Loop(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_LoopInterval:
              return new emitter_cond.EmitterCondition_LoopInterval(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_EmitInterval:
              return new emitter_cond.EmitterCondition_EmitInterval(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_PerEmitCount:
              return new emitter_cond.EmitterCondition_PerEmitCount(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_PerEmitInterval:
              return new emitter_cond.EmitterCondition_PerEmitInterval(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_PerEmitOffsetX:
              return new emitter_cond.EmitterCondition_PerEmitOffsetX(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Angle:
              return new emitter_cond.EmitterCondition_Angle(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Count:
              return new emitter_cond.EmitterCondition_Count(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_Duration:
              return new emitter_cond.EmitterCondition_BulletDuration(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_Speed:
              return new emitter_cond.EmitterCondition_BulletSpeed(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_Acceleration:
              return new emitter_cond.EmitterCondition_BulletAcceleration(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_AccelerationAngle:
              return new emitter_cond.EmitterCondition_BulletAccelerationAngle(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_FacingMoveDir:
              return new emitter_cond.EmitterCondition_BulletFacingMoveDir(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_TrackingTarget:
              return new emitter_cond.EmitterCondition_BulletTrackingTarget(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_Destructive:
              return new emitter_cond.EmitterCondition_BulletDestructive(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_DestructiveOnHit:
              return new emitter_cond.EmitterCondition_BulletDestructiveOnHit(data);
            // case eEmitterCondition.Bullet_Sprite:
            //     return new emitter_cond.EmitterCondition_BulletSprite(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_Scale:
              return new emitter_cond.EmitterCondition_BulletScale(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_ColorR:
              return new emitter_cond.EmitterCondition_BulletColorR(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_ColorG:
              return new emitter_cond.EmitterCondition_BulletColorG(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_ColorB:
              return new emitter_cond.EmitterCondition_BulletColorB(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Bullet_DefaultFacing:
              return new emitter_cond.EmitterCondition_BulletDefaultFacing(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Player_ActLevel:
              return new emitter_cond.EmitterCondition_PlayerActLevel(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Player_PosX:
              return new emitter_cond.EmitterCondition_PlayerPosX(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Player_PosY:
              return new emitter_cond.EmitterCondition_PlayerPosY(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Player_LifePercent:
              return new emitter_cond.EmitterCondition_PlayerLifePercent(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Player_GainBuff:
              return new emitter_cond.EmitterCondition_PlayerGainBuff(data);
            // ... bullet cases

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_Duration:
              return new bullet_cond.BulletCondition_Duration(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_ElapsedTime:
              return new bullet_cond.BulletCondition_ElapsedTime(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_PosX:
              return new bullet_cond.BulletCondition_PosX(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_PosY:
              return new bullet_cond.BulletCondition_PosY(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_Speed:
              return new bullet_cond.BulletCondition_Speed(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_SpeedAngle:
              return new bullet_cond.BulletCondition_SpeedAngle(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_Acceleration:
              return new bullet_cond.BulletCondition_Acceleration(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_AccelerationAngle:
              return new bullet_cond.BulletCondition_AccelerationAngle(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_Scale:
              return new bullet_cond.BulletCondition_Scale(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_ColorR:
              return new bullet_cond.BulletCondition_ColorR(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_ColorG:
              return new bullet_cond.BulletCondition_ColorG(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_ColorB:
              return new bullet_cond.BulletCondition_ColorB(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_FacingMoveDir:
              return new bullet_cond.BulletCondition_FacingMoveDir(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_Destructive:
              return new bullet_cond.BulletCondition_Destructive(data);

            case (_crd && eBulletCondition === void 0 ? (_reportPossibleCrUseOfeBulletCondition({
              error: Error()
            }), eBulletCondition) : eBulletCondition).Bullet_DestructiveOnHit:
              return new bullet_cond.BulletCondition_DestructiveOnHit(data);

            default:
              throw new Error(`Unknown condition type: ${data.type}`);
          }
        }

      };
      ActionFactory = class ActionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Active:
              return new emitter_act.EmitterAction_Active(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_InitialDelay:
              return new emitter_act.EmitterAction_InitialDelay(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Prewarm:
              return new emitter_act.EmitterAction_Prewarm(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_PrewarmDuration:
              return new emitter_act.EmitterAction_PrewarmDuration(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Duration:
              return new emitter_act.EmitterAction_Duration(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_ElapsedTime:
              return new emitter_act.EmitterAction_ElapsedTime(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Loop:
              return new emitter_act.EmitterAction_Loop(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_LoopInterval:
              return new emitter_act.EmitterAction_LoopInterval(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_EmitInterval:
              return new emitter_act.EmitterAction_EmitInterval(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_PerEmitCount:
              return new emitter_act.EmitterAction_PerEmitCount(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_PerEmitInterval:
              return new emitter_act.EmitterAction_PerEmitInterval(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_PerEmitOffsetX:
              return new emitter_act.EmitterAction_PerEmitOffsetX(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Angle:
              return new emitter_act.EmitterAction_Angle(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Emitter_Count:
              return new emitter_act.EmitterAction_Count(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Duration:
              return new emitter_act.EmitterAction_BulletDuration(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Damage:
              return new emitter_act.EmitterAction_BulletDamage(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Speed:
              return new emitter_act.EmitterAction_BulletSpeed(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_SpeedAngle:
              return new emitter_act.EmitterAction_BulletSpeedAngle(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Acceleration:
              return new emitter_act.EmitterAction_BulletAcceleration(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_AccelerationAngle:
              return new emitter_act.EmitterAction_BulletAccelerationAngle(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Scale:
              return new emitter_act.EmitterAction_BulletScale(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_ColorR:
              return new emitter_act.EmitterAction_BulletColorR(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_ColorG:
              return new emitter_act.EmitterAction_BulletColorG(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_ColorB:
              return new emitter_act.EmitterAction_BulletColorB(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_FacingMoveDir:
              return new emitter_act.EmitterAction_BulletFacingMoveDir(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_TrackingTarget:
              return new emitter_act.EmitterAction_BulletTrackingTarget(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_Destructive:
              return new emitter_act.EmitterAction_BulletDestructive(data);

            case (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
              error: Error()
            }), eEmitterAction) : eEmitterAction).Bullet_DestructiveOnHit:
              return new emitter_act.EmitterAction_BulletDestructiveOnHit(data);
            // ... bullet cases

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_Duration:
              return new bullet_act.BulletAction_Duration(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_ElapsedTime:
              return new bullet_act.BulletAction_ElapsedTime(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_PosX:
              return new bullet_act.BulletAction_PosX(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_PosY:
              return new bullet_act.BulletAction_PosY(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_Speed:
              return new bullet_act.BulletAction_Speed(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_SpeedAngle:
              return new bullet_act.BulletAction_SpeedAngle(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_Acceleration:
              return new bullet_act.BulletAction_Acceleration(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_AccelerationAngle:
              return new bullet_act.BulletAction_AccelerationAngle(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_Scale:
              return new bullet_act.BulletAction_Scale(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_ColorR:
              return new bullet_act.BulletAction_ColorR(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_ColorG:
              return new bullet_act.BulletAction_ColorG(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_ColorB:
              return new bullet_act.BulletAction_ColorB(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_FacingMoveDir:
              return new bullet_act.BulletAction_FacingMoveDir(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_Destructive:
              return new bullet_act.BulletAction_Destructive(data);

            case (_crd && eBulletAction === void 0 ? (_reportPossibleCrUseOfeBulletAction({
              error: Error()
            }), eBulletAction) : eBulletAction).Bullet_DestructiveOnHit:
              return new bullet_act.BulletAction_DestructiveOnHit(data);

            default:
              throw new Error(`Unknown action type: ${data.type}`);
          }
        }

      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=158afc7440e63c7f7618c0058dd23db5fc547f1f.js.map