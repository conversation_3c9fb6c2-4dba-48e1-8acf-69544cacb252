{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "Component", "instantiate", "Node", "view", "LevelDataEventTriggerType", "MyApp", "Wave", "GameIns", "ccclass", "TerrainsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelLayerUI", "backgrounds", "_offSetY", "_bTrackBackground", "terrainsNode", "dynamicNode", "waves", "events", "enableEvents", "TrackBackground", "value", "onLoad", "initByLevelData", "data", "offSetY", "node", "setPosition", "_getOrAddNode", "console", "log", "terrains", "for<PERSON>ach", "terrain", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "uuid", "load", "err", "prefab", "error", "terrainNode", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "tick", "deltaTime", "speed", "posY", "getPosition", "topPosY", "getVisibleSize", "height", "prePosY", "wave", "shift", "waveUUID", "waveComp", "getComponent", "waveManager", "addWaveByLevel", "length", "event", "push", "i", "condResult", "cond", "conditions", "splice", "trigger", "triggers", "_type", "Log", "message", "Audio", "waveTriger", "Math", "max", "node_parent", "name", "getChildByName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA0BC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAoBC,MAAAA,I,OAAAA,I;;AAItEC,MAAAA,yB,iBAAAA,yB;;AAGAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcT,U;AAEdU,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;8BAGTC,Y,WADZL,OAAO,CAAC,cAAD,C,gBAAR,MACaK,YADb,SACkCb,SADlC,CAC4C;AAAA;AAAA;AAAA,eACjCc,WADiC,GACT,EADS;AAAA,eAEhCC,QAFgC,GAEb,CAFa;AAEV;AAFU,eAGhCC,iBAHgC,GAGH,IAHG;AAGG;AAHH,eAKhCC,YALgC,GAKN,IALM;AAAA,eAMhCC,WANgC,GAMP,IANO;AAAA,eAOhCC,KAPgC,GAOP,EAPO;AAAA,eAQhCC,MARgC,GAQL,EARK;AAAA,eAShCC,YATgC,GASC,EATD;AAAA;;AAWd,YAAfC,eAAe,GAAY;AAClC,iBAAO,KAAKN,iBAAZ;AACH;;AACyB,YAAfM,eAAe,CAACC,KAAD,EAAiB;AACvC,eAAKP,iBAAL,GAAyBO,KAAzB;AACH;;AAEDC,QAAAA,MAAM,GAAS,CAEd;;AAEMC,QAAAA,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAA6C;AAAA;;AAC/D,eAAKZ,QAAL,GAAgBY,OAAhB;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBF,OAAzB,EAAkC,CAAlC;AAEA,eAAKV,YAAL,GAAoB,KAAKa,aAAL,CAAmB,KAAKF,IAAxB,EAA8BnB,gBAA9B,CAApB;AACA,eAAKS,WAAL,GAAmB,KAAKY,aAAL,CAAmB,KAAKF,IAAxB,EAA8BlB,eAA9B,CAAnB;AAEAqB,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA2B,kBAA3B;AACA,eAAKlB,WAAL,GAAmB,EAAnB;AAEA,4BAAAY,IAAI,CAACO,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChC,gBAAMC,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,gCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAsC;AAC1D,kBAAID,GAAJ,EAAS;AACLX,gBAAAA,OAAO,CAACa,KAAR,CAAc,cAAd,EAA6B,0CAA7B,EAAyEF,GAAzE;AACA;AACH;;AACD,kBAAIG,WAAW,GAAG5C,WAAW,CAAC0C,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAAChB,WAAZ,CAAwBM,OAAO,CAACW,QAAR,CAAiBC,CAAzC,EAA4CZ,OAAO,CAACW,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAH,cAAAA,WAAW,CAACI,QAAZ,CAAqBd,OAAO,CAACe,KAAR,CAAcH,CAAnC,EAAsCZ,OAAO,CAACe,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAH,cAAAA,WAAW,CAACM,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuChB,OAAO,CAACiB,QAA/C;AACA,mBAAKnC,YAAL,CAAmBoC,QAAnB,CAA4BR,WAA5B;AACH,aAVD;AAWH,WAbD;AAcA,eAAK1B,KAAL,GAAa,CAAC,GAAGO,IAAI,CAACP,KAAT,CAAb;AACA,eAAKA,KAAL,CAAWmC,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACT,QAAF,CAAWE,CAAX,GAAeQ,CAAC,CAACV,QAAF,CAAWE,CAApD;AACA,eAAK5B,MAAL,GAAc,CAAC,GAAGM,IAAI,CAACN,MAAT,CAAd;AACA,eAAKA,MAAL,CAAYkC,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACT,QAAF,CAAWE,CAAX,GAAeQ,CAAC,CAACV,QAAF,CAAWE,CAArD;AACH;;AAEMS,QAAAA,IAAI,CAACC,SAAD,EAAoBC,KAApB,EAAuC;AAAA;;AAC9C,cAAI,KAAKrC,eAAL,KAAyB,IAA7B,EAAmC;AAC/B,gBAAMsC,IAAI,GAAG,KAAKhC,IAAL,CAAUiC,WAAV,GAAwBb,CAArC;AACA,gBAAMc,OAAO,GAAG3D,IAAI,CAAC4D,cAAL,GAAsBC,MAAtB,GAA+B,CAA/C;;AACA,gBAAIJ,IAAI,GAAGE,OAAX,EAAoB;AAChB,mBAAK9C,iBAAL,GAAyB,KAAzB;AACH;AACJ;;AACD,cAAMiD,OAAO,GAAG,KAAKrC,IAAL,CAAUiC,WAAV,GAAwBb,CAAxC;AACA,eAAKpB,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBoC,OAAO,GAAGP,SAAS,GAAGC,KAA/C,EAAsD,CAAtD;;AAT8C,uCAUuC;AACjF,gBAAMO,IAAI,GAAG,KAAI,CAAC/C,KAAL,CAAW,CAAX,CAAb;;AACA,YAAA,KAAI,CAACA,KAAL,CAAWgD,KAAX;;AACA,gBAAM/B,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0D2B,IAAI,CAACE,QAA/D,CAAb;AACA;AAAA;AAAA,gCAAM/B,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAsC;AAC1D,kBAAID,GAAJ,EAAS;AACLX,gBAAAA,OAAO,CAACa,KAAR,CAAc,cAAd,EAA6B,4BAA7B,EAA2DF,GAA3D;AACA;AACH;;AACD,kBAAM2B,QAAQ,GAAGpE,WAAW,CAAC0C,MAAD,CAAX,CAAoB2B,YAApB;AAAA;AAAA,+BAAjB;AACA;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA8CH,IAAI,CAACpB,QAAL,CAAcC,CAA5D,EAA+DmB,IAAI,CAACpB,QAAL,CAAcE,CAAd,GAAgB,KAAI,CAACpB,IAAL,CAAUkB,QAAV,CAAmBE,CAAlG;AACH,aAPD;AAQH,WAtB6C;;AAU9C,iBAAM,KAAK7B,KAAL,CAAWsD,MAAX,GAAoB,CAApB,IAAyB,KAAKtD,KAAL,CAAW,CAAX,EAAc2B,QAAd,CAAuBE,CAAvB,GAA2B,KAAKpB,IAAL,CAAUiC,WAAV,GAAwBb,CAAlF;AAAA;AAAA;;AAaA,iBAAM,KAAK5B,MAAL,CAAYqD,MAAZ,GAAqB,CAArB,IAA0B,KAAKrD,MAAL,CAAY,CAAZ,EAAe0B,QAAf,CAAwBE,CAAxB,GAA4B,KAAKpB,IAAL,CAAUiC,WAAV,GAAwBb,CAApF,EAAuF;AACnF,gBAAM0B,KAAK,GAAG,KAAKtD,MAAL,CAAY,CAAZ,CAAd;AACA,iBAAKA,MAAL,CAAY+C,KAAZ;AACA,iBAAK9C,YAAL,CAAkBsD,IAAlB,CAAuBD,KAAvB;AACH;;AA3B6C,yCA4BQ;AAClD,gBAAMA,KAAK,GAAG,KAAI,CAACrD,YAAL,CAAkBuD,CAAlB,CAAd;AACA,gBAAIC,UAAU,GAAG,IAAjB;;AACA,iBAAK,IAAIC,IAAT,IAAiBJ,KAAK,CAACK,UAAvB,EAAmC,CAClC;;AACD,gBAAIF,UAAJ,EAAgB;AACZ,cAAA,KAAI,CAACxD,YAAL,CAAkB2D,MAAlB,CAAyBJ,CAAzB,EAA4B,CAA5B;;AACA,mBAAK,IAAIK,OAAT,IAAoBP,KAAK,CAACQ,QAA1B,EAAoC;AAChC,wBAAOD,OAAO,CAACE,KAAf;AACI,uBAAK;AAAA;AAAA,8EAA0BC,GAA/B;AACIrD,oBAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,aAA5B,EAA4CiD,OAAD,CAAsCI,OAAjF;AACA;;AACJ,uBAAK;AAAA;AAAA,8EAA0BC,KAA/B;AACI;;AACJ,uBAAK;AAAA;AAAA,8EAA0BhF,IAA/B;AACI,wBAAMiF,UAAU,GAAGN,OAAnB;AACA,wBAAM7C,IAAI,GAAG;AAAA;AAAA,wCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,wCAAMD,MAAN,CAAaE,iBAAvC,EAA0DgD,UAAU,CAACnB,QAArE,CAAb;AACA;AAAA;AAAA,wCAAM/B,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAsC;AAC1D,0BAAID,GAAJ,EAAS;AACLX,wBAAAA,OAAO,CAACa,KAAR,CAAc,cAAd,EAA6B,4BAA7B,EAA2DF,GAA3D;AACA;AACH;;AACD,0BAAM2B,QAAQ,GAAGpE,WAAW,CAAC0C,MAAD,CAAX,CAAoB2B,YAApB;AAAA;AAAA,uCAAjB;AACA;AAAA;AAAA,8CAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA6CK,KAAK,CAAC5B,QAAN,CAAeC,CAA5D,EAA+DyC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYf,KAAK,CAAC5B,QAAN,CAAeE,CAAf,GAAmB,KAAI,CAACpB,IAAL,CAAUkB,QAAV,CAAmBE,CAAlD,CAA/D;AACH,qBAPD;AAQA;AAjBR;AAmBH;AACJ;AACJ,WAzD6C;;AA4B9C,eAAK,IAAI4B,CAAC,GAAG,KAAKvD,YAAL,CAAkBoD,MAAlB,GAAyB,CAAtC,EAAyCG,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD;AAAA;AAAA;AA8BH;;AAEO9C,QAAAA,aAAa,CAAC4D,WAAD,EAAoBC,IAApB,EAAwC;AACzD,cAAI/D,IAAI,GAAG8D,WAAW,CAACE,cAAZ,CAA2BD,IAA3B,CAAX;;AACA,cAAI/D,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAI1B,IAAJ,CAASyF,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACrC,QAAZ,CAAqBzB,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAvHuC,O", "sourcesContent": ["import { _decorator, assetManager, Component, instantiate, Node, Prefab, Vec2, view } from \"cc\";\r\nimport { LevelDataEvent, LevelDataLayer, LevelDataWave } from \"../../../leveldata/leveldata\";\r\nimport { LevelWaveUI } from \"./LevelWaveUI\";\r\nimport { LevelEventUI } from \"./LevelEventUI\";\r\nimport { LevelDataEventTriggerType } from \"../../../leveldata/trigger/LevelDataEventTrigger\";\r\nimport { LevelDataEventTriggerWave } from \"../../../leveldata/trigger/LevelDataEventTriggerWave\";\r\nimport { LevelDataEventTriggerLog } from \"../../../leveldata/trigger/LevelDataEventTriggerLog\";\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\nimport { Wave } from \"../../wave/Wave\";\r\nimport { GameIns } from \"../../GameIns\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends Component {\r\n    public backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）\r\n\r\n    private terrainsNode: Node|null = null;\r\n    private dynamicNode: Node|null = null;\r\n    private waves: LevelDataWave[] = [];\r\n    private events: LevelDataEvent[] = [];\r\n    private enableEvents: LevelDataEvent[] = [];\r\n\r\n    public get TrackBackground(): boolean {\r\n        return this._bTrackBackground;\r\n    }\r\n    public set TrackBackground(value: boolean) {\r\n        this._bTrackBackground = value;\r\n    }\r\n\r\n    onLoad(): void {\r\n        \r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer, offSetY: number):void {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n        \r\n        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);\r\n        \r\n        console.log('LevelLayerUI',\" initByLevelData\");\r\n        this.backgrounds = [];\r\n\r\n        data.terrains?.forEach((terrain) => {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI',\" initByLevelData load terrain prefab err\", err);\r\n                    return;\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);  \r\n            });\r\n        });\r\n        this.waves = [...data.waves]\r\n        this.waves.sort((a, b) => a.position.y - b.position.y);\r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n    }\r\n\r\n    public tick(deltaTime: number, speed:number):void {\r\n        if (this.TrackBackground === true) {\r\n            const posY = this.node.getPosition().y;\r\n            const topPosY = view.getVisibleSize().height / 2;\r\n            if (posY < topPosY) {\r\n                this._bTrackBackground = false;\r\n            }\r\n        }\r\n        const prePosY = this.node.getPosition().y;\r\n        this.node.setPosition(0, prePosY - deltaTime * speed, 0);\r\n        while(this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {\r\n            const wave = this.waves[0];\r\n            this.waves.shift();\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI',\" tick load wave prefab err\", err);\r\n                    return;\r\n                } \r\n                const waveComp = instantiate(prefab).getComponent(Wave)\r\n                GameIns.waveManager.addWaveByLevel(waveComp!, wave.position.x, wave.position.y-this.node.position.y);\r\n            });\r\n        }\r\n        while(this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {\r\n            const event = this.events[0];\r\n            this.events.shift();\r\n            this.enableEvents.push(event);\r\n        }\r\n        for (let i = this.enableEvents.length-1; i >= 0; i--) {\r\n            const event = this.enableEvents[i];\r\n            let condResult = true\r\n            for (let cond of event.conditions) {\r\n            }\r\n            if (condResult) {\r\n                this.enableEvents.splice(i, 1);\r\n                for (let trigger of event.triggers) {\r\n                    switch(trigger._type) {\r\n                        case LevelDataEventTriggerType.Log:\r\n                            console.log(\"LevelLayerUI\", \"trigger log\", (trigger as LevelDataEventTriggerLog).message);\r\n                            break;\r\n                        case LevelDataEventTriggerType.Audio:\r\n                            break;\r\n                        case LevelDataEventTriggerType.Wave:\r\n                            const waveTriger = trigger as LevelDataEventTriggerWave;\r\n                            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveTriger.waveUUID)\r\n                            MyApp.resMgr.load(path, (err: Error | null, prefab:Prefab) => {\r\n                                if (err) {\r\n                                    console.error('LevelLayerUI',\" tick load wave prefab err\", err);\r\n                                    return;\r\n                                } \r\n                                const waveComp = instantiate(prefab).getComponent(Wave)!\r\n                                GameIns.waveManager.addWaveByLevel(waveComp, event.position.x, Math.max(0, event.position.y - this.node.position.y));\r\n                            });\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n}"]}