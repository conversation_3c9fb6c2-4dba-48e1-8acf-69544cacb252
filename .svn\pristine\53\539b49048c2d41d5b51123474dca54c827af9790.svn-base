'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
const path_1 = __importDefault(require("path"));
exports.template = `
<div class="component-container"></div>

<ui-prop>
    <ui-label slot="label">请从下拉框中选择:</ui-label>
    <ui-select slot="content" class="prefab-dropdown"></ui-select>
</ui-prop>
<ui-prop>
    <ui-button class="btn-preview" style="display: none;">添加到场景</ui-button>
    <ui-button class="btn-new">新建emitter</ui-button>
    <ui-button class="btn-copy" style="display: none;">从选择复制新emitter</ui-button>
    <ui-button class="btn-reset" style="display: none;">重置预览</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    prefabDropdown: '.prefab-dropdown',
    btnPreview: '.btn-preview',
    btnNew: '.btn-new',
    btnCopy: '.btn-copy',
    btnReset: '.btn-reset',
};
const emitterDir = 'db://assets/resources/game/prefabs/emitter/';
let prefabList = [];
let selectedPrefab = null;
async function loadPrefabList() {
    const pattern = `${emitterDir}**/*.prefab`;
    try {
        // @ts-ignore
        const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
        const prefabs = arr
            .filter((a) => a && !a.isDirectory && a.name.endsWith('.prefab'))
            .map((a) => ({
            name: String(a.name || '').replace(/\.prefab$/i, ''),
            path: a.path || '',
            uuid: a.uuid || ''
        }))
            .filter(p => p.name)
            .sort((a, b) => a.name.localeCompare(b.name));
        return prefabs;
    }
    catch (e) {
        console.warn('loadPrefabList failed', e);
        return [];
    }
}
function updateButtonVisibility() {
    var _a, _b;
    const hasSelection = selectedPrefab !== null;
    this.$.btnPreview.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnCopy.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnReset.style.display = ((_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.uuid) ? 'inline-block' : 'none';
}
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    // Load prefab list
    prefabList = await loadPrefabList();
    // Setup dropdown options
    const dropdown = this.$.prefabDropdown;
    dropdown.innerHTML = '';
    if (prefabList.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '(无可用的prefab文件)';
        option.disabled = true;
        dropdown.appendChild(option);
    }
    else {
        // Add prefab options
        prefabList.forEach(prefab => {
            const option = document.createElement('option');
            option.value = prefab.uuid;
            option.textContent = prefab.name;
            dropdown.appendChild(option);
        });
    }
    // Handle dropdown selection change
    dropdown.addEventListener('change', () => {
        const selectedUuid = dropdown.value;
        selectedPrefab = prefabList.find(p => p.uuid === selectedUuid) || null;
        updateButtonVisibility.call(this);
    });
    // Handle preview button
    this.$.btnPreview.addEventListener('click', () => {
        var _a;
        if (selectedPrefab) {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'instantiatePrefab',
                args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
            });
        }
    });
    // Handle new emitter button
    this.$.btnNew.addEventListener('click', async () => {
        console.log('Create new emitter');
        // @ts-ignore
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "game", "prefabs", "emitter");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Prefab', extensions: ['prefab'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        const nameWithoutExt = name.replace(/\.prefab$/i, '');
        console.log('New emitter name:', name);
        const filePath = `${emitterDir}${name}`;
        try {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'createNewEmitter',
                args: [nameWithoutExt, filePath]
            });
            // Refresh prefab list
            prefabList = await loadPrefabList();
        }
        catch (e) {
            console.error('Failed to create new emitter:', e);
        }
    });
    // Handle copy emitter button
    this.$.btnCopy.addEventListener('click', async () => {
        var _a;
        if (!selectedPrefab)
            return;
        const sourceUrl = selectedPrefab.path;
        const targetUrl = sourceUrl + '_copy';
        const nameWithoutExt = selectedPrefab.name + '_copy';
        console.log('Copy emitter from ', sourceUrl, ' to ', targetUrl);
        try {
            // @ts-ignore
            await Editor.Message.request('asset-db', 'copy-asset', sourceUrl + '.prefab', targetUrl + '.prefab');
            // Refresh prefab list
            prefabList = await loadPrefabList();
            selectedPrefab = prefabList.find(p => p.name === nameWithoutExt) || null;
            if (selectedPrefab) {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'emitter-editor',
                    method: 'instantiatePrefab',
                    args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
                });
            }
        }
        catch (e) {
            console.error('Failed to copy emitter:', e);
        }
    });
    // Handle reset preview button
    this.$.btnReset.addEventListener('click', () => {
        var _a, _b;
        if ((_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.uuid) {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'reset',
                args: [this.dump.value.uuid]
            });
        }
    });
    // Initial button visibility update
    updateButtonVisibility.call(this);
}
//# sourceMappingURL=data:application/json;base64,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