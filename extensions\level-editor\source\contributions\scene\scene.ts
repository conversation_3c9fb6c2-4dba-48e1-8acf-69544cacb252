import { join } from 'path';

// @ts-ignore
module.paths.push(join(Editor.App.path, 'node_modules'));

export function load() {};
export function unload() {};

export const methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.progress = 1;
        }
    }
};