import { _decorator, Component, Label, math, Node, Sprite } from 'cc';

import { DataMgr } from "db://assets/bundles/common/script/data/DataManager";
import { EventMgr } from "db://assets/bundles/common/script/event/EventManager";
import { ResEquip, ResItem } from 'db://assets/scripts/autogen/luban/schema';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { PlaneUIEvent } from "../../../../event/PlaneUIEvent";
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { TabStatus } from "../../PlaneTypes";
const { ccclass, property } = _decorator;

@ccclass('BagItem')
export class BagItem extends Component {
    @property(Node)
    selectedIcon: Node | null = null;
    @property(Node)
    mask: Node | null = null;
    @property(Label)
    itemNum: Label | null = null;

    private _item: csproto.cs.ICSItem | null = null;
    private _tabStatus: TabStatus = TabStatus.None;
    private _fakeColors: string[] = [
        "#A0A0A0",
        "#1EFF00",
        "#0070FF",
        "#A335EE",
        "#FF8000",
        "#80e6e6ff",
        "#E6CC80"
    ]

    protected onLoad(): void {
        this.getComponent(ButtonPlus)!.addClick(this.onClick, this)
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    private onClick() {
        logDebug("PlaneUI", `onClick`)
        if (!this._item) return
        if (this._tabStatus == TabStatus.Merge) {
            if (this.mask!.active && !this.selectedIcon) {
                return
            }
            if (!this.mask!.active && DataMgr.equip.eqCombine.isFull()) {
                return
            }
        }
        EventMgr.emit(PlaneUIEvent.BagItemClick, this._item)
    }

    onBagTabStatusRender(item: csproto.cs.ICSItem) {
        this._tabStatus = TabStatus.Bag;
        this._item = item;
        this.selectedIcon!.active = false;
        this.mask!.active = false;
        this.onMetaDataRender()
    }

    onCombineTabStatusRender(item: csproto.cs.ICSItem) {
        this._tabStatus = TabStatus.Merge;
        this._item = item;
        if (DataMgr.equip.eqCombine.isCanCombine(this._item)) {
            const info = DataMgr.equip.eqCombine.getByGuid(this._item.guid!)
            if (info) {
                this.selectedIcon!.active = true;
                this.mask!.active = true;
            } else {
                this.selectedIcon!.active = false;
                this.mask!.active = false;
            }
        } else {
            this.selectedIcon!.active = false;
            this.mask!.active = true;
        }
        this.onMetaDataRender()
    }

    private onMetaDataRender() {
        const EquipCfg = MyApp.lubanTables.TbResEquip.get(this._item!.item_id ?? 0)
        if (EquipCfg) {
            this.onEquipDataRender(EquipCfg)
        } else {
            const itemCfg = MyApp.lubanTables.TbResItem.get(this._item!.item_id ?? 0)
            if (itemCfg) {
                this.onItemDataRender(itemCfg)
            } else {
                this.getComponentInChildren(Label)!.string = "未知"
            }
        }
    }

    private onEquipDataRender(equipCfg: ResEquip) {
        this.itemNum!.node.active = false;
        this.getComponentInChildren(Label)!.string = equipCfg?.name + `(品质:${equipCfg?.quality})`
        this.node.getComponentInChildren(Sprite)!.color = math.color(this._fakeColors[equipCfg.quality])
    }

    private onItemDataRender(itemCfg: ResItem) {
        this.itemNum!.node.active = true;
        this.itemNum!.string = this._item?.count!.toString() ?? "0"
        this.getComponentInChildren(Label)!.string = itemCfg?.name + `(品质:${itemCfg?.quality})`
        this.node.getComponentInChildren(Sprite)!.color = math.color(this._fakeColors[itemCfg.quality])
    }
}