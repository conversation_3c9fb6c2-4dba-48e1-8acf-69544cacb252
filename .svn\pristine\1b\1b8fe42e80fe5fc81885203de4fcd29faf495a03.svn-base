
import { MyApp } from 'db://assets/scripts/MyApp';
import csproto, { comm } from '../../../../../scripts/autogen/pb/cs_proto.js';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { IData } from "../DataManager";
export class PK implements IData {
    self_info?: (comm.IGamePvpInfoSide | null);
    other_info?: (comm.IGamePvpInfoSide | null);
    get_list?: (comm.IGamePvpHistory[] | null);
    award_info?: (comm.IGameRewardInfo | null);
    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, this.onGamePvpMatch, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_START, this.onGamePvpStart, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_CANCEL, this.onGamePvpCancel, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_END, this.onGamePvpEnd, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGamePvpGetAward, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, this.onGamePvpGetInfo, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, this.onGamePvpGetList, this);
    }
    onGamePvpMatch(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_match) {
            return;
        }
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
        } else {
            EventMgr.emit(DataEvent.GamePvpMatchSuc);
        }
    }

    onGamePvpStart(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_start) {
            return;
        }
        this.self_info = msg.body.game_pvp_start.info?.self_info;
        this.other_info = msg.body.game_pvp_start.info?.other_info;
    }

    onGamePvpCancel(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_cancel) {
            return;
        }
        let reason = msg.body.game_pvp_cancel.reason;
        switch (reason) {
            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_USER_CANCEL:
                break;
            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_TIMEOUT:
                break;
            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_RETRY:
                break;
            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_STOP:
                break;
        }
    }

    onGamePvpEnd(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_end) {
            return;
        }
        let result = msg.body.game_pvp_end.pvp_result;
        if (result) {
            let res = msg.body.game_pvp_end.pvp_result?.result_code;
            switch (res) {
                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_WIN:
                    break;
                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_LOSE:
                    break;
                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_DRAW:
                    break;
                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_CANCEL:
                    break;
            }
        }
    }

    onGamePvpGetAward(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_get_reward) {
            return;
        }
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
        }
        this.award_info = msg.body.game_pvp_get_reward.reward_info;
        EventMgr.emit(DataEvent.GamePvpGetAward)
    }

    onGamePvpGetInfo(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_get_info) {
            return;
        }
        const info = msg.body.game_pvp_get_info.info!
        const status = msg.body.game_pvp_get_info.status!
        if (status == csproto.comm.GAME_PVP_STATUS.GAME_PVP_STATUS_IN_MATCH) {

        }
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
        }
    }

    //登录成功后，拉取PVP列表
    onGamePvpGetList(msg: csproto.cs.IS2CMsg): void {
        if (!msg.body || !msg.body.game_pvp_get_list) {
            return;
        }

        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            return;
        }
        this.get_list = msg.body.game_pvp_get_list.list!
        EventMgr.emit(DataEvent.GamePvpGetList)
    }

    public update(): void {
    }
}
