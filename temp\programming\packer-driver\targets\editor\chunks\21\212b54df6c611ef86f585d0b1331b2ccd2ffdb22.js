System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, eEasing, eEmitterCondition, eEmitterConditionCn, eBulletConditionCn, eEmitterAction, eEmitterActionCn, eBulletActionCn, ExpressionValue, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _dec19, _dec20, _dec21, _dec22, _dec23, _class7, _class8, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _crd, ccclass, property, eConditionOp, eCompareOp, eTargetValueType, EventConditionData, EventActionData, EventGroupData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../../bullet/Easing", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventConditionType(extras) {
    _reporterNs.report("eEventConditionType", "./EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterCondition(extras) {
    _reporterNs.report("eEmitterCondition", "./EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterConditionCn(extras) {
    _reporterNs.report("eEmitterConditionCn", "./EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletCondition(extras) {
    _reporterNs.report("eBulletCondition", "./EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletConditionCn(extras) {
    _reporterNs.report("eBulletConditionCn", "./EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventActionType(extras) {
    _reporterNs.report("eEventActionType", "./EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterAction(extras) {
    _reporterNs.report("eEmitterAction", "./EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterActionCn(extras) {
    _reporterNs.report("eEmitterActionCn", "./EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletAction(extras) {
    _reporterNs.report("eBulletAction", "./EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletActionCn(extras) {
    _reporterNs.report("eBulletActionCn", "./EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "./ExpressionValue", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      eEasing = _unresolved_2.eEasing;
    }, function (_unresolved_3) {
      eEmitterCondition = _unresolved_3.eEmitterCondition;
      eEmitterConditionCn = _unresolved_3.eEmitterConditionCn;
      eBulletConditionCn = _unresolved_3.eBulletConditionCn;
    }, function (_unresolved_4) {
      eEmitterAction = _unresolved_4.eEmitterAction;
      eEmitterActionCn = _unresolved_4.eEmitterActionCn;
      eBulletActionCn = _unresolved_4.eBulletActionCn;
    }, function (_unresolved_5) {
      ExpressionValue = _unresolved_5.ExpressionValue;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eConditionOp", eConditionOp = /*#__PURE__*/function (eConditionOp) {
        eConditionOp[eConditionOp["And"] = 0] = "And";
        eConditionOp[eConditionOp["Or"] = 1] = "Or";
        return eConditionOp;
      }({}));

      _export("eCompareOp", eCompareOp = /*#__PURE__*/function (eCompareOp) {
        eCompareOp[eCompareOp["Equal"] = 0] = "Equal";
        eCompareOp[eCompareOp["NotEqual"] = 1] = "NotEqual";
        eCompareOp[eCompareOp["Greater"] = 2] = "Greater";
        eCompareOp[eCompareOp["Less"] = 3] = "Less";
        eCompareOp[eCompareOp["GreaterEqual"] = 4] = "GreaterEqual";
        eCompareOp[eCompareOp["LessEqual"] = 5] = "LessEqual";
        return eCompareOp;
      }({}));

      _export("eTargetValueType", eTargetValueType = /*#__PURE__*/function (eTargetValueType) {
        eTargetValueType[eTargetValueType["Absolute"] = 1] = "Absolute";
        eTargetValueType[eTargetValueType["Relative"] = 2] = "Relative";
        return eTargetValueType;
      }({})); // 数据基类(不含具体的type类型)


      _export("EventConditionData", EventConditionData = (_dec = ccclass('EventConditionData'), _dec2 = property({
        type: Enum(eConditionOp),
        displayName: '条件关系'
      }), _dec3 = property({
        visible: false
      }), _dec4 = property({
        type: Enum(_crd && eEmitterConditionCn === void 0 ? (_reportPossibleCrUseOfeEmitterConditionCn({
          error: Error()
        }), eEmitterConditionCn) : eEmitterConditionCn),
        displayName: '发射器条件'
      }), _dec5 = property({
        type: Enum(_crd && eBulletConditionCn === void 0 ? (_reportPossibleCrUseOfeBulletConditionCn({
          error: Error()
        }), eBulletConditionCn) : eBulletConditionCn),
        displayName: '子弹条件'
      }), _dec6 = property({
        type: Enum(eCompareOp),
        displayName: '比较方式'
      }), _dec7 = property({
        visible: false
      }), _dec8 = property({
        displayName: '目标值'
      }), _dec(_class = (_class2 = class EventConditionData {
        constructor() {
          _initializerDefineProperty(this, "op", _descriptor, this);

          _initializerDefineProperty(this, "type", _descriptor2, this);

          _initializerDefineProperty(this, "compareOp", _descriptor3, this);

          // 条件值: 例如持续时间、距离
          _initializerDefineProperty(this, "targetValue", _descriptor4, this);
        }

        get emitterConditionType() {
          return this.type;
        }

        set emitterConditionType(value) {
          this.type = value;
        }

        get bulletConditionType() {
          return this.type;
        }

        set bulletConditionType(value) {
          this.type = value;
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

        static fromJSON(json) {
          const data = new EventConditionData();
          if (json) Object.assign(data, json);
          return data;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "op", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eConditionOp.And;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "type", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
            error: Error()
          }), eEmitterCondition) : eEmitterCondition).Level_Duration;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "emitterConditionType", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "emitterConditionType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "bulletConditionType", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "bulletConditionType"), _class2.prototype), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "compareOp", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eCompareOp.Equal;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "targetValue", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "targetValueStr", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "targetValueStr"), _class2.prototype)), _class2)) || _class));

      _export("EventActionData", EventActionData = (_dec9 = ccclass('EventActionData'), _dec10 = property({
        visible: false
      }), _dec11 = property({
        type: Enum(_crd && eEmitterActionCn === void 0 ? (_reportPossibleCrUseOfeEmitterActionCn({
          error: Error()
        }), eEmitterActionCn) : eEmitterActionCn),
        displayName: '发射器操作',
        group: '发射器'
      }), _dec12 = property({
        type: Enum(_crd && eBulletActionCn === void 0 ? (_reportPossibleCrUseOfeBulletActionCn({
          error: Error()
        }), eBulletActionCn) : eBulletActionCn),
        displayName: '子弹操作',
        group: '子弹'
      }), _dec13 = property({
        visible: false
      }), _dec14 = property({
        displayName: '持续时间'
      }), _dec15 = property({
        visible: false
      }), _dec16 = property({
        displayName: '目标值'
      }), _dec17 = property({
        type: Enum(eTargetValueType),
        displayName: '目标值类型'
      }), _dec18 = property({
        type: Enum(_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
          error: Error()
        }), eEasing) : eEasing),
        displayName: '缓动函数'
      }), _dec9(_class4 = (_class5 = class EventActionData {
        constructor() {
          _initializerDefineProperty(this, "type", _descriptor5, this);

          // 持续时间: 0表示立即执行
          _initializerDefineProperty(this, "duration", _descriptor6, this);

          _initializerDefineProperty(this, "targetValue", _descriptor7, this);

          _initializerDefineProperty(this, "targetValueType", _descriptor8, this);

          _initializerDefineProperty(this, "easing", _descriptor9, this);
        }

        get emitterActionType() {
          return this.type;
        }

        set emitterActionType(value) {
          this.type = value;
        }

        get bulletActionType() {
          return this.type;
        }

        set bulletActionType(value) {
          this.type = value;
        }

        get durationStr() {
          return this.duration.value;
        }

        set durationStr(value) {
          this.duration.value = value;
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

        static fromJSON(json) {
          const data = new EventActionData();
          if (json) Object.assign(data, json);
          return data;
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "type", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eEmitterAction === void 0 ? (_reportPossibleCrUseOfeEmitterAction({
            error: Error()
          }), eEmitterAction) : eEmitterAction).Emitter_Active;
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "emitterActionType", [_dec11], Object.getOwnPropertyDescriptor(_class5.prototype, "emitterActionType"), _class5.prototype), _applyDecoratedDescriptor(_class5.prototype, "bulletActionType", [_dec12], Object.getOwnPropertyDescriptor(_class5.prototype, "bulletActionType"), _class5.prototype), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "duration", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "durationStr", [_dec14], Object.getOwnPropertyDescriptor(_class5.prototype, "durationStr"), _class5.prototype), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "targetValue", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "targetValueStr", [_dec16], Object.getOwnPropertyDescriptor(_class5.prototype, "targetValueStr"), _class5.prototype), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "targetValueType", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eTargetValueType.Absolute;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "easing", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear;
        }
      })), _class5)) || _class4));

      _export("EventGroupData", EventGroupData = (_dec19 = ccclass('EventGroupData'), _dec20 = property({
        displayName: '事件组名称'
      }), _dec21 = property({
        displayName: '触发次数'
      }), _dec22 = property({
        type: [EventConditionData],
        displayName: '条件列表'
      }), _dec23 = property({
        type: [EventActionData],
        displayName: '行为列表'
      }), _dec19(_class7 = (_class8 = class EventGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor10, this);

          // 重复触发次数(默认1,只能触发一次; -1表示循环触发)
          _initializerDefineProperty(this, "triggerCount", _descriptor11, this);

          _initializerDefineProperty(this, "conditions", _descriptor12, this);

          _initializerDefineProperty(this, "actions", _descriptor13, this);
        }

        static fromJSON(json) {
          const data = new EventGroupData();

          if (json) {
            // Extract conditions and actions before Object.assign
            Object.assign(data, json);
            data.conditions = (json.conditions || []).map(EventConditionData.fromJSON);
            data.actions = (json.actions || []).map(EventActionData.fromJSON);
          }

          return data;
        }

      }, (_descriptor10 = _applyDecoratedDescriptor(_class8.prototype, "name", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class8.prototype, "triggerCount", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class8.prototype, "conditions", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class8.prototype, "actions", [_dec23], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class8)) || _class7));

      _crd = false;
    }
  };
});
//# sourceMappingURL=212b54df6c611ef86f585d0b1331b2ccd2ffdb22.js.map