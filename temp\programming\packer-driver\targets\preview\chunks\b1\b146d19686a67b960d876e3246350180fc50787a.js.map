{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts"], "names": ["_decorator", "ccclass", "property", "eEmitterAction", "eBulletAction", "eEmitterActionCn", "eBulletActionCn"], "mappings": ";;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;AACA;;gCACYG,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;AA+CZ;AACA;AACA;AACA;;;+BACYC,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;;AAuBZ;kCACYC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;;iCAyCAC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是发射器的行为\r\n */\r\nexport enum eEmitterAction {\r\n    Emitter_Active = 1,         // 发射器是否启用\r\n    Emitter_InitialDelay,       // 发射器当前的初始延迟\r\n    Emitter_Prewarm,            // 发射器是否启用预热\r\n    Emitter_PrewarmDuration,    // 发射器预热的持续时间\r\n    Emitter_Duration,           // 发射器配置的持续时间\r\n    Emitter_ElapsedTime,        // 发射器已运行的时间\r\n    Emitter_Loop,               // 发射器是否循环\r\n    Emitter_LoopInterval,       // 发射器循环的间隔时间\r\n\r\n    Emitter_EmitInterval,       // 发射器开火间隔时间\r\n\r\n    Emitter_PerEmitCount,      // 发射器单次开火子弹数量\r\n    Emitter_PerEmitInterval,   // 发射器单次开火子弹间隔\r\n    Emitter_PerEmitOffsetX,    // 发射器单次开火子弹偏移\r\n\r\n    Emitter_Angle,             // 发射器弹道角度\r\n    Emitter_Count,             // 发射器弹道数量\r\n\r\n    Emitter_FireEffect,         // 发射器开火特效\r\n    \r\n    Bullet_Duration,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    // Bullet_Effect,              // 子弹-拖尾特效?\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n    \r\n    Unit_Life,\r\n    Unit_LifePercent,\r\n    Unit_PosX,\r\n    Unit_PosY,\r\n    Unit_Speed,\r\n    Unit_SpeedAngle,\r\n    Unit_Acceleration,\r\n    Unit_AccelerationAngle,\r\n}\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是子弹的行为\r\n */\r\nexport enum eBulletAction {\r\n    Bullet_Duration = 100,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport type eEventActionType = eEmitterAction | eBulletAction;\r\n\r\n\r\n// 以下枚举值用于编辑器显示，实际运行时不会用到\r\nexport enum eEmitterActionCn {\r\n    发射器启用 = eEmitterAction.Emitter_Active,\r\n    发射器初始延迟 = eEmitterAction.Emitter_InitialDelay,\r\n    发射器预热 = eEmitterAction.Emitter_Prewarm,\r\n    发射器预热持续时间 = eEmitterAction.Emitter_PrewarmDuration,\r\n    发射器持续时间 = eEmitterAction.Emitter_Duration,\r\n    发射器已运行时间 = eEmitterAction.Emitter_ElapsedTime,\r\n    发射器循环 = eEmitterAction.Emitter_Loop,\r\n    发射器循环间隔 = eEmitterAction.Emitter_LoopInterval,\r\n    发射器开火间隔 = eEmitterAction.Emitter_EmitInterval,\r\n    发射器单次开火次数 = eEmitterAction.Emitter_PerEmitCount,\r\n    发射器单次开火间隔 = eEmitterAction.Emitter_PerEmitInterval,\r\n    发射器单次开火偏移 = eEmitterAction.Emitter_PerEmitOffsetX,\r\n    发射器弹道角度 = eEmitterAction.Emitter_Angle,\r\n    发射器弹道数量 = eEmitterAction.Emitter_Count,\r\n\r\n    子弹持续时间 = eEmitterAction.Bullet_Duration,\r\n    子弹伤害 = eEmitterAction.Bullet_Damage,\r\n    子弹速度 = eEmitterAction.Bullet_Speed,\r\n    子弹速度角度 = eEmitterAction.Bullet_SpeedAngle,\r\n    子弹加速度 = eEmitterAction.Bullet_Acceleration,\r\n    子弹加速度角度 = eEmitterAction.Bullet_AccelerationAngle,\r\n    子弹缩放 = eEmitterAction.Bullet_Scale,\r\n    子弹颜色R = eEmitterAction.Bullet_ColorR,\r\n    子弹颜色G = eEmitterAction.Bullet_ColorG,\r\n    子弹颜色B = eEmitterAction.Bullet_ColorB,\r\n    子弹面向移动方向 = eEmitterAction.Bullet_FacingMoveDir,\r\n    子弹追踪目标 = eEmitterAction.Bullet_TrackingTarget,\r\n    子弹破坏性 = eEmitterAction.Bullet_Destructive,\r\n    子弹命中时破坏 = eEmitterAction.Bullet_DestructiveOnHit,\r\n\r\n    单位生命值 = eEmitterAction.Unit_Life,\r\n    单位生命值百分比 = eEmitterAction.Unit_LifePercent,\r\n    单位位置X = eEmitterAction.Unit_PosX,\r\n    单位位置Y = eEmitterAction.Unit_PosY,\r\n    单位速度 = eEmitterAction.Unit_Speed,\r\n    单位速度角度 = eEmitterAction.Unit_SpeedAngle,\r\n    单位加速度 = eEmitterAction.Unit_Acceleration,\r\n    单位加速度角度 = eEmitterAction.Unit_AccelerationAngle,\r\n}\r\n\r\nexport enum eBulletActionCn {\r\n    子弹持续时间 = eBulletAction.Bullet_Duration,\r\n    子弹已运行时间 = eBulletAction.Bullet_ElapsedTime,\r\n    子弹位置X = eBulletAction.Bullet_PosX,\r\n    子弹位置Y = eBulletAction.Bullet_PosY,\r\n    子弹伤害 = eBulletAction.Bullet_Damage,\r\n    子弹速度 = eBulletAction.Bullet_Speed,\r\n    子弹速度角度 = eBulletAction.Bullet_SpeedAngle,\r\n    子弹加速度 = eBulletAction.Bullet_Acceleration,\r\n    子弹加速度角度 = eBulletAction.Bullet_AccelerationAngle,\r\n    子弹缩放 = eBulletAction.Bullet_Scale,\r\n    子弹颜色R = eBulletAction.Bullet_ColorR,\r\n    子弹颜色G = eBulletAction.Bullet_ColorG,\r\n    子弹颜色B = eBulletAction.Bullet_ColorB,\r\n    子弹面向移动方向 = eBulletAction.Bullet_FacingMoveDir,\r\n    子弹追踪目标 = eBulletAction.Bullet_TrackingTarget,\r\n    子弹破坏性 = eBulletAction.Bullet_Destructive,\r\n    子弹命中时破坏 = eBulletAction.Bullet_DestructiveOnHit,\r\n}\r\n"]}