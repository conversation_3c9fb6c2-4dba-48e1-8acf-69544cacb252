{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/BuffComp.ts"], "names": ["Buff", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "randomRange", "logInfo", "log<PERSON>arn", "BaseComp", "MyApp", "<PERSON>llComp", "constructor", "data", "id", "res", "time", "duration", "cycleTimes", "incID", "durationBonus", "buffs", "Map", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>", "buffD<PERSON>", "lubanTables", "<PERSON><PERSON><PERSON><PERSON>", "get", "buff", "set", "stack", "maxStack", "refreshType", "for<PERSON>ach", "b", "push", "length", "removeBuff", "effects", "applyEffect", "forEachByTargetType", "entity", "target", "ApplyBuffEffect", "update", "dt", "index", "cycle", "RemoveBuffEffect", "splice", "delete"], "mappings": ";;;+HAQaA,I,EAcQC,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBJC,MAAAA,W,OAAAA,W;;AANRC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AACXC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AAGFC,MAAAA,S;;;;;;;sBAGMP,I,GAAN,MAAMA,IAAN,CAAW;AAOdQ,QAAAA,WAAW,CAACC,IAAD,EAAe;AAAA,eAN1BC,EAM0B;AAAA,eAL1BC,GAK0B;AAAA,eAJ1BC,IAI0B,GAJnB,CAImB;AAAA,eAH1BC,QAG0B,GAHf,CAGe;AAAA,eAF1BC,UAE0B,GAFb,CAEa;AACtB,eAAKJ,EAAL,GAAUV,IAAI,CAACe,KAAL,EAAV;AACA,eAAKJ,GAAL,GAAWF,IAAX;AACA,eAAKI,QAAL,GAAgB,KAAKF,GAAL,CAASE,QAAT,IAAqB,CAAC,CAAtB,GAA0B,CAAC,CAA3B,GAA+B,KAAKF,GAAL,CAASE,QAAT,GAAoBX,WAAW,CAAC,CAAD,EAAI,KAAKS,GAAL,CAASK,aAAb,CAA9E;AACH;;AAXa,O;;AAALhB,MAAAA,I,CAMFe,K,GAAQ,C;;yBAQEd,Q,GAAN,MAAMA,QAAN;AAAA;AAAA,gCAAgC;AAAA;AAAA;AAAA,eACnCgB,KADmC,GACN,IAAIC,GAAJ,EADM;AAAA;;AAE3CC,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB;AAAA;AAAA,kCAAQ,MAAR,EAAiB,cAAaA,MAAO,EAArC;AACA,cAAIC,QAAQ,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,QAAlB,CAA2BC,GAA3B,CAA+BJ,MAA/B,CAAf;;AACA,cAAI,CAACC,QAAL,EAAe;AACX;AAAA;AAAA,oCAAQ,MAAR,EAAiB,cAAaD,MAAO,uBAArC;AACA;AACH;;AACD,cAAIK,IAAJ;;AACA,cAAIJ,QAAQ,CAACR,QAAT,IAAqB,CAAzB,EAA4B;AACxBY,YAAAA,IAAI,GAAG,IAAIzB,IAAJ,CAASqB,QAAT,CAAP;AACA,gBAAIJ,KAAK,GAAG,KAAKA,KAAL,CAAWO,GAAX,CAAeJ,MAAf,CAAZ;;AACA,gBAAI,CAACH,KAAL,EAAY;AACRA,cAAAA,KAAK,GAAG,EAAR;AACA,mBAAKA,KAAL,CAAWS,GAAX,CAAeN,MAAf,EAAuBH,KAAvB;AACH;;AACD,gBAAIU,KAAK,GAAGN,QAAQ,CAACO,QAAT,GAAoB,CAApB,GAAwB,CAAxB,GAA4BP,QAAQ,CAACO,QAAjD;;AACA,gBAAIP,QAAQ,CAACQ,WAAT,IAAwBJ,IAAI,CAACd,GAAL,CAASE,QAAT,IAAqB,CAAC,CAAlD,EAAqD;AACjDI,cAAAA,KAAK,CAACa,OAAN,CAAeC,CAAD,IAAO;AACjBA,gBAAAA,CAAC,CAAClB,QAAF,GAAakB,CAAC,CAACnB,IAAF,GAASa,IAAI,CAAEZ,QAA5B;AACH,eAFD;AAGH;;AACDI,YAAAA,KAAK,CAACe,IAAN,CAAWP,IAAX;;AACA,mBAAOR,KAAK,CAACgB,MAAN,GAAeN,KAAtB,EAA6B;AACzB,mBAAKO,UAAL,CAAgBjB,KAAK,CAAC,CAAD,CAArB,EAA0BA,KAA1B,EAAiC,CAAjC,EAAoCG,MAApC;AACH;AACJ;;AACDC,UAAAA,QAAQ,CAACc,OAAT,CAAiBL,OAAjB,CAA0BM,WAAD,IAAiB;AACtC;AAAA;AAAA,wCAAUC,mBAAV,CAA8B,KAAKC,MAAnC,EAAwDF,WAAW,CAACG,MAApE,EAA6ED,MAAD,IAAY;AACpFA,cAAAA,MAAM,CAACE,eAAP,CAAuBf,IAAvB,EAA6BW,WAA7B;AACH,aAFD;AAGH,WAJD;AAKH;;AACDK,QAAAA,MAAM,CAACC,EAAD,EAAmB;AACrB,eAAKzB,KAAL,CAAWa,OAAX,CAAmB,CAACb,KAAD,EAAQG,MAAR,KAAmB;AAClCH,YAAAA,KAAK,CAACa,OAAN,CAAc,CAACL,IAAD,EAAOkB,KAAP,KAAiB;AAC3BlB,cAAAA,IAAI,CAACb,IAAL,IAAa8B,EAAE,GAAC,IAAhB;;AACA,kBAAIjB,IAAI,CAACd,GAAL,CAASiC,KAAT,GAAiB,CAAjB,IACAnB,IAAI,CAACb,IAAL,IAAa,CAACa,IAAI,CAACX,UAAL,GAAgB,CAAjB,IAAsBW,IAAI,CAACd,GAAL,CAASiC,KAD5C,KAECnB,IAAI,CAACd,GAAL,CAASG,UAAT,IAAuB,CAAvB,IAA4BW,IAAI,CAACX,UAAL,GAAkBW,IAAI,CAACd,GAAL,CAASG,UAFxD,CAAJ,EAGE;AACEW,gBAAAA,IAAI,CAACX,UAAL;AACAW,gBAAAA,IAAI,CAACd,GAAL,CAASwB,OAAT,CAAiBL,OAAjB,CAA0BM,WAAD,IAAiB;AACtC;AAAA;AAAA,8CAAUC,mBAAV,CAA8B,KAAKC,MAAnC,EAAwDF,WAAW,CAACG,MAApE,EAA6ED,MAAD,IAAY;AACpFA,oBAAAA,MAAM,CAACE,eAAP,CAAuBf,IAAvB,EAA6BW,WAA7B;AACH,mBAFD;AAGH,iBAJD;AAKH;;AACD,kBAAIX,IAAI,CAACZ,QAAL,IAAiB,CAAC,CAAlB,IAAsBY,IAAI,CAACb,IAAL,IAAaa,IAAI,CAACZ,QAA5C,EAAsD;AAClD,qBAAKqB,UAAL,CAAgBT,IAAhB,EAAsBR,KAAtB,EAA6B0B,KAA7B,EAAoCvB,MAApC;AACH;AACJ,aAhBD;AAiBH,WAlBD;AAmBH;;AAEOc,QAAAA,UAAU,CAACT,IAAD,EAAaR,KAAb,EAA4B0B,KAA5B,EAA2CvB,MAA3C,EAA2D;AACzEK,UAAAA,IAAI,CAACd,GAAL,CAASwB,OAAT,CAAiBL,OAAjB,CAA0BM,WAAD,IAAiB;AACtC;AACA;AACA;AACA;AAAA;AAAA,wCAAUC,mBAAV,CAA8B,KAAKC,MAAnC,EAAwDF,WAAW,CAACG,MAApE,EAA6ED,MAAD,IAAY;AACpFA,cAAAA,MAAM,CAACO,gBAAP,CAAwBpB,IAAxB,EAA8BW,WAA9B;AACH,aAFD;AAGH,WAPD;AAQAnB,UAAAA,KAAK,CAAC6B,MAAN,CAAaH,KAAb,EAAoB,CAApB;;AACA,cAAI1B,KAAK,CAACgB,MAAN,KAAiB,CAArB,EAAwB;AACpB,iBAAKhB,KAAL,CAAW8B,MAAX,CAAkB3B,MAAlB;AACH;AACJ;;AArE0C,O", "sourcesContent": ["import { logInfo, logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport BaseComp from \"../../base/BaseComp\";\r\nimport { MyApp } from \"db://assets/scripts/MyApp\";\r\nimport { buffer } from \"db://assets/scripts/autogen/luban/schema\";\r\nimport PlaneBase from \"../PlaneBase\";\r\nimport SkillComp from \"./SkillComp\";\r\nimport { random, randomRange } from \"cc\";\r\n\r\nexport class Buff {\r\n    id:number;\r\n    res: buffer\r\n    time = 0;\r\n    duration = 0;\r\n    cycleTimes = 0;\r\n    static incID = 1;\r\n    constructor(data: buffer) {\r\n        this.id = Buff.incID++;\r\n        this.res = data;\r\n        this.duration = this.res.duration == -1 ? -1 : this.res.duration + randomRange(0, this.res.durationBonus);\r\n    }\r\n}\r\n\r\nexport default class BuffComp extends BaseComp {\r\n    private buffs: Map<number, Buff[]> = new Map();\r\n    ApplyBuff(buffID: number) {\r\n        logInfo(\"Buff\", `apply buff ${buffID}`)\r\n        let buffData = MyApp.lubanTables.Tbbuffer.get(buffID);\r\n        if (!buffData) {\r\n            logWarn(\"Buff\", `apply buff ${buffID} but config not found`)\r\n            return;\r\n        }\r\n        let buff: Buff|null\r\n        if (buffData.duration != 0) {\r\n            buff = new Buff(buffData)!;\r\n            let buffs = this.buffs.get(buffID)\r\n            if (!buffs) {\r\n                buffs = [];\r\n                this.buffs.set(buffID, buffs);\r\n            }\r\n            let stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;\r\n            if (buffData.refreshType && buff.res.duration != -1) {\r\n                buffs.forEach((b) => {\r\n                    b.duration = b.time + buff!.duration;\r\n                })\r\n            }\r\n            buffs.push(buff);\r\n            while (buffs.length > stack) {\r\n                this.removeBuff(buffs[0], buffs, 0, buffID);\r\n            }\r\n        }\r\n        buffData.effects.forEach((applyEffect) => {\r\n            SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                entity.ApplyBuffEffect(buff, applyEffect);\r\n            })\r\n        })\r\n    }\r\n    update(dt: number): void {\r\n        this.buffs.forEach((buffs, buffID) => {\r\n            buffs.forEach((buff, index) => {\r\n                buff.time += dt*1000;\r\n                if (buff.res.cycle > 0 && \r\n                    buff.time >= (buff.cycleTimes+1) * buff.res.cycle &&\r\n                    (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes)\r\n                ) {\r\n                    buff.cycleTimes++;\r\n                    buff.res.effects.forEach((applyEffect) => {\r\n                        SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                            entity.ApplyBuffEffect(buff, applyEffect);\r\n                        })\r\n                    })\r\n                }\r\n                if (buff.duration != -1 &&buff.time >= buff.duration) {\r\n                    this.removeBuff(buff, buffs, index, buffID);\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    private removeBuff(buff: Buff, buffs: Buff[], index: number, buffID: number) {\r\n        buff.res.effects.forEach((applyEffect) => {\r\n            // 这个地方和加的时候查出来的target会不同\r\n            // 1. 需要保证查出来的target只多不少\r\n            // 2. remove接口里面需要判断时候是这个buff的效果\r\n            SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                entity.RemoveBuffEffect(buff, applyEffect);\r\n            })\r\n        })\r\n        buffs.splice(index, 1);\r\n        if (buffs.length === 0) {\r\n            this.buffs.delete(buffID);\r\n        }    \r\n    }\r\n}"]}