System.register(["__unresolved_0"], function (_export, _context) {
  "use strict";

  var _reporterNs, BaseComp, _crd;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "./Entity", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }],
    execute: function () {
      _crd = true;

      _export("default", BaseComp = class BaseComp {
        constructor() {
          this.m_entity = null;
          // 绑定的实体
          this.m_enabled = true;
        }

        // 是否启用

        /**
         * 初始化组件
         * @param {Entity} entity 绑定的实体
         */
        init(entity) {
          this.m_entity = entity;
          this.enabled = true;
          this.onInit();
        }
        /**
         * 获取组件是否启用
         * @returns {boolean}
         */


        get enabled() {
          return this.m_enabled;
        }
        /**
         * 设置组件是否启用
         * @param {boolean} value 是否启用
         */


        set enabled(value) {
          if (this.m_enabled !== value) {
            this.m_enabled = value;
          }
        }
        /**
         * 获取绑定的实体
         * @returns {Entity}
         */


        get entity() {
          return this.m_entity;
        }
        /**
         * 移除组件
         */


        remove() {
          this.enabled = false;
        }
        /**
         * 初始化时的回调
         */


        onInit() {}

        update(dt) {}

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=b34a19b4f6294f1f8dcc4007803a5db614ca3ba1.js.map