System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, GameIns, GameEnum, PlaneBase, Bullet, Plane, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, EnemyPlaneBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      PlaneBase = _unresolved_4.default;
    }, function (_unresolved_5) {
      Bullet = _unresolved_5.Bullet;
    }, function (_unresolved_6) {
      Plane = _unresolved_6.Plane;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlaneBase = (_dec = ccclass('EnemyPlaneBase'), _dec2 = property(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
        error: Error()
      }), Plane) : Plane), _dec(_class = (_class2 = class EnemyPlaneBase extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "plane", _descriptor, this);

          this.removeAble = false;
          this.bullets = [];
        }

        die(destroyType) {
          if (!super.toDie()) {
            return false;
          }

          this.colliderEnabled = false;
          this.onDie(destroyType);
        }

        onDie(destroyType) {
          this.willRemove();

          switch (destroyType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die:
              // this.playDieAnim();
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver:
              break;
          }
        }

        onCollide(collider) {
          if (!this.isDead) {
            if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
              error: Error()
            }), Bullet) : Bullet)) {
              var attack = collider.entity.getAttack();
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);
              this.hurt(attack);
            }
          }
        }
        /**
         * 准备移除敌机
         */


        willRemove() {}
        /**
         * 检查是否可以移除
         * @param {number} deltaTime 帧间隔时间
         */


        _checkRemoveAble(deltaTime) {
          this.removeAble = true;
        }

        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 从敌人移除子弹
         * @param {Bullet} bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.bullets) {
            var index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }

        setPos(x, y) {
          this.node.setPosition(x, y);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "plane", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=298d203ad1ed77325702cea28ff7c8eb408807b4.js.map