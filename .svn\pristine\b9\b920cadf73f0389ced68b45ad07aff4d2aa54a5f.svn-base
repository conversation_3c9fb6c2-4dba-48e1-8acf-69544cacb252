import { _decorator, misc, size, Component, Enum, Vec2, Vec3, Node, UITransform } from 'cc';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;
import { IMovable } from './IMovable';
import { BulletSystem } from '../bullet/BulletSystem';
import FCollider, { ColliderGroupType } from 'db://assets/scripts/game/collider-system/FCollider';
import Entity from 'db://assets/scripts/game/ui/base/Entity';

export enum eSpriteDefaultFacing {
    Right = 0,    // →
    Up = -90,     // ↑
    Down = 90,    // ↓
    Left = 180    // ←
}

@ccclass('Movable')
@executeInEditMode
export class Movable extends Component implements IMovable {

    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;

    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向
    public isTrackingTarget: boolean = false;     // 是否正在追踪目标
    public speed: number = 1;                     // 速度
    public speedAngle: number = 0;                // 速度方向 (用角度表示)
    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）
    public acceleration: number = 0;              // 加速度
    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)

    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
    public tiltSpeed: number = 0;                 // 偏移速度
    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
    public tiltOffset: number = 100;               // 偏移距离

    public target: Node | null = null;            // 追踪的目标节点
    public arrivalDistance: number = 10;          // 到达目标的距离

    private _selfSize: Vec2 = new Vec2();
    private _position: Vec3 = new Vec3();
    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间
    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）

    private _isVisible: boolean = true;           // 是否可见
    public get isVisible() { return this._isVisible; }
    private _isMovable: boolean = true;           // 是否可移动
    public get isMovable() { return this._isMovable; }

    // Callbacks:
    public onBecomeVisibleCallback: Function | null = null;
    public onBecomeInvisibleCallback: Function | null = null;
    // public onCollideCallback: Function | null = null;

    onLoad() {
        const uiTransform = this.node.getComponent(UITransform);
        const self_size = uiTransform ? uiTransform.contentSize : {width: 0, height: 0};
        this._selfSize.set(self_size.width / 2, self_size.height / 2);
    }

    public tick(dt: number): void {
        if (!this._isMovable) return;

        const speedRadians = degreesToRadians(this.speedAngle);
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(speedRadians);
        let velocityY = this.speed * Math.sin(speedRadians);

        if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this.node.getPosition();
            
            // Calculate direction to target
            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);
            
            if (distance > 0) {
                // Calculate desired angle to target
                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));
                
                // Smoothly adjust speedAngle toward target
                const angleDiff = desiredAngle - this.speedAngle;
                // Normalize angle difference to [-180, 180] range
                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;
                
                // Apply tracking adjustment (you can add a trackingStrength property to control this)
                const trackingStrength = 1.0; // Can be made configurable
                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable
                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
                
                this.speedAngle += turnAmount * trackingStrength;
                
                // Recalculate velocity with new angle
                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));
            }
        }

        // Convert acceleration and angle to acceleration vector
        if (this.acceleration !== 0) {
            const accelerationRadians = degreesToRadians(this.accelerationAngle);
            const accelerationX = this.acceleration * Math.cos(accelerationRadians);
            const accelerationY = this.acceleration * Math.sin(accelerationRadians);
            // Update velocity vector: v = v + a * dt
            velocityX += accelerationX * dt;
            velocityY += accelerationY * dt;
        }

        // Convert back to speed and angle
        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));

        // Update position: p = p + v * dt
        if (velocityX !== 0 || velocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += velocityX * dt;
            this._basePosition.y += velocityY * dt;

            // Start with base position
            this._position.set(this._basePosition);

            // Apply tilting behavior if enabled
            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
                // Update tilt time
                this._tiltTime += dt;

                // Calculate perpendicular direction to movement
                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))
                const moveAngleRad = degreesToRadians(this.speedAngle);
                const perpX = -Math.sin(moveAngleRad);
                const perpY = Math.cos(moveAngleRad);

                // Calculate tilt offset using sine wave
                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;

                // Apply tilt offset in perpendicular direction (as position offset, not velocity)
                this._position.x += perpX * tiltAmount;
                this._position.y += perpY * tiltAmount;
            }

            this.node.setPosition(this._position);
            this.checkVisibility();
        }
        
        if (this.isFacingMoveDir && this.speed > 0) {
            const movementAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));
            const finalAngle = movementAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
        }
    }

    public checkVisibility(): void {
        // 这里目前的检查逻辑没有考虑旋转和缩放
        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
        const visibleSize = BulletSystem.worldBounds;
        this.node.getWorldPosition(this._position);
        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&
                          (this._position.x - this._selfSize.x) <= visibleSize.xMax &&
                          (this._position.y - this._selfSize.y) <= visibleSize.yMax && 
                          (this._position.y + this._selfSize.y) >= visibleSize.yMin;

        // debug visibility
        // if (!isVisible) {
        //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
        //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
        //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
        //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
        //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
        // }

        this.setVisible(isVisible);
    }

    public setVisible(visible: boolean) {
        if (this._isVisible === visible) return;

        this._isVisible = visible;
        if (visible && this.onBecomeVisibleCallback) {
            this.onBecomeVisibleCallback();
        } else if (!visible && this.onBecomeInvisibleCallback) {
            this.onBecomeInvisibleCallback();
        }
    }

    /**
     * Set the target to track
     */
    public setTarget(target: Node | null): void {
        this.target = target;
        this.isTrackingTarget = target !== null;
    }

    public setMovable(movable: boolean) {
        this._isMovable = movable;

        if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
        }
    }
}