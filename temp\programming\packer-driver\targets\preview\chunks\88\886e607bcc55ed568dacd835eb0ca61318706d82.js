System.register([], function (_export, _context) {
  "use strict";

  var _crd, GameEnum;

  _export("GameEnum", void 0);

  return {
    setters: [],
    execute: function () {
      _crd = true;

      (function (_GameEnum) {
        var GameType = /*#__PURE__*/function (GameType) {
          GameType[GameType["Common"] = 0] = "Common";
          GameType[GameType["Expedition"] = 1] = "Expedition";
          GameType[GameType["Gold"] = 2] = "Gold";
          GameType[GameType["Boss"] = 3] = "Boss";
          return GameType;
        }({});

        _GameEnum.GameType = GameType;

        var GameState = /*#__PURE__*/function (GameState) {
          GameState[GameState["Idle"] = 0] = "Idle";
          GameState[GameState["Ready"] = 1] = "Ready";
          GameState[GameState["Sortie"] = 2] = "Sortie";
          GameState[GameState["Battle"] = 3] = "Battle";
          GameState[GameState["Pause"] = 4] = "Pause";
          GameState[GameState["WillOver"] = 5] = "WillOver";
          GameState[GameState["Over"] = 6] = "Over";
          GameState[GameState["BossIn"] = 7] = "BossIn";
          return GameState;
        }({});

        _GameEnum.GameState = GameState;

        var EnemyAction = /*#__PURE__*/function (EnemyAction) {
          EnemyAction[EnemyAction["Sneak"] = 0] = "Sneak";
          EnemyAction[EnemyAction["GoUp"] = 1] = "GoUp";
          EnemyAction[EnemyAction["Track"] = 2] = "Track";
          EnemyAction[EnemyAction["Transform"] = 3] = "Transform";
          EnemyAction[EnemyAction["AttackPrepare"] = 4] = "AttackPrepare";
          EnemyAction[EnemyAction["AttackIng"] = 5] = "AttackIng";
          EnemyAction[EnemyAction["AttackOver"] = 6] = "AttackOver";
          EnemyAction[EnemyAction["Leave"] = 7] = "Leave";
          return EnemyAction;
        }({});

        _GameEnum.EnemyAction = EnemyAction;

        var EnemyType = /*#__PURE__*/function (EnemyType) {
          EnemyType[EnemyType["Normal"] = 0] = "Normal";
          EnemyType[EnemyType["Missile"] = 1] = "Missile";
          EnemyType[EnemyType["Turret"] = 2] = "Turret";
          EnemyType[EnemyType["Ligature"] = 3] = "Ligature";
          EnemyType[EnemyType["LigatureLine"] = 4] = "LigatureLine";
          EnemyType[EnemyType["LigatureUnit"] = 5] = "LigatureUnit";
          EnemyType[EnemyType["Build"] = 6] = "Build";
          EnemyType[EnemyType["Ship"] = 7] = "Ship";
          EnemyType[EnemyType["ShipHeart"] = 8] = "ShipHeart";
          EnemyType[EnemyType["Train"] = 9] = "Train";
          EnemyType[EnemyType["ParkourItem"] = 10] = "ParkourItem";
          EnemyType[EnemyType["GoldShip"] = 11] = "GoldShip";
          EnemyType[EnemyType["GoldBox"] = 12] = "GoldBox";
          EnemyType[EnemyType["BossLigature"] = 13] = "BossLigature";
          EnemyType[EnemyType["BossUnit"] = 10001] = "BossUnit";
          EnemyType[EnemyType["BossNormal"] = 20001] = "BossNormal";
          EnemyType[EnemyType["BossSnake"] = 21001] = "BossSnake";
          EnemyType[EnemyType["BossUFO"] = 22001] = "BossUFO";
          return EnemyType;
        }({});

        _GameEnum.EnemyType = EnemyType;

        var EnemyCollideLevel = /*#__PURE__*/function (EnemyCollideLevel) {
          EnemyCollideLevel[EnemyCollideLevel["None"] = 0] = "None";
          EnemyCollideLevel[EnemyCollideLevel["MainBullet"] = 1] = "MainBullet";
          EnemyCollideLevel[EnemyCollideLevel["Main"] = 2] = "Main";
          return EnemyCollideLevel;
        }({});

        _GameEnum.EnemyCollideLevel = EnemyCollideLevel;

        var EnemyDestroyType = /*#__PURE__*/function (EnemyDestroyType) {
          EnemyDestroyType[EnemyDestroyType["Die"] = 0] = "Die";
          EnemyDestroyType[EnemyDestroyType["Leave"] = 1] = "Leave";
          EnemyDestroyType[EnemyDestroyType["TrackOver"] = 2] = "TrackOver";
          EnemyDestroyType[EnemyDestroyType["TimeOver"] = 3] = "TimeOver";
          return EnemyDestroyType;
        }({});

        _GameEnum.EnemyDestroyType = EnemyDestroyType;

        var BossAction = /*#__PURE__*/function (BossAction) {
          BossAction[BossAction["Normal"] = 0] = "Normal";
          BossAction[BossAction["Appear"] = 1] = "Appear";
          BossAction[BossAction["Transform"] = 2] = "Transform";
          BossAction[BossAction["AttackPrepare"] = 3] = "AttackPrepare";
          BossAction[BossAction["AttackIng"] = 4] = "AttackIng";
          BossAction[BossAction["AttackOver"] = 5] = "AttackOver";
          BossAction[BossAction["Switch"] = 6] = "Switch";
          BossAction[BossAction["Blast"] = 7] = "Blast";
          return BossAction;
        }({});

        _GameEnum.BossAction = BossAction;

        var EnemyBuff = /*#__PURE__*/function (EnemyBuff) {
          EnemyBuff[EnemyBuff["Ice"] = 1] = "Ice";
          EnemyBuff[EnemyBuff["Fire"] = 2] = "Fire";
          EnemyBuff[EnemyBuff["Treat"] = 100] = "Treat";
          return EnemyBuff;
        }({});

        _GameEnum.EnemyBuff = EnemyBuff;

        var EnemyAttr = /*#__PURE__*/function (EnemyAttr) {
          EnemyAttr[EnemyAttr["Doctor"] = 1] = "Doctor";
          EnemyAttr[EnemyAttr["Shield"] = 2] = "Shield";
          return EnemyAttr;
        }({});

        _GameEnum.EnemyAttr = EnemyAttr;
      })(GameEnum || _export("GameEnum", GameEnum = {}));

      _crd = false;
    }
  };
});
//# sourceMappingURL=886e607bcc55ed568dacd835eb0ca61318706d82.js.map