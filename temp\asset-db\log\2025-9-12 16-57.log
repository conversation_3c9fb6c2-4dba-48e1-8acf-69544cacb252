2025-9-12 16:57:41-debug: start **** info
2025-9-12 16:57:41-log: Cannot access game frame or container.
2025-9-12 16:57:41-debug: asset-db:require-engine-code (397ms)
2025-9-12 16:57:41-log: meshopt wasm decoder initialized
2025-9-12 16:57:41-log: [bullet]:bullet wasm lib loaded.
2025-9-12 16:57:41-log: [box2d]:box2d wasm lib loaded.
2025-9-12 16:57:41-log: Cocos Creator v3.8.6
2025-9-12 16:57:41-log: Forward render pipeline initialized.
2025-9-12 16:57:41-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.77MB, end 80.06MB, increase: 49.30MB
2025-9-12 16:57:41-log: Using legacy pipeline
2025-9-12 16:57:41-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.97MB, end 83.97MB, increase: 3.00MB
2025-9-12 16:57:42-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.09MB, end 287.34MB, increase: 207.26MB
2025-9-12 16:57:42-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.01MB, end 288.86MB, increase: 204.85MB
2025-9-12 16:57:42-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.82MB, end 287.32MB, increase: 206.49MB
2025-9-12 16:57:42-debug: run package(google-play) handler(enable) start
2025-9-12 16:57:42-debug: run package(google-play) handler(enable) success!
2025-9-12 16:57:42-debug: run package(harmonyos-next) handler(enable) start
2025-9-12 16:57:42-debug: run package(honor-mini-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(honor-mini-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(huawei-agc) handler(enable) success!
2025-9-12 16:57:42-debug: run package(harmonyos-next) handler(enable) success!
2025-9-12 16:57:42-debug: run package(huawei-agc) handler(enable) start
2025-9-12 16:57:42-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(ios) handler(enable) start
2025-9-12 16:57:42-debug: run package(huawei-quick-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(ios) handler(enable) success!
2025-9-12 16:57:42-debug: run package(linux) handler(enable) start
2025-9-12 16:57:42-debug: run package(linux) handler(enable) success!
2025-9-12 16:57:42-debug: run package(mac) handler(enable) start
2025-9-12 16:57:42-debug: run package(mac) handler(enable) success!
2025-9-12 16:57:42-debug: run package(migu-mini-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(native) handler(enable) start
2025-9-12 16:57:42-debug: run package(ohos) handler(enable) start
2025-9-12 16:57:42-debug: run package(oppo-mini-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(ohos) handler(enable) success!
2025-9-12 16:57:42-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-12 16:57:42-debug: run package(native) handler(enable) success!
2025-9-12 16:57:42-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-12 16:57:42-debug: run package(taobao-mini-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(vivo-mini-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(migu-mini-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(web-desktop) handler(enable) start
2025-9-12 16:57:42-debug: run package(web-mobile) handler(enable) success!
2025-9-12 16:57:42-debug: run package(web-desktop) handler(enable) success!
2025-9-12 16:57:42-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(wechatgame) handler(enable) start
2025-9-12 16:57:42-debug: run package(wechatgame) handler(enable) success!
2025-9-12 16:57:42-debug: run package(web-mobile) handler(enable) start
2025-9-12 16:57:42-debug: run package(wechatprogram) handler(enable) success!
2025-9-12 16:57:42-debug: run package(windows) handler(enable) success!
2025-9-12 16:57:42-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-12 16:57:42-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-12 16:57:42-debug: run package(wechatprogram) handler(enable) start
2025-9-12 16:57:42-debug: run package(windows) handler(enable) start
2025-9-12 16:57:42-debug: run package(cocos-service) handler(enable) start
2025-9-12 16:57:42-debug: run package(cocos-service) handler(enable) success!
2025-9-12 16:57:42-debug: run package(im-plugin) handler(enable) success!
2025-9-12 16:57:42-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-12 16:57:42-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-12 16:57:42-debug: run package(im-plugin) handler(enable) start
2025-9-12 16:57:42-debug: run package(emitter-editor) handler(enable) start
2025-9-12 16:57:42-debug: asset-db:worker-init: initPlugin (1039ms)
2025-9-12 16:57:42-debug: run package(emitter-editor) handler(enable) success!
2025-9-12 16:57:42-debug: [Assets Memory track]: asset-db:worker-init start:30.76MB, end 289.55MB, increase: 258.80MB
2025-9-12 16:57:42-debug: Run asset db hook programming:beforePreStart success!
2025-9-12 16:57:42-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-12 16:57:42-debug: Run asset db hook programming:beforePreStart ...
2025-9-12 16:57:42-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-12 16:57:42-debug: run package(level-editor) handler(enable) success!
2025-9-12 16:57:42-debug: run package(level-editor) handler(enable) start
2025-9-12 16:57:42-debug: Preimport db internal success
2025-9-12 16:57:42-debug: run package(localization-editor) handler(enable) success!
2025-9-12 16:57:42-debug: asset-db:worker-init (1631ms)
2025-9-12 16:57:42-debug: run package(localization-editor) handler(enable) start
2025-9-12 16:57:42-debug: asset-db-hook-programming-beforePreStart (113ms)
2025-9-12 16:57:42-debug: asset-db-hook-engine-extends-beforePreStart (113ms)
2025-9-12 16:57:42-debug: Run asset db hook programming:afterPreStart ...
2025-9-12 16:57:42-debug: run package(wave-editor) handler(enable) start
2025-9-12 16:57:42-debug: starting packer-driver...
2025-9-12 16:57:42-debug: Preimport db assets success
2025-9-12 16:57:42-debug: run package(wave-editor) handler(enable) success!
2025-9-12 16:57:42-debug: run package(placeholder) handler(enable) start
2025-9-12 16:57:42-debug: run package(placeholder) handler(enable) success!
2025-9-12 16:57:47-debug: initialize scripting environment...
2025-9-12 16:57:47-debug: [[Executor]] prepare before lock
2025-9-12 16:57:47-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-12 16:57:47-debug: [[Executor]] prepare after unlock
2025-9-12 16:57:47-debug: Run asset db hook programming:afterPreStart success!
2025-9-12 16:57:47-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-12 16:57:47-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-12 16:57:47-debug: [Assets Memory track]: asset-db:worker-init: preStart start:289.57MB, end 303.73MB, increase: 14.16MB
2025-9-12 16:57:47-debug: Start up the 'internal' database...
2025-9-12 16:57:47-debug: asset-db-hook-programming-afterPreStart (5255ms)
2025-9-12 16:57:47-debug: asset-db:worker-effect-data-processing (237ms)
2025-9-12 16:57:47-debug: asset-db-hook-engine-extends-afterPreStart (237ms)
2025-9-12 16:57:47-debug: Start up the 'assets' database...
2025-9-12 16:57:48-debug: asset-db:worker-startup-database[internal] (5513ms)
2025-9-12 16:57:48-debug: lazy register asset handler directory
2025-9-12 16:57:48-debug: lazy register asset handler json
2025-9-12 16:57:48-debug: lazy register asset handler *
2025-9-12 16:57:48-debug: lazy register asset handler text
2025-9-12 16:57:48-debug: lazy register asset handler spine-data
2025-9-12 16:57:48-debug: lazy register asset handler dragonbones
2025-9-12 16:57:48-debug: lazy register asset handler dragonbones-atlas
2025-9-12 16:57:48-debug: lazy register asset handler terrain
2025-9-12 16:57:48-debug: lazy register asset handler javascript
2025-9-12 16:57:48-debug: lazy register asset handler scene
2025-9-12 16:57:48-debug: lazy register asset handler typescript
2025-9-12 16:57:48-debug: lazy register asset handler tiled-map
2025-9-12 16:57:48-debug: lazy register asset handler prefab
2025-9-12 16:57:48-debug: lazy register asset handler sprite-frame
2025-9-12 16:57:48-debug: lazy register asset handler buffer
2025-9-12 16:57:48-debug: lazy register asset handler image
2025-9-12 16:57:48-debug: lazy register asset handler alpha-image
2025-9-12 16:57:48-debug: lazy register asset handler sign-image
2025-9-12 16:57:48-debug: lazy register asset handler texture-cube
2025-9-12 16:57:48-debug: lazy register asset handler render-texture
2025-9-12 16:57:48-debug: lazy register asset handler texture
2025-9-12 16:57:48-debug: lazy register asset handler rt-sprite-frame
2025-9-12 16:57:48-debug: lazy register asset handler gltf
2025-9-12 16:57:48-debug: lazy register asset handler gltf-animation
2025-9-12 16:57:48-debug: lazy register asset handler erp-texture-cube
2025-9-12 16:57:48-debug: lazy register asset handler gltf-skeleton
2025-9-12 16:57:48-debug: lazy register asset handler gltf-mesh
2025-9-12 16:57:48-debug: lazy register asset handler gltf-material
2025-9-12 16:57:48-debug: lazy register asset handler texture-cube-face
2025-9-12 16:57:48-debug: lazy register asset handler gltf-embeded-image
2025-9-12 16:57:48-debug: lazy register asset handler fbx
2025-9-12 16:57:48-debug: lazy register asset handler gltf-scene
2025-9-12 16:57:48-debug: lazy register asset handler physics-material
2025-9-12 16:57:48-debug: lazy register asset handler effect
2025-9-12 16:57:48-debug: lazy register asset handler effect-header
2025-9-12 16:57:48-debug: lazy register asset handler audio-clip
2025-9-12 16:57:48-debug: lazy register asset handler animation-clip
2025-9-12 16:57:48-debug: lazy register asset handler animation-graph
2025-9-12 16:57:48-debug: lazy register asset handler animation-graph-variant
2025-9-12 16:57:48-debug: lazy register asset handler material
2025-9-12 16:57:48-debug: lazy register asset handler animation-mask
2025-9-12 16:57:48-debug: lazy register asset handler particle
2025-9-12 16:57:48-debug: lazy register asset handler ttf-font
2025-9-12 16:57:48-debug: lazy register asset handler bitmap-font
2025-9-12 16:57:48-debug: lazy register asset handler sprite-atlas
2025-9-12 16:57:48-debug: lazy register asset handler auto-atlas
2025-9-12 16:57:48-debug: lazy register asset handler label-atlas
2025-9-12 16:57:48-debug: lazy register asset handler render-pipeline
2025-9-12 16:57:48-debug: lazy register asset handler render-stage
2025-9-12 16:57:48-debug: lazy register asset handler instantiation-mesh
2025-9-12 16:57:48-debug: lazy register asset handler render-flow
2025-9-12 16:57:48-debug: lazy register asset handler instantiation-material
2025-9-12 16:57:48-debug: lazy register asset handler instantiation-skeleton
2025-9-12 16:57:48-debug: lazy register asset handler instantiation-animation
2025-9-12 16:57:48-debug: lazy register asset handler video-clip
2025-9-12 16:57:48-debug: asset-db:worker-startup-database[assets] (5515ms)
2025-9-12 16:57:48-debug: asset-db:start-database (5601ms)
2025-9-12 16:57:48-debug: asset-db:ready (8852ms)
2025-9-12 16:57:48-debug: fix the bug of updateDefaultUserData
2025-9-12 16:57:48-debug: init worker message success
2025-9-12 16:57:48-debug: programming:execute-script (3ms)
2025-9-12 16:57:48-debug: [Build Memory track]: builder:worker-init start:195.54MB, end 208.14MB, increase: 12.60MB
2025-9-12 16:57:48-debug: builder:worker-init (288ms)
2025-9-12 17:01:54-debug: refresh db internal success
2025-9-12 17:01:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:01:55-debug: refresh db assets success
2025-9-12 17:01:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:01:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:01:55-debug: asset-db:refresh-all-database (180ms)
2025-9-12 17:02:00-debug: refresh db internal success
2025-9-12 17:02:00-debug: refresh db assets success
2025-9-12 17:02:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:02:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:02:00-debug: asset-db:refresh-all-database (121ms)
2025-9-12 17:02:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:02:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:02:07-debug: refresh db internal success
2025-9-12 17:02:07-debug: refresh db assets success
2025-9-12 17:02:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:02:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:02:07-debug: asset-db:refresh-all-database (120ms)
2025-9-12 17:02:36-debug: refresh db internal success
2025-9-12 17:02:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:02:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:02:36-debug: refresh db assets success
2025-9-12 17:02:36-debug: asset-db:refresh-all-database (117ms)
2025-9-12 17:02:38-debug: refresh db internal success
2025-9-12 17:02:38-debug: refresh db assets success
2025-9-12 17:02:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:02:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:02:38-debug: asset-db:refresh-all-database (116ms)
2025-9-12 17:02:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:02:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:02:45-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:02:45-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-12 17:07:00-debug: refresh db internal success
2025-9-12 17:07:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:07:00-debug: refresh db assets success
2025-9-12 17:07:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:07:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:07:00-debug: asset-db:refresh-all-database (167ms)
2025-9-12 17:07:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:07:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:07:35-debug: refresh db internal success
2025-9-12 17:07:35-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:07:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:07:35-debug: refresh db assets success
2025-9-12 17:07:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:07:35-debug: asset-db:refresh-all-database (173ms)
2025-9-12 17:07:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:07:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:09:28-debug: refresh db internal success
2025-9-12 17:09:28-debug: refresh db assets success
2025-9-12 17:09:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:09:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:09:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:09:28-debug: asset-db:refresh-all-database (122ms)
2025-9-12 17:09:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:09:33-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:09:33-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-12 17:11:01-debug: refresh db internal success
2025-9-12 17:11:01-debug: refresh db assets success
2025-9-12 17:11:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:11:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:11:01-debug: asset-db:refresh-all-database (151ms)
2025-9-12 17:11:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:11:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:12:10-debug: refresh db internal success
2025-9-12 17:12:10-debug: refresh db assets success
2025-9-12 17:12:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:12:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:12:10-debug: asset-db:refresh-all-database (127ms)
2025-9-12 17:12:18-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:18-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-12 17:12:49-debug: refresh db internal success
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\luban
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\autogen\pb
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_mail\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\autogen\pb\cs_proto.js
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\autogen\pb\cs_proto.d.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\data\EnemyWave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\data\StageData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\manager\StageManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res\PlaneRes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_mail\prefab\ui\MailUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\PKHistoryUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\PKUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story\prefab\ui\StoryUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipCombine.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm\GM.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk\PK.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\PKHistoryCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\PKHistoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\StoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\skill\SkillComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\BagGrid.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\BagItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\SortTypeDropdown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display\EquipDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:12:50-debug: refresh db assets success
2025-9-12 17:12:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:12:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:12:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:12:50-debug: asset-db:refresh-all-database (222ms)
2025-9-12 17:12:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:13:50-debug: refresh db internal success
2025-9-12 17:13:50-debug: refresh db assets success
2025-9-12 17:13:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:13:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:13:50-debug: asset-db:refresh-all-database (144ms)
2025-9-12 17:14:00-debug: refresh db internal success
2025-9-12 17:14:00-debug: refresh db assets success
2025-9-12 17:14:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:14:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:14:00-debug: asset-db:refresh-all-database (118ms)
2025-9-12 17:14:11-debug: refresh db internal success
2025-9-12 17:14:11-debug: refresh db assets success
2025-9-12 17:14:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:14:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:14:11-debug: asset-db:refresh-all-database (114ms)
2025-9-12 17:15:35-debug: refresh db internal success
2025-9-12 17:15:35-debug: refresh db assets success
2025-9-12 17:15:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:15:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:15:35-debug: asset-db:refresh-all-database (147ms)
2025-9-12 17:25:32-debug: refresh db internal success
2025-9-12 17:25:33-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:25:33-debug: refresh db assets success
2025-9-12 17:25:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:25:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:25:33-debug: asset-db:refresh-all-database (155ms)
2025-9-12 17:25:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-12 17:25:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 17:30:39-debug: refresh db internal success
2025-9-12 17:30:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:30:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:30:39-debug: refresh db assets success
2025-9-12 17:30:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:30:39-debug: asset-db:refresh-all-database (156ms)
2025-9-12 17:30:39-debug: asset-db:worker-effect-data-processing (-2ms)
2025-9-12 17:30:39-debug: asset-db-hook-engine-extends-afterRefresh (-2ms)
2025-9-12 17:49:48-debug: refresh db internal success
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\ObjectPool.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:49:49-debug: refresh db assets success
2025-9-12 17:49:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:49:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:49:49-debug: asset-db:refresh-all-database (171ms)
2025-9-12 17:50:10-debug: refresh db internal success
2025-9-12 17:50:10-debug: refresh db assets success
2025-9-12 17:50:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:50:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:50:10-debug: asset-db:refresh-all-database (146ms)
2025-9-12 17:50:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:50:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:50:18-debug: refresh db internal success
2025-9-12 17:50:18-debug: refresh db assets success
2025-9-12 17:50:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:50:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:50:18-debug: asset-db:refresh-all-database (121ms)
2025-9-12 17:50:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:50:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:50:23-debug: refresh db internal success
2025-9-12 17:50:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:50:23-debug: refresh db assets success
2025-9-12 17:50:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:50:23-debug: asset-db:refresh-all-database (111ms)
2025-9-12 17:50:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:50:30-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:50:30-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-12 17:50:41-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:50:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-12 17:51:01-debug: refresh db internal success
2025-9-12 17:51:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:51:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:51:01-debug: refresh db assets success
2025-9-12 17:51:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:51:01-debug: asset-db:refresh-all-database (159ms)
2025-9-12 17:51:55-debug: refresh db internal success
2025-9-12 17:51:55-debug: refresh db assets success
2025-9-12 17:51:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:51:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:51:55-debug: asset-db:refresh-all-database (154ms)
2025-9-12 17:51:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:51:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:52:20-debug: refresh db internal success
2025-9-12 17:52:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:52:21-debug: refresh db assets success
2025-9-12 17:52:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:52:21-debug: asset-db:refresh-all-database (168ms)
2025-9-12 17:52:27-debug: refresh db internal success
2025-9-12 17:52:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:52:27-debug: refresh db assets success
2025-9-12 17:52:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:52:27-debug: asset-db:refresh-all-database (114ms)
2025-9-12 17:52:30-debug: refresh db internal success
2025-9-12 17:52:30-debug: refresh db assets success
2025-9-12 17:52:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:52:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:52:30-debug: asset-db:refresh-all-database (116ms)
2025-9-12 17:52:33-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab...
2025-9-12 17:52:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:52:33-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-9-12 17:52:33-debug: refresh db internal success
2025-9-12 17:52:34-debug: refresh db assets success
2025-9-12 17:52:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:52:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:52:34-debug: asset-db:refresh-all-database (125ms)
2025-9-12 17:52:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:53:25-debug: refresh db internal success
2025-9-12 17:53:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:53:25-debug: refresh db assets success
2025-9-12 17:53:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:53:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:53:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:53:25-debug: asset-db:refresh-all-database (162ms)
2025-9-12 17:53:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:53:31-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab...
2025-9-12 17:53:31-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:53:31-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-9-12 17:53:32-debug: refresh db internal success
2025-9-12 17:53:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:53:32-debug: refresh db assets success
2025-9-12 17:53:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:53:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:53:32-debug: asset-db:refresh-all-database (120ms)
2025-9-12 17:53:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:53:48-debug: refresh db internal success
2025-9-12 17:53:48-debug: refresh db assets success
2025-9-12 17:53:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:53:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:53:48-debug: asset-db:refresh-all-database (145ms)
2025-9-12 17:53:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:53:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:55:21-debug: refresh db internal success
2025-9-12 17:55:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:55:21-debug: refresh db assets success
2025-9-12 17:55:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:55:21-debug: asset-db:refresh-all-database (122ms)
2025-9-12 17:55:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:55:47-debug: refresh db internal success
2025-9-12 17:55:47-debug: refresh db assets success
2025-9-12 17:55:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:55:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:55:47-debug: asset-db:refresh-all-database (143ms)
2025-9-12 17:55:47-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-12 17:55:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:56:45-debug: refresh db internal success
2025-9-12 17:56:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:56:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:56:45-debug: refresh db assets success
2025-9-12 17:56:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:56:45-debug: asset-db:refresh-all-database (159ms)
2025-9-12 17:56:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:56:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:57:52-debug: refresh db internal success
2025-9-12 17:57:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:57:52-debug: refresh db assets success
2025-9-12 17:57:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:57:52-debug: asset-db:refresh-all-database (166ms)
2025-9-12 17:57:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:57:52-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 17:57:58-debug: refresh db internal success
2025-9-12 17:57:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:57:58-debug: refresh db assets success
2025-9-12 17:57:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:57:58-debug: asset-db:refresh-all-database (152ms)
2025-9-12 17:57:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:57:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:58:38-debug: refresh db internal success
2025-9-12 17:58:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:58:39-debug: refresh db assets success
2025-9-12 17:58:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:58:39-debug: asset-db:refresh-all-database (120ms)
2025-9-12 17:58:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 17:59:16-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab...
2025-9-12 17:59:16-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 17:59:16-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-9-12 17:59:17-debug: refresh db internal success
2025-9-12 17:59:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 17:59:17-debug: refresh db assets success
2025-9-12 17:59:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 17:59:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 17:59:17-debug: asset-db:refresh-all-database (119ms)
2025-9-12 17:59:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:00:02-debug: refresh db internal success
2025-9-12 18:00:02-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:00:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:00:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:00:02-debug: refresh db assets success
2025-9-12 18:00:02-debug: asset-db:refresh-all-database (163ms)
2025-9-12 18:00:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:00:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:01:43-debug: refresh db internal success
2025-9-12 18:01:43-debug: refresh db assets success
2025-9-12 18:01:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:01:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:01:43-debug: asset-db:refresh-all-database (147ms)
2025-9-12 18:01:43-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-12 18:01:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 18:02:34-debug: refresh db internal success
2025-9-12 18:02:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:02:34-debug: refresh db assets success
2025-9-12 18:02:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:02:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:02:34-debug: asset-db:refresh-all-database (156ms)
2025-9-12 18:02:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:02:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 18:03:02-debug: refresh db internal success
2025-9-12 18:03:02-debug: refresh db assets success
2025-9-12 18:03:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:03:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:03:02-debug: asset-db:refresh-all-database (119ms)
2025-9-12 18:03:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:03:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:03:30-debug: refresh db internal success
2025-9-12 18:03:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:03:30-debug: refresh db assets success
2025-9-12 18:03:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:03:30-debug: asset-db:refresh-all-database (130ms)
2025-9-12 18:03:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:03:30-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 18:03:33-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab...
2025-9-12 18:03:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:03:33-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-9-12 18:03:33-debug: refresh db internal success
2025-9-12 18:03:33-debug: refresh db assets success
2025-9-12 18:03:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:03:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:03:33-debug: asset-db:refresh-all-database (117ms)
2025-9-12 18:03:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:03:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:03:57-debug: refresh db internal success
2025-9-12 18:03:57-debug: refresh db assets success
2025-9-12 18:03:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:03:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:03:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:03:57-debug: asset-db:refresh-all-database (115ms)
2025-9-12 18:03:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:08:36-debug: refresh db internal success
2025-9-12 18:08:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:08:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:08:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:08:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:08:37-debug: refresh db assets success
2025-9-12 18:08:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:08:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:08:37-debug: asset-db:refresh-all-database (152ms)
2025-9-12 18:11:03-debug: refresh db internal success
2025-9-12 18:11:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:11:03-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:11:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:11:03-debug: refresh db assets success
2025-9-12 18:11:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:11:03-debug: asset-db:refresh-all-database (155ms)
2025-9-12 18:11:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 18:11:32-debug: refresh db internal success
2025-9-12 18:11:32-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:11:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:11:32-debug: refresh db assets success
2025-9-12 18:11:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:11:32-debug: asset-db:refresh-all-database (118ms)
2025-9-12 18:17:14-debug: refresh db internal success
2025-9-12 18:17:15-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:17:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-12 18:17:15-debug: refresh db assets success
2025-9-12 18:17:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 18:17:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 18:17:15-debug: asset-db:refresh-all-database (164ms)
2025-9-12 18:17:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 18:17:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
