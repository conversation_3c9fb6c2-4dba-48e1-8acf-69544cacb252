{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/BulletEventConditions.ts"], "names": ["BulletConditionBase", "BulletCondition_Duration", "BulletCondition_ElapsedTime", "BulletCondition_PosX", "BulletCondition_PosY", "BulletCondition_Damage", "BulletCondition_Speed", "BulletCondition_SpeedAngle", "BulletCondition_Acceleration", "BulletCondition_AccelerationAngle", "BulletCondition_Scale", "BulletCondition_ColorR", "BulletCondition_ColorG", "BulletCondition_ColorB", "BulletCondition_FacingMoveDir", "BulletCondition_Destructive", "BulletCondition_DestructiveOnHit", "EventConditionBase", "Comparer", "eCompareOp", "evaluate", "context", "compare", "bullet", "prop", "duration", "value", "_targetValue", "data", "compareOp", "elapsedTime", "node", "position", "x", "y", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "r", "g", "b", "Equal", "isFacingMoveDir", "NotEqual", "isDestructive", "isDestructiveOnHit"], "mappings": ";;;6DAIaA,mB,EAGAC,wB,EAOAC,2B,EAMAC,oB,EAMAC,oB,EAMAC,sB,EAOAC,qB,EAMAC,0B,EAMAC,4B,EAMAC,iC,EAMAC,qB,EAMAC,sB,EAMAC,sB,EAMAC,sB,EAMAC,6B,EAaAC,2B,EAaAC,gC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjHJC,MAAAA,kB,iBAAAA,kB;;AACmBC,MAAAA,Q,iBAAAA,Q;;AACCC,MAAAA,U,iBAAAA,U;;;;;qCAEhBnB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,oDAAqD,E;;0CAG/CC,wB,GAAN,MAAMA,wBAAN,SAAuCD,mBAAvC,CAA2D;AACvDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBC,QAArB,CAA8BC,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAJ6D,O;;6CAOrD3B,2B,GAAN,MAAMA,2BAAN,SAA0CF,mBAA1C,CAA8D;AAC1DoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBO,WAAjC,EAA8C,KAAKH,YAAnD,EAAiE,KAAKC,IAAL,CAAUC,SAA3E,CAAP;AACH;;AAHgE,O;;sCAMxD1B,oB,GAAN,MAAMA,oBAAN,SAAmCH,mBAAnC,CAAuD;AACnDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBQ,IAAhB,CAAqBC,QAArB,CAA8BC,CAA/C,EAAkD,KAAKN,YAAvD,EAAqE,KAAKC,IAAL,CAAUC,SAA/E,CAAP;AACH;;AAHyD,O;;sCAMjDzB,oB,GAAN,MAAMA,oBAAN,SAAmCJ,mBAAnC,CAAuD;AACnDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBQ,IAAhB,CAAqBC,QAArB,CAA8BE,CAA/C,EAAkD,KAAKP,YAAvD,EAAqE,KAAKC,IAAL,CAAUC,SAA/E,CAAP;AACH;;AAHyD,O;;wCAMjDxB,sB,GAAN,MAAMA,sBAAN,SAAqCL,mBAArC,CAAyD;AACrDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAO,KAAP;AACH;;AAJ2D,O;;uCAOnDf,qB,GAAN,MAAMA,qBAAN,SAAoCN,mBAApC,CAAwD;AACpDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBW,KAArB,CAA2BT,KAA5C,EAAmD,KAAKC,YAAxD,EAAsE,KAAKC,IAAL,CAAUC,SAAhF,CAAP;AACH;;AAH0D,O;;4CAMlDtB,0B,GAAN,MAAMA,0BAAN,SAAyCP,mBAAzC,CAA6D;AACzDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBY,UAArB,CAAgCV,KAAjD,EAAwD,KAAKC,YAA7D,EAA2E,KAAKC,IAAL,CAAUC,SAArF,CAAP;AACH;;AAH+D,O;;8CAMvDrB,4B,GAAN,MAAMA,4BAAN,SAA2CR,mBAA3C,CAA+D;AAC3DoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBa,YAArB,CAAkCX,KAAnD,EAA0D,KAAKC,YAA/D,EAA6E,KAAKC,IAAL,CAAUC,SAAvF,CAAP;AACH;;AAHiE,O;;mDAMzDpB,iC,GAAN,MAAMA,iCAAN,SAAgDT,mBAAhD,CAAoE;AAChEoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBc,iBAArB,CAAuCZ,KAAxD,EAA+D,KAAKC,YAApE,EAAkF,KAAKC,IAAL,CAAUC,SAA5F,CAAP;AACH;;AAHsE,O;;uCAM9DnB,qB,GAAN,MAAMA,qBAAN,SAAoCV,mBAApC,CAAwD;AACpDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBQ,IAAhB,CAAqBQ,KAArB,CAA2BN,CAA5C,EAA+C,KAAKN,YAApD,EAAkE,KAAKC,IAAL,CAAUC,SAA5E,CAAP;AACH;;AAH0D,O;;wCAMlDlB,sB,GAAN,MAAMA,sBAAN,SAAqCX,mBAArC,CAAyD;AACrDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBgB,KAArB,CAA2Bd,KAA3B,CAAiCe,CAAlD,EAAqD,KAAKd,YAA1D,EAAwE,KAAKC,IAAL,CAAUC,SAAlF,CAAP;AACH;;AAH2D,O;;wCAMnDjB,sB,GAAN,MAAMA,sBAAN,SAAqCZ,mBAArC,CAAyD;AACrDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBgB,KAArB,CAA2Bd,KAA3B,CAAiCgB,CAAlD,EAAqD,KAAKf,YAA1D,EAAwE,KAAKC,IAAL,CAAUC,SAAlF,CAAP;AACH;;AAH2D,O;;wCAMnDhB,sB,GAAN,MAAMA,sBAAN,SAAqCb,mBAArC,CAAyD;AACrDoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBgB,KAArB,CAA2Bd,KAA3B,CAAiCiB,CAAlD,EAAqD,KAAKhB,YAA1D,EAAwE,KAAKC,IAAL,CAAUC,SAAlF,CAAP;AACH;;AAH2D,O;;+CAMnDf,6B,GAAN,MAAMA,6BAAN,SAA4Cd,mBAA5C,CAAgE;AAC5DoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWe,KAAhB;AACI,qBAAOvB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBqB,eAArB,CAAqCnB,KAArC,MAAgD,KAAKC,YAAL,KAAsB,CAAtE,IAA2E,IAA3E,GAAkF,KAAzF;;AACJ,iBAAK;AAAA;AAAA,0CAAWmB,QAAhB;AACI,qBAAOzB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBqB,eAArB,CAAqCnB,KAArC,MAAgD,KAAKC,YAAL,KAAsB,CAAtE,IAA2E,IAA3E,GAAkF,KAAzF;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVkE,O;;6CAa1DZ,2B,GAAN,MAAMA,2BAAN,SAA0Cf,mBAA1C,CAA8D;AAC1DoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWe,KAAhB;AACI,qBAAOvB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBuB,aAArB,CAAmCrB,KAAnC,MAA8C,KAAKC,YAAL,KAAsB,CAApE,IAAyE,IAAzE,GAAgF,KAAvF;;AACJ,iBAAK;AAAA;AAAA,0CAAWmB,QAAhB;AACI,qBAAOzB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBuB,aAArB,CAAmCrB,KAAnC,MAA8C,KAAKC,YAAL,KAAsB,CAApE,IAAyE,IAAzE,GAAgF,KAAvF;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVgE,O;;kDAaxDX,gC,GAAN,MAAMA,gCAAN,SAA+ChB,mBAA/C,CAAmE;AAC/DoB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWe,KAAhB;AACI,qBAAOvB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBwB,kBAArB,CAAwCtB,KAAxC,MAAmD,KAAKC,YAAL,KAAsB,CAAzE,IAA8E,IAA9E,GAAqF,KAA5F;;AACJ,iBAAK;AAAA;AAAA,0CAAWmB,QAAhB;AACI,qBAAOzB,OAAO,CAACE,MAAR,CAAgBC,IAAhB,CAAqBwB,kBAArB,CAAwCtB,KAAxC,MAAmD,KAAKC,YAAL,KAAsB,CAAzE,IAA8E,IAA9E,GAAqF,KAA5F;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVqE,O", "sourcesContent": ["import { EventConditionBase } from \"./IEventCondition\";\r\nimport { EventGroupContext, Comparer } from \"../EventGroup\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class BulletConditionBase extends EventConditionBase {\r\n}\r\n\r\nexport class BulletCondition_Duration extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // Custom evaluation logic for active condition\r\n        return Comparer.compare(context.bullet!.prop.duration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ElapsedTime extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.elapsedTime, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_PosX extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.position.x, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_PosY extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.position.y, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Damage extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // return Comparer.compare(context.bullet!.damage.value, this._targetValue, this.data.compareOp);\r\n        return false;\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Speed extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.speed.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_SpeedAngle extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.speedAngle.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Acceleration extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.acceleration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_AccelerationAngle extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.accelerationAngle.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Scale extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.scale.x, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorR extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.color.value.r, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorG extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.color.value.g, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorB extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.prop.color.value.b, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_FacingMoveDir extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {        \r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.bullet!.prop.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.bullet!.prop.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Destructive extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.bullet!.prop.isDestructive.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.bullet!.prop.isDestructive.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class BulletCondition_DestructiveOnHit extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.bullet!.prop.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.bullet!.prop.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}"]}