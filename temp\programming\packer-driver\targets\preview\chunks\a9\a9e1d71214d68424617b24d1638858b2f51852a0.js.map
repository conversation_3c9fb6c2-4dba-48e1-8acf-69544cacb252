{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/StageManager.ts"], "names": ["StageManager", "SingletonBase", "StageData", "GameIns", "MyApp", "constructor", "_allStageDataArr", "_curStageDataArr", "initConfig", "stages", "lubanTables", "TbStage", "getDataList", "data", "stage", "loadJson", "push", "initBattle", "mainId", "subId", "splice", "for<PERSON>ach", "mainStage", "subStage", "getBossTips", "gameStart", "waveManager", "setEnemyActions", "checkStage"], "mappings": ";;;6DAKaA,Y;;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;8BAEIJ,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAK1DK,QAAAA,WAAW,GAAG;AACV;AADU,eAHdC,gBAGc,GAHiB,EAGjB;AAAA,eAFdC,gBAEc,GAFiB,EAEjB;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAE;AACR,cAAIC,MAAM,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,WAA1B,EAAb;;AACA,eAAK,IAAIC,IAAT,IAAiBJ,MAAjB,EAAyB;AACrB,gBAAMK,KAAK,GAAG;AAAA;AAAA,yCAAd;AACAA,YAAAA,KAAK,CAACC,QAAN,CAAeF,IAAf;;AACA,iBAAKP,gBAAL,CAAsBU,IAAtB,CAA2BF,KAA3B;AACH;AACJ;;AAEDG,QAAAA,UAAU,CAACC,MAAD,EAAgBC,KAAhB,EAA8B;AACpC,eAAKZ,gBAAL,CAAsBa,MAAtB,CAA6B,CAA7B;;AACA,eAAKd,gBAAL,CAAsBe,OAAtB,CAA8BP,KAAK,IAAI;AACnC,gBAAIA,KAAK,CAACQ,SAAN,KAAoBJ,MAApB,IAA8BJ,KAAK,CAACS,QAAN,KAAmBJ,KAArD,EAA4D;AACxD,mBAAKZ,gBAAL,CAAsBS,IAAtB,CAA2BF,KAA3B;AACH;AACJ,WAJD;AAKH;;AAEDU,QAAAA,WAAW,GAAG;AACV,iBAAO,EAAP;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,WAAR,CAAoBC,eAApB,CAAoC,KAAKpB,gBAAzC;AACH;;AAEDqB,QAAAA,UAAU,CAACV,MAAD,EAAeC,KAAf,EAAoC;AAC1C;AACA;AACA;AACA;AACA;AACA,iBAAO,KAAP;AACH;;AA3CyD,O", "sourcesContent": ["import { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { StageData} from \"../data/StageData\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\n\r\nexport class StageManager extends SingletonBase<StageManager> {\r\n\r\n    _allStageDataArr:StageData[] = [];\r\n    _curStageDataArr:StageData[] = [];\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig(){\r\n        let stages = MyApp.lubanTables.TbStage.getDataList();\r\n        for (let data of stages) {\r\n            const stage = new StageData();\r\n            stage.loadJson(data);\r\n            this._allStageDataArr.push(stage);\r\n        }\r\n    }\r\n\r\n    initBattle(mainId:number, subId:number) {\r\n        this._curStageDataArr.splice(0);\r\n        this._allStageDataArr.forEach(stage => {\r\n            if (stage.mainStage === mainId && stage.subStage === subId) {\r\n                this._curStageDataArr.push(stage);\r\n            }\r\n        });\r\n    }\r\n\r\n    getBossTips() {\r\n        return \"\";\r\n    }\r\n\r\n    gameStart() {\r\n        GameIns.waveManager.setEnemyActions(this._curStageDataArr);\r\n    }\r\n\r\n    checkStage(mainId:number,subId:number):boolean{\r\n        // for (const stage of this._allStageDataArr) {\r\n        //     if (stage.mainStage === mainId && stage.subStage === subId) {\r\n        //         return true;\r\n        //     }\r\n        // }\r\n        return false;\r\n    }\r\n}"]}