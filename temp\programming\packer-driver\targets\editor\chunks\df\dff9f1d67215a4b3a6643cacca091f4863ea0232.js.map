{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/GameIns.ts"], "names": ["_GameIns", "SingletonBase", "GameDataManager", "MainPlaneManager", "BattleManager", "BossManager", "EnemyManager", "GameRuleManager", "HurtEffectManager", "GamePlaneManager", "StageManager", "GameResManager", "SceneManager", "WaveManager", "FColliderManager", "gameMainUI", "battleManager", "getInstance", "boss<PERSON><PERSON><PERSON>", "enemyManager", "gameDataManager", "gameRuleManager", "hurtEffectManager", "mainPlaneManager", "gamePlaneManager", "stageManager", "gameResManager", "sceneManager", "waveManager", "fColliderManager", "instance", "GameIns"], "mappings": ";;;gPAgBMA,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBGC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,gB,kBAAAA,gB;;AACAC,MAAAA,Y,kBAAAA,Y;;AACFC,MAAAA,c;;AACEC,MAAAA,Y,kBAAAA,Y;;AACFC,MAAAA,W;;AAEAC,MAAAA,gB;;;;AAL+C;AAOhDd,MAAAA,Q,GAAN,MAAMA,QAAN;AAAA;AAAA,0CAA+C;AAAA;AAAA;AAAA,eAe3Ce,UAf2C,GAehB,IAfgB;AAAA;;AAC1B,YAAbC,aAAa,GAAG;AAAE,iBAAO;AAAA;AAAA,8CAAcC,WAAd;AAAA;AAAA,6CAAP;AAAiE;;AACxE,YAAXC,WAAW,GAAG;AAAE,iBAAO;AAAA;AAAA,0CAAYD,WAAZ;AAAA;AAAA,yCAAP;AAA2D;;AAC/D,YAAZE,YAAY,GAAG;AAAE,iBAAO;AAAA;AAAA,4CAAaF,WAAb;AAAA;AAAA,2CAAP;AAA8D;;AAChE,YAAfG,eAAe,GAAG;AAAE,iBAAO;AAAA;AAAA,kDAAgBH,WAAhB;AAAA;AAAA,iDAAP;AAAuE;;AAC5E,YAAfI,eAAe,GAAG;AAAE,iBAAO;AAAA;AAAA,kDAAgBJ,WAAhB;AAAA;AAAA,iDAAP;AAAuE;;AAC1E,YAAjBK,iBAAiB,GAAG;AAAE,iBAAO;AAAA;AAAA,sDAAkBL,WAAlB;AAAA;AAAA,qDAAP;AAA6E;;AACnF,YAAhBM,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBN,WAAjB;AAAA;AAAA,mDAAP;AAA0E;;AAC/E,YAAhBO,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBP,WAAjB;AAAA;AAAA,mDAAP;AAA0E;;AACnF,YAAZQ,YAAY,GAAG;AAAE,iBAAO;AAAA;AAAA,4CAAaR,WAAb;AAAA;AAAA,2CAAP;AAA8D;;AACjE,YAAdS,cAAc,GAAG;AAAE,iBAAO;AAAA;AAAA,gDAAeT,WAAf;AAAA;AAAA,+CAAP;AAAoE;;AAC3E,YAAZU,YAAY,GAAG;AAAE,iBAAO;AAAA;AAAA,4CAAaV,WAAb;AAAA;AAAA,2CAAP;AAA8D;;AACpE,YAAXW,WAAW,GAAG;AAAE,iBAAO;AAAA;AAAA,0CAAYX,WAAZ;AAAA;AAAA,yCAAP;AAA2D;;AAC3D,YAAhBY,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBC,QAAxB;AAAmC;;AAbjB,O;;yBAoBlCC,O,GAAoB/B,QAAQ,CAACiB,WAAT,CAA+BjB,QAA/B,C", "sourcesContent": ["import { SingletonBase } from \"../core/base/SingletonBase\";\r\nimport { GameDataManager } from \"./manager/GameDataManager\";\r\nimport { MainPlaneManager } from \"./manager/MainPlaneManager\";\r\nimport { BattleManager } from \"./manager/BattleManager\";\r\nimport { BossManager } from \"./manager/BossManager\";\r\nimport { EnemyManager } from \"./manager/EnemyManager\";\r\nimport { GameRuleManager } from \"./manager/GameRuleManager\";\r\nimport { HurtEffectManager } from \"./manager/HurtEffectManager\";\r\nimport { GamePlaneManager } from \"./manager/GamePlaneManager\";\r\nimport { StageManager } from \"./manager/StageManager\";;\r\nimport GameResManager from \"./manager/GameResManager\";\r\nimport { SceneManager } from \"./manager/SceneManager\";\r\nimport WaveManager from \"./manager/WaveManager\";\r\nimport { GameMain } from \"./scenes/GameMain\";\r\nimport FColliderManager from \"./collider-system/FColliderManager\";\r\n\r\nclass _GameIns extends SingletonBase<_GameIns> {\r\n    get battleManager() { return BattleManager.getInstance<BattleManager>(BattleManager); }\r\n    get bossManager() { return BossManager.getInstance<BossManager>(BossManager); }\r\n    get enemyManager() { return EnemyManager.getInstance<EnemyManager>(EnemyManager); }\r\n    get gameDataManager() { return GameDataManager.getInstance<GameDataManager>(GameDataManager); }\r\n    get gameRuleManager() { return GameRuleManager.getInstance<GameRuleManager>(GameRuleManager); }\r\n    get hurtEffectManager() { return HurtEffectManager.getInstance<HurtEffectManager>(HurtEffectManager); }\r\n    get mainPlaneManager() { return MainPlaneManager.getInstance<MainPlaneManager>(MainPlaneManager); }\r\n    get gamePlaneManager() { return GamePlaneManager.getInstance<GamePlaneManager>(GamePlaneManager); }\r\n    get stageManager() { return StageManager.getInstance<StageManager>(StageManager); }\r\n    get gameResManager() { return GameResManager.getInstance<GameResManager>(GameResManager); }\r\n    get sceneManager() { return SceneManager.getInstance<SceneManager>(SceneManager); }\r\n    get waveManager() { return WaveManager.getInstance<WaveManager>(WaveManager); }\r\n    get fColliderManager() { return FColliderManager.instance; }\r\n\r\n    gameMainUI:GameMain|null = null;\r\n}\r\n\r\n\r\n\r\nexport const GameIns: _GameIns = _GameIns.getInstance<_GameIns>(_GameIns);"]}