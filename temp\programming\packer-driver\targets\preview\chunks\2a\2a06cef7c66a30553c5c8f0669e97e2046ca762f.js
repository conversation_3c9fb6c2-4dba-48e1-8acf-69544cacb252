System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2, WaveData, eSpawnOrder, GameIns, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec6, _dec7, _dec8, _dec9, _dec10, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _dec23, _dec24, _class7, _class8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _crd, ccclass, property, executeInEditMode, WaveTrack, WaveTrackGroup, Wave;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfWaveData(extras) {
    _reporterNs.report("WaveData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSpawnOrder(extras) {
    _reporterNs.report("eSpawnOrder", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/scripts/Game/GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCBoolean = _cc.CCBoolean;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      WaveData = _unresolved_2.WaveData;
      eSpawnOrder = _unresolved_2.eSpawnOrder;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("WaveTrack", WaveTrack = (_dec = ccclass('WaveTrack'), _dec2 = property(CCInteger), _dec3 = property(CCFloat), _dec4 = property(CCFloat), _dec5 = property(CCFloat), _dec(_class = (_class2 = class WaveTrack {
        constructor() {
          _initializerDefineProperty(this, "id", _descriptor, this);

          _initializerDefineProperty(this, "speed", _descriptor2, this);

          _initializerDefineProperty(this, "accelerate", _descriptor3, this);

          _initializerDefineProperty(this, "Interval", _descriptor4, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "id", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "accelerate", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "Interval", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));

      _export("WaveTrackGroup", WaveTrackGroup = (_dec6 = ccclass('WaveTrackGroup'), _dec7 = property(CCInteger), _dec8 = property(CCInteger), _dec9 = property(CCInteger), _dec10 = property([WaveTrack]), _dec6(_class4 = (_class5 = class WaveTrackGroup {
        constructor() {
          _initializerDefineProperty(this, "type", _descriptor5, this);

          _initializerDefineProperty(this, "loopNum", _descriptor6, this);

          _initializerDefineProperty(this, "formIndex", _descriptor7, this);

          _initializerDefineProperty(this, "tracks", _descriptor8, this);
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "type", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "loopNum", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "formIndex", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "tracks", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class5)) || _class4));

      _export("Wave", Wave = (_dec11 = ccclass('Wave'), _dec12 = executeInEditMode(), _dec13 = property(CCBoolean), _dec14 = property(CCInteger), _dec15 = property(CCFloat), _dec16 = property(CCInteger), _dec17 = property(CCInteger), _dec18 = property(CCFloat), _dec19 = property(CCInteger), _dec20 = property(CCFloat), _dec21 = property(Vec2), _dec22 = property([WaveTrackGroup]), _dec23 = property([CCFloat]), _dec24 = property({
        type: _crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
          error: Error()
        }), WaveData) : WaveData
      }), _dec11(_class7 = _dec12(_class7 = (_class8 = class Wave extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "enemyGroupID", _descriptor9, this);

          _initializerDefineProperty(this, "delay", _descriptor10, this);

          _initializerDefineProperty(this, "planeID", _descriptor11, this);

          _initializerDefineProperty(this, "planeType", _descriptor12, this);

          _initializerDefineProperty(this, "interval", _descriptor13, this);

          _initializerDefineProperty(this, "num", _descriptor14, this);

          _initializerDefineProperty(this, "rotateSpeed", _descriptor15, this);

          _initializerDefineProperty(this, "startPos", _descriptor16, this);

          _initializerDefineProperty(this, "trackGroups", _descriptor17, this);

          _initializerDefineProperty(this, "firstShootDelay", _descriptor18, this);

          _initializerDefineProperty(this, "waveData", _descriptor19, this);

          /*
           * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
           */
          this._isCompleted = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._nextSpawnIndex = 0;
          this._spawnQueue = [];
        }

        set play(value) {}

        get play() {
          return false;
        }

        // 当前波次是否已完成
        get isCompleted() {
          return this._isCompleted;
        }

        _reset() {
          this._isCompleted = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._nextSpawnIndex = 0;
          this._spawnQueue = this.waveData.planeList;
        }

        trigger() {
          this._reset(); // shuffle spawn queue


          if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
            error: Error()
          }), eSpawnOrder) : eSpawnOrder).Random) {
            this._spawnQueue = this._spawnQueue.sort(() => Math.random() - 0.5);
          }
        } // tick wave


        tick(dtInMiliseconds) {
          if (this._isCompleted) return;
          this._waveElapsedTime += dtInMiliseconds;

          if (this._waveElapsedTime >= this._nextSpawnTime) {
            if (!this.spawn()) {
              this._isCompleted = true;
            }
          }
        }

        spawn() {
          if (this._nextSpawnIndex >= this._spawnQueue.length) {
            return false;
          }

          this.spawnSingle(this._nextSpawnIndex++);
          this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval;
          return true;
        }

        spawnSingle(index) {
          if (index >= this._spawnQueue.length) {
            return;
          }

          var spawnPos = this.waveData.spawnPos;
          var spawnAngle = this.waveData.spawnAngle.eval();
          var spawnSpeed = this.waveData.spawnSpeed;
          this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle, spawnSpeed);
        }

        createPlane(planeId, pos, angle, speed) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var enemy = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.addPlane(planeId);

            if (enemy) {
              // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);
              console.log("createPlane", planeId, pos, angle, speed);
              enemy.setStandByTime(0);
              enemy.setPos(pos.x, pos.y);
              enemy.initMove(speed, angle);
              enemy.initDelayDestroy(_this.waveData.delayDestroy);
            }
          })();
        }

      }, (_applyDecoratedDescriptor(_class8.prototype, "play", [_dec13], Object.getOwnPropertyDescriptor(_class8.prototype, "play"), _class8.prototype), _descriptor9 = _applyDecoratedDescriptor(_class8.prototype, "enemyGroupID", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class8.prototype, "delay", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class8.prototype, "planeID", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class8.prototype, "planeType", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class8.prototype, "interval", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class8.prototype, "num", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class8.prototype, "rotateSpeed", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class8.prototype, "startPos", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new Vec2();
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class8.prototype, "trackGroups", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class8.prototype, "firstShootDelay", [_dec23], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class8.prototype, "waveData", [_dec24], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
            error: Error()
          }), WaveData) : WaveData)();
        }
      })), _class8)) || _class7) || _class7));

      _crd = false;
    }
  };
});
//# sourceMappingURL=2a06cef7c66a30553c5c8f0669e97e2046ca762f.js.map