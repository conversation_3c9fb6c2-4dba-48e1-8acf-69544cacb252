System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, SingletonBase, GameIns, BattleLayer, GameConst, GameEnum, FCollider, GamePlaneManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../collider-system/FCollider", _context.meta, extras);
  }

  _export("GamePlaneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      BattleLayer = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameConst = _unresolved_5.GameConst;
    }, function (_unresolved_6) {
      GameEnum = _unresolved_6.GameEnum;
    }, function (_unresolved_7) {
      FCollider = _unresolved_7.default;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['Vec2', 'Vec3']);

      _export("GamePlaneManager", GamePlaneManager = class GamePlaneManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.enemyTarget = null;
          this.enemyCollider = null;
          this.frienPlane = [];
          this.m_planeTable = void 0;
          this.enemyTargetPos = {
            x: 0,
            y: 0
          };
        }

        /**
         * 清理数据
         */
        clear() {
          this.enemyTarget = null;
          this.enemyCollider = null;
          this.frienPlane.forEach(plane => {
            if (plane && plane.node) {
              plane.node.destroy();
            }
          });
          this.frienPlane = [];
        }
        /**
         * 获取飞机配置
         * @param id 配置 ID
         * @returns 配置数据
         */


        getConfig(id) {
          return this.m_planeTable.getRecorder(id);
        }
        /**
         * 添加友军飞机
         * @param plane 飞机对象
         */


        addFriendPlane(plane) {
          (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).me.addFriendPlane(plane);
          this.frienPlane.push(plane);
        }
        /**
         * 移除友军飞机
         * @param plane 飞机对象
         */


        removeFriendPlane(plane) {
          const index = this.frienPlane.indexOf(plane);

          if (index >= 0) {
            this.frienPlane.splice(index, 1);
          }

          if (plane && plane.node) {
            plane.node.parent = null;
          }
        }
        /**
         * 获取两点之间的距离
         * @param point1 点1
         * @param point2 点2
         * @returns 距离
         */


        getLength(point1, point2) {
          return Math.sqrt((point1.x - point2.x) * (point1.x - point2.x) + (point1.y - point2.y) * (point1.y - point2.y));
        }

        getRandomTargetEnemy() {
          const viewHeight = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight;
          const targets = [];
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType; // 遍历敌机

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.planes.forEach(enemy => {
            switch (enemy.type) {
              case EnemyType.Normal:
                const normalCollider = enemy.getComponent(_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
                  error: Error()
                }), FCollider) : FCollider);

                if (!(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).fColliderManager.isOutOfScreen(normalCollider == null ? void 0 : normalCollider.aabb)) {
                  targets.push(normalCollider);
                }

                break;
            }
          }); // 随机选择一个目标

          const randomIndex = Math.floor(Math.random() * targets.length);
          return targets[randomIndex] || null;
        }

        getTargetEnemy() {
          const mainPlanePos = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.position;
          let closestDistance = Infinity;
          let closestEnemy = null;
          let closestCollider = null;
          const viewHeight = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight;
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType; // 遍历敌机

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.planes.forEach(enemy => {
            if (!enemy.isDead) {
              switch (enemy.type) {
                case EnemyType.Normal:
                  if (enemy.colliderEnabled) {
                    const normalCollider = enemy.getComponent(_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
                      error: Error()
                    }), FCollider) : FCollider);

                    if (!(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).fColliderManager.isOutOfScreen(normalCollider == null ? void 0 : normalCollider.aabb)) {
                      const enemyPos = enemy.node.position;
                      const distance = this.getLength(mainPlanePos, enemyPos);

                      if (distance < closestDistance) {
                        closestDistance = distance;
                        closestEnemy = enemy;
                        closestCollider = enemy.getComponent(_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
                          error: Error()
                        }), FCollider) : FCollider);
                      }
                    }
                  }

                  break;
              }
            }
          }); // 更新目标敌人和碰撞器

          if (closestEnemy) {
            this.enemyTarget = closestEnemy;
            this.enemyTargetPos.x = closestEnemy.node.position.x;
            this.enemyTargetPos.y = closestEnemy.node.position.y;
            this.enemyCollider = closestCollider;
          }
        }
        /**
         * 更新函数
         * @param deltaTime 帧间隔时间
         */


        update(deltaTime) {
          this.getTargetEnemy();
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=dc09fd82f82ed0b6e39beb68c91608f15d11b9f5.js.map