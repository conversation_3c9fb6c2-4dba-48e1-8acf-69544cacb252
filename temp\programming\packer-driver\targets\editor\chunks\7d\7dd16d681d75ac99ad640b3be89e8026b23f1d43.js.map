{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts"], "names": ["_GameConst", "v2", "Vec2", "view", "<PERSON><PERSON>", "ColliderDraw", "ActionFrameTime", "EnemyPos", "ZERO", "ViewHeight", "getVisibleSize", "height", "ViewSize", "ViewWidth", "width", "ViewCenter", "GameConst", "MainPlaneAnimationName", "Entry", "Moveleft", "MovelRight", "Dodge", "Super", "Hurt", "Idle"], "mappings": ";;;uEAGMA,U;;;;;;AAFQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;;;;;;AAElBH,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAEJI,KAFI,GAEa,KAFb;AAAA,eAGJC,YAHI,GAGoB,IAHpB;AAAA,eAIJC,eAJI,GAIsB,MAJtB;AAMb;AANa,eAOJC,QAPI,GAOaL,IAAI,CAACM,IAPlB;AAAA;;AASC,YAAVC,UAAU,GAAE;AACZ,iBAAON,IAAI,CAACO,cAAL,GAAsBC,MAA7B;AACH;;AACW,YAARC,QAAQ,GAAE;AACV,iBAAOT,IAAI,CAACO,cAAL,EAAP;AACH;;AACY,YAATG,SAAS,GAAE;AACX,iBAAOV,IAAI,CAACO,cAAL,GAAsBI,KAA7B;AACH;;AACa,YAAVC,UAAU,GAAE;AACZ,iBAAOd,EAAE,CAAC,KAAKY,SAAL,GAAe,CAAhB,EAAmB,KAAKJ,UAAL,GAAgB,CAAnC,CAAT;AACH;;AApBY,O;;2BAuBJO,S,GAAY,IAAIhB,UAAJ,E;;wCAEZiB,sB,GAAyB;AAClCC,QAAAA,KAAK,EAAE,OAD2B;AAElCC,QAAAA,QAAQ,EAAE,UAFwB;AAGlCC,QAAAA,UAAU,EAAE,WAHsB;AAIlCC,QAAAA,KAAK,EAAE,OAJ2B;AAKlCC,QAAAA,KAAK,EAAE,OAL2B;AAMlCC,QAAAA,IAAI,EAAE,MAN4B;AAOlCC,QAAAA,IAAI,EAAE;AAP4B,O", "sourcesContent": ["\r\nimport {Size, v2, Vec2, view } from \"cc\";\r\n\r\nclass _GameConst {\r\n\r\n    readonly Cache: boolean = false;\r\n    readonly ColliderDraw: boolean = true;\r\n    readonly ActionFrameTime: number = 0.0333;\r\n\r\n    // 敌人相关\r\n    readonly EnemyPos: Vec2 = Vec2.ZERO;\r\n\r\n    get ViewHeight(){\r\n        return view.getVisibleSize().height\r\n    }\r\n    get ViewSize(){\r\n        return view.getVisibleSize()\r\n    }\r\n    get ViewWidth(){\r\n        return view.getVisibleSize().width\r\n    }\r\n    get ViewCenter(){\r\n        return v2(this.ViewWidth/2, this.ViewHeight/2)\r\n    }\r\n}\r\n\r\nexport const GameConst = new _GameConst();\r\n\r\nexport const MainPlaneAnimationName = {\r\n    Entry: \"Entry\",\r\n    Moveleft: \"MoveLeft\",\r\n    MovelRight: \"MoveRight\",\r\n    Dodge: \"Dodge\",\r\n    Super: \"Super\",\r\n    Hurt: \"Hurt\",\r\n    Idle: \"Idle\",\r\n}"]}