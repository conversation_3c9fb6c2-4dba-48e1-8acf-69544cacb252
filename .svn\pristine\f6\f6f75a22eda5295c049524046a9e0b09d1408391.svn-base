import { _decorator, Component, Node, Prefab, assetManager, instantiate, Vec2, UITransform, v2 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent, LevelDataLayer, LevelDataWave } from 'db://assets/scripts/leveldata/leveldata';

import { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';
import { LevelEditorWaveUI } from './LevelEditorWaveUI';
import { LevelEditorEventUI } from './LevelEditorEventUI';

const TerrainsNodeName = "terrains";
const ScrollsNodeName = "scrolls";
const DynamicNodeName = "dynamic";
const WaveNodeName = "waves";
const EventNodeName = "events"

@ccclass('LevelEditorLayerUI')
@executeInEditMode()
export class LevelEditorLayerUI extends Component {
    public terrainsNode: Node|null = null;
    public scrollsNode: Node|null = null;
    public dynamicNode: Node|null = null;
    public wavesNode: Node|null = null;
    public eventsNode: Node|null = null;

    onLoad(): void {
        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);
        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);
        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);
        this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);
        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);
    }

    public initByLevelData(data: LevelDataLayer):void {
        console.log("LevelEditorLayerUI initByLevelData");
        if (!data) {
            return;
        }

        if (this.terrainsNode === null) {
            return;
        }
        data.terrains?.forEach((terrain) => {
            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                    return
                } 
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);                
            });
        });

        data.dynamics?.forEach((dynamic, index) => {
            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, 'dyna_'+index);
            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);
            dynamic.terrains.forEach((terrain)=>{
                assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                    if (err) {
                        console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                        return
                    } 
                    var dynamicNode = instantiate(prefab);
                    dynaNode!.addChild(dynamicNode);                
                });
            });
        });

        data.waves?.forEach((wave)=>{
            var node = new Node();
            var waveUIComp = node.addComponent(LevelEditorWaveUI);
            waveUIComp.initByLevelData(wave);
            this.wavesNode!.addChild(node);
        })
        data.events?.forEach((event)=>{
            var node = new Node();
            var eventUIComp = node.addComponent(LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode!.addChild(node);
        })
    }

    public initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):void {
        if (!data || this.scrollsNode === null) {
            return;
        } 

        data.scrolls?.forEach((scroll, index) => {
            assetManager.loadAny({uuid:scroll.uuid}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load scrolls prefab err", err); 
                    return
                }
                
                const scrollLayer = new LevelScrollLayerUI();
                scrollLayer.scrollPrefab = prefab;
                layerData.scrollLayers.push(scrollLayer);

                const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);

                var totalHeight = data.speed * data.totalTime;
                var childCount = totalHeight / data.speed - 1;
                var posOffsetY = 0;
                for (let i = 0; i < childCount; i++) { 
                    const child = instantiate(prefab);
                    child.setPosition(0, posOffsetY, 0);
                    var offY = child.getComponent(UITransform)!.contentSize.height;
                    scrollsNode.addChild(child);
                    posOffsetY += offY;
                }      
            });
        });
    }

    public fillLevelData(data: LevelDataLayer):void {
        data.terrains = []
        this.terrainsNode!.children.forEach((terrainNode) => {
            data.terrains.push({
                // @ts-ignore
                uuid: terrainNode._prefab.asset._uuid,
                position: new Vec2(terrainNode.position.x, terrainNode.position.y),
                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),
                rotation: terrainNode.rotation.z
            })
        })

        //data.scrolls = [] 在上层有保存其它信息，所以这里不清空
        console.log("LevelEditorLayerUI fillLevelData scrolls", data.scrolls);
        this.scrollsNode!.children.forEach((scrollNode, index) => {
            // @ts-ignore
            data.scrolls[index].position = v2(scrollNode.position.x, scrollNode.position.y);
            data.scrolls[index].scale = v2(scrollNode.scale.x, scrollNode.scale.y);
            data.scrolls[index].rotation = scrollNode.rotation.z;
        });

        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空
        this.dynamicNode!.children.forEach((dynamicNode, index) => {
            data.dynamics[index].position = v2(dynamicNode.position.x, dynamicNode.position.y);
            data.dynamics[index].scale = v2(dynamicNode.scale.x, dynamicNode.scale.y);
            data.dynamics[index].rotation = dynamicNode.rotation.z;
        });

        data.waves = []
        this.wavesNode!.children.forEach((waveNode) => {
            var wave = new LevelDataWave()
            var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);
            waveUIComp!.fillLevelData(wave)
            data.waves.push(wave)
        })
        data.events = []
        this.eventsNode!.children.forEach((eventNode) => {
            var event = new LevelDataEvent()
            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            eventUIComp!.fillLevelData(event)
            data.events.push(event)
        })
    }

    public tick(progress: number, totalTime:number, speed:number):void {
        this.node.setPosition(0, - progress * totalTime * speed, 0);
    }
}