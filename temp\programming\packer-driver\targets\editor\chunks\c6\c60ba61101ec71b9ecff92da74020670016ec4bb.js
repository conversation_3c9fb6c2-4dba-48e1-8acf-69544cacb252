System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, Tools, StageData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStage(extras) {
    _reporterNs.report("Stage", "../../autogen/luban/schema", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['error']);

      _export("StageData", StageData = class StageData {
        constructor() {
          this.id = 0;
          this.mainStage = 0;
          this.subStage = 0;
          this.type = 0;
          this.enemyNorIDs = [];
          this.enemyNorInterval = 0;
          this.enemyNorRate = [];
        }

        loadJson(data) {
          this.mainStage = data.mainStage;
          this.subStage = data.subStage;
          this.type = data.type;
          this.enemyNorIDs = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.enemyGroupID, ',');
          this.enemyNorInterval = Number(data.delay);

          if (data.hasOwnProperty('enemyNorRate') && data.enemyNorRate !== '') {
            this.enemyNorRate = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.enemyNorRate, ',');
          }
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=c60ba61101ec71b9ecff92da74020670016ec4bb.js.map