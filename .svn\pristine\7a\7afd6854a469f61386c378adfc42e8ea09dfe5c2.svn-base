import { _decorator, Button, Component, Label, Sprite } from 'cc';
import { ResItem } from 'db://assets/scripts/autogen/luban/schema';
import { MyApp } from 'db://assets/scripts/MyApp';
import { UIMgr } from 'db://assets/scripts/ui/UIMgr';
import { PopupUI } from '../home/<USER>';

const { ccclass, property } = _decorator;

@ccclass('MailCellUI')
export class MailCellUI extends Component {

    @property(Sprite)
    mailIcon: Sprite | null = null;

    @property(Label)
    mailTitle: Label | null = null;

    @property(Label)
    mailContent: Label | null = null;

    @property(Button)
    btnClick: Button | null = null;

    itemID: number | null = null;

    start() {

    }

    update(deltaTime: number) {

    }
    onButtonClick() {
        UIMgr.openUI(PopupUI, '物品ID：' + this.itemID);
    }
    public setData(itemID: number): void {
        this.itemID = itemID;
        let item: ResItem | undefined = MyApp.lubanTables.TbResItem.get(itemID);
        this.mailTitle!.string = item?.name || "";
        this.mailContent!.string = item?.name || "";
        MyApp.resMgr.loadCoin(this.mailIcon!);
    }
}


