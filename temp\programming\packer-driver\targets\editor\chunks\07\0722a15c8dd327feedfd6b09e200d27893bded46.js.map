{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/wave/Wave.ts"], "names": ["_decorator", "CCFloat", "CCInteger", "Component", "WaveData", "eSpawnOrder", "GameIns", "ccclass", "property", "executeInEditMode", "WaveTrack", "WaveTrackGroup", "Wave", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_nextSpawnIndex", "_spawnQueue", "isCompleted", "_reset", "waveData", "planeList", "trigger", "spawnOrder", "Random", "sort", "Math", "random", "tick", "dtInMiliseconds", "spawn", "length", "spawnSingle", "spawnInterval", "index", "spawnPos", "spawnAngle", "eval", "spawnSpeed", "createPlane", "planeId", "pos", "angle", "speed", "enemy", "enemyManager", "addPlane", "console", "log", "setPos", "x", "y", "initMove", "initDelayDestroy", "delayDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AACVC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;2BAGpCU,S,WADZH,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACN,SAAD,C,UAERM,QAAQ,CAACP,OAAD,C,UAERO,QAAQ,CAACP,OAAD,C,UAERO,QAAQ,CAACP,OAAD,C,2BARb,MACaS,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZJ,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACN,SAAD,C,UAERM,QAAQ,CAACN,SAAD,C,UAERM,QAAQ,CAACN,SAAD,C,WAERM,QAAQ,CAAC,CAACE,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAKpBC,I,aAFZL,OAAO,CAAC,MAAD,C,WACPE,iBAAiB,E,WAGbD,QAAQ,CAAC;AAACK,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,+CAJb,MAEaD,IAFb,SAE0BT,SAF1B,CAEoC;AAAA;AAAA;;AAAA;;AAKhC;AACJ;AACA;AAPoC,eAQxBW,YARwB,GAQA,KARA;AAAA,eAWxBC,gBAXwB,GAWG,CAXH;AAAA,eAYxBC,cAZwB,GAYC,CAZD;AAAA,eAaxBC,eAbwB,GAaE,CAbF;AAAA,eAcxBC,WAdwB,GAcA,EAdA;AAAA;;AAShC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKL,YAAZ;AAA2B;;AAM9CM,QAAAA,MAAM,GAAG;AACb,eAAKN,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,GAAmB,KAAKG,QAAL,CAAcC,SAAjC;AACH;;AAEDC,QAAAA,OAAO,GAAG;AACN,eAAKH,MAAL,GADM,CAEN;;;AACA,cAAI,KAAKC,QAAL,CAAcG,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,iBAAKP,WAAL,GAAmB,KAAKA,WAAL,CAAiBQ,IAAjB,CAAsB,MAAMC,IAAI,CAACC,MAAL,KAAgB,GAA5C,CAAnB;AACH;AACJ,SA9B+B,CAgChC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAKhB,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyBe,eAAzB;;AACA,cAAI,KAAKf,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,gBAAI,CAAC,KAAKe,KAAL,EAAL,EAAmB;AACf,mBAAKjB,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ;;AAEOiB,QAAAA,KAAK,GAAY;AACrB,cAAI,KAAKd,eAAL,IAAwB,KAAKC,WAAL,CAAiBc,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKC,WAAL,CAAiB,KAAKhB,eAAL,EAAjB;AACA,eAAKD,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKM,QAAL,CAAca,aAA5D;AACA,iBAAO,IAAP;AACH;;AAEOD,QAAAA,WAAW,CAACE,KAAD,EAAsB;AACrC,cAAIA,KAAK,IAAI,KAAKjB,WAAL,CAAiBc,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAII,QAAQ,GAAG,KAAKf,QAAL,CAAce,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKhB,QAAL,CAAcgB,UAAd,CAAyBC,IAAzB,EAAjB;AACA,cAAIC,UAAU,GAAG,KAAKlB,QAAL,CAAckB,UAA/B;AAEA,eAAKC,WAAL,CAAiB,KAAKtB,WAAL,CAAiBiB,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD,EAAgEE,UAAhE;AACH;;AAEwB,cAAXC,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4CC,KAA5C,EAA2D;AAChF,cAAIC,KAAK,GAAG,MAAM;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BN,OAA9B,EAAuC,IAAvC,CAAlB;;AACA,cAAII,KAAJ,EAAW;AACP;AACA;AACAG,YAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BR,OAA3B,EAAoCC,GAApC,EAAyCC,KAAzC,EAAgDC,KAAhD;AACAC,YAAAA,KAAK,CAACK,MAAN,CAAaR,GAAG,CAACS,CAAjB,EAAoBT,GAAG,CAACU,CAAxB;AACAP,YAAAA,KAAK,CAACQ,QAAN,CAAeT,KAAf,EAAsBD,KAAtB;AACAE,YAAAA,KAAK,CAACS,gBAAN,CAAuB,KAAKjC,QAAL,CAAckC,YAArC;AACH;AACJ;;AA5E+B,O;;;;;iBAGF;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/scripts/game/GameIns';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    \r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n\r\n    private _reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue = this.waveData.planeList;\r\n    }\r\n\r\n    trigger() {\r\n        this._reset();\r\n        // shuffle spawn queue\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._spawnQueue = this._spawnQueue.sort(() => Math.random() - 0.5);\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n            if (!this.spawn()) {\r\n                this._isCompleted = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawn(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingle(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval;\r\n        return true;\r\n    }\r\n\r\n    private spawnSingle(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        let spawnSpeed = this.waveData.spawnSpeed;\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle, spawnSpeed);\r\n    }\r\n\r\n    private async createPlane(planeId: number, pos: Vec2, angle: number, speed: number) {\r\n        let enemy = await GameIns.enemyManager.addPlane(planeId, null);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(speed, angle);\r\n            enemy.initDelayDestroy(this.waveData.delayDestroy);\r\n        }\r\n    }\r\n\r\n}"]}