{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts"], "names": ["_decorator", "Component", "Enum", "rect", "v2", "Vec2", "GameIns", "ccclass", "property", "menu", "ColliderType", "ColliderGroupType", "DEFAULT", "PLAYER", "BULLET_SELF", "ENEMY_NORMAL", "BULLET_ENEMY", "ENEMY_BUILE", "PROP", "FCollider", "type", "displayName", "isConvex", "isEnable", "isImmunityBullet", "isImmunityBulletHurt", "isImmunityCollider", "isImmunityColliderHurt", "entity", "aabb", "colliderId", "Box", "x", "y", "width", "height", "initCollider", "_baseId", "offset", "_offset", "value", "onEnable", "fColliderManager", "addCollider", "draw", "onDisable", "removeCollider", "initBaseData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AAC7CC,MAAAA,O,iBAAAA,O;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA8BT,U;;8BAExBU,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;cAMZ;;;mCACWC,iB,GAAoBT,IAAI,CAAC;AAChCU,QAAAA,OAAO,EAAE,CADuB;AAEhCC,QAAAA,MAAM,EAAE,CAFwB;AAGhCC,QAAAA,WAAW,EAAE,CAHmB;AAIhCC,QAAAA,YAAY,EAAE,CAJkB;AAKhCC,QAAAA,YAAY,EAAE,CALkB;AAMhCC,QAAAA,WAAW,EAAE,CANmB;AAOhCC,QAAAA,IAAI,EAAE,CAP0B,CAOxB;;AAPwB,OAAD,C;;yBAYdC,S,WA+BhBX,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAQRG,QAAQ,CAAC;AACNY,QAAAA,IAAI,EAAET,iBADA;AAENU,QAAAA,WAAW,EAAE;AAFP,OAAD,C,EA3CZd,O,gCAAD,MACqBY,SADrB,SACuClB,SADvC,CACiD;AAAA;AAAA;AAAA,eAItCqB,QAJsC,GAIlB,IAJkB;AAAA,eAKtCC,QALsC,GAKlB,IALkB;AAKb;AALa,eAMtCC,gBANsC,GAMV,KANU;AAMJ;AANI,eAOtCC,oBAPsC,GAON,KAPM;AAOA;AAPA,eAQtCC,kBARsC,GAQR,KARQ;AAQF;AARE,eAStCC,sBATsC,GASJ,KATI;AASE;AATF,eAUtCC,MAVsC,GAUd,IAVc;AAe7C;AAf6C,eAgBtCC,IAhBsC,GAgBzB1B,IAAI,EAhBqB;AAAA,eAuBtC2B,UAvBsC,GAuBjB,CAvBiB;;AAAA;;AAAA;AAAA;;AAC9B,YAAJV,IAAI,GAAG;AACd,iBAAOV,YAAY,CAACqB,GAApB;AACH;;AAeW,YAADC,CAAC,GAAG;AAAE,iBAAO,KAAKH,IAAL,CAAUG,CAAjB;AAAqB;;AAC1B,YAADC,CAAC,GAAG;AAAE,iBAAO,KAAKJ,IAAL,CAAUI,CAAjB;AAAqB;;AACtB,YAALC,KAAK,GAAG;AAAE,iBAAO,KAAKL,IAAL,CAAUK,KAAjB;AAAyB;;AAC7B,YAANC,MAAM,GAAG;AAAE,iBAAO,KAAKN,IAAL,CAAUM,MAAjB;AAA0B;;AAGzCC,QAAAA,YAAY,GAAG;AAClB,eAAKN,UAAL,GAAkBX,SAAS,CAACkB,OAAV,EAAlB;;AACA,cAAIlB,SAAS,CAACkB,OAAV,GAAoB,IAAxB,EAA8B;AAAC;AAC3BlB,YAAAA,SAAS,CAACkB,OAAV,GAAoB,CAApB;AACH;AACJ;;AAMgB,YAANC,MAAM,GAAS;AACtB,iBAAO,KAAKC,OAAZ;AACH;;AACgB,YAAND,MAAM,CAACE,KAAD,EAAc;AAC3B,eAAKD,OAAL,GAAeC,KAAf;AACH;;AAQSC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,WAAzB,CAAqC,IAArC;AACA,eAAKC,IAAL;AACH;;AAEDA,QAAAA,IAAI,GAAE,CAEL;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,kCAAQH,gBAAR,CAAyBI,cAAzB,CAAwC,IAAxC;AACH;;AAEDC,QAAAA,YAAY,CAACnB,MAAD,EAAgBU,MAAY,GAAGlC,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAjC,EAAyC;AACjD,eAAKwB,MAAL,GAAcA,MAAd;AACA,eAAKU,MAAL,GAAcA,MAAd;AACH;;AAhE4C,O,UAa9BD,O,GAAkB,C;;;;;iBAmBTjC,EAAE,E;;;;;;;iBAcNO,iBAAiB,CAACC,O", "sourcesContent": ["import { _decorator, Component, Enum, Rect, rect, v2, Vec2 } from 'cc';\nimport { GameIns } from '../GameIns';\nimport Entity from '../ui/base/Entity';\nconst { ccclass, property, menu } = _decorator;\n\nexport enum ColliderType {\n    Circle = 1,\n    Box = 2,\n    Polygon = 4,//凸多边形\n}\n\n//碰撞分组\nexport let ColliderGroupType = Enum({\n    DEFAULT: 1,\n    PLAYER: 2,\n    BULLET_SELF: 3,\n    ENEMY_NORMAL: 4,\n    BULLET_ENEMY: 5,\n    ENEMY_BUILE: 6,\n    PROP: 7,//道具\n})\n\n\n@ccclass\nexport default class FCollider extends Component {\n    public get type() {\n        return ColliderType.Box;\n    }\n    public isConvex: boolean = true;\n    public isEnable: boolean = true;//是否启用碰撞检测\n    public isImmunityBullet: boolean = false;//是否免疫子弹碰撞（不碰撞，直接穿透）\n    public isImmunityBulletHurt: boolean = false;//是否免疫子弹碰撞伤害（会碰撞，没伤害）\n    public isImmunityCollider: boolean = false;//无视撞击(免疫撞击也免疫子弹)\n    public isImmunityColliderHurt: boolean = false;//是否免疫碰撞伤害(免疫撞击也免疫子弹)\n    public entity: Entity | null = null;\n\n    //自增id\n    private static _baseId: number = 1;\n\n    //AABB\n    public aabb: Rect = rect();\n\n    public get x() { return this.aabb.x; }\n    public get y() { return this.aabb.y; }\n    public get width() { return this.aabb.width; }\n    public get height() { return this.aabb.height; }\n\n    public colliderId: number = 0;\n    public initCollider() {\n        this.colliderId = FCollider._baseId++;\n        if (FCollider._baseId > 5e10) {//防止id太大，做个轮回\n            FCollider._baseId = 1;\n        }\n    }\n\n    @property(Vec2)\n    private _offset: Vec2 = v2();\n\n    @property(Vec2)\n    public get offset(): Vec2 {\n        return this._offset;\n    }\n    public set offset(value: Vec2) {\n        this._offset = value;\n    }\n\n    @property({\n        type: ColliderGroupType,\n        displayName: '碰撞分组',\n    })\n    groupType: number = ColliderGroupType.DEFAULT;\n\n    protected onEnable(): void {\n        GameIns.fColliderManager.addCollider(this);\n        this.draw();\n    }\n    \n    draw(){\n\n    }\n\n    protected onDisable(): void {\n        GameIns.fColliderManager.removeCollider(this);\n    }\n\n    initBaseData(entity:Entity, offset: Vec2 = v2(0, 0)) {\n        this.entity = entity;\n        this.offset = offset;\n    }\n\n}\n"]}