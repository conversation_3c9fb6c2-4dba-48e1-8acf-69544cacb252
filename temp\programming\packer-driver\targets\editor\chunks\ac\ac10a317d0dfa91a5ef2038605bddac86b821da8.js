System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Prefab, assetManager, EDITOR, BulletProperty, Bullet, EmitterData, BulletData, ObjectPool, BulletSystem, EventGroupContext, PropertyContainerComponent, MyApp, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, degreesToRadians, radiansToDegrees, eEmitterStatus, eEmitterProp, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletProperty(extras) {
    _reporterNs.report("BulletProperty", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterData(extras) {
    _reporterNs.report("EmitterData", "../data/bullet/EmitterData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../data/bullet/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropertyContainerComponent(extras) {
    _reporterNs.report("PropertyContainerComponent", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/scripts/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResBullet(extras) {
    _reporterNs.report("ResBullet", "db://assets/scripts/autogen/luban/schema", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Prefab = _cc.Prefab;
      assetManager = _cc.assetManager;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      BulletProperty = _unresolved_2.BulletProperty;
      Bullet = _unresolved_2.Bullet;
    }, function (_unresolved_3) {
      EmitterData = _unresolved_3.EmitterData;
    }, function (_unresolved_4) {
      BulletData = _unresolved_4.BulletData;
    }, function (_unresolved_5) {
      ObjectPool = _unresolved_5.ObjectPool;
    }, function (_unresolved_6) {
      BulletSystem = _unresolved_6.BulletSystem;
    }, function (_unresolved_7) {
      EventGroupContext = _unresolved_7.EventGroupContext;
    }, function (_unresolved_8) {
      PropertyContainerComponent = _unresolved_8.PropertyContainerComponent;
    }, function (_unresolved_9) {
      MyApp = _unresolved_9.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'misc', 'instantiate', 'Node', 'Component', 'Prefab', 'Color', 'Vec3', 'Quat', 'assetManager']);

      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu
      } = _decorator);
      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);

      _export("eEmitterStatus", eEmitterStatus = /*#__PURE__*/function (eEmitterStatus) {
        eEmitterStatus[eEmitterStatus["None"] = 0] = "None";
        eEmitterStatus[eEmitterStatus["Prewarm"] = 1] = "Prewarm";
        eEmitterStatus[eEmitterStatus["Emitting"] = 2] = "Emitting";
        eEmitterStatus[eEmitterStatus["LoopEndReached"] = 3] = "LoopEndReached";
        eEmitterStatus[eEmitterStatus["Completed"] = 4] = "Completed";
        return eEmitterStatus;
      }({})); // 用枚举定义属性


      _export("eEmitterProp", eEmitterProp = /*#__PURE__*/function (eEmitterProp) {
        eEmitterProp[eEmitterProp["IsActive"] = 1] = "IsActive";
        eEmitterProp[eEmitterProp["IsOnlyInScreen"] = 2] = "IsOnlyInScreen";
        eEmitterProp[eEmitterProp["IsPreWarm"] = 3] = "IsPreWarm";
        eEmitterProp[eEmitterProp["IsLoop"] = 4] = "IsLoop";
        eEmitterProp[eEmitterProp["InitialDelay"] = 5] = "InitialDelay";
        eEmitterProp[eEmitterProp["PreWarmDuration"] = 6] = "PreWarmDuration";
        eEmitterProp[eEmitterProp["EmitBulletID"] = 7] = "EmitBulletID";
        eEmitterProp[eEmitterProp["EmitDuration"] = 8] = "EmitDuration";
        eEmitterProp[eEmitterProp["EmitInterval"] = 9] = "EmitInterval";
        eEmitterProp[eEmitterProp["EmitPower"] = 10] = "EmitPower";
        eEmitterProp[eEmitterProp["LoopInterval"] = 11] = "LoopInterval";
        eEmitterProp[eEmitterProp["PerEmitCount"] = 12] = "PerEmitCount";
        eEmitterProp[eEmitterProp["PerEmitInterval"] = 13] = "PerEmitInterval";
        eEmitterProp[eEmitterProp["PerEmitOffsetX"] = 14] = "PerEmitOffsetX";
        eEmitterProp[eEmitterProp["Angle"] = 15] = "Angle";
        eEmitterProp[eEmitterProp["Count"] = 16] = "Count";
        eEmitterProp[eEmitterProp["Arc"] = 17] = "Arc";
        eEmitterProp[eEmitterProp["Radius"] = 18] = "Radius";
        eEmitterProp[eEmitterProp["TotalElapsedTime"] = 19] = "TotalElapsedTime";
        return eEmitterProp;
      }({}));

      /**
       * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱
       * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮
       * - young
       */
      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = menu('子弹系统/发射器'), _dec3 = executeInEditMode(true), _dec4 = disallowMultiple(true), _dec5 = property({
        displayName: "子弹ID"
      }), _dec6 = property({
        type: Prefab,
        displayName: "子弹Prefab(仅编辑器下)",
        editorOnly: true
      }), _dec7 = property({
        type: _crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
          error: Error()
        }), EmitterData) : EmitterData,
        displayName: "发射器属性"
      }), _dec8 = property({
        type: _crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
          error: Error()
        }), BulletData) : BulletData,
        displayName: "子弹属性"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = (_class2 = (_class3 = class Emitter extends (_crd && PropertyContainerComponent === void 0 ? (_reportPossibleCrUseOfPropertyContainerComponent({
        error: Error()
      }), PropertyContainerComponent) : PropertyContainerComponent) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletID", _descriptor, this);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor2, this);

          _initializerDefineProperty(this, "emitterData", _descriptor3, this);

          _initializerDefineProperty(this, "bulletData", _descriptor4, this);

          // callbacks
          this.onBulletCreatedCallback = null;
          this.onEmitterStatusChangedCallback = null;
          // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
          this.isActive = void 0;
          this.isOnlyInScreen = void 0;
          this.isPreWarm = void 0;
          this.isLoop = void 0;
          this.initialDelay = void 0;
          this.preWarmDuration = void 0;
          this.emitBulletID = void 0;
          this.emitDuration = void 0;
          this.emitInterval = void 0;
          this.emitPower = void 0;
          this.loopInterval = void 0;
          this.perEmitCount = void 0;
          this.perEmitInterval = void 0;
          this.perEmitOffsetX = void 0;
          this.angle = void 0;
          this.count = void 0;
          this.arc = void 0;
          this.radius = void 0;
          this.totalElapsedTime = void 0;
          // 以下用于事件组修改子弹的属性，（不直接修改bulletData)
          this.bulletProp = void 0;
          // 发射器自己的事件组
          this.eventGroups = [];
          // 私有变量
          this._status = eEmitterStatus.None;
          this._statusElapsedTime = 0;
          this._isEmitting = false;
          this._nextEmitTime = 0;
          this._bulletPrefab = null;
          this._prewarmEffectPrefab = null;
          this._emitEffectPrefab = null;
          this._entity = null;
          this._bulletConfig = undefined;
          // Per-emit timing tracking
          this._perEmitBulletQueue = [];
        }

        get isEmitting() {
          return this._isEmitting;
        }

        get status() {
          return this._status;
        }

        get statusElapsedTime() {
          return this._statusElapsedTime;
        }

        get bulletConfig() {
          return this._bulletConfig;
        }

        onLoad() {
          this.createProperties();
          this.createEventGroups(); // 更新属性

          this.resetProperties();
        } //#region "Editor Region"


        onLostFocusInEditor() {
          this.updatePropertiesInEditor();
          this.createEventGroups();
        }

        updatePropertiesInEditor() {
          if (!this.emitterData) return;
          this.isActive.value = true;
          this.emitBulletID.value = this.bulletID;
          this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
          this.isPreWarm.value = this.emitterData.isPreWarm;
          this.isLoop.value = this.emitterData.isLoop;
          this.initialDelay.value = this.emitterData.initialDelay.eval();
          this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();
          this.emitDuration.value = this.emitterData.emitDuration.eval();
          this.emitInterval.value = this.emitterData.emitInterval.eval();
          this.emitPower.value = this.emitterData.emitPower.eval();
          this.loopInterval.value = this.emitterData.loopInterval.eval();
          this.perEmitCount.value = this.emitterData.perEmitCount.eval();
          this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();
          this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();
          this.angle.value = this.emitterData.angle.eval();
          this.count.value = this.emitterData.count.eval();
          this.arc.value = this.emitterData.arc.eval();
          this.radius.value = this.emitterData.radius.eval();
          this.notifyAll(true);
        } //#endregion "Editor Region"
        // 通过这个接口来启用和禁用发射器


        setIsActive(active) {
          this.isActive.value = active;
          this.isActive.notify();
        } // 这个接口清理发射器的状态，全部从头开始


        reset() {
          this._isEmitting = false;
          this.changeStatus(eEmitterStatus.None);
          this.resetProperties();

          if (this.eventGroups.length > 0) {
            this.eventGroups.forEach(group => group.reset());
          }
        }

        setEntity(entity) {
          this._entity = entity;
        }

        getEntity() {
          return this._entity;
        }

        createProperties() {
          this.clear();
          this.isActive = this.addProperty(eEmitterProp.IsActive, false);
          this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);
          this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);
          this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);
          this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);
          this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);
          this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);
          this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, 0);
          this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);
          this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);
          this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);
          this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);
          this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);
          this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);
          this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);
          this.angle = this.addProperty(eEmitterProp.Angle, 0);
          this.count = this.addProperty(eEmitterProp.Count, 1);
          this.arc = this.addProperty(eEmitterProp.Arc, 0);
          this.radius = this.addProperty(eEmitterProp.Radius, 0); // 子弹相关属性

          this.bulletProp = new (_crd && BulletProperty === void 0 ? (_reportPossibleCrUseOfBulletProperty({
            error: Error()
          }), BulletProperty) : BulletProperty)(); // 子弹表->Prefab路径

          this.emitBulletID.on(value => {
            if (value > 0 && (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).GetInstance()) {
              this._bulletConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbResBullet.get(value);

              if (this._bulletConfig) {
                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.load(this._bulletConfig.prefab, Prefab, (error, prefab) => {
                  if (error) {
                    console.error("Emitter load bullet prefab err", error);
                    return;
                  }

                  this._bulletPrefab = prefab;
                });
              }
            }
          });
          this.isActive.on(value => {
            if (value) {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onCreateEmitter(this);
            } else {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onDestroyEmitter(this);
            }
          });
        }

        createEventGroups() {
          if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;
          this.eventGroups = [];
          let ctx = new (_crd && EventGroupContext === void 0 ? (_reportPossibleCrUseOfEventGroupContext({
            error: Error()
          }), EventGroupContext) : EventGroupContext)();
          ctx.emitter = this;
          ctx.playerPlane = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).playerPlane;

          for (const eventGroup of this.emitterData.eventGroupData) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).createEmitterEventGroup(ctx, eventGroup);
          }
        } // reset properties from emitterData


        resetProperties() {
          if (!this.emitterData) return;
          this.isActive.value = false;
          this.totalElapsedTime.value = 0;
          this.emitBulletID.value = this.bulletID;
          this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
          this.isPreWarm.value = this.emitterData.isPreWarm;
          this.isLoop.value = this.emitterData.isLoop;
          this.initialDelay.value = this.emitterData.initialDelay.eval();
          this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();
          this.emitDuration.value = this.emitterData.emitDuration.eval();
          this.emitInterval.value = this.emitterData.emitInterval.eval();
          this.emitPower.value = this.emitterData.emitPower.eval();
          this.loopInterval.value = this.emitterData.loopInterval.eval();
          this.perEmitCount.value = this.emitterData.perEmitCount.eval();
          this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();
          this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();
          this.angle.value = this.emitterData.angle.eval();
          this.count.value = this.emitterData.count.eval();
          this.arc.value = this.emitterData.arc.eval();
          this.radius.value = this.emitterData.radius.eval();
          this.bulletProp.resetFromData(this.bulletData);
          this.notifyAll(true);
        }
        /**
         * public apis
         */


        changeStatus(status) {
          if (this._status === status) return;
          const oldStatus = this._status;
          this._status = status;
          this._statusElapsedTime = 0;
          this._nextEmitTime = 0; // Clear per-emit queue when changing status

          this._perEmitBulletQueue = [];

          if (status === eEmitterStatus.None) {
            if (this.eventGroups.length > 0) {
              this.eventGroups.forEach(group => group.tryStop());
            }
          } else {
            if (this.eventGroups.length > 0) {
              this.eventGroups.forEach(group => group.tryStart());
            }
          }

          if (this.onEmitterStatusChangedCallback != null) {
            this.onEmitterStatusChangedCallback(this, oldStatus, status);
          }
        }

        scheduleNextEmit() {
          // re-eval
          this.emitInterval.value = this.emitterData.emitInterval.eval(); // Schedule the next emit after emitInterval

          this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;
        }

        startEmitting() {
          this._isEmitting = true; // 下一次update时触发发射
          // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
        }

        stopEmitting() {
          this._isEmitting = false; // Clear the per-emit bullet queue

          this._perEmitBulletQueue = [];
          this.unscheduleAllCallbacks();
        }

        canEmit() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }

        emit() {
          // re-eval
          this.angle.value = this.emitterData.angle.eval();
          this.count.value = this.emitterData.count.eval();
          this.arc.value = this.emitterData.arc.eval();
          this.radius.value = this.emitterData.radius.eval();
          this.perEmitCount.value = this.emitterData.perEmitCount.eval();

          if (this.perEmitInterval.value > 0) {
            // Generate bullets in time-sorted order directly
            for (let j = 0; j < this.perEmitCount.value; j++) {
              this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();
              const targetTime = this._statusElapsedTime + this.perEmitInterval.value * j;

              for (let i = 0; i < this.count.value; i++) {
                this._perEmitBulletQueue.push({
                  index: i,
                  perEmitIndex: j,
                  targetTime: targetTime
                });
              }
            }
          } else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count.value; i++) {
              for (let j = 0; j < this.perEmitCount.value; j++) {
                this.emitSingle(i, j);
              }
            }
          }
        }

        processPerEmitQueue() {
          // Process bullets that should be emitted based on current time
          while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0]; // Check if it's time to emit this bullet

            if (this._statusElapsedTime >= nextBullet.targetTime) {
              // Remove from queue and emit
              this._perEmitBulletQueue.shift();

              this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
              // No more bullets ready to emit yet
              break;
            }
          }
        }

        tryEmit() {
          if (this.canEmit()) {
            this.emit();
            return true;
          }

          return false;
        }

        emitSingle(index, perEmitIndex) {
          const direction = this.getSpawnDirection(index);
          const position = this.getSpawnPosition(index, perEmitIndex);
          this.createBullet(direction, position);
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 计算发射方向
          const angleOffset = this.count.value > 1 ? this.arc.value / (this.count.value - 1) * index - this.arc.value / 2 : 0;
          const radian = degreesToRadians(this.angle.value + angleOffset);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * odd number to the right, even number to the left
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index, perEmitIndex) {
          // add perEmitOffsetX by perEmitIndex, with the rules:
          // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;
          const getEmitOffsetX = (perEmitIndex, perEmitCount, perEmitOffsetX) => {
            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;
            const interval = perEmitOffsetX / (perEmitCount - 1); //const middle = 0;

            if (perEmitCount % 2 === 1) {
              // 奇数情况
              if (perEmitIndex === 0) return 0;

              if (perEmitIndex % 2 === 0) {
                // 偶数索引在左边
                const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                return -stepsFromMiddle * interval;
              } else {
                // 奇数索引在右边
                const stepsFromMiddle = Math.ceil(perEmitIndex / 2);
                return stepsFromMiddle * interval;
              }
            } else {
              // 偶数情况
              if (perEmitIndex === 0) return -interval / 2;

              if (perEmitIndex % 2 === 0) {
                // 偶数索引在左边
                const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                return -interval / 2 - stepsFromMiddle * interval;
              } else {
                // 奇数索引在右边
                const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                return interval / 2 + stepsFromMiddle * interval;
              }
            }
          };

          this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();
          const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);

          if (this.radius.value <= 0) {
            return {
              x: perEmitOffsetX,
              y: 0
            };
          }

          const direction = this.getSpawnDirection(index);
          return {
            x: direction.x * this.radius.value + perEmitOffsetX,
            y: direction.y * this.radius.value
          };
        }

        createBullet(direction, position) {
          if (!this._bulletPrefab) {
            if (this.bulletPrefab) {
              this._bulletPrefab = this.bulletPrefab;
            } else {
              if (EDITOR) {
                this.createBulletInEditor(direction, position);
              } else {
                console.warn("Emitter: No bullet prefab assigned");
              }

              return;
            }
          }

          const bullet = this.instantiateBullet();
          if (!bullet) return;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateBullet(this, bullet); // Set bullet position relative to emitter

          const emitterPos = this.node.getWorldPosition();
          bullet.node.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
          bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
          bullet.prop.speed.value *= this.emitPower.value; // 为什么需要在这里resetEventGroups?
          // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性

          bullet.onReady();

          if (this.onBulletCreatedCallback != null) {
            this.onBulletCreatedCallback(bullet);
          }
        }

        async createBulletInEditor(direction, position) {
          // use a default bullet prefab
          const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab'; // @ts-ignore

          Editor.Message.request('asset-db', 'query-uuid', prefabPath).then(uuid => {
            assetManager.loadAny({
              uuid: uuid
            }, (err, prefab) => {
              if (err) {
                console.error(err);
                return;
              }

              this._bulletPrefab = prefab;
              const bullet = this.instantiateBullet();
              if (!bullet) return;
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onCreateBullet(this, bullet); // Set bullet position relative to emitter

              const emitterPos = this.node.getWorldPosition();
              bullet.node.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
              bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
              bullet.prop.speed.value *= this.emitPower.value;
              bullet.onReady();
            });
          });
        }

        instantiateBullet() {
          const bulletNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent, this._bulletPrefab);

          if (!bulletNode) {
            console.error("Emitter: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component


          const bullet = bulletNode.getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);

          if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
          }

          if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
          }

          return bullet;
        }

        playEffect(prefab, position, rotation, duration) {
          if (!prefab) return;
          const effectNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode(this.node, prefab);
          if (!effectNode) return;
          effectNode.setWorldPosition(position);
          effectNode.setWorldRotation(rotation); // Play the effect and destroy it after duration
          // effectNode.getComponent(ParticleSystem)?.play();

          this.scheduleOnce(() => {
            (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
              error: Error()
            }), ObjectPool) : ObjectPool).returnNode(effectNode);
          }, duration);
        }
        /**
         * Return true if this.node is in screen
         */


        isInScreen() {
          // TODO: Get mainCamera.containsNode(this.node)
          return true;
        }

        tick(deltaTime) {
          if (!this.isActive || !this.isActive.value) {
            return;
          }

          switch (this._status) {
            case eEmitterStatus.None:
              this.updateStatusNone();
              break;

            case eEmitterStatus.Prewarm:
              this.updateStatusPrewarm();
              break;

            case eEmitterStatus.Emitting:
              this.updateStatusEmitting();
              break;

            case eEmitterStatus.LoopEndReached:
              this.updateStatusLoopEndReached();
              break;

            case eEmitterStatus.Completed:
              this.updateStatusCompleted();
              break;

            default:
              break;
          }

          this._statusElapsedTime += deltaTime;
          this.totalElapsedTime.value += deltaTime;
          this.notifyAll();
        }

        updateStatusNone() {
          if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusPrewarm() {
          if (!this.isPreWarm.value) this.changeStatus(eEmitterStatus.Emitting);else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
              this.changeStatus(eEmitterStatus.Emitting);
            }
          }
        }

        updateStatusEmitting() {
          if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop) this.changeStatus(eEmitterStatus.LoopEndReached);else this.changeStatus(eEmitterStatus.Completed);
            return;
          } // Start emitting if not already started


          if (!this._isEmitting) {
            this.startEmitting();
          } else if (this._statusElapsedTime >= this._nextEmitTime) {
            this.tryEmit();

            if (this.perEmitInterval.value <= 0) {
              this.scheduleNextEmit();
            } else {
              // 开始这一波
              this._nextEmitTime = this._statusElapsedTime + 10000000;
            }
          }

          let wasEmitting = this._perEmitBulletQueue.length > 0; // Process per-emit bullet queue based on precise timing

          this.processPerEmitQueue();

          if (wasEmitting && this._perEmitBulletQueue.length <= 0) {
            this.scheduleNextEmit();
          }
        }

        updateStatusLoopEndReached() {
          if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusCompleted() {
          // Do nothing or cleanup if needed
          this.isActive.value = false;
          this.isActive.notify();
        }

      }, _class3.kBulletNameInEditor = "_bullet_", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletID", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "emitterData", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
            error: Error()
          }), EmitterData) : EmitterData)();
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "bulletData", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
            error: Error()
          }), BulletData) : BulletData)();
        }
      })), _class2)) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ac10a317d0dfa91a5ef2038605bddac86b821da8.js.map