{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts"], "names": ["Property", "PropertyContainer", "PropertyContainerComponent", "_decorator", "Component", "ccclass", "property", "constructor", "value", "_value", "_isDirty", "_listeners", "isDirty", "set<PERSON>irty", "newValue", "on", "listener", "push", "off", "filter", "l", "notify", "force", "for<PERSON>ach", "clear", "length", "_properties", "Map", "addProperty", "key", "get", "set", "removeProperty", "delete", "getProperty", "getPropertyValue", "setProperty", "forEachProperty", "callback", "notifyAll", "_propertyContainer"], "mappings": ";;;8EAcaA,Q,EAkDAC,iB,EAkDAC,0B;;;;;;;;;;;;AAlHJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U,GAE9B;;0BAWaH,Q,GAAN,MAAMA,QAAN,CAAuC;AAMnCO,QAAAA,WAAW,CAACC,KAAD,EAAW;AAAA,eALrBC,MAKqB;AAAA,eAJrBC,QAIqB,GAJD,KAIC;AAH7B;AAG6B,eAFrBC,UAEqB,GAFmB,EAEnB;AACzB,eAAKF,MAAL,GAAcD,KAAd;AACA,eAAKE,QAAL,GAAgB,KAAhB;AACH;;AAEU,YAAPE,OAAO,GAAY;AACnB,iBAAO,KAAKF,QAAZ;AACH;;AAEDG,QAAAA,QAAQ,CAACL,KAAD,EAAuB;AAC3B,eAAKE,QAAL,GAAgBF,KAAhB;AACH;;AAEQ,YAALA,KAAK,GAAM;AACX,iBAAO,KAAKC,MAAZ;AACH;;AAEQ,YAALD,KAAK,CAACM,QAAD,EAAc;AACnB,cAAI,KAAKL,MAAL,KAAgBK,QAApB,EAA8B;AAE9B,eAAKL,MAAL,GAAcK,QAAd;AACA,eAAKD,QAAL,CAAc,IAAd;AACH;;AAEME,QAAAA,EAAE,CAACC,QAAD,EAAqC;AAC1C,eAAKL,UAAL,CAAgBM,IAAhB,CAAqBD,QAArB;AACH;;AAEME,QAAAA,GAAG,CAACF,QAAD,EAAqC;AAC3C,eAAKL,UAAL,GAAkB,KAAKA,UAAL,CAAgBQ,MAAhB,CAAuBC,CAAC,IAAIA,CAAC,KAAKJ,QAAlC,CAAlB;AACH;;AAEMK,QAAAA,MAAM,CAACC,KAAD,EAA+B;AAAA,cAA9BA,KAA8B;AAA9BA,YAAAA,KAA8B,GAAb,KAAa;AAAA;;AACxC,cAAIA,KAAK,IAAI,KAAKV,OAAlB,EAA2B;AACvB,iBAAKD,UAAL,CAAgBY,OAAhB,CAAwBP,QAAQ,IAAIA,QAAQ,CAAC,KAAKP,MAAN,CAA5C;;AACA,iBAAKI,QAAL,CAAc,KAAd;AACH;AACJ;;AAEMW,QAAAA,KAAK,GAAG;AACX,eAAKb,UAAL,CAAgBc,MAAhB,GAAyB,CAAzB;AACH;;AA/CyC,O;;mCAkDjCxB,iB,GAAN,MAAMA,iBAAN,CAA2B;AAAA;AAAA,eACtByB,WADsB,GACW,IAAIC,GAAJ,EADX;AAAA;;AAGvBC,QAAAA,WAAW,CAAIC,GAAJ,EAAYrB,KAAZ,EAAmC;AACjD,cAAIF,QAAQ,GAAG,KAAKoB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAf;;AACA,cAAIvB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH,WAFD,MAEO;AACHF,YAAAA,QAAQ,GAAG,IAAIN,QAAJ,CAAgBQ,KAAhB,CAAX;;AACA,iBAAKkB,WAAL,CAAiBK,GAAjB,CAAqBF,GAArB,EAA0BvB,QAA1B;AACH;;AAED,iBAAOA,QAAP;AACH;;AAEM0B,QAAAA,cAAc,CAAIH,GAAJ,EAAkB;AACnC,eAAKH,WAAL,CAAiBO,MAAjB,CAAwBJ,GAAxB;AACH;;AAEMK,QAAAA,WAAW,CAAIL,GAAJ,EAAqC;AACnD,iBAAO,KAAKH,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAP;AACH;;AAEMM,QAAAA,gBAAgB,CAAIN,GAAJ,EAA2B;AAC9C;AACA,cAAMvB,QAAQ,GAAG,KAAKoB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,iBAAOvB,QAAP,oBAAOA,QAAQ,CAAEE,KAAjB;AACH;;AAEM4B,QAAAA,WAAW,CAAIP,GAAJ,EAAYrB,KAAZ,EAA4B;AAC1C,cAAMF,QAAQ,GAAG,KAAKoB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,cAAIvB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH;AACJ;;AAEM6B,QAAAA,eAAe,CAAIC,QAAJ,EAA6D;AAC/E,eAAKZ,WAAL,CAAiBH,OAAjB,CAAyB,CAACjB,QAAD,EAAWuB,GAAX,KAAmBS,QAAQ,CAACT,GAAD,EAAMvB,QAAN,CAApD;AACH;;AAEMiC,QAAAA,SAAS,CAACjB,KAAD,EAA+B;AAAA,cAA9BA,KAA8B;AAA9BA,YAAAA,KAA8B,GAAb,KAAa;AAAA;;AAC3C,eAAKI,WAAL,CAAiBH,OAAjB,CAAyBjB,QAAQ,IAAIA,QAAQ,CAACe,MAAT,CAAgBC,KAAhB,CAArC;AACH;;AAEME,QAAAA,KAAK,GAAG;AACX,eAAKE,WAAL,CAAiBF,KAAjB;AACH;;AA9C6B,O,GAiDlC;;;4CACatB,0B,GAAN,MAAMA,0BAAN,SAA4CE,SAA5C,CAAsD;AAAA;AAAA;AAAA,eACjDoC,kBADiD,GACN,IAAIvC,iBAAJ,EADM;AAAA;;AAGlD2B,QAAAA,WAAW,CAAIC,GAAJ,EAAYrB,KAAZ,EAAmC;AACjD,iBAAO,KAAKgC,kBAAL,CAAwBZ,WAAxB,CAAoCC,GAApC,EAAyCrB,KAAzC,CAAP;AACH;;AAEMwB,QAAAA,cAAc,CAAIH,GAAJ,EAAkB;AACnC,eAAKW,kBAAL,CAAwBR,cAAxB,CAAuCH,GAAvC;AACH;;AAEMK,QAAAA,WAAW,CAAIL,GAAJ,EAAqC;AACnD,iBAAO,KAAKW,kBAAL,CAAwBN,WAAxB,CAAoCL,GAApC,CAAP;AACH;;AAEMM,QAAAA,gBAAgB,CAAIN,GAAJ,EAA2B;AAC9C,iBAAO,KAAKW,kBAAL,CAAwBL,gBAAxB,CAAyCN,GAAzC,CAAP;AACH;;AAEMO,QAAAA,WAAW,CAAIP,GAAJ,EAAYrB,KAAZ,EAA4B;AAC1C,eAAKgC,kBAAL,CAAwBJ,WAAxB,CAAoCP,GAApC,EAAyCrB,KAAzC;AACH;;AAEM+B,QAAAA,SAAS,CAACjB,KAAD,EAA+B;AAAA,cAA9BA,KAA8B;AAA9BA,YAAAA,KAA8B,GAAb,KAAa;AAAA;;AAC3C,eAAKkB,kBAAL,CAAwBD,SAAxB,CAAkCjB,KAAlC;AACH;;AAEME,QAAAA,KAAK,GAAG;AACX,eAAKgB,kBAAL,CAAwBhB,KAAxB;AACH;;AA7BwD,O", "sourcesContent": ["import { _decorator, Component } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n// Expression wrapper\r\ninterface Expression {\r\n    raw: string;             // the original expression text\r\n    compiled?: Function;     // compiled JS function\r\n}\r\n\r\nexport interface IProperty {\r\n    get isDirty(): boolean;\r\n    notify(force?: boolean): void;\r\n}\r\n\r\nexport class Property<T> implements IProperty {\r\n    private _value: T;\r\n    private _isDirty: boolean = false;\r\n    // listeners\r\n    private _listeners: Array<(value: T) => void> = [];\r\n\r\n    public constructor(value: T) {\r\n        this._value = value;\r\n        this._isDirty = false;\r\n    }\r\n\r\n    get isDirty(): boolean {\r\n        return this._isDirty;\r\n    }\r\n\r\n    setDirty(value: boolean): void {\r\n        this._isDirty = value;\r\n    }\r\n\r\n    get value(): T {\r\n        return this._value;\r\n    }\r\n\r\n    set value(newValue: T) {\r\n        if (this._value === newValue) return;\r\n\r\n        this._value = newValue;\r\n        this.setDirty(true);\r\n    }\r\n\r\n    public on(listener: (value: T) => void): void {\r\n        this._listeners.push(listener);\r\n    }\r\n\r\n    public off(listener: (value: T) => void): void {\r\n        this._listeners = this._listeners.filter(l => l !== listener);\r\n    }\r\n\r\n    public notify(force: boolean = false): void {\r\n        if (force || this.isDirty) {\r\n            this._listeners.forEach(listener => listener(this._value));\r\n            this.setDirty(false);\r\n        }\r\n    }\r\n\r\n    public clear() {\r\n        this._listeners.length = 0;\r\n    }\r\n}\r\n\r\nexport class PropertyContainer<K> {\r\n    private _properties: Map<K, IProperty> = new Map();\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        let property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        } else {\r\n            property = new Property<T>(value);\r\n            this._properties.set(key, property);\r\n        }\r\n\r\n        return property;\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._properties.delete(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._properties.get(key) as Property<T> | undefined;\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        // get property as PropertyValue<T>\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        return property?.value; \r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        }\r\n    }\r\n\r\n    public forEachProperty<T>(callback: (key: K, property: Property<T>) => void): void {\r\n        this._properties.forEach((property, key) => callback(key, property as Property<T>));\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._properties.forEach(property => property.notify(force));\r\n    }\r\n\r\n    public clear() {\r\n        this._properties.clear();\r\n    }\r\n}\r\n\r\n// Use this one to simplify class hierarchy\r\nexport class PropertyContainerComponent<K> extends Component {\r\n    private _propertyContainer: PropertyContainer<K> = new PropertyContainer<K>();\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        return this._propertyContainer.addProperty(key, value);\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._propertyContainer.removeProperty(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._propertyContainer.getProperty(key);\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        return this._propertyContainer.getPropertyValue(key);\r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        this._propertyContainer.setProperty(key, value);\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._propertyContainer.notifyAll(force);\r\n    }\r\n    \r\n    public clear() {\r\n        this._propertyContainer.clear();\r\n    }\r\n}"]}