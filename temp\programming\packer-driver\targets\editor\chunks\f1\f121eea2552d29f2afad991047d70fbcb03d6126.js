System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, SingletonBase, GameEnum, GameIns, GameMapRun, UIMgr, LoadingUI, BulletSystem, Rect, BattleManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "../ui/map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "../../ui/LoadingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  _export("BattleManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Rect = _cc.Rect;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameMapRun = _unresolved_5.default;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      LoadingUI = _unresolved_7.LoadingUI;
    }, function (_unresolved_8) {
      BulletSystem = _unresolved_8.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64d867wWTtD/LPANmmLR3y9", "BattleManager", undefined);

      __checkObsolete__(['Rect']);

      _export("BattleManager", BattleManager = class BattleManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this._percent = 0;
          this.gameType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameType.Common;
          this.initBattleEnd = false;
          this.gameStart = false;
          this.animSpeed = 1;
          this._gameTime = 0;
          this.mainStage = 0;
          this.subStage = 0;
          this._loadFinish = false;
          this._loadTotal = 0;
          this._loadCount = 0;
        }

        setBattleInfo(mainStage, subStage, planeData) {
          this.mainStage = mainStage;
          this.subStage = subStage;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.setPlaneData(planeData);
        }

        mainReset() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainReset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.reset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.reset();
        }

        subReset() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.subReset();
        }
        /**
         * 检查所有资源是否加载完成
         */


        checkLoadFinish() {
          this._loadCount++;
          let loadingUI = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).get(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
            error: Error()
          }), LoadingUI) : LoadingUI);
          loadingUI.updateProgress(this._loadCount / this._loadTotal);

          if (this._loadCount >= this._loadTotal) {
            this.initBattle();
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI);
          }
        }

        addLoadCount(count) {
          this._loadTotal += count;
        }

        startLoading() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameSortie();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameResManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.preLoad(); //伤害特效资源

          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.initData(this.mainStage); //地图背景初始化

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.preLoad(); //敌人资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.preLoad(); //boss资源
        }

        initBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).stageManager.initBattle(this.mainStage, this.subStage);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.initBattle();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.planeIn();
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).init(new Rect(0, 0, 750, 1334));
        }

        onPlaneIn() {
          this.initBattleEnd = true;
          this.beginBattle();
        }

        beginBattle() {
          if (this.initBattleEnd && !this.gameStart) {
            this.gameStart = true;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).stageManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane.begine(true);
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        update(dt) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isGameOver()) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
            }

            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isInBattle() || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isGameWillOver()) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.update(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.updateGameLogic(dt); //子弹发射器系统

            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tick(dt);
            this._gameTime += dt;
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gamePlaneManager) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
          }
        }
        /**
         * 战斗失败逻辑
         */


        async battleDie() {
          // GameFunc.addDialog(ReplayUI.default);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gamePause();
        } //     /**
        //      * 战斗复活逻辑
        //      */
        //     relifeBattle() {
        //         GameIns.eventManager.emit(GameEvent.MainRelife);
        //         GameIns.gameRuleManager.gameResume();
        //     }

        /**
         * 战斗失败结算
         */


        battleFail() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMainUI.showGameResult(false);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpNode.active = false;
          this.endBattle();
        }
        /**
         * 战斗胜利逻辑
         */


        battleSucc() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpNode.active = false;
          this.endBattle();

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).stageManager.checkStage(this.mainStage, this.subStage + 1)) {
            this.startNextBattle();
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMainUI.showGameResult(true);
          }
        }
        /**
        * 继续下一场战斗
        */


        startNextBattle() {
          this.subReset();
          this.subStage += 1;
          this.initBattle();
        }
        /**
         * 结束战斗
         */


        endBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameOver();
          this.gameStart = false;
          this.initBattleEnd = false;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroy();
        }
        /**
         * Boss切换完成
         * @param {string} bossName Boss名称
         */


        bossChangeFinish(bossName) {// const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          // if (bossEnterDialog) {
          //     bossEnterDialog.node.active = true;
          //     GameIns.mainPlaneManager.moveAble = false;
          //     bossEnterDialog.showTips(bossName);
          // }
        }

        bossWillEnter() {//        GameIns.mainPlaneManager.fireEnable = false;
          //        GameIns.mainPlaneManager.moveAble = false;
          //         WinePlaneManager.default.me.pauseBattle();
          //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);
          //         if (inGameUI) {
          //             inGameUI.hideUI();
          //         }
          //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          //         if (bossEnterDialog) {
          //             if (!bossEnterDialog.node.parent) {
          //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);
          //             }
          //             bossEnterDialog.node.active = true;
          //             bossEnterDialog.play();
          //         }
          //         GameIns.audioManager.playbg("bg_3");
        }
        /**
         * 开始Boss战斗
         */


        bossFightStart() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setFireEnable(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.moveAble = true;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.bossFightStart();
        }
        /**
         * 获取屏幕比例
         * @returns {number} 屏幕比例
         */


        getRatio() {
          return 0.666667; // 固定比例值
        }

        isGameType(gameType) {
          return this.gameType == gameType;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f121eea2552d29f2afad991047d70fbcb03d6126.js.map