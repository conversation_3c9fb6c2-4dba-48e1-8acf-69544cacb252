System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, UITransform, v2, Vec2, Vec3, GameIns, Tools, _dec, _class, _crd, ccclass, property, TrackComponent;

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Component', 'UITransform', 'v2', 'Vec2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", TrackComponent = (_dec = ccclass('TrackComponent'), _dec(_class = class TrackComponent extends Component {
        constructor(...args) {
          super(...args);
          this.TRACK_LOOP_FOREVER = 999;
          this._target = null;
          this._trackAble = false;
          this._formIndex = -100;
          this._bForm = false;
          this._initPos = Vec2.ZERO;
          this._posX = 0;
          this._posY = 0;
          this._prePosX = 0;
          this._prePosY = 0;
          this._trackType = -1;
          this._isTrackLeave = false;
          this._curTrack = null;
          this._curSpeed = 0;
          this._curAccelerate = 0;
          this._curTrackInterval = 0;
          this._trackLoop = 0;
          this._trackGroups = [];
          this._tracks = [];
          this._speeds = [];
          this._accelerates = [];
          this._trackIntervals = [];
          this._trackGroupIndex = 0;
          this._trackIndex = 0;
          this._trackTime = 0;
          this._trackOffX = 0;
          this._trackOffY = 0;
          this._bTrackOver = false;
          this._bTrackOverDie = false;
          this._trackStayTime = 0;
          this._bMoving = false;
          this._liveTime = -1;
          this._leaveAct = -1;
          this._leaveSpeed = 0;
          this._leaveAccelerate = 0;
          this._liveCount = 0;
          this._rushTime = 0;
          this._rushDuration = 0.2;
          this._trackChangeCall = null;
          this._trackStartCall = null;
          this._trackGroupStartCall = null;
          this._trackGroupOverCall = null;
          this._trackOverCall = null;
          this._trackLeaveCall = null;
          this._trackFinish = false;
          this.fpsAble = true;
        }

        /**
         * 初始化轨迹组件
         * @param target 目标对象
         * @param trackGroups 轨迹组
         * @param trackParams 轨迹参数
         * @param startX 起始 X 坐标
         * @param startY 起始 Y 坐标
         */
        init(target, trackGroups, trackParams, pos) {
          this.reset();
          this._target = target;
          this._initPos = pos;
          this._posX = pos.x;
          this._posY = pos.y;

          this._setTargetPos(this._posX, this._posY);

          if (trackGroups.length > 0) {
            this._trackGroups = trackGroups;

            if (trackParams && trackParams.length === 3) {
              this._bTrackOverDie = false;
              this._liveTime = trackParams[0];
              this._leaveAct = trackParams[1];
              this._leaveSpeed = trackParams[2];
            } else {
              this._bTrackOverDie = true;
            }
          }
        }

        reset() {
          this._trackAble = false;
          this._formIndex = -100;
          this._isTrackLeave = false;
          this._trackType = -1;
          this._curTrack = null;
          this._curSpeed = 0;
          this._curAccelerate = 0;
          this._curTrackInterval = 0;
          this._trackLoop = 0;
          this._trackGroups = [];
          this._tracks = [];
          this._speeds = [];
          this._accelerates = [];
          this._trackIntervals = [];
          this._trackGroupIndex = 0;
          this._trackIndex = 0;
          this._trackTime = 0;
          this._trackOffX = 0;
          this._trackOffY = 0;
          this._bTrackOver = false;
          this._bTrackOverDie = false;
          this._trackStayTime = 0;
          this._bMoving = false;
          this._liveTime = -1;
          this._leaveAct = -1;
          this._leaveSpeed = 0;
          this._leaveAccelerate = 0;
          this._liveCount = 0;
          this._trackFinish = false;
        }
        /**
         * 更新轨迹逻辑
         * @param deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          if (this._trackAble) {
            this._checkLiveAble(deltaTime);

            this._updateMove(deltaTime);
          }
        }
        /**
         * 开始轨迹
         */


        startTrack() {
          if (this._trackGroups && this._trackGroups.length > 0) {
            this._refreshTrackDatas();

            this._refreshCurTrackData(true);

            this._bMoving = true;
          }
        }
        /**
         * 重置轨迹
         */


        resetTrack() {
          this._trackGroupIndex = 0;
          this._trackIndex = 0;
          this._posX = this._initPos.x;
          this._posY = this._initPos.y;

          this._setTargetPos(this._posX, this._posY);

          this.startTrack();
        }
        /**
         * 设置轨迹组索引
         * @param index 轨迹组索引
         */


        setTrackGroupIndex(index) {
          this._trackGroupIndex = index;
          this._trackIndex = 0;
          this.startTrack();
        }
        /**
         * 刷新轨迹数据
         */


        _refreshTrackDatas() {
          if (this._trackGroups) {
            const group = this._trackGroups[this._trackGroupIndex];
            this._trackLoop = group.loopNum;
            this._tracks = group.trackIDs.map(id => (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.getTrackDataForID(id));
            this._speeds = [...group.speeds];
            this._accelerates = [...group.accelerates];
            this._trackIntervals = [...group.trackIntervals];
            this._bForm = false;

            if (this._formIndex !== group.formIndex) {
              this._formIndex = group.formIndex;

              this._setTargetFormIndex(this._formIndex);

              this._bForm = true;
            }

            if (this._trackGroupStartCall) {
              this._trackGroupStartCall(this._trackGroupIndex, this._trackType, group.type);
            }

            this._trackType = group.type;
          }
        }
        /**
         * 刷新当前轨迹数据
         * @param isFirst 是否为首次刷新
         */


        _refreshCurTrackData(isFirst = false) {
          const track = this._tracks[this._trackIndex];
          this._curSpeed = this._speeds[this._trackIndex];
          this._curTrack = track;
          this._curTrackInterval = this._trackIntervals[this._trackIndex];
          this._isTrackLeave = false;
          this._trackFinish = false;

          if (track.type === 11) {
            this._bTrackOverDie = false;
          }

          if (isFirst) {
            this.refreshTrackOffset();
          }

          if (this._trackStartCall) {
            this._trackStartCall(this._curTrack);
          }
        }
        /**
         * 更新移动逻辑
         * @param deltaTime 帧间隔时间
         */


        _updateMove(deltaTime) {
          if (this._bMoving) {
            this._prePosX = this._posX;
            this._prePosY = this._posY;
            const track = this._curTrack;
            const speed = this._curSpeed;

            switch (track.type) {
              case 0:
              case 4:
                const bezierX = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getBezier(track.startX, track.control1X, track.control2X, track.endX, this._trackTime);
                const bezierY = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getBezier(track.startY, track.control1Y, track.control2Y, track.endY, this._trackTime);
                this.setPos(bezierX + this._trackOffX, bezierY + this._trackOffY);

                if (this._trackTime > 0) {
                  const dir = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).getDir(this._prePosX, this._prePosY, this._posX, this._posY);

                  this._setTargetDestDir(dir);

                  this._setTargetDestAngle(-(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).getDegreeForDir(dir));
                }

                break;

              case 1:
              case 5:
                const straight = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getStraight(new Vec2(this._prePosX, this._prePosY), new Vec2(track.endX, track.endY).add(new Vec2(this._trackOffX, this._trackOffY)), speed, deltaTime);
                this.setPos(straight.x, straight.y);

                if (this._trackTime === 0) {
                  const dir = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).getDir(this._prePosX, this._prePosY, this._posX, this._posY);

                  this._setTargetDestDir(dir);

                  this._setTargetDestAngle(-(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).getDegreeForDir(dir));
                }

                break;

              case 2:
              case 3:
                this.setPos(this.node.position.x, this.node.position.y);
                break;

              case 11:
                const straightForDir = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getStraightForDir(new Vec2(this._prePosX, this._posY), this._target.getDir(), speed, deltaTime);
                this.setPos(straightForDir.x, straightForDir.y);

                if (this._trackTime < this._curTrackInterval) {
                  this._rushTime += deltaTime;

                  if (this._rushTime > this._rushDuration) {
                    this._rushTime = 0;
                    const worldPos = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO).add(new Vec3(this._curTrack.endX, this._curTrack.endY));
                    const localPos = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                    const dir = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).getDir(localPos.x, localPos.y, worldPos.x, worldPos.y);

                    this._setTargetDestDir(dir);

                    this._setTargetDestAngle(-(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).getDegreeForDir(dir));
                  }
                }

                break;
            }

            this._setTargetPos(this._posX, this._posY);

            let isTrackOver = false;

            switch (track.type) {
              case 0:
              case 4:
                this._trackTime += speed;

                if (this._trackTime > 1) {
                  isTrackOver = true;
                }

                break;

              case 1:
              case 5:
                this._trackTime = 1;
                const offsetX = this._posX - (track.endX + this._trackOffX);
                const offsetY = this._posY - (track.endY + this._trackOffY);
                const deltaX = track.endX - track.startX;
                const deltaY = track.endY - track.startY;

                if (offsetX * deltaX >= 0 && offsetY * deltaY >= 0) {
                  isTrackOver = true;
                }

                break;

              case 2:
              case 3:
                if (this._trackFinish) {
                  isTrackOver = true;
                }

                break;

              case 11:
                this._trackTime += deltaTime;

                if (this._trackTime > this._curTrackInterval && (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {
                  this._setTargetDestDir(this._target.getDir());

                  this._setTargetDestAngle(this._target.getAngle());

                  isTrackOver = this._isTrackLeave = true;
                  this._curTrackInterval = 0;
                }

                break;
            }

            if (isTrackOver) {
              if (this._curTrackInterval > 0) {
                this._bMoving = false;
                this._trackStayTime = 0;
              } else if (this._isTrackLeave) {
                if (this._trackLeaveCall) {
                  this._trackLeaveCall();

                  this._trackLeaveCall = null;
                }
              } else {
                this._gotoNextTrack();
              }
            }
          } else {
            this._trackStayTime += deltaTime;

            if (!this._bTrackOver && this._trackStayTime > this._curTrackInterval) {
              this._gotoNextTrack();
            }
          }
        }
        /**
         * 跳转到下一轨迹
         */


        _gotoNextTrack() {
          this._trackIndex++;

          if (this._trackIndex >= this._tracks.length && this._trackOver()) {} else {
            this._refreshCurTrackData();

            this._trackOffX = this._posX - this._curTrack.startX;
            this._trackOffY = this._posY - this._curTrack.startY;
            this._trackTime = 0;

            if (this._trackChangeCall) {
              this._trackChangeCall(this._trackIndex);

              this._trackChangeCall = null;
            }

            this._bForm = false;
            this._bMoving = true;
          }
        }
        /**
         * 检查是否存活
         * @param deltaTime 帧间隔时间
         * @returns {boolean} 是否存活
         */


        _checkLiveAble(deltaTime) {
          this._liveCount += deltaTime;

          if (this._liveTime >= 0 && this._liveCount > this._liveTime && this._target.checkLiveAble && this._target.checkLiveAble()) {
            if (this._leaveAct === 0 || this._leaveAct > 0 && this._gotoLeaveTrack()) {
              return true;
            }
          }

          return false;
        }
        /**
         * 跳转到离开轨迹
         */


        _gotoLeaveTrack() {
          this._curTrack = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.getTrackDataForID(this._leaveAct);
          this._curSpeed = this._leaveSpeed;
          this._curTrackInterval = 0;
          this._trackOffX = this._posX - this._curTrack.startX;
          this._trackOffY = this._posY - this._curTrack.startY;
          this._trackTime = 0;
          this._bMoving = true;
          this._isTrackLeave = true;
        }
        /**
         * 轨迹结束
         * @returns {boolean} 是否结束
         */


        _trackOver() {
          if (this._trackLoop < this.TRACK_LOOP_FOREVER) {
            this._trackLoop--;

            if (this._trackLoop <= 0) {
              if (this._trackGroupOverCall) {
                this._trackGroupOverCall(this._trackGroupIndex);
              }

              this._trackGroupIndex++;

              if (this._trackGroupIndex >= this._trackGroups.length) {
                this._bTrackOver = true;

                if (this._bTrackOverDie) {
                  if (this._trackOverCall) {
                    this._trackOverCall();

                    this._trackOverCall = null;
                  }
                } else {
                  this._bMoving = false;
                }

                return true;
              }

              this._refreshTrackDatas();
            }
          }

          this._trackIndex = 0;
          this._trackTime = 0;

          this._refreshCurTrackData();

          return false;
        }
        /**
        * 设置轨迹是否可用
        * @param {boolean} isAble 是否可用
        */


        setTrackAble(isAble) {
          if (this._trackGroups && this._trackGroups.length > 0) {
            this._trackAble = isAble;
          }
        }
        /**
         * 获取轨迹是否完成
         * @returns {boolean} 是否完成
         */


        get trackFinish() {
          return this._trackFinish;
        }
        /**
         * 设置轨迹是否完成
         * @param {boolean} isFinish 是否完成
         */


        set trackFinish(isFinish) {
          this._trackFinish = isFinish;
        }
        /**
         * 获取轨迹是否可用
         * @returns {boolean} 是否可用
         */


        get trackAble() {
          return this._trackAble;
        }
        /**
         * 检查轨迹是否结束
         * @returns {boolean} 是否结束
         */


        isTrackOver() {
          return this._bTrackOver;
        }
        /**
         * 获取轨迹的 X 偏移量
         * @returns {number} X 偏移量
         */


        get trackOffsetX() {
          return this._trackOffX;
        }
        /**
         * 获取轨迹的 Y 偏移量
         * @returns {number} Y 偏移量
         */


        get trackOffsetY() {
          return this._trackOffY;
        }
        /**
         * 检查是否正在移动
         * @returns {boolean} 是否正在移动
         */


        get isMoving() {
          return this._bMoving;
        }
        /**
         * 设置目标位置
         * @param {number} x X 坐标
         * @param {number} y Y 坐标
         */


        setPos(x, y) {
          this._posX = x;
          this._posY = y;
        }
        /**
         * 刷新轨迹偏移量
         */


        refreshTrackOffset() {
          this._trackOffX = this._posX - this._curTrack.startX;
          this._trackOffY = this._posY - this._curTrack.startY;
        }
        /**
         * 设置轨迹变化回调
         * @param {Function} callback 回调函数
         */


        setTrackChangeCall(callback) {
          this._trackChangeCall = callback;
        }
        /**
         * 设置轨迹开始回调
         * @param {Function} callback 回调函数
         */


        setTrackStartCall(callback) {
          this._trackStartCall = callback;
        }
        /**
         * 设置轨迹组开始回调
         * @param {Function} callback 回调函数
         */


        setTrackGroupStartCall(callback) {
          this._trackGroupStartCall = callback;
        }
        /**
         * 设置轨迹组结束回调
         * @param {Function} callback 回调函数
         */


        setTrackGroupOverCall(callback) {
          this._trackGroupOverCall = callback;
        }
        /**
         * 设置轨迹结束回调
         * @param {Function} callback 回调函数
         */


        setTrackOverCall(callback) {
          this._trackOverCall = callback;
        }
        /**
         * 设置轨迹离开回调
         * @param {Function} callback 回调函数
         */


        setTrackLeaveCall(callback) {
          this._trackLeaveCall = callback;
        }
        /**
         * 设置目标位置
         * @param {number} x X 坐标
         * @param {number} y Y 坐标
         */


        _setTargetPos(x, y) {
          if (this._target.setPos) {
            this._target.setPos(x, y, true);
          }
        }
        /**
         * 设置目标方向
         * @param {Vec2} dir 方向
         */


        _setTargetDir(dir) {
          if (this._target.setDir) {
            this._target.setDir(dir);
          }
        }
        /**
         * 设置目标目标方向
         * @param {Vec2} dir 目标方向
         */


        _setTargetDestDir(dir) {
          if (this._target.setDestDir) {
            this._target.setDestDir(dir);
          }
        }
        /**
         * 设置目标目标角度
         * @param {number} angle 目标角度
         */


        _setTargetDestAngle(angle) {
          if (this._target.setDestAngle) {
            this._target.setDestAngle(angle);
          }
        }
        /**
         * 设置目标形态索引
         * @param {number} index 形态索引
         */


        _setTargetFormIndex(index) {
          if (this._target.setFormIndex) {
            this._target.setFormIndex(index);
          }
        }

      }) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=497f206f6c83c084c7377e934f24fe020e2b6896.js.map