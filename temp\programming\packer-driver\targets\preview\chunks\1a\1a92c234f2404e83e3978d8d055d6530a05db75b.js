System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, Prefab, instantiate, SingletonBase, GameIns, Tools, TrackData, GameFunc, EnemyPlane, GameEnum, BattleLayer, MyApp, GameResourceList, EnemyData, EnemyManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackData(extras) {
    _reporterNs.report("TrackData", "../data/TrackData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../data/EnemyData", _context.meta, extras);
  }

  _export("EnemyManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      TrackData = _unresolved_5.TrackData;
    }, function (_unresolved_6) {
      GameFunc = _unresolved_6.GameFunc;
    }, function (_unresolved_7) {
      EnemyPlane = _unresolved_7.default;
    }, function (_unresolved_8) {
      GameEnum = _unresolved_8.GameEnum;
    }, function (_unresolved_9) {
      BattleLayer = _unresolved_9.default;
    }, function (_unresolved_10) {
      MyApp = _unresolved_10.MyApp;
    }, function (_unresolved_11) {
      GameResourceList = _unresolved_11.default;
    }, function (_unresolved_12) {
      EnemyData = _unresolved_12.EnemyData;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['NodePool', 'Prefab', 'Sprite', 'SpriteAtlas', 'Node', 'instantiate']);

      _export("EnemyManager", EnemyManager = class EnemyManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get enemies() {
          return this._planeArr;
        }

        constructor() {
          super();
          this._normalCount = 0;
          this._trackDatas = new Map();
          this._pfPlane = null;
          this._planePool = [];
          this._planeArr = [];
          this._willDeadPlane = [];
          this.initConfig();
        }

        initConfig() {
          var tracks = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbTrack.getDataList();

          for (var track of tracks) {
            var trackData = new (_crd && TrackData === void 0 ? (_reportPossibleCrUseOfTrackData({
              error: Error()
            }), TrackData) : TrackData)();
            trackData.loadJson(track);

            this._trackDatas.set(trackData.trackID, trackData);
          }
        }
        /**
         * 预加载资源
         * @param stage 当前关卡
         */


        preLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EnemyPlane, Prefab, (error, prefab) => {
            this._pfPlane = prefab;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }

        addPlane(id, trackData) {
          var _this = this;

          return _asyncToGenerator(function* () {
            try {
              var planeData = new (_crd && EnemyData === void 0 ? (_reportPossibleCrUseOfEnemyData({
                error: Error()
              }), EnemyData) : EnemyData)();
              planeData.planeId = id;

              var node = _this._planePool.pop() || _this.createNewPlane();

              (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
                error: Error()
              }), BattleLayer) : BattleLayer).me.addEnemy(node);
              var plane = node.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
                error: Error()
              }), EnemyPlane) : EnemyPlane);
              plane.initPlane(planeData, trackData);
              plane.new_uuid = (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
                error: Error()
              }), GameFunc) : GameFunc).uuid;

              _this.pushPlane(plane);

              _this._normalCount++;
              return plane;
            } catch (error) {
              return null;
            }
          })();
        }

        createNewPlane() {
          if (!this._pfPlane) {
            throw new Error("Plane prefab is not initialized. Call preLoad() first.");
          }

          var node = instantiate(this._pfPlane);
          return node;
        }
        /**
         * 获取所有敌机
         */


        get planes() {
          return this._planeArr;
        }
        /**
         * 移除所有存活的敌机
         */


        removeAllAlivePlane() {
          for (var plane of this._planeArr) {
            if (!plane.isDead) {
              plane.willRemove();
            }
          }
        }
        /**
         * 添加敌机到管理器
         * @param plane 敌机对象
         */


        pushPlane(plane) {
          if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrContain(this._planeArr, plane)) {
            this._planeArr.push(plane);
          }
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (var i = 0; i < this._planeArr.length; i++) {
            var plane = this._planeArr[i];

            if (plane.removeAble) {
              this.removePlaneForIndex(i);
              i--;
            } else {
              if (plane.isDead) {
                if (plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Turret || plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Ship) {
                  this._willDeadPlane.push(plane);

                  this._planeArr.splice(i, 1);

                  i--;
                  continue;
                }
              }

              plane.updateGameLogic(deltaTime);
            }
          }

          for (var _i = 0; _i < this._willDeadPlane.length; _i++) {
            var _plane = this._willDeadPlane[_i];

            if (_plane.removeAble) {
              this.removePlaneForIndex(_i, true);
              _i--;
            } else {
              _plane.updateGameLogic(deltaTime);
            }
          }
        }
        /**
        * 重置主关卡
        */


        mainReset() {
          this.subReset(); // this._mainStage = -1;
          // this._subStage = -1;
          // 清理飞机池

          for (var plane of this._planePool) {
            plane.destroy();
          }

          this._planePool.splice(0); // 清理即将死亡的飞机


          for (var _plane2 of this._willDeadPlane) {
            if (_plane2 && _plane2.node) {
              _plane2.node.destroy();
            }
          }

          this._willDeadPlane = [];
        }
        /**
         * 重置子关卡
         */


        subReset() {
          var EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          for (var plane of this._planeArr) {
            switch (plane.type) {
              case EnemyType.Normal:
                plane.willRemove();

                this._planePool.push(plane.node);

                break;
            }

            plane.node.removeFromParent();
          }

          this._planeArr.splice(0);
        }
        /**
         * 清理敌人管理器
         */


        clear() {}
        /**
         * 检查敌人是否全部消灭
         */


        isEnemyOver() {
          return this._planeArr.length === 0;
        }
        /**
         * 获取普通敌机数量
         */


        getNormalPlaneCount() {
          return this._normalCount;
        }
        /**
         * 根据轨迹 ID 获取轨迹数据
         * @param trackId 轨迹 ID
         */


        getTrackDataForID(trackId) {
          var trackData = null;

          try {
            trackData = this._trackDatas.get(trackId) || null;
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("getTrackData error: " + trackId, error);
          }

          return trackData;
        }
        /**
         * 根据索引移除敌机
         * @param index 索引
         * @param isDead 是否为死亡敌机
         */


        removePlaneForIndex(index, isDead) {
          if (isDead === void 0) {
            isDead = false;
          }

          if (isDead) {
            this._willRemovePlane(this._willDeadPlane[index]);

            this._willDeadPlane.splice(index, 1);
          } else {
            this._willRemovePlane(this._planeArr[index]);

            this._planeArr.splice(index, 1);
          }
        }
        /**
         * 处理即将移除的敌机
         * @param plane 敌机对象
         */


        _willRemovePlane(plane) {
          var EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          for (var _plane3 of this._planeArr) {
            switch (_plane3.type) {
              case EnemyType.Normal:
                this._normalCount--;

                this._planePool.push(_plane3.node);

                break;
            }
          }

          plane.node.removeFromParent();
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=1a92c234f2404e83e3978d8d055d6530a05db75b.js.map