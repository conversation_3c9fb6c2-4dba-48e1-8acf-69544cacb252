{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/BulletData.ts"], "names": ["_decorator", "EventGroupData", "ExpressionValue", "ccclass", "property", "BulletData", "displayName", "group", "visible", "type", "scaleStr", "scale", "raw", "value", "durationStr", "duration", "delayDestroyStr", "delayDestroy", "speedStr", "speed", "accelerationStr", "acceleration", "accelerationAngleStr", "accelerationAngle", "fromJSON", "json", "data", "Object", "assign", "eventGroupData", "map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;AAE9B;AACA;AACA;AACA;;4BAEaK,U,WADZF,OAAO,CAAC,YAAD,C,UASHC,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,UAAd;AAA0BC,QAAAA,KAAK,EAAE;AAAjC,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,UAAd;AAA0BC,QAAAA,KAAK,EAAE;AAAjC,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,UAAd;AAA0BC,QAAAA,KAAK,EAAE;AAAjC,OAAD,C,UAIRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,UAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,OAAd;AAAuBC,QAAAA,KAAK,EAAE;AAA9B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,OAAd;AAAuBC,QAAAA,KAAK,EAAE;AAA9B,OAAD,C,WAIRH,QAAQ,CAAC;AAACK,QAAAA,IAAI,EAAE;AAAA;AAAA,6CAAP;AAAyBH,QAAAA,WAAW,EAAE,KAAtC;AAA6CC,QAAAA,KAAK,EAAE;AAApD,OAAD,C,2BAlEb,MACaF,UADb,CACwB;AAAA;AACpB;AACA;AACA;AACA;AACA;AAEA;AAPoB;;AAUpB;AAVoB;;AAapB;AAboB;;AAgBpB;AAhBoB;;AAmBpB;AAnBoB;;AAuBpB;AAvBoB;;AA8BpB;AA9BoB;;AAqCpB;AArCoB;;AA4CpB;AA5CoB;;AAmDpB;AAnDoB;;AA0DpB;AA1DoB;;AAAA;AAAA;;AA2BD,YAARK,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWC,GAAlB;AAAwB;;AACrC,YAARF,QAAQ,CAACG,KAAD,EAAgB;AAAE,eAAKF,KAAL,CAAWC,GAAX,GAAiBC,KAAjB;AAAyB;;AAMxC,YAAXC,WAAW,GAAW;AAAE,iBAAO,KAAKC,QAAL,CAAcH,GAArB;AAA2B;;AACxC,YAAXE,WAAW,CAACD,KAAD,EAAgB;AAAE,eAAKE,QAAL,CAAcH,GAAd,GAAoBC,KAApB;AAA4B;;AAM1C,YAAfG,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBL,GAAzB;AAA+B;;AAC5C,YAAfI,eAAe,CAACH,KAAD,EAAgB;AAAE,eAAKI,YAAL,CAAkBL,GAAlB,GAAwBC,KAAxB;AAAgC;;AAMzD,YAARK,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWP,GAAlB;AAAwB;;AACrC,YAARM,QAAQ,CAACL,KAAD,EAAgB;AAAE,eAAKM,KAAL,CAAWP,GAAX,GAAiBC,KAAjB;AAAyB;;AAMpC,YAAfO,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBT,GAAzB;AAA+B;;AAC5C,YAAfQ,eAAe,CAACP,KAAD,EAAgB;AAAE,eAAKQ,YAAL,CAAkBT,GAAlB,GAAwBC,KAAxB;AAAgC;;AAM7C,YAApBS,oBAAoB,GAAW;AAAE,iBAAO,KAAKC,iBAAL,CAAuBX,GAA9B;AAAoC;;AACjD,YAApBU,oBAAoB,CAACT,KAAD,EAAgB;AAAE,eAAKU,iBAAL,CAAuBX,GAAvB,GAA6BC,KAA7B;AAAqC;;AAKvE,eAARW,QAAQ,CAACC,IAAD,EAAwB;AACnC,cAAMC,IAAI,GAAG,IAAIrB,UAAJ,EAAb;;AACA,cAAIoB,IAAJ,EAAU;AACNE,YAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACAC,YAAAA,IAAI,CAACG,cAAL,GAAsB,CAACJ,IAAI,CAACI,cAAL,IAAuB,EAAxB,EAA4BC,GAA5B,CAAgC;AAAA;AAAA,kDAAeN,QAA/C,CAAtB;AACH;;AAED,iBAAOE,IAAP;AACH;;AA5EmB,O;;;;;iBASQ,K;;;;;;;iBAGC,K;;;;;;;iBAGE,I;;;;;;;iBAGL,K;;;;;;;iBAGK,K;;;;;;;iBAIE;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOG;AAAA;AAAA,kDAAoB,OAApB,C;;;;;;;iBAOI;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOP;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAOO;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOF;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMH,E", "sourcesContent": ["import { _decorator, error, v2, Vec2 } from \"cc\";\r\nimport { EventGroupData } from \"./EventGroupData\";\r\nimport { ExpressionValue } from \"./ExpressionValue\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 子弹数据\r\n * 所有时间相关的，单位都是毫秒(ms)\r\n */\r\n@ccclass(\"BulletData\")\r\nexport class BulletData {\r\n    // 这些数据在子弹表格里\r\n    // @property({displayName: '子弹伤害'})\r\n    // damage : number = 1;                      // 子弹伤害\r\n    // @property({displayName: '子弹Prefab'})\r\n    // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等\r\n\r\n    // 是否朝向行进方向\r\n    @property({displayName: '是否朝向行进方向', group: '基础属性'})\r\n    isFacingMoveDir : boolean = false;\r\n    // 是否追踪目标\r\n    @property({displayName: '是否追踪目标', group: '基础属性'})\r\n    isTrackingTarget : boolean = false;\r\n    // 是否离开屏幕自动销毁\r\n    @property({displayName: '是否离屏自动销毁', group: '基础属性'})\r\n    isDestroyOutScreen : boolean = true;\r\n    // 是否可被破坏\r\n    @property({displayName: '是否可被破坏', group: '基础属性'})\r\n    isDestructive : boolean = false; \r\n    // 命中时是否被销毁\r\n    @property({displayName: '命中时是否被销毁', group: '基础属性'})\r\n    isDestructiveOnHit : boolean = false;\r\n\r\n    // 子弹基础缩放\r\n    @property({visible:false})\r\n    public scale : ExpressionValue = new ExpressionValue('1');\r\n    @property({displayName: '子弹基础缩放', group: '基础属性'})\r\n    public get scaleStr(): string { return this.scale.raw; }\r\n    public set scaleStr(value: string) { this.scale.raw = value; }\r\n\r\n    // 子弹持续时间(超出后销毁回收)\r\n    @property({visible:false})\r\n    public duration : ExpressionValue = new ExpressionValue('10000');\r\n    @property({displayName: '子弹持续时间', group: '基础属性'})\r\n    public get durationStr(): string { return this.duration.raw; }\r\n    public set durationStr(value: string) { this.duration.raw = value; }\r\n\r\n    // 延迟销毁时间\r\n    @property({visible:false})\r\n    public delayDestroy : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '延迟销毁时间', group: '基础属性'})\r\n    public get delayDestroyStr(): string { return this.delayDestroy.raw; }\r\n    public set delayDestroyStr(value: string) { this.delayDestroy.raw = value; }\r\n\r\n    // 子弹速度\r\n    @property({visible:false})\r\n    public speed : ExpressionValue = new ExpressionValue('600');\r\n    @property({displayName: '子弹速度', group: '基础属性'})\r\n    public get speedStr(): string { return this.speed.raw; }\r\n    public set speedStr(value: string) { this.speed.raw = value; }\r\n\r\n    // 子弹加速度\r\n    @property({visible:false})\r\n    public acceleration : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '子弹加速度', group: '基础属性'})\r\n    public get accelerationStr(): string { return this.acceleration.raw; }\r\n    public set accelerationStr(value: string) { this.acceleration.raw = value; }\r\n\r\n    // 加速度方向(角度) 0表示朝向移动方向, 90表示朝向发射方向\r\n    @property({visible:false})\r\n    accelerationAngle : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '加速度方向', group: '基础属性'})\r\n    public get accelerationAngleStr(): string { return this.accelerationAngle.raw; }\r\n    public set accelerationAngleStr(value: string) { this.accelerationAngle.raw = value; }\r\n\r\n    @property({type: [EventGroupData], displayName: '事件组', group: '事件组'})\r\n    eventGroupData: EventGroupData[] = [];\r\n\r\n    static fromJSON(json: any): BulletData {\r\n        const data = new BulletData();\r\n        if (json) {\r\n            Object.assign(data, json);\r\n            data.eventGroupData = (json.eventGroupData || []).map(EventGroupData.fromJSON);\r\n        }\r\n\r\n        return data;\r\n    }\r\n}\r\n"]}