{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts"], "names": ["_decorator", "Component", "Node", "UITransform", "ccclass", "property", "EnemyEffectLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "me", "start", "addComponent", "parent", "node", "setPosition"], "mappings": ";;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAuEC,MAAAA,W,OAAAA,W;;;;;;;OAGjG;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,gB,WADpBF,OAAO,CAAC,kBAAD,C,2BAAR,MACqBE,gBADrB,SAC8CL,SAD9C,CACwD;AAAA;AAAA;AAAA,eAGpDM,eAHoD,GAGrB,IAHqB;AAAA,eAIpDC,YAJoD,GAIxB,IAJwB;AAAA;;AAMpDC,QAAAA,MAAM,GAAG;AACLH,UAAAA,gBAAgB,CAACI,EAAjB,GAAsB,IAAtB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,eAAL,GAAuB,IAAIL,IAAJ,EAAvB;AACA,eAAKK,eAAL,CAAqBK,YAArB,CAAkCT,WAAlC;AACA,eAAKI,eAAL,CAAqBM,MAArB,GAA8B,KAAKC,IAAnC;AACA,eAAKP,eAAL,CAAqBQ,WAArB,CAAiC,CAAjC,EAAoC,CAApC;AAEA,eAAKP,YAAL,GAAoB,IAAIN,IAAJ,EAApB;AACA,eAAKM,YAAL,CAAkBI,YAAlB,CAA+BT,WAA/B;AACA,eAAKK,YAAL,CAAkBK,MAAlB,GAA2B,KAAKC,IAAhC;AACA,eAAKN,YAAL,CAAkBO,WAAlB,CAA8B,CAA9B,EAAiC,CAAjC;AACH;;AApBmD,O,UAC7CL,E", "sourcesContent": ["import { _decorator, Component, Node, Prefab, NodePool, Vec3, sp, SpriteFrame, instantiate, UIOpacity, UITransform } from 'cc';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyEffectLayer')\r\nexport default class EnemyEffectLayer extends Component {\r\n    static me: EnemyEffectLayer;\r\n\r\n    hurtEffectLayer: Node | null = null;\r\n    hurtNumLayer: Node | null = null;\r\n\r\n    onLoad() {\r\n        EnemyEffectLayer.me = this;\r\n    }\r\n\r\n    start() {\r\n        this.hurtEffectLayer = new Node();\r\n        this.hurtEffectLayer.addComponent(UITransform);\r\n        this.hurtEffectLayer.parent = this.node;\r\n        this.hurtEffectLayer.setPosition(0, 0);\r\n\r\n        this.hurtNumLayer = new Node();\r\n        this.hurtNumLayer.addComponent(UITransform);\r\n        this.hurtNumLayer.parent = this.node;\r\n        this.hurtNumLayer.setPosition(0, 0);\r\n    }\r\n}"]}