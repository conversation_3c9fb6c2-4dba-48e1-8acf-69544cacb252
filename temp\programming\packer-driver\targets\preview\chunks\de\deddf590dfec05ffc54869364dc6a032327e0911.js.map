{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts"], "names": ["BulletProperty", "_decorator", "Sprite", "Color", "EDITOR", "ObjectPool", "Movable", "eSpriteDefaultFacing", "BulletSystem", "EventGroupContext", "PropertyContainer", "FCollider", "ColliderGroupType", "FBoxCollider", "Entity", "eEntityTag", "log<PERSON>arn", "ccclass", "property", "executeInEditMode", "constructor", "duration", "delayDestroy", "attack", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "defaultFacing", "isDestroyOutScreen", "isDestructive", "isDestructiveOnHit", "isFacingMoveDir", "isTrackingTarget", "addProperty", "WHITE", "Up", "resetFromData", "data", "value", "eval", "copyFrom", "other", "forEachProperty", "k", "prop", "getPropertyValue", "clear", "Bullet", "type", "displayName", "isAlive", "elapsedTime", "emitter", "bulletData", "eventGroups", "onLoad", "mover", "getComponent", "addComponent", "onBecomeInvisibleCallback", "onDestroyBullet", "collider", "boxCollider", "on", "node", "setScale", "bulletSprite", "onCreate", "bulletID", "ent", "getEntity", "isShootFromEnemy", "hasTag", "Player", "initBaseData", "groupType", "BULLET_ENEMY", "BULLET_SELF", "isEnable", "addTag", "EnemyBullet", "<PERSON><PERSON><PERSON><PERSON>", "resetProperties", "setMovable", "destroySelf", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "returnNode", "bulletProp", "notifyAll", "resetEventGroups", "eventGroupData", "length", "ctx", "bullet", "eventName", "createBulletEventGroup", "tick", "dt", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "group", "stop", "removeAllComp", "clearTags", "scheduleOnce", "onCollide", "remove", "getAttack"], "mappings": ";;;+QAkBaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlBJC,MAAAA,U,OAAAA,U;AAAmCC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAC3CC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,oB,iBAAAA,oB;;AACTC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,iB,iBAAAA,iB;;AAGZC,MAAAA,S;AAAaC,MAAAA,iB,iBAAAA,iB;;AACbC,MAAAA,Y;;AACAC,MAAAA,M;;AACEC,MAAAA,U,kBAAAA,U;;AACQC,MAAAA,O,kBAAAA,O;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2ClB,U;;gCAEpCD,c,GAAN,MAAMA,cAAN;AAAA;AAAA,kDAAuD;AAiBP;AAEnDoB,QAAAA,WAAW,GAAG;AACV;AADU,eAlBPC,QAkBO;AAlBqC;AAkBrC,eAjBPC,YAiBO;AAjBqC;AAiBrC,eAfPC,MAeO;AAfqC;AAerC,eAdPC,KAcO;AAdqC;AAcrC,eAbPC,UAaO;AAbqC;AAarC,eAZPC,YAYO;AAZqC;AAYrC,eAXPC,iBAWO;AAXqC;AAWrC,eAVPC,KAUO;AAVqC;AAUrC,eATPC,KASO;AATqC;AASrC,eARPC,aAQO;AARkD;AAQlD,eANPC,kBAMO;AANqC;AAMrC,eALPC,aAKO;AALqC;AAKrC,eAJPC,kBAIO;AAJqC;AAIrC,eAHPC,eAGO;AAHqC;AAGrC,eAFPC,gBAEO;AAEV,eAAKd,QAAL,GAAgB,KAAKe,WAAL,CAAiB,CAAjB,EAAoB,IAApB,CAAhB;AACA,eAAKd,YAAL,GAAoB,KAAKc,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKb,MAAL,GAAc,KAAKa,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAd;AACA,eAAKZ,KAAL,GAAa,KAAKY,WAAL,CAAiB,CAAjB,EAAoB,GAApB,CAAb;AACA,eAAKX,UAAL,GAAkB,KAAKW,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAlB;AACA,eAAKV,YAAL,GAAoB,KAAKU,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKT,iBAAL,GAAyB,KAAKS,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAzB;AACA,eAAKR,KAAL,GAAa,KAAKQ,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAb;AACA,eAAKP,KAAL,GAAa,KAAKO,WAAL,CAAiB,CAAjB,EAAoBjC,KAAK,CAACkC,KAA1B,CAAb;AACA,eAAKP,aAAL,GAAqB,KAAKM,WAAL,CAAuC,CAAvC,EAA0C;AAAA;AAAA,4DAAqBE,EAA/D,CAArB;AACA,eAAKP,kBAAL,GAA0B,KAAKK,WAAL,CAAiB,EAAjB,EAAqB,IAArB,CAA1B;AACA,eAAKJ,aAAL,GAAqB,KAAKI,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAArB;AACA,eAAKH,kBAAL,GAA0B,KAAKG,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAA1B;AACA,eAAKF,eAAL,GAAuB,KAAKE,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAvB;AACA,eAAKD,gBAAL,GAAwB,KAAKC,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAxB;AACH;;AAEMG,QAAAA,aAAa,CAACC,IAAD,EAAmB;AACnC,eAAKnB,QAAL,CAAcoB,KAAd,GAAsBD,IAAI,CAACnB,QAAL,CAAcqB,IAAd,EAAtB;AACA,eAAKpB,YAAL,CAAkBmB,KAAlB,GAA0BD,IAAI,CAAClB,YAAL,CAAkBoB,IAAlB,EAA1B;AACA,eAAKlB,KAAL,CAAWiB,KAAX,GAAmBD,IAAI,CAAChB,KAAL,CAAWkB,IAAX,EAAnB,CAHmC,CAInC;;AACA,eAAKhB,YAAL,CAAkBe,KAAlB,GAA0BD,IAAI,CAACd,YAAL,CAAkBgB,IAAlB,EAA1B;AACA,eAAKf,iBAAL,CAAuBc,KAAvB,GAA+BD,IAAI,CAACb,iBAAL,CAAuBe,IAAvB,EAA/B;AACA,eAAKd,KAAL,CAAWa,KAAX,GAAmBD,IAAI,CAACZ,KAAL,CAAWc,IAAX,EAAnB,CAPmC,CAQnC;;AACA,eAAKX,kBAAL,CAAwBU,KAAxB,GAAgCD,IAAI,CAACT,kBAArC;AACA,eAAKC,aAAL,CAAmBS,KAAnB,GAA2BD,IAAI,CAACR,aAAhC;AACA,eAAKC,kBAAL,CAAwBQ,KAAxB,GAAgCD,IAAI,CAACP,kBAArC;AACA,eAAKC,eAAL,CAAqBO,KAArB,GAA6BD,IAAI,CAACN,eAAlC;AACA,eAAKC,gBAAL,CAAsBM,KAAtB,GAA8BD,IAAI,CAACL,gBAAnC;AACH;;AAEMQ,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKC,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAa;AAC9BA,YAAAA,IAAI,CAACN,KAAL,GAAaG,KAAK,CAACI,gBAAN,CAAuBF,CAAvB,CAAb;AACH,WAFD;AAGH;;AAEMG,QAAAA,KAAK,GAAG;AACX;AACA,eAAKJ,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAaA,IAAI,CAACE,KAAL,EAAlC;AACH;;AA/DyD,O,GAkE9D;AACA;;;wBAGaC,M,WAFZjC,OAAO,CAAC,QAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAEbD,QAAQ,CAAC;AAACiC,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRlC,QAAQ,CAAC;AAACiC,QAAAA,IAAI,EAAEjD,MAAP;AAAekD,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRlC,QAAQ,CAAC;AAACiC,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,4CATb,MAEaF,MAFb;AAAA;AAAA,4BAEmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAUxBG,OAVwB,GAUL,KAVK;AAAA,eAWxBC,WAXwB,GAWF,CAXE;AAWS;AAXT,eAYxBC,OAZwB;AAAA,eAaxBC,UAbwB;AAc/B;AAd+B,eAexBT,IAfwB,GAeD,IAAI/C,cAAJ,EAfC;AAAA,eAgBxByD,WAhBwB,GAgBI,EAhBJ;AAAA;;AAkB/BC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AAAA;;AACb,iBAAKA,KAAL,yBAAa,KAAKC,YAAL;AAAA;AAAA,mCAAb,qBAAa,mBAA4BC,YAA5B;AAAA;AAAA,mCAAb;AACH;;AACD,eAAKF,KAAL,CAAWG,yBAAX,GAAuC,MAAM;AACzC,gBAAI,KAAKf,IAAL,CAAUhB,kBAAV,CAA6BU,KAAjC,EAAwC;AACpC;AAAA;AAAA,gDAAasB,eAAb,CAA6B,IAA7B;AACH;AACJ,WAJD;;AAKA,cAAI,CAAC,KAAKC,QAAV,EAAoB;AAChB,gBAAIC,WAAW,GAAG,KAAKJ,YAAL;AAAA;AAAA,6CAAlB;AACA,iBAAKG,QAAL,GAAgBC,WAAhB;AACH;;AAED,eAAKlB,IAAL,CAAUjB,aAAV,CAAwBW,KAAxB,GAAgC,KAAKkB,KAAL,CAAW7B,aAA3C,CAdW,CAeX;;AACA,eAAKiB,IAAL,CAAUb,eAAV,CAA0BgC,EAA1B,CAA8BzB,KAAD,IAAW;AACpC,iBAAKkB,KAAL,CAAWzB,eAAX,GAA6BO,KAA7B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUZ,gBAAV,CAA2B+B,EAA3B,CAA+BzB,KAAD,IAAW;AACrC,iBAAKkB,KAAL,CAAWxB,gBAAX,GAA8BM,KAA9B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUvB,KAAV,CAAgB0C,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAKkB,KAAL,CAAWnC,KAAX,GAAmBiB,KAAnB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUtB,UAAV,CAAqByC,EAArB,CAAyBzB,KAAD,IAAW;AAC/B,iBAAKkB,KAAL,CAAWlC,UAAX,GAAwBgB,KAAxB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUrB,YAAV,CAAuBwC,EAAvB,CAA2BzB,KAAD,IAAW;AACjC,iBAAKkB,KAAL,CAAWjC,YAAX,GAA0Be,KAA1B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUpB,iBAAV,CAA4BuC,EAA5B,CAAgCzB,KAAD,IAAW;AACtC,iBAAKkB,KAAL,CAAWhC,iBAAX,GAA+Bc,KAA/B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUnB,KAAV,CAAgBsC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAK0B,IAAL,CAAUC,QAAV,CAAmB3B,KAAnB,EAA0BA,KAA1B,EAAiCA,KAAjC;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUlB,KAAV,CAAgBqC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,gBAAI,KAAK4B,YAAT,EAAuB;AACnB,mBAAKA,YAAL,CAAkBxC,KAAlB,GAA0BY,KAA1B;AACH;AACJ,WAJD;AAKH;;AAEM6B,QAAAA,QAAQ,CAACf,OAAD,EAAmBgB,QAAnB,EAA2C;AACtD,eAAKlB,OAAL,GAAe,IAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKC,UAAL,GAAkBD,OAAO,CAACC,UAA1B,CAJsD,CAMtD;;AACA,cAAMgB,GAAG,GAAGjB,OAAO,CAACkB,SAAR,EAAZ;;AACA,cAAID,GAAJ,EACA;AACI,gBAAME,gBAAgB,GAAGF,GAAG,CAACG,MAAJ,CAAW;AAAA;AAAA,0CAAWC,MAAtB,IAAgC,KAAhC,GAAsC,IAA/D;AACA,iBAAKZ,QAAL,CAAea,YAAf,CAA4B,IAA5B;AACA,iBAAKb,QAAL,CAAec,SAAf,GAA2BJ,gBAAgB,GAAG;AAAA;AAAA,wDAAkBK,YAArB,GAAoC;AAAA;AAAA,wDAAkBC,WAAjG;AACA,iBAAKhB,QAAL,CAAeiB,QAAf,GAA0B,IAA1B;AACA,iBAAKC,MAAL,CAAYR,gBAAgB,GAAG;AAAA;AAAA,0CAAWS,WAAd,GAA4B;AAAA;AAAA,0CAAWC,YAAnE;AACH,WAPD,MAOK;AACD;AAAA;AAAA,oCAAQ,SAAR,EAAkB,iBAAlB,EAAsC,eAAtC;AACH;;AAED,eAAKC,eAAL;AACA,eAAK1B,KAAL,CAAW2B,UAAX,CAAsB,IAAtB;AACH;;AAEOC,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKpB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUqB,OAA7B,EAAsC;AAEtC,eAAKzC,IAAL,CAAUE,KAAV;;AAEA,cAAI7C,MAAJ,EAAY;AACR,iBAAK+D,IAAL,CAAUsB,OAAV;AACH,WAFD,MAEO;AACH;AAAA;AAAA,0CAAWC,UAAX,CAAsB,KAAKvB,IAA3B;AACH;AACJ;;AAEMkB,QAAAA,eAAe,GAAS;AAC3B,cAAI,CAAC,KAAK9B,OAAV,EAAmB;AAEnB,eAAKR,IAAL,CAAUJ,QAAV,CAAmB,KAAKY,OAAL,CAAaoC,UAAhC;AACA,eAAK5C,IAAL,CAAU6C,SAAV,CAAoB,IAApB;AACH;;AAEMC,QAAAA,gBAAgB,GAAS;AAC5B;AACA,cAAI,KAAKrC,UAAL,IAAmB,KAAKA,UAAL,CAAgBsC,cAAhB,CAA+BC,MAA/B,GAAwC,CAA/D,EAAkE;AAC9D,gBAAIC,GAAG,GAAG;AAAA;AAAA,yDAAV;AACAA,YAAAA,GAAG,CAACC,MAAJ,GAAa,IAAb;AACAD,YAAAA,GAAG,CAACzC,OAAJ,GAAc,KAAKA,OAAnB;;AACA,iBAAK,IAAM2C,SAAX,IAAwB,KAAK1C,UAAL,CAAgBsC,cAAxC,EAAwD;AACpD;AAAA;AAAA,gDAAaK,sBAAb,CAAoCH,GAApC,EAAyCE,SAAzC;AACH;AACJ;AACJ;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKhD,OAAV,EAAmB;AAEnB,eAAKC,WAAL,IAAoB+C,EAApB;;AACA,cAAI,KAAK/C,WAAL,GAAmB,KAAKP,IAAL,CAAU1B,QAAV,CAAmBoB,KAA1C,EAAiD;AAC7C;AAAA;AAAA,8CAAasB,eAAb,CAA6B,IAA7B;AACA;AACH,WAPyB,CAS1B;;;AACA,eAAKJ,KAAL,CAAYyC,IAAZ,CAAiBC,EAAE,GAAG,IAAtB;AACA,eAAKtD,IAAL,CAAU6C,SAAV;AACH;;AAEMU,QAAAA,WAAW,GAAS;AACvB,eAAKjD,OAAL,GAAe,KAAf;;AACA,cAAI,KAAKI,WAAL,IAAoB,KAAKA,WAAL,CAAiBsC,MAAjB,GAA0B,CAAlD,EACA;AACI,iBAAKtC,WAAL,CAAiB8C,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAlC,EADJ,CACqD;;AACjD,iBAAKhD,WAAL,GAAmB,EAAnB,CAFJ,CAE2B;AAC1B;;AAED,eAAKE,KAAL,CAAW2B,UAAX,CAAsB,KAAtB;AACA,eAAKtB,QAAL,CAAeiB,QAAf,GAA0B,KAA1B;AACA,eAAKyB,aAAL;AACA,eAAKC,SAAL;;AACA,cAAI,KAAK5D,IAAL,CAAUzB,YAAV,IAA0B,KAAKyB,IAAL,CAAUzB,YAAV,CAAuBmB,KAAvB,GAA+B,CAA7D,EAAgE;AAC5D,iBAAKmE,YAAL,CAAkB,MAAM;AACpB,mBAAKrB,WAAL;AACH,aAFD,EAEG,KAAKxC,IAAL,CAAUzB,YAAV,CAAuBmB,KAF1B;AAGH,WAJD,MAIO;AACH,iBAAK8C,WAAL;AACH;AACJ;;AAEDsB,QAAAA,SAAS,CAAC7C,QAAD,EAAsB;AAC3B,eAAK8C,MAAL;AACH;;AAEMA,QAAAA,MAAM,GAAG;AACZ;AAAA;AAAA,4CAAa/C,eAAb,CAA6B,IAA7B;AACH;;AAEDgD,QAAAA,SAAS,GAAU;AACf,cAAMvC,GAAG,GAAG,KAAKjB,OAAL,CAAakB,SAAb,EAAZ;AACA,iBAAOD,GAAG,CAAEuC,SAAL,EAAP;AACH;;AAlK8B,O;;;;;;;;;;iBAKI,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, misc, Component, Node, Sprite, Color, CCString } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable, eSpriteDefaultFacing } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from './EventGroup';\r\nimport { Property, PropertyContainer } from './PropertyContainer';\r\nimport { Emitter } from './Emitter';\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/scripts/game/collider-system/FCollider';\r\nimport FBoxCollider from 'db://assets/scripts/game/collider-system/FBoxCollider';\r\nimport Entity from 'db://assets/scripts/game/ui/base/Entity';\r\nimport { eEntityTag } from 'db://assets/scripts/game/ui/base/Entity';\r\nimport { Logger, logWarn } from '../../utils/Logger';\r\n\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport class BulletProperty extends PropertyContainer<number> {\r\n    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)\r\n    public delayDestroy!: Property<number>;            // 延迟销毁时间\r\n\r\n    public attack!: Property<number>;                  // 子弹伤害\r\n    public speed!: Property<number>;                   // 子弹速度\r\n    public speedAngle!: Property<number>;              // 子弹速度角度\r\n    public acceleration!: Property<number>;            // 子弹加速度\r\n    public accelerationAngle!: Property<number>;       // 子弹加速度角度\r\n    public scale!: Property<number>;                   // 子弹缩放\r\n    public color!: Property<Color>;                    // 子弹颜色\r\n    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向\r\n\r\n    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁\r\n    public isDestructive!: Property<boolean>;          // 是否可被破坏\r\n    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁\r\n    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向\r\n    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标\r\n\r\n    constructor() {\r\n        super();\r\n        this.duration = this.addProperty(0, 6000);\r\n        this.delayDestroy = this.addProperty(1, 0);\r\n        this.attack = this.addProperty(2, 1);\r\n        this.speed = this.addProperty(3, 600);\r\n        this.speedAngle = this.addProperty(4, 0);\r\n        this.acceleration = this.addProperty(5, 0);\r\n        this.accelerationAngle = this.addProperty(6, 0);\r\n        this.scale = this.addProperty(7, 1);\r\n        this.color = this.addProperty(8, Color.WHITE);\r\n        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(9, eSpriteDefaultFacing.Up);\r\n        this.isDestroyOutScreen = this.addProperty(10, true);\r\n        this.isDestructive = this.addProperty(11, false);\r\n        this.isDestructiveOnHit = this.addProperty(12, false);\r\n        this.isFacingMoveDir = this.addProperty(13, false);\r\n        this.isTrackingTarget = this.addProperty(14, false);\r\n    }\r\n\r\n    public resetFromData(data: BulletData) {\r\n        this.duration.value = data.duration.eval(); \r\n        this.delayDestroy.value = data.delayDestroy.eval(); \r\n        this.speed.value = data.speed.eval(); \r\n        // this.speedAngle.value = data.speedAngle.eval(); \r\n        this.acceleration.value = data.acceleration.eval(); \r\n        this.accelerationAngle.value = data.accelerationAngle.eval(); \r\n        this.scale.value = data.scale.eval(); \r\n        // this.color.value = data.color.eval(); \r\n        this.isDestroyOutScreen.value = data.isDestroyOutScreen; \r\n        this.isDestructive.value = data.isDestructive; \r\n        this.isDestructiveOnHit.value = data.isDestructiveOnHit; \r\n        this.isFacingMoveDir.value = data.isFacingMoveDir; \r\n        this.isTrackingTarget.value = data.isTrackingTarget;\r\n    }\r\n\r\n    public copyFrom(other: BulletProperty) {\r\n        this.forEachProperty((k, prop) => {\r\n            prop.value = other.getPropertyValue(k)!;\r\n        });\r\n    }\r\n\r\n    public clear() {\r\n        // Clear all listeners\r\n        this.forEachProperty((k, prop) => prop.clear());\r\n    }\r\n}\r\n\r\n// 子弹 Bullet\r\n// 如何集成到项目里? \r\n@ccclass('Bullet')\r\n@executeInEditMode(true)\r\nexport class Bullet extends Entity {\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover!: Movable;\r\n\r\n    @property({type: Sprite, displayName: \"子弹精灵\"})\r\n    public bulletSprite: Sprite|null = null;\r\n\r\n    @property({type: FCollider, displayName: '碰撞组件'})\r\n    public collider: FCollider | null = null;\r\n    \r\n    public isAlive: boolean = false;\r\n    public elapsedTime: number = 0;         // 子弹存活时间\r\n    public emitter!: Emitter;\r\n    public bulletData!: BulletData;\r\n    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里\r\n    public prop: BulletProperty = new BulletProperty();\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;\r\n        }\r\n        this.mover.onBecomeInvisibleCallback = () => {\r\n            if (this.prop.isDestroyOutScreen.value) {\r\n                BulletSystem.onDestroyBullet(this);\r\n            }\r\n        };\r\n        if (!this.collider) {\r\n            let boxCollider = this.addComponent(FBoxCollider);\r\n            this.collider = boxCollider;\r\n        }\r\n \r\n        this.prop.defaultFacing.value = this.mover.defaultFacing;\r\n        // listen to property changes\r\n        this.prop.isFacingMoveDir.on((value) => {\r\n            this.mover.isFacingMoveDir = value;\r\n        });\r\n        this.prop.isTrackingTarget.on((value) => {\r\n            this.mover.isTrackingTarget = value;\r\n        });\r\n        this.prop.speed.on((value) => {\r\n            this.mover.speed = value;\r\n        });\r\n        this.prop.speedAngle.on((value) => {\r\n            this.mover.speedAngle = value;\r\n        });\r\n        this.prop.acceleration.on((value) => {\r\n            this.mover.acceleration = value;\r\n        });\r\n        this.prop.accelerationAngle.on((value) => {\r\n            this.mover.accelerationAngle = value;\r\n        });\r\n        this.prop.scale.on((value) => {\r\n            this.node.setScale(value, value, value);\r\n        });\r\n        this.prop.color.on((value) => {\r\n            if (this.bulletSprite) {\r\n                this.bulletSprite.color = value;\r\n            }\r\n        });\r\n    }\r\n\r\n    public onCreate(emitter: Emitter, bulletID: number): void {\r\n        this.isAlive = true;\r\n        this.elapsedTime = 0;\r\n        this.emitter = emitter;\r\n        this.bulletData = emitter.bulletData!;\r\n\r\n        // TODO: 创建entity的时候，设置正确的tag.\r\n        const ent = emitter.getEntity();\r\n        if (ent)\r\n        {\r\n            const isShootFromEnemy = ent.hasTag(eEntityTag.Player) ? false:true;\r\n            this.collider!.initBaseData(this);\r\n            this.collider!.groupType = isShootFromEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;\r\n            this.collider!.isEnable = true;\r\n            this.addTag(isShootFromEnemy ? eEntityTag.EnemyBullet : eEntityTag.PlayerBullet);\r\n        }else{\r\n            logWarn(\"emitter\",\"onCreate bullet\",  \"has no entity\");\r\n        }\r\n\r\n        this.resetProperties();\r\n        this.mover.setMovable(true);\r\n    }\r\n\r\n    private destroySelf() {\r\n        if (!this.node || !this.node.isValid) return;\r\n        \r\n        this.prop.clear();\r\n\r\n        if (EDITOR) {\r\n            this.node.destroy();\r\n        } else {\r\n            ObjectPool.returnNode(this.node);\r\n        }\r\n    }\r\n\r\n    public resetProperties(): void {\r\n        if (!this.emitter) return;\r\n\r\n        this.prop.copyFrom(this.emitter.bulletProp);\r\n        this.prop.notifyAll(true);\r\n    }\r\n\r\n    public resetEventGroups(): void {\r\n        // create event groups here\r\n        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {\r\n            let ctx = new EventGroupContext();\r\n            ctx.bullet = this;\r\n            ctx.emitter = this.emitter;\r\n            for (const eventName of this.bulletData.eventGroupData) {\r\n                BulletSystem.createBulletEventGroup(ctx, eventName);\r\n            }\r\n        }\r\n    }\r\n\r\n    public tick(dt:number) : void {\r\n        if (!this.isAlive) return;\r\n\r\n        this.elapsedTime += dt;\r\n        if (this.elapsedTime > this.prop.duration.value) {\r\n            BulletSystem.onDestroyBullet(this);\r\n            return;\r\n        }\r\n        \r\n        // 毫秒 -> 秒\r\n        this.mover!.tick(dt / 1000);\r\n        this.prop.notifyAll();\r\n    }\r\n\r\n    public willDestroy(): void {\r\n        this.isAlive = false;\r\n        if (this.eventGroups && this.eventGroups.length > 0)\r\n        {\r\n            this.eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.\r\n            this.eventGroups = []; // clear the event groups array\r\n        }\r\n        \r\n        this.mover.setMovable(false);\r\n        this.collider!.isEnable = false;\r\n        this.removeAllComp();\r\n        this.clearTags();\r\n        if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {\r\n            this.scheduleOnce(() => {\r\n                this.destroySelf();\r\n            }, this.prop.delayDestroy.value);\r\n        } else {\r\n            this.destroySelf();\r\n        }\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        this.remove();\r\n    }\r\n\r\n    public remove() {\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n\r\n    getAttack():number {\r\n        const ent = this.emitter.getEntity();\r\n        return ent!.getAttack();\r\n    }\r\n}\r\n"]}