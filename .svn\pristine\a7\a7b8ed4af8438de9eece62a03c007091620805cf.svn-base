import { _decorator, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/Bundle';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/ui/UIMgr';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MailCellUI } from './MailCellUI';
import { MyApp } from 'db://assets/scripts/MyApp';
import List from '../common/components/list/List';
import { HomeUI } from '../home/<USER>';
import { Mail } from '../../data/mail/Mail';

const { ccclass, property } = _decorator;

@ccclass('MailUI')
export class MailUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(List)
    list: List | null = null;
    keys: number[] = [];
    items: csproto.cs.ICSItem[] = [];
    public static getUrl(): string { return "prefab/ui/MailUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomeMail; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        this.keys = Array.from(MyApp.lubanTables.TbResItem.getDataMap().keys());
        this.list!.node.active = true;
        this.list!.numItems = Math.min(this.keys.length, 20);
    }
    async closeUI() {
        UIMgr.closeUI(MailUI);
        //await UIMgr.openUI(HomeUI)
    }
    async onShow(): Promise<void> {
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }
    start() {

    }
    onListRender(listItem: Node, row: number) {// 有数据要在 this.list.numItems 之前设置
        const key = row < this.keys.length ? this.keys[row] : this.keys[0];
        const cell = listItem.getComponent(MailCellUI);
        if (cell !== null) {
            cell.setData(key);
        }
    }
}


