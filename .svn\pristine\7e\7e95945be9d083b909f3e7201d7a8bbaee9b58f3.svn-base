import { _decorator, Component, Node, UITransform } from 'cc';
import { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';
import { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { UIMgr } from "db://assets/scripts/ui/UIMgr";
import { PlaneUIEvent } from '../../../../event/PlaneUIEvent';
import List from '../../../common/components/list/List';
import { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';
import { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';
import { BagItem } from './BagItem';

const { ccclass, property } = _decorator;

@ccclass('BagGrid')
export class BagGrid extends Component {
    @property(List)
    bagList: List | null = null;
    @property(Node)
    separator: Node | null = null;
    @property(Node)
    mergeSelectMaskBg: Node | null = null;

    private _sortedItems: csproto.cs.ICSItem[] = [];
    private _sortedEquips: csproto.cs.ICSItem[] = [];
    private _lineGridNum: number = 5;
    private _separatorRow: number = 0;
    private _tabStatus: TabStatus = TabStatus.None;

    onLoad() {
        this.separator!.removeFromParent();
        this.mergeSelectMaskBg!.active = false;
        EventMgr.on(PlaneUIEvent.SortTypeChange, this.onSortTypeChange, this);
        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this)
        EventMgr.on(PlaneUIEvent.UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this)
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    private onUpdateMergeEquipStatus() {
        this.bagList!.updateAll();
    }

    /*暂时只有装备点击*/
    private onBagItemClick(item: csproto.cs.ICSItem) {
        switch (this._tabStatus) {
            case TabStatus.Bag:
                this.mergeSelectMaskBg!.active = false;
                logDebug("PlaneUI", `onBagItemClick item:${item}`)
                UIMgr.openUI(PlaneEquipInfoUI, item, OpenEquipInfoUISource.BagGrid)
                break;
            case TabStatus.Merge:
                if (DataMgr.equip.eqCombine.isFull() ||
                    !DataMgr.equip.eqCombine.isCanCombine(item)) {
                    return
                }
                this.onUpdateMergeEquipStatus();
                break;
        }
    }

    private onSortTypeChange(tabStatus: TabStatus, items: csproto.cs.ICSItem[]) {
        this._tabStatus = tabStatus;
        this.mergeSelectMaskBg!.active = false;
        this.separator!.active = false
        this.separator!.removeFromParent();
        this.bagList!._customSize = {}
        this.bagList!._resizeContent();
        let listNum = 0;
        switch (tabStatus) {
            case TabStatus.Bag:
                this._sortedItems = items.filter(v => MyApp.lubanTables.TbResItem.get(v.item_id!) != null)
                this._sortedEquips = items.filter(v => MyApp.lubanTables.TbResEquip.get(v.item_id!) != null)
                this._separatorRow = Math.ceil(this._sortedEquips.length / this._lineGridNum)
                const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum)
                listNum = this._separatorRow + itemRowNum + 1;
                break;
            case TabStatus.Merge:
                this._separatorRow = -1
                this._sortedItems = items
                listNum = Math.ceil(this._sortedItems.length / this._lineGridNum)
                break;
        }
        logDebug("PlaneUI", `onSortTypeChange list num:${this.bagList!.numItems} maxPlanePartRowNum:${this._separatorRow}`)
        this.bagList!.numItems = listNum
        this.bagList!.scrollTo(0, 1)
    }

    onListRenderInBagStatus(listItem: Node, row: number) {
        listItem.name = `${row}`
        if (row == this._separatorRow) {
            const normalSize = this.bagList!.tmpNode!.getComponent(UITransform)!.contentSize
            listItem.children.forEach(v => v.active = false)
            this.separator!.removeFromParent();
            this.separator!.active = true;
            listItem.addChild(this.separator!)
            listItem.getComponent(UITransform)!.setContentSize(normalSize.width, normalSize.height / 2)
            return
        }

        if (listItem.children.length > 5) {
            this.separator!.removeFromParent();
            this.separator!.active = false;
        }

        const bagItems = listItem.getComponentsInChildren(BagItem)
        if (row < this._separatorRow) {
            for (let index = 0; index < bagItems.length; index++) {
                const item = bagItems[index];
                const dataIndex = row * this._lineGridNum + index;
                if (dataIndex >= this._sortedEquips.length) {
                    item.node.active = false;
                    logDebug("PlaneUI", `onListRender bagItem index:${index} dataIndex:${dataIndex} row:${row} sortedLen:${this._sortedEquips.length}`)
                    continue
                }
                item.node.active = true;
                item.onBagTabStatusRender(this._sortedEquips[dataIndex]);
            }
        } else {
            for (let index = 0; index < bagItems.length; index++) {
                const item = bagItems[index];
                const dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + index;
                if (dataIndex >= this._sortedItems.length) {
                    item.node.active = false;
                    continue
                }
                item.node.active = true;
                item.onBagTabStatusRender(this._sortedItems[dataIndex]);
            }
        }
    }

    private onListRenderInCombineStatus(listItem: Node, row: number) {
        const bagItems = listItem.getComponentsInChildren(BagItem)
        for (let index = 0; index < bagItems.length; index++) {
            const item = bagItems[index];
            const dataIndex = row * this._lineGridNum + index;
            if (dataIndex >= this._sortedItems.length) {
                item.node.active = false;
                continue
            }
            item.node.active = true;
            item.onCombineTabStatusRender(this._sortedItems[dataIndex]);
        }
    }

    onListRender(listItem: Node, row: number) {
        listItem.name = `listItem${row}`
        if (this._tabStatus == TabStatus.Bag) {
            this.onListRenderInBagStatus(listItem, row)
        } else {
            this.onListRenderInCombineStatus(listItem, row)
        }
    }
}