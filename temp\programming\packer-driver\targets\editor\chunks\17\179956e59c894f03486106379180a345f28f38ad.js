System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, Color, misc, QuadTree, _crd, color, catchTree;

  _export("QuadTree", void 0);

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
      misc = _cc.misc;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Color', 'Graphics', 'misc']);

      color = [Color.BLACK, Color.RED, Color.BLUE, new Color(255, 127, 0), Color.GREEN, Color.GREEN, Color.GREEN];
      catchTree = [];

      _export("QuadTree", QuadTree = class QuadTree {
        constructor(bounds, depth, maxDepth, maxChildren) {
          //subnodes
          this.nodes = void 0;
          //children contained directly in the node
          this.children = void 0;
          this._bounds = void 0;
          //read only
          this._depth = 0;
          this._maxChildren = 6;
          this._maxDepth = 6;
          this._bounds = bounds;
          this.children = [];
          this.nodes = [];
          this._maxChildren = maxChildren || 10;
          this._maxDepth = maxDepth || 4;
          this._depth = depth || 0;
          if (!this._bounds.halfHeight) this._bounds.halfHeight = this._bounds.height / 2;
          if (!this._bounds.halfWidth) this._bounds.halfWidth = this._bounds.width / 2;
        }

        getAllNeedTestColliders(out) {
          if (this.children.length) out.push(this.children);

          for (let i = 0, len = this.nodes.length; i < len; i++) {
            let node = this.nodes[i];
            node.getAllNeedTestColliders(out);
          }
        }

        render(pen) {
          for (let i = 0, len = this.nodes.length; i < len; i++) {
            let node = this.nodes[i];
            node && node.render(pen);
          }

          pen.lineWidth = misc.clampf(8 - this._depth, 2, 8);
          pen.strokeColor = color[this._depth] || Color.GREEN;
          pen.moveTo(this._bounds.x, this._bounds.y);
          pen.lineTo(this._bounds.x + this._bounds.width, this._bounds.y);
          pen.lineTo(this._bounds.x + this._bounds.width, this._bounds.y + this._bounds.height);
          pen.lineTo(this._bounds.x, this._bounds.y + this._bounds.height);
          pen.close();
          pen.stroke();
        }

        insert(item) {
          let indexes;

          if (this.nodes.length) {
            indexes = this._findIndexs(item);

            for (let i = 0, len = indexes.length; i < len; i++) {
              this.nodes[indexes[i]].insert(item);
            }

            return;
          }

          this.children.push(item);
          let len = this.children.length;

          if (this._depth < this._maxDepth && len > this._maxChildren
          /*&& (item.width < this._bounds.halfWidth && item.height < this._bounds.halfHeight)*/
          ) {
            if (!this.nodes.length) this.subdivide();

            for (let j = 0; j < len; j++) {
              indexes = this._findIndexs(this.children[j]);

              for (let k = 0, klen = indexes.length; k < klen; k++) {
                this.nodes[indexes[k]].insert(this.children[j]);
              }
            }

            this.children.length = 0;
          }
        }

        retrieve(item, out) {
          let indexes = this._findIndexs(item);

          if (this.children.length) out.push.apply(out, this.children);

          if (this.nodes.length) {
            for (let i = 0, len = indexes.length; i < len; i++) {
              this.nodes[indexes[i]].retrieve(item, out);
            }
          }

          out = out.filter((item, index) => {
            return out.indexOf(item) >= index;
          });
        }
        /**
         *查找当前碰撞体在那几个象限
         *
         * ---------
         * | 0 | 3 |
         * ----|----
         * | 1 | 2 |
         * ---------
         *
         * @param {T} item
         * @return {*} 
         * @memberof QuadTree
         */


        _findIndexs(pRect) {
          let b = this._bounds; //象限中点

          let verticalMidpoint = b.x + b.halfWidth;
          let horizontalMidpoint = b.y + b.halfHeight;
          let bottom = pRect.y < horizontalMidpoint,
              left = pRect.x < verticalMidpoint,
              right = pRect.x + pRect.width > verticalMidpoint,
              top = pRect.y + pRect.height > horizontalMidpoint;
          let indexes = []; //top-left quad

          if (top && left) {
            indexes.push(0);
          } //bottom-left quad


          if (bottom && left) {
            indexes.push(1);
          } //bottom-right quad


          if (bottom && right) {
            indexes.push(2);
          } //top-right quad


          if (top && right) {
            indexes.push(3);
          }

          return indexes;
        }
        /**
         * ---------
         * | 0 | 3 |
         * ----|----
         * | 1 | 2 |
         * ---------
         */


        subdivide() {
          let nextLevel = this._depth + 1,
              subWidth = this._bounds.halfWidth,
              subHeight = this._bounds.halfHeight,
              x = this._bounds.x,
              y = this._bounds.y; //top left node

          this.nodes[0] = new QuadTree({
            x: x,
            y: y + subHeight,
            width: subWidth,
            height: subHeight
          }, nextLevel, this._maxDepth, this._maxChildren); //bottom left node

          this.nodes[1] = new QuadTree({
            x: x,
            y: y,
            width: subWidth,
            height: subHeight
          }, nextLevel, this._maxDepth, this._maxChildren); //bottom right node

          this.nodes[2] = new QuadTree({
            x: x + subWidth,
            y: y,
            width: subWidth,
            height: subHeight
          }, nextLevel, this._maxDepth, this._maxChildren); //top right node

          this.nodes[3] = new QuadTree({
            x: x + subWidth,
            y: y + subHeight,
            width: subWidth,
            height: subHeight
          }, nextLevel, this._maxDepth, this._maxChildren);
        }

        clear() {
          this.children.length = 0;

          for (let i = 0, len = this.nodes.length; i < len; i++) {
            this.nodes[i].clear();
          }

          this.nodes.length = 0;
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=179956e59c894f03486106379180a345f28f38ad.js.map