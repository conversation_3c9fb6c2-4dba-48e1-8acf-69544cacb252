[{"id": 240000000, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 1, "target": 1, "param": [1000]}], "conditionID": 0}, {"id": 240000001, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 2, "target": 1, "param": [1000]}], "conditionID": 0}, {"id": 240000002, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 3, "target": 1, "param": [1000]}], "conditionID": 0}, {"id": 240000003, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 4, "target": 1, "param": [2]}], "conditionID": 0}, {"id": 240000004, "buffType": 0, "duration": 10000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 1000, "cycleTimes": 10, "effects": [{"type": 5, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000005, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 5, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000006, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 6, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000007, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 7, "target": 1, "param": [500]}], "conditionID": 0}, {"id": 240000008, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 8, "target": 1, "param": [100]}], "conditionID": 0}, {"id": 240000009, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 9, "target": 1, "param": [500]}], "conditionID": 0}, {"id": 240000010, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 10, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000011, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 11, "target": 1, "param": [2000]}], "conditionID": 0}, {"id": 240000012, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 12, "target": 1, "param": [3000]}], "conditionID": 0}, {"id": 240000013, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 13, "target": 1, "param": [500]}], "conditionID": 0}, {"id": 240000014, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 14, "target": 1, "param": [50]}], "conditionID": 0}, {"id": 240000015, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 15, "target": 1, "param": [100]}], "conditionID": 0}, {"id": 240000016, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 16, "target": 1, "param": [500]}], "conditionID": 0}, {"id": 240000017, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 17, "target": 1, "param": [50]}], "conditionID": 0}, {"id": 240000018, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 21, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000019, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 19, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000020, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 20, "target": 1, "param": [100]}], "conditionID": 0}, {"id": 240000021, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 18, "target": 1, "param": [200]}], "conditionID": 0}, {"id": 240000022, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 22, "target": 1, "param": [50]}], "conditionID": 0}, {"id": 240000023, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 23, "target": 1, "param": [50]}], "conditionID": 0}, {"id": 240000024, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 24, "target": 1, "param": []}], "conditionID": 0}, {"id": 240000025, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 25, "target": 1, "param": [50]}], "conditionID": 0}, {"id": 240000026, "buffType": 0, "duration": 3000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 1000, "cycleTimes": 3, "effects": [{"type": 25, "target": 1, "param": [10]}], "conditionID": 0}, {"id": 240000027, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 26, "target": 1, "param": [1000]}], "conditionID": 0}, {"id": 240000028, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 28, "target": 1, "param": [240000050, 1]}], "conditionID": 0}, {"id": 240000029, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 29, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000030, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 30, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000031, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 31, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000032, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 32, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000033, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 33, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000034, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 34, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000035, "buffType": 0, "duration": 1000, "durationBonus": 0, "maxStack": 1, "refreshType": true, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 35, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000036, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 1000, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 36, "target": 1, "param": [1]}], "conditionID": 0}, {"id": 240000037, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 38, "target": 1, "param": [1000, 1]}], "conditionID": 0}, {"id": 240000038, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 37, "target": 1, "param": [1000, 0]}], "conditionID": 0}, {"id": 240000039, "buffType": 0, "duration": -1, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [], "conditionID": 0}, {"id": 240000040, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 39, "target": 3, "param": [2500]}], "conditionID": 0}, {"id": 240000041, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 40, "target": 3, "param": [5000]}], "conditionID": 0}, {"id": 240000042, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 41, "target": 1, "param": [2000]}], "conditionID": 0}, {"id": 240000043, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 1, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 42, "target": 1, "param": [200]}], "conditionID": 0}]