System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Component, Enum, Vec2, Vec3, UITransform, BulletSystem, _dec, _dec2, _class, _class2, _descriptor, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, eSpriteDefaultFacing, Movable;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Component = _cc.Component;
      Enum = _cc.Enum;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'misc', 'size', 'Component', 'Enum', 'Vec2', 'Vec3', 'Node', 'UITransform']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("eSpriteDefaultFacing", eSpriteDefaultFacing = /*#__PURE__*/function (eSpriteDefaultFacing) {
        eSpriteDefaultFacing[eSpriteDefaultFacing["Right"] = 0] = "Right";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Up"] = -90] = "Up";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Down"] = 90] = "Down";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Left"] = 180] = "Left";
        return eSpriteDefaultFacing;
      }({}));

      _export("Movable", Movable = (_dec = ccclass('Movable'), _dec2 = property({
        type: Enum(eSpriteDefaultFacing),
        displayName: '图片默认朝向'
      }), _dec(_class = executeInEditMode(_class = (_class2 = class Movable extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "defaultFacing", _descriptor, this);

          this.isFacingMoveDir = false;
          // 是否朝向行进方向
          this.isTrackingTarget = false;
          // 是否正在追踪目标
          this.speed = 1;
          // 速度
          this.speedAngle = 0;
          // 速度方向 (用角度表示)
          this.turnSpeed = 60;
          // 转向速度（仅用在追踪目标时）
          this.acceleration = 0;
          // 加速度
          this.accelerationAngle = 0;
          // 加速度方向 (用角度表示)
          // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
          this.tiltSpeed = 0;
          // 偏移速度
          // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
          this.tiltOffset = 100;
          // 偏移距离
          this.target = null;
          // 追踪的目标节点
          this.arrivalDistance = 10;
          // 到达目标的距离
          this._selfSize = new Vec2();
          this._position = new Vec3();
          this._tiltTime = 0;
          // 用于计算倾斜偏移的累积时间
          this._basePosition = new Vec3();
          // 基础位置（不包含倾斜偏移）
          this._isVisible = true;
          this._isMovable = true;
          // Callbacks:
          this.onBecomeVisibleCallback = null;
          this.onBecomeInvisibleCallback = null;
        }

        // 是否可见
        get isVisible() {
          return this._isVisible;
        }

        // 是否可移动
        get isMovable() {
          return this._isMovable;
        }

        // public onCollideCallback: Function | null = null;
        onLoad() {
          const uiTransform = this.node.getComponent(UITransform);
          const self_size = uiTransform ? uiTransform.contentSize : {
            width: 0,
            height: 0
          };

          this._selfSize.set(self_size.width / 2, self_size.height / 2);
        }

        tick(dt) {
          if (!this._isMovable) return; // Convert speed and angle to velocity vector

          let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
          let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));

          if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this.node.getPosition(); // Calculate direction to target

            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);

            if (distance > 0) {
              // Calculate desired angle to target
              const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX)); // Smoothly adjust speedAngle toward target

              const angleDiff = desiredAngle - this.speedAngle; // Normalize angle difference to [-180, 180] range

              const normalizedAngleDiff = (angleDiff + 180) % 360 - 180; // Apply tracking adjustment (you can add a trackingStrength property to control this)

              const trackingStrength = 1.0; // Can be made configurable

              const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable

              const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
              this.speedAngle += turnAmount * trackingStrength; // Recalculate velocity with new angle

              velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
              velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));
            }
          } // Convert acceleration and angle to acceleration vector


          const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));
          const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle)); // Update velocity vector: v = v + a * dt

          const newVelocityX = velocityX + accelerationX * dt;
          const newVelocityY = velocityY + accelerationY * dt; // Convert back to speed and angle

          this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);
          this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX)); // Update position: p = p + v * dt

          if (newVelocityX !== 0 || newVelocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += newVelocityX * dt;
            this._basePosition.y += newVelocityY * dt; // Start with base position

            this._position.set(this._basePosition); // Apply tilting behavior if enabled


            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
              // Update tilt time
              this._tiltTime += dt; // Calculate perpendicular direction to movement
              // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))

              const moveAngleRad = degreesToRadians(this.speedAngle);
              const perpX = -Math.sin(moveAngleRad);
              const perpY = Math.cos(moveAngleRad); // Calculate tilt offset using sine wave

              const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset; // Apply tilt offset in perpendicular direction (as position offset, not velocity)

              this._position.x += perpX * tiltAmount;
              this._position.y += perpY * tiltAmount;
            }

            this.node.setPosition(this._position);
            this.checkVisibility();
          }

          if (this.isFacingMoveDir && this.speed > 0) {
            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));
            const finalAngle = movementAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
          }
        }

        checkVisibility() {
          // 这里目前的检查逻辑没有考虑旋转和缩放
          // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
          const visibleSize = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).worldBounds;
          this.node.getWorldPosition(this._position);
          const isVisible = this._position.x + this._selfSize.x >= visibleSize.xMin && this._position.x - this._selfSize.x <= visibleSize.xMax && this._position.y - this._selfSize.y <= visibleSize.yMax && this._position.y + this._selfSize.y >= visibleSize.yMin; // debug visibility
          // if (!isVisible) {
          //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
          //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
          //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
          //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
          //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
          // }

          this.setVisible(isVisible);
        }

        setVisible(visible) {
          if (this._isVisible === visible) return;
          this._isVisible = visible;

          if (visible && this.onBecomeVisibleCallback) {
            this.onBecomeVisibleCallback();
          } else if (!visible && this.onBecomeInvisibleCallback) {
            this.onBecomeInvisibleCallback();
          }
        }
        /**
         * Set the target to track
         */


        setTarget(target) {
          this.target = target;
          this.isTrackingTarget = target !== null;
        }

        setMovable(movable) {
          this._isMovable = movable;

          if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "defaultFacing", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eSpriteDefaultFacing.Up;
        }
      })), _class2)) || _class) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=74d99d57d6cae72651016f5964274bf61390b81d.js.map