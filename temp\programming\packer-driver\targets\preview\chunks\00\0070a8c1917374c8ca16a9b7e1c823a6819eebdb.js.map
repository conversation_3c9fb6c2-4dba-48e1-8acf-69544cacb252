{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts"], "names": ["_decorator", "Component", "instantiate", "Node", "UITransform", "view", "LevelLayerUI", "MyApp", "ccclass", "property", "BackgroundsNodeName", "<PERSON><PERSON><PERSON><PERSON>", "node", "speed", "LevelBackgroundLayer", "backgrounds", "backgroundsNode", "LevelBaseUI", "_curLevelIndex", "_totalTime", "_preLevelHeight", "_preLevelOffsetY", "_backgroundLayerNode", "_floorLayersNode", "_skyLayersNode", "_backgroundLayer", "_floorLayers", "_skyLayers", "_lastLevelUpdate", "floorLayers", "skyLayers", "background<PERSON>ayer", "Error", "TotalTime", "getLevelTotalHeightByIndex", "index", "totalHeight", "levelNode", "getChildByName", "preBgNode", "children", "for<PERSON>ach", "bg", "height", "getComponent", "contentSize", "onLoad", "_getOrAddNode", "node_parent", "name", "<PERSON><PERSON><PERSON><PERSON>", "levelPrefab", "levelData", "levelInfo", "bFristLevel", "_initByLevelData", "switchLevel", "time", "levelIndex", "_removeNode", "parentNode", "removeFromParent", "data", "levelBackground", "levelFloor", "levelSky", "_initBackgroundLayer", "_initLayers", "layers", "dataLayers", "layer", "i", "<PERSON><PERSON>ayer", "_addLayer", "initByLevelData", "push", "length", "bgCount", "Math", "ceil", "totalTime", "loadPromises", "map", "Promise", "resolve", "reject", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "load", "err", "prefab", "console", "error", "all", "offsetY", "childrenCount", "preLevel", "getPosition", "y", "log", "setSiblingIndex", "pos", "setPosition", "layerNode", "layerCom", "addComponent", "tick", "deltaTime", "child", "layerUI", "TrackBackground", "_setupInfiniteScroll", "schedule", "lastLevelNode", "lastBgNode", "bgPosY", "screenHeight", "getVisibleSize", "lastFloorNode", "lastSkyNode", "_setupLastLevelUpdate", "onDestroy", "unschedule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAE/DC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAExBU,MAAAA,mB,GAAsB,a;AAGtBC,MAAAA,U,WADLH,OAAO,CAAC,YAAD,C,gBAAR,MACMG,UADN,CACiB;AAAA;AAAA,eACNC,IADM,GACc,IADd;AAAA,eAENC,KAFM,GAEU,CAFV;AAAA;;AAAA,O;AAMXC,MAAAA,oB,YADLN,OAAO,CAAC,sBAAD,C,kBAAR,MACMM,oBADN,SACmCH,UADnC,CAC8C;AAAA;AAAA;AAAA,eACnCI,WADmC,GACX,EADW;AAAA,eAEnCC,eAFmC,GAEN,IAFM;AAAA;;AAAA,O;;6BAMjCC,W,YADZT,OAAO,CAAC,aAAD,C,kBAAR,MACaS,WADb,SACiChB,SADjC,CAC2C;AAAA;AAAA;AAAA,eAC/BiB,cAD+B,GACN,CAAC,CADK;AACF;AADE,eAE/BC,UAF+B,GAEV,EAFU;AAEN;AAFM,eAG/BC,eAH+B,GAGL,CAHK;AAGF;AAHE,eAI/BC,gBAJ+B,GAIJ,CAJI;AAID;AAJC,eAM/BC,oBAN+B,GAME,IANF;AAAA,eAO/BC,gBAP+B,GAOF,IAPE;AAAA,eAQ/BC,cAR+B,GAQJ,IARI;AAAA,eAU/BC,gBAV+B,GAUiB,IAVjB;AAAA,eAW/BC,YAX+B,GAWF,EAXE;AAAA,eAY/BC,UAZ+B,GAYJ,EAZI;AAAA,eAc/BC,gBAd+B,GAcS,IAdT;AAAA;;AAgBjB,YAAXC,WAAW,GAAiB;AACnC,iBAAO,KAAKH,YAAZ;AACH;;AACmB,YAATI,SAAS,GAAiB;AACjC,iBAAO,KAAKH,UAAZ;AACH;;AACyB,YAAfI,eAAe,GAAgC;AACtD,cAAI,CAAC,KAAKN,gBAAV,EAA4B;AACxB,kBAAM,IAAIO,KAAJ,CAAU,oCAAV,CAAN;AACH;;AACD,iBAAO,KAAKP,gBAAZ;AACH;;AAEmB,YAATQ,SAAS,GAAW;AAC3B,iBAAO,KAAKd,UAAZ;AACH;;AAEMe,QAAAA,0BAA0B,CAACC,KAAD,EAAwB;AACrD,cAAIC,WAAW,GAAG,CAAlB;;AACA,cAAI,KAAKd,oBAAT,EAA+B;AAC3B,gBAAMe,SAAS,GAAG,KAAKf,oBAAL,CAA0BgB,cAA1B,YAAkDH,KAAlD,CAAlB;;AACA,gBAAIE,SAAJ,EAAe;AACX,kBAAIE,SAAS,GAAGF,SAAS,CAACC,cAAV,WAAhB;;AACA,kBAAIC,SAAJ,EAAe;AACX,oBAAMvB,eAAe,GAAGuB,SAAS,CAACD,cAAV,CAAyB5B,mBAAzB,CAAxB;;AACA,oBAAIM,eAAJ,EAAqB;AACjBA,kBAAAA,eAAe,CAACwB,QAAhB,CAAyBC,OAAzB,CAAkCC,EAAD,IAAQ;AACrC,wBAAIC,MAAM,GAAGD,EAAE,CAACE,YAAH,CAAgBxC,WAAhB,EAA8ByC,WAA9B,CAA0CF,MAAvD;AACAP,oBAAAA,WAAW,IAAIO,MAAf;AACH,mBAHD;AAIH;AACJ;AACJ;AACJ;;AAED,iBAAOP,WAAP;AACH;;AAESU,QAAAA,MAAM,GAAS,CACxB;;AAEOC,QAAAA,aAAa,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AACzD,cAAIrC,IAAI,GAAGoC,WAAW,CAACV,cAAZ,CAA2BW,IAA3B,CAAX;;AACA,cAAIrC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIT,IAAJ,CAAS8C,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACE,QAAZ,CAAqBtC,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEYuC,QAAAA,WAAW,CAACC,SAAD,EAAuBC,SAAvB,EAA8FC,WAA9F,EAA0I;AAAA;;AAAA;AAAA,gBAA5CA,WAA4C;AAA5CA,cAAAA,WAA4C,GAArB,KAAqB;AAAA;;AAC9J,YAAA,KAAI,CAAChC,oBAAL,GAA4B,KAAI,CAACyB,aAAL,CAAmB,KAAI,CAACnC,IAAxB,EAA8B,iBAA9B,CAA5B;AACA,YAAA,KAAI,CAACW,gBAAL,GAAwB,KAAI,CAACwB,aAAL,CAAmB,KAAI,CAACnC,IAAxB,EAA8B,aAA9B,CAAxB;AACA,YAAA,KAAI,CAACY,cAAL,GAAsB,KAAI,CAACuB,aAAL,CAAmB,KAAI,CAACnC,IAAxB,EAA8B,WAA9B,CAAtB;;AAEA,gBAAI0C,WAAJ,EAAiB;AACb,oBAAM,KAAI,CAACC,gBAAL,CAAsBH,SAAtB,EAAgCC,SAAhC,CAAN;AACH,aAFD,MAEO;AACH,cAAA,KAAI,CAACE,gBAAL,CAAsBH,SAAtB,EAAgCC,SAAhC;AACH,aAT6J,CAW9J;;AACA;AACR;AACA;AACA;;AAfsK;AAgBjK;;AAEMG,QAAAA,WAAW,CAAC3C,KAAD,EAAgB4C,IAAhB,EAA8BC,UAA9B,EAAwD;AACtE,eAAK3B,eAAL,CAAsBlB,KAAtB,GAA8BA,KAA9B;AACA,eAAKM,UAAL,GAAkBsC,IAAlB,CAFsE,CAItE;;AACA,eAAKE,WAAL,CAAiB,KAAKrC,oBAAtB,aAAsD,KAAKJ,cAA3D;;AACA,eAAKyC,WAAL,CAAiB,KAAKpC,gBAAtB,aAAkD,KAAKL,cAAvD;;AACA,eAAKyC,WAAL,CAAiB,KAAKnC,cAAtB,aAAgD,KAAKN,cAArD;;AAEA,eAAKA,cAAL,GAAsBwC,UAAtB;AACH;;AAEOC,QAAAA,WAAW,CAACC,UAAD,EAAmBX,IAAnB,EAAuC;AACtD,cAAIrC,IAAI,GAAGgD,UAAU,CAACtB,cAAX,CAA0BW,IAA1B,CAAX;;AACA,cAAIrC,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACiD,gBAAL;AACH;AACJ;;AAEYN,QAAAA,gBAAgB,CAACO,IAAD,EAAkBT,SAAlB,EAAuG;AAAA;;AAAA;AAChI,gBAAMU,eAAe,GAAG,MAAI,CAAChB,aAAL,CAAmB,MAAI,CAACzB,oBAAxB,aAAwD+B,SAAS,CAACK,UAAlE,CAAxB;;AACA,gBAAMM,UAAU,GAAG,MAAI,CAACjB,aAAL,CAAmB,MAAI,CAACxB,gBAAxB,aAAoD8B,SAAS,CAACK,UAA9D,CAAnB;;AACA,gBAAMO,QAAQ,GAAG,MAAI,CAAClB,aAAL,CAAmB,MAAI,CAACvB,cAAxB,aAAkD6B,SAAS,CAACK,UAA5D,CAAjB;;AAEA,kBAAM,MAAI,CAACQ,oBAAL,CAA0BH,eAA1B,EAA2CD,IAA3C,EAAiDT,SAAjD,CAAN;;AACA,YAAA,MAAI,CAACc,WAAL,CAAiBH,UAAjB,EAA6B,MAAI,CAACnC,WAAlC,EAA+CiC,IAAI,CAACjC,WAApD;;AACA,YAAA,MAAI,CAACsC,WAAL,CAAiBF,QAAjB,EAA2B,MAAI,CAACnC,SAAhC,EAA2CgC,IAAI,CAAChC,SAAhD;AAPgI;AAQnI;;AAEOqC,QAAAA,WAAW,CAACP,UAAD,EAAmBQ,MAAnB,EAAyCC,UAAzC,EAA6E;AAC5FA,UAAAA,UAAU,CAAC5B,OAAX,CAAmB,CAAC6B,KAAD,EAAQC,CAAR,KAAc;AAC7B,gBAAIC,UAAU,GAAG,IAAI7D,UAAJ,EAAjB;AACA6D,YAAAA,UAAU,CAAC3D,KAAX,GAAmByD,KAAK,CAACzD,KAAzB;AACA2D,YAAAA,UAAU,CAAC5D,IAAX,GAAkB,KAAK6D,SAAL,CAAeb,UAAf,aAAoCW,CAApC,EAAyC3D,IAA3D;AACA4D,YAAAA,UAAU,CAAE5D,IAAZ,CAAkBgC,YAAlB;AAAA;AAAA,8CAA4D8B,eAA5D,CAA4EJ,KAA5E,EAAkF,KAAKjD,gBAAvF;AACA+C,YAAAA,MAAM,CAACO,IAAP,CAAYH,UAAZ;AACH,WAND;AAOH;;AAEaN,QAAAA,oBAAoB,CAACN,UAAD,EAAmBE,IAAnB,EAAoCT,SAApC,EAAyH;AAAA;;AAAA;AACvJ,gBAAIS,IAAI,CAAC/B,eAAL,CAAqBhB,WAArB,CAAiC6D,MAAjC,GAA0C,CAA9C,EAAiD;AAC7C,kBAAI,MAAI,CAACnD,gBAAL,KAA0B,IAA9B,EAAoC;AAChC,gBAAA,MAAI,CAACA,gBAAL,GAAwB,IAAIX,oBAAJ,EAAxB;AACA,gBAAA,MAAI,CAACW,gBAAL,CAAsBV,WAAtB,GAAoC,EAApC;AACH;;AACD,cAAA,MAAI,CAACU,gBAAL,CAAsBZ,KAAtB,GAA8BiD,IAAI,CAAC/B,eAAL,CAAqBlB,KAAnD;AACA,kBAAIgE,OAAO,GAAGC,IAAI,CAACC,IAAL,CAAUjB,IAAI,CAACkB,SAAL,GAAiB,MAAI,CAACvD,gBAAL,CAAsBZ,KAAvC,GAA+C,IAAzD,CAAd;AACA,kBAAMoE,YAAY,GAAGnB,IAAI,CAAC/B,eAAL,CAAqBhB,WAArB,CAAiCmE,GAAjC,CAAsCnD,eAAD,IAAqB;AAC3E,uBAAO,IAAIoD,OAAJ,CAAkB,CAACC,OAAD,EAAUC,MAAV,KAAqB;AAC1C,sBAAMC,IAAI,GAAG;AAAA;AAAA,sCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,sCAAMD,MAAN,CAAaE,iBAAvC,EAA0D1D,eAA1D,CAAb;AACA;AAAA;AAAA,sCAAMwD,MAAN,CAAaG,IAAb,CAAkBJ,IAAlB,EAAwB,CAACK,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,wBAAID,GAAJ,EAAS;AACLE,sBAAAA,OAAO,CAACC,KAAR,CAAc,aAAd,EAA6B,4CAA7B,EAA2EH,GAA3E;AACAN,sBAAAA,MAAM,CAACM,GAAD,CAAN;AACA;AACH;;AACD,oBAAA,MAAI,CAAClE,gBAAL,CAAuBV,WAAvB,CAAmC4D,IAAnC,CAAwCiB,MAAxC;;AACAR,oBAAAA,OAAO;AACV,mBARD;AASH,iBAXM,CAAP;AAYH,eAboB,CAArB;AAeA,oBAAMD,OAAO,CAACY,GAAR,CAAYd,YAAZ,CAAN,CAtB6C,CAuB7C;;AACA,kBAAIe,OAAO,GAAG,CAAd;AACA,cAAA,MAAI,CAAC5E,eAAL,GAAuB,CAAvB;;AACA,kBAAI,MAAI,CAACW,eAAT,EAA0B;AACtB,oBAAMM,SAAS,GAAG,MAAI,CAACf,oBAAvB;AACA,oBAAM2E,aAAa,GAAG5D,SAAS,CAACG,QAAV,CAAmBoC,MAAzC,CAFsB,CAGtB;;AACA,oBAAIqB,aAAa,GAAG,CAApB,EAAuB;AACnB,sBAAMC,QAAQ,GAAG,MAAI,CAAC5E,oBAAL,CAA2BkB,QAA3B,CAAoCyD,aAAa,GAAG,CAApD,CAAjB;;AACA,sBAAIC,QAAJ,EAAc;AACV,wBAAM3D,SAAS,GAAG2D,QAAQ,CAAC5D,cAAT,WAAlB;;AACA,wBAAIC,SAAJ,EAAe;AACXyD,sBAAAA,OAAO,GAAGzD,SAAS,CAAC4D,WAAV,GAAwBC,CAAlC;AACA,0BAAMpF,eAAe,GAAGuB,SAAS,CAACD,cAAV,CAAyB5B,mBAAzB,CAAxB;;AACA,0BAAIM,eAAJ,EAAqB;AACjBA,wBAAAA,eAAe,CAACwB,QAAhB,CAAyBC,OAAzB,CAAkCC,EAAD,IAAQ;AACrC,8BAAMC,MAAM,GAAGD,EAAE,CAACE,YAAH,CAAgBxC,WAAhB,EAA8ByC,WAA9B,CAA0CF,MAAzD;AACA,0BAAA,MAAI,CAACvB,eAAL,IAAwBuB,MAAxB;AACH,yBAHD;AAIH;AACJ;AACJ;AACJ;AACJ;;AAED,cAAA,MAAI,CAACtB,gBAAL,GAAwB,MAAI,CAACD,eAAL,GAAuB4E,OAA/C;AACAH,cAAAA,OAAO,CAACQ,GAAR,CAAY,aAAZ,EAA2B,sCAA3B,EAAmE,MAAI,CAACjF,eAAxE,EAAyF,SAAzF,EAAoG4E,OAApG;AAEA,cAAA,MAAI,CAACjE,eAAL,CAAsBnB,IAAtB,GAA6B,MAAI,CAAC6D,SAAL,CAAeb,UAAf,aAAsChD,IAAnE;AACA,cAAA,MAAI,CAACmB,eAAL,CAAsBf,eAAtB,GAAwC,MAAI,CAAC+B,aAAL,CAAmB,MAAI,CAAChB,eAAL,CAAsBnB,IAAzC,EAA+CF,mBAA/C,CAAxC;;AACA,cAAA,MAAI,CAACqB,eAAL,CAAsBnB,IAAtB,CAA2BgC,YAA3B;AAAA;AAAA,gDAAqE8B,eAArE,CAAqFZ,IAAI,CAAC/B,eAA1F,EAA2G,MAAI,CAACV,gBAAhH;;AACA,cAAA,MAAI,CAACU,eAAL,CAAsBf,eAAtB,CAAsCsF,eAAtC,CAAsD,CAAtD;;AAEA,kBAAIC,GAAG,GAAG,CAAV;;AACA,qBAAO,MAAI,CAAC9E,gBAAL,CAAsBV,WAAtB,CAAkC6D,MAAlC,GAA2C,CAA3C,IAAgDC,OAAO,GAAG,MAAI,CAACpD,gBAAL,CAAsBT,eAAtB,CAAuCwB,QAAvC,CAAgDoC,MAAjH,EAAyH;AACrH,oBAAIlC,EAAE,GAAGxC,WAAW,CAAC,MAAI,CAACuB,gBAAL,CAAsBV,WAAtB,CAAkC,MAAI,CAACU,gBAAL,CAAsBT,eAAtB,CAAuCwB,QAAvC,CAAgDoC,MAAhD,GAAyD,MAAI,CAACnD,gBAAL,CAAsBV,WAAtB,CAAkC6D,MAA7H,CAAD,CAApB;AACA,oBAAMjC,MAAM,GAAGD,EAAE,CAACE,YAAH,CAAgBxC,WAAhB,EAA8ByC,WAA9B,CAA0CF,MAAzD;AAEAD,gBAAAA,EAAE,CAAC8D,WAAH,CAAe,CAAf,EAAkBD,GAAlB,EAAuB,CAAvB;AACAA,gBAAAA,GAAG,IAAI5D,MAAP;;AACA,gBAAA,MAAI,CAAClB,gBAAL,CAAsBT,eAAtB,CAAuCkC,QAAvC,CAAgDR,EAAhD;AACH;AACJ;AAlEsJ;AAmE1J;;AAEO+B,QAAAA,SAAS,CAACb,UAAD,EAAmBX,IAAnB,EAA+C;AAC5D,cAAIwD,SAAS,GAAG,IAAItG,IAAJ,CAAS8C,IAAT,CAAhB;AACA,cAAIyD,QAAQ,GAAGD,SAAS,CAACE,YAAV;AAAA;AAAA,2CAAf;AACA/C,UAAAA,UAAU,CAACV,QAAX,CAAoBuD,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEME,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,eAAKvF,oBAAL,CAA2BkB,QAA3B,CAAoCC,OAApC,CAA6C7B,IAAD,IAAU;AAClDA,YAAAA,IAAI,CAAC4B,QAAL,CAAcC,OAAd,CAAuBqE,KAAD,IAAW;AAC7B,kBAAMC,OAAO,GAAGD,KAAK,CAAClE,YAAN;AAAA;AAAA,+CAAhB;;AACA,kBAAImE,OAAJ,EAAa;AACTA,gBAAAA,OAAO,CAACH,IAAR,CAAaC,SAAb,EAAwB,KAAK9E,eAAL,CAAsBlB,KAA9C;AACH;AACJ,aALD;AAMH,WAPD;;AASA,eAAKgB,WAAL,CAAiBY,OAAjB,CAA0B6B,KAAD,IAAW;AAAA;;AAChC,gBAAMyC,OAAO,kBAAGzC,KAAK,CAAC1D,IAAT,qBAAG,YAAYgC,YAAZ;AAAA;AAAA,6CAAhB;;AACA,gBAAImE,OAAJ,EAAa;AACT,kBAAIA,OAAO,CAACC,eAAZ,EAA6B;AACzBD,gBAAAA,OAAO,CAACH,IAAR,CAAaC,SAAb,EAAwB,KAAK9E,eAAL,CAAsBlB,KAA9C;AACH,eAFD,MAEO;AACHkG,gBAAAA,OAAO,CAACH,IAAR,CAAaC,SAAb,EAAwBvC,KAAK,CAACzD,KAA9B;AACH;AACJ;AACJ,WATD;AAUA,eAAKiB,SAAL,CAAeW,OAAf,CAAwB6B,KAAD,IAAW;AAAA;;AAC9B,gBAAMyC,OAAO,mBAAGzC,KAAK,CAAC1D,IAAT,qBAAG,aAAYgC,YAAZ;AAAA;AAAA,6CAAhB;;AACI,gBAAImE,OAAJ,EAAa;AACb,kBAAIA,OAAO,CAACC,eAAZ,EAA6B;AACzBD,gBAAAA,OAAO,CAACH,IAAR,CAAaC,SAAb,EAAwB,KAAK9E,eAAL,CAAsBlB,KAA9C;AACH,eAFD,MAEO;AACHkG,gBAAAA,OAAO,CAACH,IAAR,CAAaC,SAAb,EAAwBvC,KAAK,CAACzD,KAA9B;AACH;AACJ;AACJ,WATD;AAUH;;AAEOoG,QAAAA,oBAAoB,GAAS;AAChC,cAAI,CAAC,KAAK3F,oBAAV,EAAgC;AAEjC,eAAK4F,QAAL,CAAc,MAAM;AAChB,gBAAMC,aAAa,GAAG,KAAK7F,oBAAL,CAA2BgB,cAA3B,YAAmD,KAAKpB,cAAxD,CAAtB;;AACA,gBAAI,CAACiG,aAAL,EAAoB;AAEpB,gBAAMC,UAAU,GAAGD,aAAa,CAAC7E,cAAd,YAAsC,KAAKpB,cAA3C,CAAnB;AACA,gBAAI,CAACkG,UAAL,EAAiB;AAEjB,gBAAMC,MAAM,GAAGD,UAAU,CAACjB,WAAX,GAAyBC,CAAxC;AACA,gBAAMkB,YAAY,GAAGjH,IAAI,CAACkH,cAAL,GAAsB5E,MAA3C,CARgB,CAUhB;;AACA,gBAAI0E,MAAM,GAAG,CAACC,YAAD,GAAgB,CAA7B,EAAgC;AAC5BF,cAAAA,UAAU,CAACZ,WAAX,CAAuB,CAAvB,EAA0Ba,MAAM,GAAGC,YAAY,GAAG,CAAlD,EAAqD,CAArD;;AAEA,kBAAME,aAAa,GAAG,KAAKjG,gBAAL,CAAuBe,cAAvB,YAA+C,KAAKpB,cAApD,CAAtB;;AACA,kBAAIsG,aAAJ,EAAmB;AACfA,gBAAAA,aAAa,CAAChB,WAAd,CAA0B,CAA1B,EAA6Ba,MAAM,GAAGC,YAAY,GAAG,CAArD,EAAwD,CAAxD;AACH;;AAED,kBAAMG,WAAW,GAAG,KAAKjG,cAAL,CAAqBc,cAArB,UAA2C,KAAKpB,cAAhD,CAApB;;AACA,kBAAIuG,WAAJ,EAAiB;AACbA,gBAAAA,WAAW,CAACjB,WAAZ,CAAwB,CAAxB,EAA2Ba,MAAM,GAAGC,YAAY,GAAG,CAAnD,EAAsD,CAAtD;AACH;AACJ;AACJ,WAxBD,EAwBG,GAxBH;AAyBH;;AAEOI,QAAAA,qBAAqB,GAAS;AAClC,eAAK9F,gBAAL,GAAwB,MAAM;AAC1B,iBAAKgF,IAAL,CAAU,KAAV;AACH,WAFD;;AAIA,eAAKM,QAAL,CAAc,KAAKtF,gBAAnB,EAAqC,KAArC;AACH;;AAES+F,QAAAA,SAAS,GAAS;AACxB,eAAKC,UAAL,CAAgB,KAAKX,oBAArB;;AACA,cAAI,KAAKrF,gBAAT,EAA2B;AACvB,iBAAKgG,UAAL,CAAgB,KAAKhG,gBAArB;AACH;AACJ;;AAlRsC,O", "sourcesContent": ["import { _decorator, Component, instantiate, Node, Prefab, UITransform, view } from \"cc\";\r\nimport { LevelData, LevelDataLayer } from \"../../../leveldata/leveldata\";\r\nimport { LevelLayerUI } from \"./LevelLayerUI\";\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('LevelLayer')\r\nclass LevelLayer {\r\n    public node: Node | null = null;\r\n    public speed: number = 0;\r\n}\r\n\r\n@ccclass('LevelBackgroundLayer')\r\nclass LevelBackgroundLayer extends LevelLayer {\r\n    public backgrounds: Prefab[] = [];\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\n@ccclass('LevelBaseUI')\r\nexport class LevelBaseUI extends Component {\r\n    private _curLevelIndex: number = -1; // 当前关卡索引\r\n    private _totalTime: number = 10; // 当前关卡的时长\r\n    private _preLevelHeight: number = 0; // 上一关的关卡高度\r\n    private _preLevelOffsetY: number = 0; // 上一关的关卡偏移量\r\n    \r\n    private _backgroundLayerNode:Node|null = null;\r\n    private _floorLayersNode:Node|null = null;\r\n    private _skyLayersNode:Node|null = null;\r\n\r\n    private _backgroundLayer: LevelBackgroundLayer | null = null;\r\n    private _floorLayers: LevelLayer[] = [];\r\n    private _skyLayers: LevelLayer[] = [];\r\n\r\n    private _lastLevelUpdate: (() => void) | null = null; \r\n\r\n    public get floorLayers(): LevelLayer[] {\r\n        return this._floorLayers;\r\n    }\r\n    public get skyLayers(): LevelLayer[] {\r\n        return this._skyLayers;\r\n    }\r\n    public get backgroundLayer(): LevelBackgroundLayer | null {\r\n        if (!this._backgroundLayer) {\r\n            throw new Error(\"backgroundLayer is not initialized\");\r\n        }\r\n        return this._backgroundLayer;\r\n    }\r\n\r\n    public get TotalTime(): number {\r\n        return this._totalTime;\r\n    }\r\n\r\n    public getLevelTotalHeightByIndex(index: number): number {\r\n        var totalHeight = 0;\r\n        if (this._backgroundLayerNode) {\r\n            const levelNode = this._backgroundLayerNode.getChildByName(`level_${index}`);\r\n            if (levelNode) {\r\n                var preBgNode = levelNode.getChildByName(`layer_0`);\r\n                if (preBgNode) {\r\n                    const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);\r\n                    if (backgroundsNode) {\r\n                        backgroundsNode.children.forEach((bg) => {\r\n                            var height = bg.getComponent(UITransform)!.contentSize.height;\r\n                            totalHeight += height;\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return totalHeight;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public async levelPrefab(levelData: LevelData, levelInfo:{ levelID: number, levelCount: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {\r\n        this._backgroundLayerNode = this._getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this._floorLayersNode = this._getOrAddNode(this.node, \"FloorLayers\");\r\n        this._skyLayersNode = this._getOrAddNode(this.node, \"SkyLayers\");\r\n\r\n        if (bFristLevel) {\r\n            await this._initByLevelData(levelData,levelInfo);\r\n        } else {\r\n            this._initByLevelData(levelData,levelInfo);\r\n        }\r\n\r\n        // 如果是最后一关，设置无限循环滚动逻辑\r\n        /*if (levelInfo.levelIndex + 1 >= levelInfo.levelCount) {\r\n            this._setupInfiniteScroll();\r\n            this._setupLastLevelUpdate(); \r\n        }*/\r\n    }\r\n\r\n    public switchLevel(speed: number, time: number, levelIndex: number): void {\r\n        this.backgroundLayer!.speed = speed;\r\n        this._totalTime = time;\r\n\r\n        // 释放上一关资源\r\n        this._removeNode(this._backgroundLayerNode!, `level_${this._curLevelIndex}`);\r\n        this._removeNode(this._floorLayersNode!, `level_${this._curLevelIndex}`);\r\n        this._removeNode(this._skyLayersNode!, `level_${this._curLevelIndex}`);\r\n        \r\n        this._curLevelIndex = levelIndex;\r\n    }\r\n\r\n    private _removeNode(parentNode: Node, name: string): void {\r\n        var node = parentNode.getChildByName(name);\r\n        if (node) {\r\n            node.removeFromParent();\r\n        }\r\n    }\r\n\r\n    public async _initByLevelData(data: LevelData, levelInfo:{ levelID: number, levelCount: number, levelIndex: number }):Promise<void> {\r\n        const levelBackground = this._getOrAddNode(this._backgroundLayerNode!, `level_${levelInfo.levelIndex}`);\r\n        const levelFloor = this._getOrAddNode(this._floorLayersNode!, `level_${levelInfo.levelIndex}`);\r\n        const levelSky = this._getOrAddNode(this._skyLayersNode!, `level_${levelInfo.levelIndex}`);\r\n \r\n        await this._initBackgroundLayer(levelBackground, data, levelInfo);\r\n        this._initLayers(levelFloor, this.floorLayers, data.floorLayers);\r\n        this._initLayers(levelSky, this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    private _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]): void {\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;\r\n            levelLayer!.node!.getComponent<LevelLayerUI>(LevelLayerUI)!.initByLevelData(layer,this._preLevelOffsetY);\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private async _initBackgroundLayer(parentNode: Node, data: LevelData, levelInfo:{ levelID: number,levelCount: number, levelIndex: number }): Promise<void> {\r\n        if (data.backgroundLayer.backgrounds.length > 0) { \r\n            if (this._backgroundLayer === null) {\r\n                this._backgroundLayer = new LevelBackgroundLayer();\r\n                this._backgroundLayer.backgrounds = [];\r\n            }\r\n            this._backgroundLayer.speed = data.backgroundLayer.speed;\r\n            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1334);\r\n            const loadPromises = data.backgroundLayer.backgrounds.map((backgroundLayer) => {\r\n                return new Promise<void>((resolve, reject) => {\r\n                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, backgroundLayer);\r\n                    MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                        if (err) {\r\n                            console.error('LevelBaseUI', \"initByLevelData load background prefab err\", err);\r\n                            reject(err);\r\n                            return;\r\n                        }\r\n                        this._backgroundLayer!.backgrounds.push(prefab);\r\n                        resolve();\r\n                    });\r\n                });\r\n            });\r\n\r\n            await Promise.all(loadPromises);\r\n            // 节点设置偏移\r\n            var offsetY = 0;\r\n            this._preLevelHeight = 0;\r\n            if (this.backgroundLayer) {\r\n                const levelNode = this._backgroundLayerNode!;\r\n                const childrenCount = levelNode.children.length;\r\n                // 获取上一关的背景层\r\n                if (childrenCount > 1) {\r\n                    const preLevel = this._backgroundLayerNode!.children[childrenCount - 2];\r\n                    if (preLevel) {\r\n                        const preBgNode = preLevel.getChildByName(`layer_0`);\r\n                        if (preBgNode) {\r\n                            offsetY = preBgNode.getPosition().y;\r\n                            const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);\r\n                            if (backgroundsNode) {\r\n                                backgroundsNode.children.forEach((bg) => {\r\n                                    const height = bg.getComponent(UITransform)!.contentSize.height;\r\n                                    this._preLevelHeight += height;\r\n                                });\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            this._preLevelOffsetY = this._preLevelHeight + offsetY;\r\n            console.log('LevelBaseUI', \"_initBackgroundLayer _preLevelHeight\", this._preLevelHeight, \"offsetY\", offsetY);\r\n\r\n            this.backgroundLayer!.node = this._addLayer(parentNode, `layer_0`).node;\r\n            this.backgroundLayer!.backgroundsNode = this._getOrAddNode(this.backgroundLayer!.node, BackgroundsNodeName);\r\n            this.backgroundLayer!.node.getComponent<LevelLayerUI>(LevelLayerUI)!.initByLevelData(data.backgroundLayer, this._preLevelOffsetY);\r\n            this.backgroundLayer!.backgroundsNode.setSiblingIndex(0);\r\n\r\n            var pos = 0;\r\n            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode!.children.length) {\r\n                var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode!.children.length % this._backgroundLayer.backgrounds.length]);\r\n                const height = bg.getComponent(UITransform)!.contentSize.height;\r\n                \r\n                bg.setPosition(0, pos, 0);\r\n                pos += height;\r\n                this._backgroundLayer.backgroundsNode!.addChild(bg);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _addLayer(parentNode: Node, name: string): LevelLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        this._backgroundLayerNode!.children.forEach((node) => {\r\n            node.children.forEach((child) => {\r\n                const layerUI = child.getComponent<LevelLayerUI>(LevelLayerUI);\r\n                if (layerUI) {\r\n                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);\r\n                }\r\n            });\r\n        });\r\n\r\n        this.floorLayers.forEach((layer) => {\r\n            const layerUI = layer.node?.getComponent<LevelLayerUI>(LevelLayerUI);\r\n            if (layerUI) {\r\n                if (layerUI.TrackBackground) {\r\n                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);\r\n                } else {\r\n                    layerUI.tick(deltaTime, layer.speed);\r\n                }\r\n            }\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            const layerUI = layer.node?.getComponent<LevelLayerUI>(LevelLayerUI);\r\n                if (layerUI) {\r\n                if (layerUI.TrackBackground) {\r\n                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);\r\n                } else {\r\n                    layerUI.tick(deltaTime, layer.speed);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _setupInfiniteScroll(): void {\r\n         if (!this._backgroundLayerNode) return;\r\n\r\n        this.schedule(() => {\r\n            const lastLevelNode = this._backgroundLayerNode!.getChildByName(`level_${this._curLevelIndex}`);\r\n            if (!lastLevelNode) return;\r\n\r\n            const lastBgNode = lastLevelNode.getChildByName(`layer_${this._curLevelIndex}`);\r\n            if (!lastBgNode) return;\r\n\r\n            const bgPosY = lastBgNode.getPosition().y;\r\n            const screenHeight = view.getVisibleSize().height;\r\n\r\n            // 当原始节点完全滚出屏幕时，重置其位置到副本下方，并移除副本\r\n            if (bgPosY < -screenHeight * 2) {\r\n                lastBgNode.setPosition(0, bgPosY + screenHeight * 2, 0);\r\n\r\n                const lastFloorNode = this._floorLayersNode!.getChildByName(`floor_${this._curLevelIndex}`);\r\n                if (lastFloorNode) {\r\n                    lastFloorNode.setPosition(0, bgPosY + screenHeight * 2, 0);\r\n                }\r\n\r\n                const lastSkyNode = this._skyLayersNode!.getChildByName(`sky_${this._curLevelIndex}`);\r\n                if (lastSkyNode) {\r\n                    lastSkyNode.setPosition(0, bgPosY + screenHeight * 2, 0);\r\n                }\r\n            }\r\n        }, 0.1);\r\n    }\r\n\r\n    private _setupLastLevelUpdate(): void {\r\n        this._lastLevelUpdate = () => {\r\n            this.tick(0.016); \r\n        };\r\n\r\n        this.schedule(this._lastLevelUpdate, 0.016);\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.unschedule(this._setupInfiniteScroll);\r\n        if (this._lastLevelUpdate) {\r\n            this.unschedule(this._lastLevelUpdate);\r\n        }\r\n    }\r\n}"]}