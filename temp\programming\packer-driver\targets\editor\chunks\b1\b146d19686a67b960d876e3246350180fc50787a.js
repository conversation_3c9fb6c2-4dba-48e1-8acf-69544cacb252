System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _crd, ccclass, property, eEmitterAction, eBulletAction, eEmitterActionCn, eBulletActionCn;

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * ActionType对应要修改的属性
       * 以下是发射器的行为
       */

      _export("eEmitterAction", eEmitterAction = /*#__PURE__*/function (eEmitterAction) {
        eEmitterAction[eEmitterAction["Emitter_Active"] = 1] = "Emitter_Active";
        eEmitterAction[eEmitterAction["Emitter_InitialDelay"] = 2] = "Emitter_InitialDelay";
        eEmitterAction[eEmitterAction["Emitter_Prewarm"] = 3] = "Emitter_Prewarm";
        eEmitterAction[eEmitterAction["Emitter_PrewarmDuration"] = 4] = "Emitter_PrewarmDuration";
        eEmitterAction[eEmitterAction["Emitter_Duration"] = 5] = "Emitter_Duration";
        eEmitterAction[eEmitterAction["Emitter_ElapsedTime"] = 6] = "Emitter_ElapsedTime";
        eEmitterAction[eEmitterAction["Emitter_Loop"] = 7] = "Emitter_Loop";
        eEmitterAction[eEmitterAction["Emitter_LoopInterval"] = 8] = "Emitter_LoopInterval";
        eEmitterAction[eEmitterAction["Emitter_EmitInterval"] = 9] = "Emitter_EmitInterval";
        eEmitterAction[eEmitterAction["Emitter_PerEmitCount"] = 10] = "Emitter_PerEmitCount";
        eEmitterAction[eEmitterAction["Emitter_PerEmitInterval"] = 11] = "Emitter_PerEmitInterval";
        eEmitterAction[eEmitterAction["Emitter_PerEmitOffsetX"] = 12] = "Emitter_PerEmitOffsetX";
        eEmitterAction[eEmitterAction["Emitter_Angle"] = 13] = "Emitter_Angle";
        eEmitterAction[eEmitterAction["Emitter_Count"] = 14] = "Emitter_Count";
        eEmitterAction[eEmitterAction["Emitter_FireEffect"] = 15] = "Emitter_FireEffect";
        eEmitterAction[eEmitterAction["Bullet_Duration"] = 16] = "Bullet_Duration";
        eEmitterAction[eEmitterAction["Bullet_Damage"] = 17] = "Bullet_Damage";
        eEmitterAction[eEmitterAction["Bullet_Speed"] = 18] = "Bullet_Speed";
        eEmitterAction[eEmitterAction["Bullet_SpeedAngle"] = 19] = "Bullet_SpeedAngle";
        eEmitterAction[eEmitterAction["Bullet_Acceleration"] = 20] = "Bullet_Acceleration";
        eEmitterAction[eEmitterAction["Bullet_AccelerationAngle"] = 21] = "Bullet_AccelerationAngle";
        eEmitterAction[eEmitterAction["Bullet_Scale"] = 22] = "Bullet_Scale";
        eEmitterAction[eEmitterAction["Bullet_ColorR"] = 23] = "Bullet_ColorR";
        eEmitterAction[eEmitterAction["Bullet_ColorG"] = 24] = "Bullet_ColorG";
        eEmitterAction[eEmitterAction["Bullet_ColorB"] = 25] = "Bullet_ColorB";
        eEmitterAction[eEmitterAction["Bullet_FacingMoveDir"] = 26] = "Bullet_FacingMoveDir";
        eEmitterAction[eEmitterAction["Bullet_TrackingTarget"] = 27] = "Bullet_TrackingTarget";
        eEmitterAction[eEmitterAction["Bullet_Destructive"] = 28] = "Bullet_Destructive";
        eEmitterAction[eEmitterAction["Bullet_DestructiveOnHit"] = 29] = "Bullet_DestructiveOnHit";
        eEmitterAction[eEmitterAction["Unit_Life"] = 30] = "Unit_Life";
        eEmitterAction[eEmitterAction["Unit_LifePercent"] = 31] = "Unit_LifePercent";
        eEmitterAction[eEmitterAction["Unit_PosX"] = 32] = "Unit_PosX";
        eEmitterAction[eEmitterAction["Unit_PosY"] = 33] = "Unit_PosY";
        eEmitterAction[eEmitterAction["Unit_Speed"] = 34] = "Unit_Speed";
        eEmitterAction[eEmitterAction["Unit_SpeedAngle"] = 35] = "Unit_SpeedAngle";
        eEmitterAction[eEmitterAction["Unit_Acceleration"] = 36] = "Unit_Acceleration";
        eEmitterAction[eEmitterAction["Unit_AccelerationAngle"] = 37] = "Unit_AccelerationAngle";
        return eEmitterAction;
      }({}));
      /**
       * ActionType对应要修改的属性
       * 以下是子弹的行为
       */


      _export("eBulletAction", eBulletAction = /*#__PURE__*/function (eBulletAction) {
        eBulletAction[eBulletAction["Bullet_Duration"] = 100] = "Bullet_Duration";
        eBulletAction[eBulletAction["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
        eBulletAction[eBulletAction["Bullet_PosX"] = 102] = "Bullet_PosX";
        eBulletAction[eBulletAction["Bullet_PosY"] = 103] = "Bullet_PosY";
        eBulletAction[eBulletAction["Bullet_Damage"] = 104] = "Bullet_Damage";
        eBulletAction[eBulletAction["Bullet_Speed"] = 105] = "Bullet_Speed";
        eBulletAction[eBulletAction["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
        eBulletAction[eBulletAction["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
        eBulletAction[eBulletAction["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
        eBulletAction[eBulletAction["Bullet_Scale"] = 109] = "Bullet_Scale";
        eBulletAction[eBulletAction["Bullet_ColorR"] = 110] = "Bullet_ColorR";
        eBulletAction[eBulletAction["Bullet_ColorG"] = 111] = "Bullet_ColorG";
        eBulletAction[eBulletAction["Bullet_ColorB"] = 112] = "Bullet_ColorB";
        eBulletAction[eBulletAction["Bullet_FacingMoveDir"] = 113] = "Bullet_FacingMoveDir";
        eBulletAction[eBulletAction["Bullet_TrackingTarget"] = 114] = "Bullet_TrackingTarget";
        eBulletAction[eBulletAction["Bullet_Destructive"] = 115] = "Bullet_Destructive";
        eBulletAction[eBulletAction["Bullet_DestructiveOnHit"] = 116] = "Bullet_DestructiveOnHit";
        return eBulletAction;
      }({}));

      // 以下枚举值用于编辑器显示，实际运行时不会用到
      _export("eEmitterActionCn", eEmitterActionCn = /*#__PURE__*/function (eEmitterActionCn) {
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u542F\u7528"] = 1] = "\u53D1\u5C04\u5668\u542F\u7528";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u521D\u59CB\u5EF6\u8FDF"] = 2] = "\u53D1\u5C04\u5668\u521D\u59CB\u5EF6\u8FDF";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u9884\u70ED"] = 3] = "\u53D1\u5C04\u5668\u9884\u70ED";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u9884\u70ED\u6301\u7EED\u65F6\u95F4"] = 4] = "\u53D1\u5C04\u5668\u9884\u70ED\u6301\u7EED\u65F6\u95F4";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u6301\u7EED\u65F6\u95F4"] = 5] = "\u53D1\u5C04\u5668\u6301\u7EED\u65F6\u95F4";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5DF2\u8FD0\u884C\u65F6\u95F4"] = 6] = "\u53D1\u5C04\u5668\u5DF2\u8FD0\u884C\u65F6\u95F4";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5FAA\u73AF"] = 7] = "\u53D1\u5C04\u5668\u5FAA\u73AF";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5FAA\u73AF\u95F4\u9694"] = 8] = "\u53D1\u5C04\u5668\u5FAA\u73AF\u95F4\u9694";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5F00\u706B\u95F4\u9694"] = 9] = "\u53D1\u5C04\u5668\u5F00\u706B\u95F4\u9694";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u6B21\u6570"] = 10] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u6B21\u6570";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u95F4\u9694"] = 11] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u95F4\u9694";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u504F\u79FB"] = 12] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u504F\u79FB";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5F39\u9053\u89D2\u5EA6"] = 13] = "\u53D1\u5C04\u5668\u5F39\u9053\u89D2\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u53D1\u5C04\u5668\u5F39\u9053\u6570\u91CF"] = 14] = "\u53D1\u5C04\u5668\u5F39\u9053\u6570\u91CF";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u6301\u7EED\u65F6\u95F4"] = 16] = "\u5B50\u5F39\u6301\u7EED\u65F6\u95F4";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u4F24\u5BB3"] = 17] = "\u5B50\u5F39\u4F24\u5BB3";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u901F\u5EA6"] = 18] = "\u5B50\u5F39\u901F\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6"] = 19] = "\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u52A0\u901F\u5EA6"] = 20] = "\u5B50\u5F39\u52A0\u901F\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 21] = "\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u7F29\u653E"] = 22] = "\u5B50\u5F39\u7F29\u653E";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u989C\u8272R"] = 23] = "\u5B50\u5F39\u989C\u8272R";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u989C\u8272G"] = 24] = "\u5B50\u5F39\u989C\u8272G";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u989C\u8272B"] = 25] = "\u5B50\u5F39\u989C\u8272B";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411"] = 26] = "\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807"] = 27] = "\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u7834\u574F\u6027"] = 28] = "\u5B50\u5F39\u7834\u574F\u6027";
        eEmitterActionCn[eEmitterActionCn["\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F"] = 29] = "\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u751F\u547D\u503C"] = 30] = "\u5355\u4F4D\u751F\u547D\u503C";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u751F\u547D\u503C\u767E\u5206\u6BD4"] = 31] = "\u5355\u4F4D\u751F\u547D\u503C\u767E\u5206\u6BD4";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u4F4D\u7F6EX"] = 32] = "\u5355\u4F4D\u4F4D\u7F6EX";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u4F4D\u7F6EY"] = 33] = "\u5355\u4F4D\u4F4D\u7F6EY";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u901F\u5EA6"] = 34] = "\u5355\u4F4D\u901F\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u901F\u5EA6\u89D2\u5EA6"] = 35] = "\u5355\u4F4D\u901F\u5EA6\u89D2\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u52A0\u901F\u5EA6"] = 36] = "\u5355\u4F4D\u52A0\u901F\u5EA6";
        eEmitterActionCn[eEmitterActionCn["\u5355\u4F4D\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 37] = "\u5355\u4F4D\u52A0\u901F\u5EA6\u89D2\u5EA6";
        return eEmitterActionCn;
      }({}));

      _export("eBulletActionCn", eBulletActionCn = /*#__PURE__*/function (eBulletActionCn) {
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u6301\u7EED\u65F6\u95F4"] = 100] = "\u5B50\u5F39\u6301\u7EED\u65F6\u95F4";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u5DF2\u8FD0\u884C\u65F6\u95F4"] = 101] = "\u5B50\u5F39\u5DF2\u8FD0\u884C\u65F6\u95F4";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u4F4D\u7F6EX"] = 102] = "\u5B50\u5F39\u4F4D\u7F6EX";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u4F4D\u7F6EY"] = 103] = "\u5B50\u5F39\u4F4D\u7F6EY";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u4F24\u5BB3"] = 104] = "\u5B50\u5F39\u4F24\u5BB3";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u901F\u5EA6"] = 105] = "\u5B50\u5F39\u901F\u5EA6";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6"] = 106] = "\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u52A0\u901F\u5EA6"] = 107] = "\u5B50\u5F39\u52A0\u901F\u5EA6";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 108] = "\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u7F29\u653E"] = 109] = "\u5B50\u5F39\u7F29\u653E";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u989C\u8272R"] = 110] = "\u5B50\u5F39\u989C\u8272R";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u989C\u8272G"] = 111] = "\u5B50\u5F39\u989C\u8272G";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u989C\u8272B"] = 112] = "\u5B50\u5F39\u989C\u8272B";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411"] = 113] = "\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807"] = 114] = "\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u7834\u574F\u6027"] = 115] = "\u5B50\u5F39\u7834\u574F\u6027";
        eBulletActionCn[eBulletActionCn["\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F"] = 116] = "\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F";
        return eBulletActionCn;
      }({}));

      _crd = false;
    }
  };
});
//# sourceMappingURL=b146d19686a67b960d876e3246350180fc50787a.js.map