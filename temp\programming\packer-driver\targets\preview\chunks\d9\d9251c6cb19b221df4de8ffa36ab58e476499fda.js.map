{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Enum", "Vec2", "Vec3", "UITransform", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "target", "arrivalDistance", "_selfSize", "_position", "_tiltTime", "_basePosition", "_isVisible", "_isMovable", "onBecomeVisibleCallback", "onBecomeInvisibleCallback", "isVisible", "isMovable", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "tick", "dt", "speedRadians", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "getPosition", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationRadians", "accelerationX", "accelerationY", "moveAngleRad", "perpX", "perpY", "tiltAmount", "setPosition", "checkVisibility", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "visibleSize", "worldBounds", "getWorldPosition", "xMin", "xMax", "yMax", "yMin", "setVisible", "visible", "<PERSON><PERSON><PERSON><PERSON>", "setMovable", "movable", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;;AAI3DC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCR,I;OACzC;AAAES,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;sCAMrCa,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;yBASCC,O,WAFZJ,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEZ,IAAI,CAACU,oBAAD,CAAX;AAAmCG,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gBAHZJ,iB,qBADD,MAEaE,OAFb,SAE6BZ,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDe,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAE9C;AAbuD,eAchDC,SAdgD,GAc5B,CAd4B;AAcT;AAC9C;AAfuD,eAgBhDC,UAhBgD,GAgB3B,GAhB2B;AAgBR;AAhBQ,eAkBhDC,MAlBgD,GAkB1B,IAlB0B;AAkBT;AAlBS,eAmBhDC,eAnBgD,GAmBtB,EAnBsB;AAmBT;AAnBS,eAqB/CC,SArB+C,GAqB7B,IAAIxB,IAAJ,EArB6B;AAAA,eAsB/CyB,SAtB+C,GAsB7B,IAAIxB,IAAJ,EAtB6B;AAAA,eAuB/CyB,SAvB+C,GAuB3B,CAvB2B;AAuBT;AAvBS,eAwB/CC,aAxB+C,GAwBzB,IAAI1B,IAAJ,EAxByB;AAwBT;AAxBS,eA0B/C2B,UA1B+C,GA0BzB,IA1ByB;AAAA,eA4B/CC,UA5B+C,GA4BzB,IA5ByB;AA+BvD;AA/BuD,eAgChDC,uBAhCgD,GAgCL,IAhCK;AAAA,eAiChDC,yBAjCgD,GAiCH,IAjCG;AAAA;;AA0BT;AAC1B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKJ,UAAZ;AAAyB;;AACJ;AAC1B,YAATK,SAAS,GAAG;AAAE,iBAAO,KAAKJ,UAAZ;AAAyB;;AAKlD;AAEAK,QAAAA,MAAM,GAAG;AACL,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBnC,WAAvB,CAApB;AACA,cAAMoC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAACC,YAAAA,KAAK,EAAE,CAAR;AAAWC,YAAAA,MAAM,EAAE;AAAnB,WAA1D;;AACA,eAAKjB,SAAL,CAAekB,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;AACH;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKf,UAAV,EAAsB;AAEtB,cAAMgB,YAAY,GAAGzC,gBAAgB,CAAC,KAAKY,UAAN,CAArC,CAH0B,CAI1B;;AACA,cAAI8B,SAAS,GAAG,KAAK/B,KAAL,GAAagC,IAAI,CAACC,GAAL,CAASH,YAAT,CAA7B;AACA,cAAII,SAAS,GAAG,KAAKlC,KAAL,GAAagC,IAAI,CAACG,GAAL,CAASL,YAAT,CAA7B;;AAEA,cAAI,KAAK/B,gBAAL,IAAyB,KAAKQ,MAAlC,EAA0C;AACtC,gBAAM6B,SAAS,GAAG,KAAK7B,MAAL,CAAY8B,WAAZ,EAAlB;AACA,gBAAMC,UAAU,GAAG,KAAKjB,IAAL,CAAUgB,WAAV,EAAnB,CAFsC,CAItC;;AACA,gBAAME,UAAU,GAAGH,SAAS,CAACI,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,gBAAMC,UAAU,GAAGL,SAAS,CAACM,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,gBAAMC,QAAQ,GAAGX,IAAI,CAACY,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,kBAAME,YAAY,GAAGvD,gBAAgB,CAAC0C,IAAI,CAACc,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,kBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAK5C,UAAtC,CALc,CAMd;;AACA,kBAAM+C,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,kBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,kBAAMC,WAAW,GAAG,KAAKhD,SAAzB,CAXc,CAWsB;;AACpC,kBAAMiD,UAAU,GAAGnB,IAAI,CAACoB,GAAL,CAASpB,IAAI,CAACqB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGrB,EAAtD,IAA4DG,IAAI,CAACsB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAK/C,UAAL,IAAmBkD,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAlB,cAAAA,SAAS,GAAG,KAAK/B,KAAL,GAAagC,IAAI,CAACC,GAAL,CAAS5C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACAiC,cAAAA,SAAS,GAAG,KAAKlC,KAAL,GAAagC,IAAI,CAACG,GAAL,CAAS9C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACH;AACJ,WArCyB,CAuC1B;;;AACA,cAAI,KAAKE,YAAL,KAAsB,CAA1B,EAA6B;AACzB,gBAAMoD,mBAAmB,GAAGlE,gBAAgB,CAAC,KAAKe,iBAAN,CAA5C;AACA,gBAAMoD,aAAa,GAAG,KAAKrD,YAAL,GAAoB6B,IAAI,CAACC,GAAL,CAASsB,mBAAT,CAA1C;AACA,gBAAME,aAAa,GAAG,KAAKtD,YAAL,GAAoB6B,IAAI,CAACG,GAAL,CAASoB,mBAAT,CAA1C,CAHyB,CAIzB;;AACAxB,YAAAA,SAAS,IAAIyB,aAAa,GAAG3B,EAA7B;AACAK,YAAAA,SAAS,IAAIuB,aAAa,GAAG5B,EAA7B;AACH,WA/CyB,CAiD1B;;;AACA,eAAK7B,KAAL,GAAagC,IAAI,CAACY,IAAL,CAAUb,SAAS,GAAGA,SAAZ,GAAwBG,SAAS,GAAGA,SAA9C,CAAb;AACA,eAAKjC,UAAL,GAAkBX,gBAAgB,CAAC0C,IAAI,CAACc,KAAL,CAAWZ,SAAX,EAAsBH,SAAtB,CAAD,CAAlC,CAnD0B,CAqD1B;;AACA,cAAIA,SAAS,KAAK,CAAd,IAAmBG,SAAS,KAAK,CAArC,EAAwC;AACpC;AACA,iBAAKtB,aAAL,CAAmB4B,CAAnB,IAAwBT,SAAS,GAAGF,EAApC;AACA,iBAAKjB,aAAL,CAAmB8B,CAAnB,IAAwBR,SAAS,GAAGL,EAApC,CAHoC,CAKpC;;AACA,iBAAKnB,SAAL,CAAeiB,GAAf,CAAmB,KAAKf,aAAxB,EANoC,CAQpC;;;AACA,gBAAI,KAAKP,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C;AACA,mBAAKK,SAAL,IAAkBkB,EAAlB,CAF2C,CAI3C;AACA;;AACA,kBAAM6B,YAAY,GAAGrE,gBAAgB,CAAC,KAAKY,UAAN,CAArC;AACA,kBAAM0D,KAAK,GAAG,CAAC3B,IAAI,CAACG,GAAL,CAASuB,YAAT,CAAf;AACA,kBAAME,KAAK,GAAG5B,IAAI,CAACC,GAAL,CAASyB,YAAT,CAAd,CAR2C,CAU3C;;AACA,kBAAMG,UAAU,GAAG7B,IAAI,CAACG,GAAL,CAAS,KAAKxB,SAAL,GAAiB,KAAKN,SAA/B,IAA4C,KAAKC,UAApE,CAX2C,CAa3C;;AACA,mBAAKI,SAAL,CAAe8B,CAAf,IAAoBmB,KAAK,GAAGE,UAA5B;AACA,mBAAKnD,SAAL,CAAegC,CAAf,IAAoBkB,KAAK,GAAGC,UAA5B;AACH;;AAED,iBAAKxC,IAAL,CAAUyC,WAAV,CAAsB,KAAKpD,SAA3B;AACA,iBAAKqD,eAAL;AACH;;AAED,cAAI,KAAKjE,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,gBAAMgE,aAAa,GAAG1E,gBAAgB,CAAC0C,IAAI,CAACc,KAAL,CAAWZ,SAAX,EAAsBH,SAAtB,CAAD,CAAtC;AACA,gBAAMkC,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAK7C,IAAL,CAAU8C,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;;AAEMF,QAAAA,eAAe,GAAS;AAC3B;AACA;AACA,cAAMK,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,eAAKhD,IAAL,CAAUiD,gBAAV,CAA2B,KAAK5D,SAAhC;AACA,cAAMO,SAAS,GAAI,KAAKP,SAAL,CAAe8B,CAAf,GAAmB,KAAK/B,SAAL,CAAe+B,CAAnC,IAAyC4B,WAAW,CAACG,IAArD,IACC,KAAK7D,SAAL,CAAe8B,CAAf,GAAmB,KAAK/B,SAAL,CAAe+B,CAAnC,IAAyC4B,WAAW,CAACI,IADrD,IAEC,KAAK9D,SAAL,CAAegC,CAAf,GAAmB,KAAKjC,SAAL,CAAeiC,CAAnC,IAAyC0B,WAAW,CAACK,IAFrD,IAGC,KAAK/D,SAAL,CAAegC,CAAf,GAAmB,KAAKjC,SAAL,CAAeiC,CAAnC,IAAyC0B,WAAW,CAACM,IAHvE,CAL2B,CAU3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAKC,UAAL,CAAgB1D,SAAhB;AACH;;AAEM0D,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAK/D,UAAL,KAAoB+D,OAAxB,EAAiC;AAEjC,eAAK/D,UAAL,GAAkB+D,OAAlB;;AACA,cAAIA,OAAO,IAAI,KAAK7D,uBAApB,EAA6C;AACzC,iBAAKA,uBAAL;AACH,WAFD,MAEO,IAAI,CAAC6D,OAAD,IAAY,KAAK5D,yBAArB,EAAgD;AACnD,iBAAKA,yBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACW6D,QAAAA,SAAS,CAACtE,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKR,gBAAL,GAAwBQ,MAAM,KAAK,IAAnC;AACH;;AAEMuE,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,eAAKjE,UAAL,GAAkBiE,OAAlB;;AAEA,cAAI,KAAKjE,UAAT,EAAqB;AACjB;AACA,iBAAKO,IAAL,CAAUgB,WAAV,CAAsB,KAAKzB,aAA3B;AACH;AACJ;;AAtLsD,O;;;;;iBAGVlB,oBAAoB,CAACsF,E", "sourcesContent": ["import { _decorator, misc, size, Component, Enum, Vec2, Vec3, Node, UITransform } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable } from './IMovable';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/scripts/game/collider-system/FCollider';\r\nimport Entity from 'db://assets/scripts/game/ui/base/Entity';\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})\r\n    public tiltSpeed: number = 0;                 // 偏移速度\r\n    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})\r\n    public tiltOffset: number = 100;               // 偏移距离\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _selfSize: Vec2 = new Vec2();\r\n    private _position: Vec3 = new Vec3();\r\n    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间\r\n    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）\r\n\r\n    private _isVisible: boolean = true;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n    private _isMovable: boolean = true;           // 是否可移动\r\n    public get isMovable() { return this._isMovable; }\r\n\r\n    // Callbacks:\r\n    public onBecomeVisibleCallback: Function | null = null;\r\n    public onBecomeInvisibleCallback: Function | null = null;\r\n    // public onCollideCallback: Function | null = null;\r\n\r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : {width: 0, height: 0};\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this._isMovable) return;\r\n\r\n        const speedRadians = degreesToRadians(this.speedAngle);\r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(speedRadians);\r\n        let velocityY = this.speed * Math.sin(speedRadians);\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n            \r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n            \r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n                \r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n                \r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n                \r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                \r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        if (this.acceleration !== 0) {\r\n            const accelerationRadians = degreesToRadians(this.accelerationAngle);\r\n            const accelerationX = this.acceleration * Math.cos(accelerationRadians);\r\n            const accelerationY = this.acceleration * Math.sin(accelerationRadians);\r\n            // Update velocity vector: v = v + a * dt\r\n            velocityX += accelerationX * dt;\r\n            velocityY += accelerationY * dt;\r\n        }\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (velocityX !== 0 || velocityY !== 0) {\r\n            // Update base position (main movement path)\r\n            this._basePosition.x += velocityX * dt;\r\n            this._basePosition.y += velocityY * dt;\r\n\r\n            // Start with base position\r\n            this._position.set(this._basePosition);\r\n\r\n            // Apply tilting behavior if enabled\r\n            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\r\n                // Update tilt time\r\n                this._tiltTime += dt;\r\n\r\n                // Calculate perpendicular direction to movement\r\n                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))\r\n                const moveAngleRad = degreesToRadians(this.speedAngle);\r\n                const perpX = -Math.sin(moveAngleRad);\r\n                const perpY = Math.cos(moveAngleRad);\r\n\r\n                // Calculate tilt offset using sine wave\r\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\r\n\r\n                // Apply tilt offset in perpendicular direction (as position offset, not velocity)\r\n                this._position.x += perpX * tiltAmount;\r\n                this._position.y += perpY * tiltAmount;\r\n            }\r\n\r\n            this.node.setPosition(this._position);\r\n            this.checkVisibility();\r\n        }\r\n        \r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 这里目前的检查逻辑没有考虑旋转和缩放\r\n        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        this.node.getWorldPosition(this._position);\r\n        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n                          (this._position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n                          (this._position.y - this._selfSize.y) <= visibleSize.yMax && \r\n                          (this._position.y + this._selfSize.y) >= visibleSize.yMin;\r\n\r\n        // debug visibility\r\n        // if (!isVisible) {\r\n        //     console.log(\"Movable\", \"checkVisibility\", this.node.name + \" is not visible\");\r\n        //     console.log(\"Movable\", \"checkLeftBound  :\", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), \"<=\", visibleSize.xMax);\r\n        //     console.log(\"Movable\", \"checkRightBound :\", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), \">=\", visibleSize.xMin);\r\n        //     console.log(\"Movable\", \"checkTopBound   :\", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), \"<=\", visibleSize.yMax);\r\n        //     console.log(\"Movable\", \"checkBottomBound:\", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), \">=\", visibleSize.yMin);\r\n        // }\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this._isVisible === visible) return;\r\n\r\n        this._isVisible = visible;\r\n        if (visible && this.onBecomeVisibleCallback) {\r\n            this.onBecomeVisibleCallback();\r\n        } else if (!visible && this.onBecomeInvisibleCallback) {\r\n            this.onBecomeInvisibleCallback();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n\r\n    public setMovable(movable: boolean) {\r\n        this._isMovable = movable;\r\n\r\n        if (this._isMovable) {\r\n            // Initialize base position to current node position\r\n            this.node.getPosition(this._basePosition);\r\n        }\r\n    }\r\n}"]}