System.register(["__unresolved_0", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, eTargetValueType, Easing, EventActionBase, _crd;

  function _reportPossibleCrUseOfeTargetValueType(extras) {
    _reporterNs.report("eTargetValueType", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventActionData(extras) {
    _reporterNs.report("IEventActionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEasing(extras) {
    _reporterNs.report("Easing", "../Easing", _context.meta, extras);
  }

  _export("EventActionBase", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      eTargetValueType = _unresolved_2.eTargetValueType;
    }, function (_unresolved_3) {
      Easing = _unresolved_3.Easing;
    }],
    execute: function () {
      _crd = true;

      _export("EventActionBase", EventActionBase = class EventActionBase {
        constructor(data) {
          this.data = void 0;
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._startValue = 0;
          this._targetValue = 0;
          this._duration = 0;
          this.data = data;
        }

        isCompleted() {
          return this._isCompleted;
        }

        canLerp() {
          return true;
        }

        onLoad(context) {
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._duration = this.data.duration.eval();
          this.resetStartValue(context);
          this.resetTargetValue(context);
        }

        onExecute(context, dt) {
          this._elapsedTime += dt;

          if (this._elapsedTime >= this._duration) {
            this.executeInternal(context, this._targetValue);
            this._isCompleted = true;
          } else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this._targetValue));
          }
        }

        lerpValue(startValue, targetValue) {
          return (_crd && Easing === void 0 ? (_reportPossibleCrUseOfEasing({
            error: Error()
          }), Easing) : Easing).lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this._duration));
        } // override this to get the correct start value


        resetStartValue(context) {
          this._startValue = 0;
        }

        resetTargetValue(context) {
          switch (this.data.targetValueType) {
            case (_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
              error: Error()
            }), eTargetValueType) : eTargetValueType).Relative:
              this._targetValue = this.data.targetValue.eval() + this._startValue;
              break;

            default:
              this._targetValue = this.data.targetValue.eval();
              break;
          }
        }

        executeInternal(context, value) {// Default implementation does nothing
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=b8c0eb2ff9144349427b80bd043445ba10e6425b.js.map