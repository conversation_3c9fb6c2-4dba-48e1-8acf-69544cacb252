{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts"], "names": ["_decorator", "Component", "Node", "Color", "tween", "UITransform", "Sprite", "UIOpacity", "GameConst", "ccclass", "property", "EffectLayer", "onLoad", "me", "whiteNode", "getComponent", "width", "ViewWidth", "height", "ViewHeight", "showWhiteScreen", "delay", "opacity", "active", "color", "WHITE", "ActionFrameTime", "to", "call", "start", "lightingShow", "BLACK", "showRedScreen", "redNode", "frameTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAChEC,MAAAA,S,iBAAAA,S;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAGTW,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACR,IAAD,C,UAGRQ,QAAQ,CAACR,IAAD,C,sCALb,MACqBS,WADrB,SACyCV,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAS/CW,QAAAA,MAAM,GAAG;AACLD,UAAAA,WAAW,CAACE,EAAZ,GAAiB,IAAjB;AACA,eAAKC,SAAL,CAAgBC,YAAhB,CAA6BV,WAA7B,EAA2CW,KAA3C,GAAmD;AAAA;AAAA,sCAAUC,SAA7D;AACA,eAAKH,SAAL,CAAgBC,YAAhB,CAA6BV,WAA7B,EAA2Ca,MAA3C,GAAoD;AAAA;AAAA,sCAAUC,UAA9D;AACH;;AAEDC,QAAAA,eAAe,CAACC,KAAD,EAAgBC,OAAhB,EAAuC;AAAA,cAAvBA,OAAuB;AAAvBA,YAAAA,OAAuB,GAAL,GAAK;AAAA;;AAClD,cAAI,CAAC,KAAKR,SAAV,EAAqB;AACrB,eAAKA,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAwCe,OAAxC,GAAkDA,OAAlD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAqCkB,KAArC,GAA6CrB,KAAK,CAACsB,KAAnD;AAEArB,UAAAA,KAAK,CAAC,KAAKU,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,CAAD,CAAL,CACKc,KADL,CACW,IAAI;AAAA;AAAA,sCAAUK,eADzB,EAEKC,EAFL,CAEQ,IAFR,EAEc;AAAEL,YAAAA,OAAO,EAAE;AAAX,WAFd,EAGKM,IAHL,CAGU,MAAM;AACR,iBAAKd,SAAL,CAAgBS,MAAhB,GAAyB,KAAzB;AACH,WALL,EAMKM,KANL;AAOH;;AAEDC,QAAAA,YAAY,GAAG;AACX,cAAI,CAAC,KAAKhB,SAAV,EAAqB;AACrB,eAAKA,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAwCe,OAAxC,GAAkD,MAAlD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAqCkB,KAArC,GAA6CrB,KAAK,CAACsB,KAAnD;AAEArB,UAAAA,KAAK,CAAC,KAAKU,SAAN,CAAL,CACKO,KADL,CACW,IAAI,EADf,EAEKO,IAFL,CAEU,MAAM;AACR,iBAAKd,SAAL,CAAgBC,YAAhB,CAA6BR,SAA7B,EAAyCe,OAAzC,GAAmD,KAAnD;AACA,iBAAKR,SAAL,CAAgBC,YAAhB,CAA6BT,MAA7B,EAAsCkB,KAAtC,GAA8CrB,KAAK,CAAC4B,KAApD;AACH,WALL,EAMKV,KANL,CAMW,IAAI,EANf,EAOKO,IAPL,CAOU,MAAM;AACR,iBAAKd,SAAL,CAAgBS,MAAhB,GAAyB,KAAzB;AACH,WATL,EAUKF,KAVL,CAUW,IAAI,EAVf,EAWKO,IAXL,CAWU,MAAM;AACR,iBAAKd,SAAL,CAAgBC,YAAhB,CAA6BR,SAA7B,EAAyCe,OAAzC,GAAmD,KAAnD;AACA,iBAAKR,SAAL,CAAgBC,YAAhB,CAA6BT,MAA7B,EAAsCkB,KAAtC,GAA8CrB,KAAK,CAAC4B,KAApD;AACH,WAdL,EAeKV,KAfL,CAeW,IAAI,EAff,EAgBKO,IAhBL,CAgBU,MAAM;AACR,iBAAKd,SAAL,CAAgBS,MAAhB,GAAyB,KAAzB;AACH,WAlBL,EAmBKM,KAnBL;AAoBH;;AAEDG,QAAAA,aAAa,GAAG;AACZ,cAAI,CAAC,KAAKC,OAAV,EAAmB;AACnB,eAAKA,OAAL,CAAalB,YAAb,CAA0BR,SAA1B,EAAsCe,OAAtC,GAAgD,CAAhD;AACA,eAAKW,OAAL,CAAalB,YAAb,CAA0BV,WAA1B,EAAwCW,KAAxC,GAAgD;AAAA;AAAA,sCAAUC,SAA1D;AACA,eAAKgB,OAAL,CAAalB,YAAb,CAA0BV,WAA1B,EAAwCa,MAAxC,GAAiD;AAAA;AAAA,sCAAUC,UAA3D;AAEA,cAAMe,SAAS,GAAG;AAAA;AAAA,sCAAUR,eAA5B;AAEAtB,UAAAA,KAAK,CAAC,KAAK6B,OAAL,CAAalB,YAAb,CAA0BR,SAA1B,CAAD,CAAL,CACKoB,EADL,CACQ,CADR,EACW;AAAEL,YAAAA,OAAO,EAAE;AAAX,WADX,EAEKK,EAFL,CAEQ,IAAIO,SAFZ,EAEuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAFvB,EAGKK,EAHL,CAGQ,IAAIO,SAHZ,EAGuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAHvB,EAIKK,EAJL,CAIQ,KAAKO,SAJb,EAIwB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAJxB,EAKKO,KALL;AAMH;;AAxE8C,O,UAOxChB,E;;;;;iBALgB,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node, Color, tween, UITransform, Sprite, UIOpacity } from 'cc';\r\nimport { GameConst } from '../../const/GameConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EffectLayer')\r\nexport default class EffectLayer extends Component {\r\n    @property(Node)\r\n    whiteNode: Node|null = null;\r\n\r\n    @property(Node)\r\n    redNode: Node|null = null;\r\n\r\n    static me: EffectLayer;\r\n\r\n    onLoad() {\r\n        EffectLayer.me = this;\r\n        this.whiteNode!.getComponent(UITransform)!.width = GameConst.ViewWidth;\r\n        this.whiteNode!.getComponent(UITransform)!.height = GameConst.ViewHeight;\r\n    }\r\n\r\n    showWhiteScreen(delay: number, opacity: number = 255) {\r\n        if (!this.whiteNode) return;\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity)!.opacity = opacity;\r\n        this.whiteNode.getComponent(Sprite)!.color = Color.WHITE;\r\n\r\n        tween(this.whiteNode.getComponent(UIOpacity)!)\r\n            .delay(4 * GameConst.ActionFrameTime)\r\n            .to(0.33, { opacity: 0 })\r\n            .call(() => {\r\n                this.whiteNode!.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    lightingShow() {\r\n        if (!this.whiteNode) return;\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity)!.opacity = 140.25;\r\n        this.whiteNode.getComponent(Sprite)!.color = Color.WHITE;\r\n\r\n        tween(this.whiteNode)\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode!.getComponent(UIOpacity)!.opacity = 178.5;\r\n                this.whiteNode!.getComponent(Sprite)!.color = Color.BLACK;\r\n            })\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode!.active = false;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode!.getComponent(UIOpacity)!.opacity = 127.5;\r\n                this.whiteNode!.getComponent(Sprite)!.color = Color.BLACK;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode!.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    showRedScreen() {\r\n        if (!this.redNode) return;\r\n        this.redNode.getComponent(UIOpacity)!.opacity = 0;\r\n        this.redNode.getComponent(UITransform)!.width = GameConst.ViewWidth;\r\n        this.redNode.getComponent(UITransform)!.height = GameConst.ViewHeight;\r\n\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        tween(this.redNode.getComponent(UIOpacity)!)\r\n            .to(0, { opacity: 204 })\r\n            .to(4 * frameTime, { opacity: 255 })\r\n            .to(2 * frameTime, { opacity: 224 })\r\n            .to(15 * frameTime, { opacity: 0 })\r\n            .start();\r\n    }\r\n}"]}