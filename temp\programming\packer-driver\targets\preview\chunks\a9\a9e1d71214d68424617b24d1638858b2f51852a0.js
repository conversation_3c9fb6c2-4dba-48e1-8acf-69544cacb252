System.register(["__unresolved_0", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, SingletonBase, StageData, GameIns, <PERSON>App, StageManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageData(extras) {
    _reporterNs.report("StageData", "../data/StageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  _export("StageManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      StageData = _unresolved_3.StageData;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }],
    execute: function () {
      _crd = true;

      _export("StageManager", StageManager = class StageManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super();
          this._allStageDataArr = [];
          this._curStageDataArr = [];
          this.initConfig();
        }

        initConfig() {
          var stages = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbStage.getDataList();

          for (var data of stages) {
            var stage = new (_crd && StageData === void 0 ? (_reportPossibleCrUseOfStageData({
              error: Error()
            }), StageData) : StageData)();
            stage.loadJson(data);

            this._allStageDataArr.push(stage);
          }
        }

        initBattle(mainId, subId) {
          this._curStageDataArr.splice(0);

          this._allStageDataArr.forEach(stage => {
            if (stage.mainStage === mainId && stage.subStage === subId) {
              this._curStageDataArr.push(stage);
            }
          });
        }

        getBossTips() {
          return "";
        }

        gameStart() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.setEnemyActions(this._curStageDataArr);
        }

        checkStage(mainId, subId) {
          // for (const stage of this._allStageDataArr) {
          //     if (stage.mainStage === mainId && stage.subStage === subId) {
          //         return true;
          //     }
          // }
          return false;
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=a9e1d71214d68424617b24d1638858b2f51852a0.js.map