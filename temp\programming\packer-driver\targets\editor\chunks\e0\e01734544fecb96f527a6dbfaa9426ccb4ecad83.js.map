{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/manager/WaveManager.ts"], "names": ["WaveManager", "warn", "SingletonBase", "GameIns", "Tools", "enemyCreateAble", "_bEnemyCreateAble", "value", "constructor", "_waveNorDatasMap", "Map", "_enemyOver", "_bEnemyNorCreateAble", "_enemyActions", "_enemyActionIndex", "_enemyCreateTime", "_curEnemyAction", "_waveCreateTime", "_waveIndexOver", "_waveIndex", "_waveArr", "_waveNumArr", "_waveTimeArr", "_waveActionArr", "_waves", "_boss<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bossCreateTime", "_bossToAddArr", "_bShowBossWarning", "reset", "splice", "setEnemyActions", "actions", "gameStart", "getNorWaveDatas", "groupID", "get", "addWaveByLevel", "wave", "posX", "posY", "trigger", "push", "console", "log", "updateGameLogic", "deltaTime", "_updateCurAction", "_updateEnemy", "_updateBoss", "length", "action", "type", "enemyNorInterval", "enemyManager", "getNormalPlaneCount", "enemyNorIDs", "_updateWaves", "_updateNorEnemys", "dtInMiliseconds", "i", "tick", "isCompleted", "waveID", "waveDatas", "arrC<PERSON>ain", "groupInterval", "isEnemyOver", "bossData", "boss", "boss<PERSON><PERSON><PERSON>", "addBoss"], "mappings": ";;;oHAYqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZbC,MAAAA,I,OAAAA,I;;AACCC,MAAAA,a,iBAAAA,a;;AAGAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;yBAOYJ,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AA4B7C,YAAfK,eAAe,GAAY;AAC3B,iBAAO,KAAKC,iBAAZ;AACH;;AAEkB,YAAfD,eAAe,CAACE,KAAD,EAAiB;AAChC,eAAKD,iBAAL,GAAyBC,KAAzB;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,kBADU,CAEV;;AAFU,eAnCNC,gBAmCM,GAnCuC,IAAIC,GAAJ,EAmCvC;AAnCiD;AAmCjD,eAlCNC,UAkCM,GAlCgB,KAkChB;AAlCsB;AAkCtB,eAhCNL,iBAgCM,GAhCuB,KAgCvB;AAhC6B;AAgC7B,eA/BNM,oBA+BM,GA/B0B,KA+B1B;AA/BgC;AA+BhC,eA7BNC,aA6BM,GA7B8B,IA6B9B;AA7BmC;AA6BnC,eA5BNC,iBA4BM,GA5BsB,CA4BtB;AA5BwB;AA4BxB,eA3BNC,gBA2BM,GA3BqB,CA2BrB;AA3BuB;AA2BvB,eA1BNC,eA0BM,GA1B8B,IA0B9B;AA1BmC;AA0BnC,eAzBNC,eAyBM,GAzBoB,CAyBpB;AAAA,eAxBNC,cAwBM,GAxBqB,EAwBrB;AAAA,eAtBNC,UAsBM,GAtBe,CAsBf;AAtBiB;AAsBjB,eArBNC,QAqBM,GArBkB,EAqBlB;AArBqB;AAqBrB,eApBNC,WAoBM,GApBkB,EAoBlB;AApBqB;AAoBrB,eAnBNC,YAmBM,GAnBmB,EAmBnB;AAnBsB;AAmBtB,eAlBNC,cAkBM,GAlBkB,EAkBlB;AAAA,eAjBNC,MAiBM,GAjBW,EAiBX;AAfd;AAec,eAdNC,gBAcM,GAdqB,CAcrB;AAAA,eAbNC,eAaM,GAboB,CAapB;AAAA,eAZNC,aAYM,GAZiB,EAYjB;AAAA,eAXNC,iBAWM,GAXuB,KAWvB;AAGb,SAvC+D,CAyChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAC,QAAAA,KAAK,GAAS;AACV,eAAKlB,UAAL,GAAkB,KAAlB;AACA,eAAKE,aAAL,GAAqB,EAArB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKT,iBAAL,GAAyB,KAAzB;AACA,eAAKM,oBAAL,GAA4B,KAA5B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKF,eAAL,GAAuB,CAAvB;;AACA,eAAKC,cAAL,CAAoBY,MAApB,CAA2B,CAA3B;;AACA,eAAKd,eAAL,GAAuB,IAAvB;AACA,eAAKI,QAAL,GAAgB,EAAhB,CAXU,CAYV;AACA;AACA;;AACA,eAAKI,MAAL,GAAc,EAAd;AACA,eAAKI,iBAAL,GAAyB,KAAzB;AACA,eAAKF,eAAL,GAAuB,CAAvB;AACH;;AAEDK,QAAAA,eAAe,CAACC,OAAD,EAA6B;AACxC,eAAKnB,aAAL,GAAqBmB,OAArB;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd,eAAK3B,iBAAL,GAAyB,IAAzB;AACA,eAAKM,oBAAL,GAA4B,IAA5B;AACA,eAAKQ,QAAL,GAAgB,EAAhB,CAHc,CAId;AACA;AACA;AAEA;AACA;AACH;;AAEDc,QAAAA,eAAe,CAACC,OAAD,EAA2C;AACtD,iBAAO,KAAK1B,gBAAL,CAAsB2B,GAAtB,CAA0BD,OAA1B,CAAP;AACH;;AAEDE,QAAAA,cAAc,CAACC,IAAD,EAAaC,IAAb,EAA2BC,IAA3B,EAA8C;AACxDF,UAAAA,IAAI,CAACG,OAAL;;AACA,eAAKjB,MAAL,CAAYkB,IAAZ,CAAiBJ,IAAjB;;AACAK,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4BN,IAAK,EAA9C,EAHwD,CAKxD;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEoB,cAAfO,eAAe,CAACC,SAAD,EAAoB;AACrC,eAAKC,gBAAL,CAAsBD,SAAtB;;AACA,eAAKE,YAAL,CAAkBF,SAAlB;;AACA,eAAKG,WAAL,CAAiBH,SAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,gBAAgB,CAACD,SAAD,EAA0B;AAC9C,cAAI,CAAC,KAAKnC,UAAV,EAAsB;AAAA;;AAClB,gBAAI,KAAKG,iBAAL,KAA2B,6BAAKD,aAAL,yCAAoBqC,MAApB,KAA8B,CAAzD,CAAJ,EAAiE;AAC7D,mBAAKvC,UAAL,GAAkB,IAAlB;AACAV,cAAAA,IAAI,CAAC,YAAD,CAAJ;AACH,aAHD,MAGO,IAAI,KAAKI,eAAL,IAAwB,CAAC,KAAKW,eAAlC,EAAmD;AACtD,oBAAMmC,MAAM,GAAG,KAAKtC,aAAL,CAAoB,KAAKC,iBAAzB,CAAf;;AACA,sBAAQqC,MAAM,CAACC,IAAf;AACI,qBAAK,CAAL;AACI,uBAAKrC,gBAAL,IAAyB+B,SAAzB;;AACA,sBACI,KAAK/B,gBAAL,IAAyBoC,MAAM,CAACE,gBAAhC,IACC,KAAKjC,QAAL,CAAc8B,MAAd,KAAyB,CAAzB,IAA8B;AAAA;AAAA,0CAAQI,YAAR,CAAqBC,mBAArB,OAA+C,CAFlF,EAGE;AACE,yBAAKvC,eAAL,GAAuBmC,MAAvB;AACH;;AACD;;AACJ;AACI,sBAAIA,MAAM,CAACC,IAAP,IAAe,GAAnB,EAAwB;AACpBT,oBAAAA,OAAO,CAAC1C,IAAR,CAAa,YAAb,EAA2BkD,MAAM,CAACC,IAAlC,EAAwCD,MAAM,CAACK,WAAP,CAAmB,CAAnB,CAAxC;AACA,yBAAK/B,gBAAL,GAAwB0B,MAAM,CAACE,gBAA/B;;AACA,yBAAK1B,aAAL,CAAmBe,IAAnB,CAAwBS,MAAxB;;AACA,yBAAKrC,iBAAL;AACH;;AAhBT;AAkBH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AAC8B,cAAZkC,YAAY,CAACF,SAAD,EAAoB;AAC1C;AACA,eAAKW,YAAL,CAAkBX,SAAlB;;AAEA,cAAI,KAAK9B,eAAT,EAA0B;AACtB,gBAAI,CAAC,KAAK0C,gBAAL,CAAsBZ,SAAtB,CAAL,EAAuC;AACnC,mBAAK9B,eAAL,GAAuB,IAAvB;AACA,mBAAKF,iBAAL;AACA,mBAAKC,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ;;AAEO0C,QAAAA,YAAY,CAACX,SAAD,EAAoB;AACpC,gBAAMa,eAAe,GAAGb,SAAS,GAAG,IAApC;;AACA,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpC,MAAL,CAAY0B,MAAhC,EAAwCU,CAAC,EAAzC,EAA6C;AACzC,kBAAMtB,IAAI,GAAG,KAAKd,MAAL,CAAYoC,CAAZ,CAAb;AACAtB,YAAAA,IAAI,CAACuB,IAAL,CAAUF,eAAV;;AACA,gBAAIrB,IAAI,CAACwB,WAAT,EAAsB;AAClB,mBAAKtC,MAAL,CAAYM,MAAZ,CAAmB8B,CAAnB,EAAsB,CAAtB;;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;;;AACYF,QAAAA,gBAAgB,CAACZ,SAAD,EAA6B;AACjD,cAAI,KAAKlC,oBAAT,EAA+B;AAC3B,gBAAI,KAAKO,UAAL,IAAmB,KAAKH,eAAL,CAAsBwC,WAAtB,CAAkCN,MAAzD,EAAiE;AAC7D,mBAAK/B,UAAL,GAAkB,CAAlB;AACA,qBAAO,KAAP;AACH;;AAED,kBAAM4C,MAAM,GAAG,KAAK/C,eAAL,CAAsBwC,WAAtB,CAAkC,KAAKrC,UAAvC,CAAf;AACA,iBAAKF,eAAL,IAAwB6B,SAAxB;AAEA,kBAAMkB,SAAS,GAAG,KAAK9B,eAAL,CAAqB6B,MAArB,CAAlB;;AACA,gBAAI,CAACC,SAAL,EAAgB;AACZ,qBAAO,KAAP;AACH;;AACDrB,YAAAA,OAAO,CAACC,GAAR,CAAa,eAAcmB,MAAO,qBAAoBC,SAAS,CAACd,MAAO,EAAvE;;AACA,iBAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGI,SAAS,CAAEd,MAA/B,EAAuCU,CAAC,EAAxC,EAA4C;AACxC,oBAAMtB,IAAI,GAAG0B,SAAS,CAAEJ,CAAF,CAAtB;;AACA,kBACI,CAAC;AAAA;AAAA,kCAAMK,UAAN,CAAiB,KAAK/C,cAAtB,EAAsC0C,CAAtC,CAAD,IACA,KAAK3C,eAAL,IAAwBqB,IAAI,CAAC4B,aAFjC,EAGE;AACE,qBAAK9C,QAAL,CAAcsB,IAAd,CAAmBJ,IAAnB;;AACA,qBAAKjB,WAAL,CAAiBqB,IAAjB,CAAsB,CAAtB;;AACA,qBAAKpB,YAAL,CAAkBoB,IAAlB,CAAuB,CAAvB;;AACA,qBAAKnB,cAAL,CAAoBmB,IAApB,CAAyB,KAAK1B,eAA9B;;AACA,qBAAKE,cAAL,CAAoBwB,IAApB,CAAyBkB,CAAzB;AACH;AACJ;;AAED,gBAAI,KAAK1C,cAAL,CAAoBgC,MAApB,IAA8Bc,SAAS,CAAEd,MAA7C,EAAqD;AACjD,mBAAKhC,cAAL,CAAoBY,MAApB,CAA2B,CAA3B;;AACA,mBAAKb,eAAL,GAAuB,CAAvB;AACA,mBAAKE,UAAL;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACY8B,QAAAA,WAAW,CAACH,SAAD,EAA0B;AACzC,cAAI,KAAKnB,aAAL,CAAmBuB,MAAnB,GAA4B,CAA5B,IAAiC;AAAA;AAAA,kCAAQI,YAAR,CAAqBa,WAArB,EAArC,EAAwE;AACpE,iBAAKzC,eAAL,IAAwBoB,SAAxB;;AACA,gBAAI,KAAKpB,eAAL,GAAuB,KAAKD,gBAAhC,EAAkD;AAC9C,oBAAM2C,QAAQ,GAAG,KAAKzC,aAAL,CAAmB,CAAnB,CAAjB;AACA,oBAAM0C,IAAI,GAAG;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,OAApB,CAA4BH,QAAQ,CAACZ,WAAT,CAAqB,CAArB,CAA5B,CAAb;;AACA,mBAAK7B,aAAL,CAAmBG,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIqC,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKxD,UAAL,IAAmB,KAAKS,QAAL,CAAc8B,MAAd,KAAyB,CAA5C,IAAiD,KAAKvB,aAAL,CAAmBuB,MAAnB,KAA8B,CAAtF;AACH;;AAzS+D,O", "sourcesContent": ["import {warn } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyWave } from \"../data/EnemyWave\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { StageData } from \"../data/StageData\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n\r\nexport default class WaveManager extends SingletonBase<WaveManager> {\r\n    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();// 波次配表数据\r\n    private _enemyOver: boolean = false;//是否加载所有敌机\r\n\r\n    private _bEnemyCreateAble: boolean = false;//是否可以创建敌机\r\n    private _bEnemyNorCreateAble: boolean = false;//是否可以创建普通敌机\r\n\r\n    private _enemyActions: StageData[] | null = null;//小阶段所有数据列表\r\n    private _enemyActionIndex: number = 0;//当前小阶段索引\r\n    private _enemyCreateTime: number = 0;//当前小阶段时间\r\n    private _curEnemyAction: StageData | null = null;//当前波次数据\r\n    private _waveCreateTime: number = 0;\r\n    private _waveIndexOver: number[] = [];\r\n\r\n    private _waveIndex: number = 0;//当前波次的索引\r\n    private _waveArr: EnemyWave[] = [];//当前波次的所有敌机数据\r\n    private _waveNumArr: number[] = [];//当前波次已创建的敌机数量\r\n    private _waveTimeArr: number[] = [];//当前波次计时\r\n    private _waveActionArr: any[] = [];\r\n    private _waves: Wave[] = [];\r\n\r\n    //boss\r\n    private _bossCreateDelay: number = 0;\r\n    private _bossCreateTime: number = 0;\r\n    private _bossToAddArr: any[] = [];\r\n    private _bShowBossWarning: boolean = false;\r\n\r\n\r\n    get enemyCreateAble(): boolean {\r\n        return this._bEnemyCreateAble;\r\n    }\r\n\r\n    set enemyCreateAble(value: boolean) {\r\n        this._bEnemyCreateAble = value;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        // this.initConfig();\r\n    }\r\n\r\n    // initConfig() {\r\n    //     let waveDatas = MyApp.lubanTables.TbWave.getDataList();\r\n    //     for (let waveData of waveDatas) {\r\n    //         const wave = new EnemyWave();\r\n    //         wave.loadJson(waveData);\r\n    //         const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];\r\n    //         group.push(wave);\r\n    //         this._waveNorDatasMap.set(wave.enemyGroupID, group);\r\n    //     }\r\n    // }\r\n\r\n    reset(): void {\r\n        this._enemyOver = false;\r\n        this._enemyActions = [];\r\n        this._enemyActionIndex = 0;\r\n        this._enemyCreateTime = 0;\r\n        this._bEnemyCreateAble = false;\r\n        this._bEnemyNorCreateAble = false;\r\n        this._waveIndex = 0;\r\n        this._waveCreateTime = 0;\r\n        this._waveIndexOver.splice(0);\r\n        this._curEnemyAction = null;\r\n        this._waveArr = [];\r\n        // this._waveActionArr = [];\r\n        // this._waveNumArr = [];\r\n        // this._waveTimeArr = [];\r\n        this._waves = [];\r\n        this._bShowBossWarning = false;\r\n        this._bossCreateTime = 0;\r\n    }\r\n\r\n    setEnemyActions(actions: StageData[]): void {\r\n        this._enemyActions = actions;\r\n    }\r\n\r\n    gameStart(): void {\r\n        this._bEnemyCreateAble = true;\r\n        this._bEnemyNorCreateAble = true;\r\n        this._waveArr = [];\r\n        // this._waveActionArr = [];\r\n        // this._waveNumArr = [];\r\n        // this._waveTimeArr = [];\r\n\r\n        // 这里不能清掉，清掉后存在问题: 时序上是先addWaveByLevel，后gameStart\r\n        // this._waves = [];\r\n    }\r\n\r\n    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {\r\n        return this._waveNorDatasMap.get(groupID);\r\n    }\r\n    \r\n    addWaveByLevel(wave: Wave, posX: number, posY: number):void {\r\n        wave.trigger();\r\n        this._waves.push(wave);\r\n        console.log(`ybgg addWaveByLevel wave: ${wave}`)\r\n\r\n        // const enemyWave = EnemyWave.fromLevelWave(wave, posX, posY);\r\n        // this._waveArr.push(enemyWave)\r\n        // this._waveNumArr.push(0)\r\n        // this._waveTimeArr.push(0)\r\n        // this._waveActionArr.push(this._curEnemyAction!)\r\n        \r\n        // const group = this._waveNorDatasMap.get(enemyWave.enemyGroupID)\r\n        // if (group == null) {\r\n        //     this._waveNorDatasMap.set(enemyWave.enemyGroupID, [enemyWave]);\r\n        // } else {\r\n        //     group.push(enemyWave);\r\n        // }\r\n    }\r\n\r\n    async updateGameLogic(deltaTime: number) {\r\n        this._updateCurAction(deltaTime);\r\n        this._updateEnemy(deltaTime);\r\n        this._updateBoss(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 更新当前敌人行为\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateCurAction(deltaTime: number): void {\r\n        if (!this._enemyOver) {\r\n            if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {\r\n                this._enemyOver = true;\r\n                warn(\"enemy over\");\r\n            } else if (this.enemyCreateAble && !this._curEnemyAction) {\r\n                const action = this._enemyActions![this._enemyActionIndex];\r\n                switch (action.type) {\r\n                    case 0:\r\n                        this._enemyCreateTime += deltaTime;\r\n                        if (\r\n                            this._enemyCreateTime >= action.enemyNorInterval ||\r\n                            (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)\r\n                        ) {\r\n                            this._curEnemyAction = action;\r\n                        }\r\n                        break;\r\n                    default:\r\n                        if (action.type >= 100) {\r\n                            console.warn(\"Boss stage\", action.type, action.enemyNorIDs[0]);\r\n                            this._bossCreateDelay = action.enemyNorInterval;\r\n                            this._bossToAddArr.push(action);\r\n                            this._enemyActionIndex++;\r\n                        }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemy(deltaTime: number) {\r\n        // await this._updateEnemyCreate(deltaTime);\r\n        this._updateWaves(deltaTime);\r\n\r\n        if (this._curEnemyAction) {\r\n            if (!this._updateNorEnemys(deltaTime)) {\r\n                this._curEnemyAction = null;\r\n                this._enemyActionIndex++;\r\n                this._enemyCreateTime = 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _updateWaves(deltaTime: number) {\r\n        const dtInMiliseconds = deltaTime * 1000;\r\n        for (let i = 0; i < this._waves.length; i++) {\r\n            const wave = this._waves[i];\r\n            wave.tick(dtInMiliseconds);\r\n            if (wave.isCompleted) {\r\n                this._waves.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    // private async _updateEnemyCreate(deltaTime: number): Promise<void> {\r\n    //     for (let i = 0; i < this._waveArr.length; i++) {\r\n    //         const wave = this._waveArr[i];\r\n    //         this._waveTimeArr[i] += deltaTime;\r\n    //         const currentEnemyCount = this._waveNumArr[i];\r\n    //         let posX = GameConst.EnemyPos.x;\r\n    //         let posY = GameConst.EnemyPos.y;\r\n\r\n    //         if (wave.bSetStartPos) {\r\n    //             posX += wave.startPosX;\r\n    //             posY += wave.startPosY;\r\n    //         }\r\n\r\n    //         for (let j = currentEnemyCount; j < wave.enemyNum; j++) {\r\n    //             if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {\r\n    //                 this._waveNumArr[i]++;\r\n    //                 let enemy:EnemyPlane|null;\r\n    //                 const enemyPosX = posX + wave.posDX * (j + 1);\r\n    //                 const enemyPosY = posY + wave.posDY * (j + 1);\r\n\r\n    //                 switch (wave.type) {\r\n    //                     case 0:\r\n    //                         enemy = await GameIns.enemyManager.addPlane(wave.enemyID);\r\n    //                         if (enemy) {\r\n    //                             if (j < wave.firstShootDelay.length) {\r\n    //                                 enemy.setFirstShootDelay(wave.firstShootDelay[j]);\r\n    //                             }\r\n    //                             enemy.setStandByTime(0);\r\n    //                             enemy.initTrack(\r\n    //                                 wave.trackGroups,\r\n    //                                 wave.liveParam,\r\n    //                                 enemyPosX,\r\n    //                                 enemyPosY,\r\n    //                                 wave.rotateSpeed\r\n    //                             );\r\n    //                         }\r\n    //                         break;\r\n    //                 }\r\n    //             }\r\n    //         }\r\n\r\n    //         if (wave.enemyNum <= this._waveNumArr[i]) {\r\n    //             this._waveArr.splice(i, 1);\r\n    //             this._waveNumArr.splice(i, 1);\r\n    //             this._waveTimeArr.splice(i, 1);\r\n    //             this._waveActionArr.splice(i, 1);\r\n    //             i--;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    /**\r\n     * 更新普通敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateNorEnemys(deltaTime: number): boolean {\r\n        if (this._bEnemyNorCreateAble) {\r\n            if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {\r\n                this._waveIndex = 0;\r\n                return false;\r\n            }\r\n\r\n            const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];\r\n            this._waveCreateTime += deltaTime;\r\n\r\n            const waveDatas = this.getNorWaveDatas(waveID);\r\n            if (!waveDatas) {\r\n                return false;\r\n            }\r\n            console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);\r\n            for (let i = 0; i < waveDatas!.length; i++) {\r\n                const wave = waveDatas![i];\r\n                if (\r\n                    !Tools.arrContain(this._waveIndexOver, i) &&\r\n                    this._waveCreateTime >= wave.groupInterval\r\n                ) {\r\n                    this._waveArr.push(wave);\r\n                    this._waveNumArr.push(0);\r\n                    this._waveTimeArr.push(0);\r\n                    this._waveActionArr.push(this._curEnemyAction);\r\n                    this._waveIndexOver.push(i);\r\n                }\r\n            }\r\n\r\n            if (this._waveIndexOver.length >= waveDatas!.length) {\r\n                this._waveIndexOver.splice(0);\r\n                this._waveCreateTime = 0;\r\n                this._waveIndex++;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 更新 Boss 生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateBoss(deltaTime: number): void {\r\n        if (this._bossToAddArr.length > 0 && GameIns.enemyManager.isEnemyOver()){\r\n            this._bossCreateTime += deltaTime;\r\n            if (this._bossCreateTime > this._bossCreateDelay) {\r\n                const bossData = this._bossToAddArr[0];\r\n                const boss = GameIns.bossManager.addBoss(bossData.enemyNorIDs[0]);\r\n                this._bossToAddArr.splice(0, 1);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 检查敌人是否全部结束\r\n     * @returns 是否所有敌人都已结束\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;\r\n    }\r\n}"]}