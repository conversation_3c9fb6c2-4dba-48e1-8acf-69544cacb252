System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _crd, ccclass, property, eEmitterCondition, eBulletCondition, eEmitterConditionCn, eBulletConditionCn;

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eEmitterCondition", eEmitterCondition = /*#__PURE__*/function (eEmitterCondition) {
        eEmitterCondition[eEmitterCondition["Emitter_Active"] = 1] = "Emitter_Active";
        eEmitterCondition[eEmitterCondition["Emitter_InitialDelay"] = 2] = "Emitter_InitialDelay";
        eEmitterCondition[eEmitterCondition["Emitter_Prewarm"] = 3] = "Emitter_Prewarm";
        eEmitterCondition[eEmitterCondition["Emitter_PrewarmDuration"] = 4] = "Emitter_PrewarmDuration";
        eEmitterCondition[eEmitterCondition["Emitter_Duration"] = 5] = "Emitter_Duration";
        eEmitterCondition[eEmitterCondition["Emitter_ElapsedTime"] = 6] = "Emitter_ElapsedTime";
        eEmitterCondition[eEmitterCondition["Emitter_Loop"] = 7] = "Emitter_Loop";
        eEmitterCondition[eEmitterCondition["Emitter_LoopInterval"] = 8] = "Emitter_LoopInterval";
        eEmitterCondition[eEmitterCondition["Emitter_EmitInterval"] = 9] = "Emitter_EmitInterval";
        eEmitterCondition[eEmitterCondition["Emitter_PerEmitCount"] = 10] = "Emitter_PerEmitCount";
        eEmitterCondition[eEmitterCondition["Emitter_PerEmitInterval"] = 11] = "Emitter_PerEmitInterval";
        eEmitterCondition[eEmitterCondition["Emitter_PerEmitOffsetX"] = 12] = "Emitter_PerEmitOffsetX";
        eEmitterCondition[eEmitterCondition["Emitter_Angle"] = 13] = "Emitter_Angle";
        eEmitterCondition[eEmitterCondition["Emitter_Count"] = 14] = "Emitter_Count";
        eEmitterCondition[eEmitterCondition["Bullet_Duration"] = 15] = "Bullet_Duration";
        eEmitterCondition[eEmitterCondition["Bullet_Speed"] = 16] = "Bullet_Speed";
        eEmitterCondition[eEmitterCondition["Bullet_Acceleration"] = 17] = "Bullet_Acceleration";
        eEmitterCondition[eEmitterCondition["Bullet_AccelerationAngle"] = 18] = "Bullet_AccelerationAngle";
        eEmitterCondition[eEmitterCondition["Bullet_FacingMoveDir"] = 19] = "Bullet_FacingMoveDir";
        eEmitterCondition[eEmitterCondition["Bullet_TrackingTarget"] = 20] = "Bullet_TrackingTarget";
        eEmitterCondition[eEmitterCondition["Bullet_Destructive"] = 21] = "Bullet_Destructive";
        eEmitterCondition[eEmitterCondition["Bullet_DestructiveOnHit"] = 22] = "Bullet_DestructiveOnHit";
        eEmitterCondition[eEmitterCondition["Bullet_Sprite"] = 23] = "Bullet_Sprite";
        eEmitterCondition[eEmitterCondition["Bullet_Scale"] = 24] = "Bullet_Scale";
        eEmitterCondition[eEmitterCondition["Bullet_ColorR"] = 25] = "Bullet_ColorR";
        eEmitterCondition[eEmitterCondition["Bullet_ColorG"] = 26] = "Bullet_ColorG";
        eEmitterCondition[eEmitterCondition["Bullet_ColorB"] = 27] = "Bullet_ColorB";
        eEmitterCondition[eEmitterCondition["Bullet_DefaultFacing"] = 28] = "Bullet_DefaultFacing";
        eEmitterCondition[eEmitterCondition["Level_Duration"] = 29] = "Level_Duration";
        eEmitterCondition[eEmitterCondition["Level_Distance"] = 30] = "Level_Distance";
        eEmitterCondition[eEmitterCondition["Level_InfLevel"] = 31] = "Level_InfLevel";
        eEmitterCondition[eEmitterCondition["Level_ChallengeLevel"] = 32] = "Level_ChallengeLevel";
        eEmitterCondition[eEmitterCondition["Player_ActLevel"] = 33] = "Player_ActLevel";
        eEmitterCondition[eEmitterCondition["Player_PosX"] = 34] = "Player_PosX";
        eEmitterCondition[eEmitterCondition["Player_PosY"] = 35] = "Player_PosY";
        eEmitterCondition[eEmitterCondition["Player_LifePercent"] = 36] = "Player_LifePercent";
        eEmitterCondition[eEmitterCondition["Player_GainBuff"] = 37] = "Player_GainBuff";
        eEmitterCondition[eEmitterCondition["Unit_Life"] = 38] = "Unit_Life";
        eEmitterCondition[eEmitterCondition["Unit_LifePercent"] = 39] = "Unit_LifePercent";
        eEmitterCondition[eEmitterCondition["Unit_ElapsedTime"] = 40] = "Unit_ElapsedTime";
        eEmitterCondition[eEmitterCondition["Unit_PosX"] = 41] = "Unit_PosX";
        eEmitterCondition[eEmitterCondition["Unit_PosY"] = 42] = "Unit_PosY";
        eEmitterCondition[eEmitterCondition["Unit_Speed"] = 43] = "Unit_Speed";
        eEmitterCondition[eEmitterCondition["Unit_SpeedAngle"] = 44] = "Unit_SpeedAngle";
        eEmitterCondition[eEmitterCondition["Unit_Acceleration"] = 45] = "Unit_Acceleration";
        eEmitterCondition[eEmitterCondition["Unit_AccelerationAngle"] = 46] = "Unit_AccelerationAngle";
        eEmitterCondition[eEmitterCondition["Unit_DistanceToPlayer"] = 47] = "Unit_DistanceToPlayer";
        eEmitterCondition[eEmitterCondition["Unit_AngleToPlayer"] = 48] = "Unit_AngleToPlayer";
        return eEmitterCondition;
      }({}));

      _export("eBulletCondition", eBulletCondition = /*#__PURE__*/function (eBulletCondition) {
        eBulletCondition[eBulletCondition["Bullet_Duration"] = 100] = "Bullet_Duration";
        eBulletCondition[eBulletCondition["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
        eBulletCondition[eBulletCondition["Bullet_PosX"] = 102] = "Bullet_PosX";
        eBulletCondition[eBulletCondition["Bullet_PosY"] = 103] = "Bullet_PosY";
        eBulletCondition[eBulletCondition["Bullet_Damage"] = 104] = "Bullet_Damage";
        eBulletCondition[eBulletCondition["Bullet_Speed"] = 105] = "Bullet_Speed";
        eBulletCondition[eBulletCondition["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
        eBulletCondition[eBulletCondition["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
        eBulletCondition[eBulletCondition["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
        eBulletCondition[eBulletCondition["Bullet_Scale"] = 109] = "Bullet_Scale";
        eBulletCondition[eBulletCondition["Bullet_ColorR"] = 110] = "Bullet_ColorR";
        eBulletCondition[eBulletCondition["Bullet_ColorG"] = 111] = "Bullet_ColorG";
        eBulletCondition[eBulletCondition["Bullet_ColorB"] = 112] = "Bullet_ColorB";
        eBulletCondition[eBulletCondition["Bullet_FacingMoveDir"] = 113] = "Bullet_FacingMoveDir";
        eBulletCondition[eBulletCondition["Bullet_Destructive"] = 114] = "Bullet_Destructive";
        eBulletCondition[eBulletCondition["Bullet_DestructiveOnHit"] = 115] = "Bullet_DestructiveOnHit";
        return eBulletCondition;
      }({}));

      // 以下枚举值用于编辑器显示，实际运行时不会用到
      _export("eEmitterConditionCn", eEmitterConditionCn = /*#__PURE__*/function (eEmitterConditionCn) {
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u662F\u5426\u542F\u7528"] = 1] = "\u53D1\u5C04\u5668\u662F\u5426\u542F\u7528";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5F53\u524D\u7684\u521D\u59CB\u5EF6\u8FDF"] = 2] = "\u53D1\u5C04\u5668\u5F53\u524D\u7684\u521D\u59CB\u5EF6\u8FDF";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u662F\u5426\u9884\u70ED"] = 3] = "\u53D1\u5C04\u5668\u662F\u5426\u9884\u70ED";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u9884\u70ED\u7684\u6301\u7EED\u65F6\u95F4"] = 4] = "\u53D1\u5C04\u5668\u9884\u70ED\u7684\u6301\u7EED\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u7684\u6301\u7EED\u65F6\u95F4"] = 5] = "\u53D1\u5C04\u5668\u7684\u6301\u7EED\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5DF2\u8FD0\u884C\u7684\u65F6\u95F4"] = 6] = "\u53D1\u5C04\u5668\u5DF2\u8FD0\u884C\u7684\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u662F\u5426\u5FAA\u73AF"] = 7] = "\u53D1\u5C04\u5668\u662F\u5426\u5FAA\u73AF";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5FAA\u73AF\u7684\u95F4\u9694\u65F6\u95F4"] = 8] = "\u53D1\u5C04\u5668\u5FAA\u73AF\u7684\u95F4\u9694\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5F00\u706B\u95F4\u9694"] = 9] = "\u53D1\u5C04\u5668\u5F00\u706B\u95F4\u9694";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u6B21\u6570"] = 10] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u6B21\u6570";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u95F4\u9694"] = 11] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u95F4\u9694";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u504F\u79FB"] = 12] = "\u53D1\u5C04\u5668\u5355\u6B21\u5F00\u706B\u504F\u79FB";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5F39\u9053\u89D2\u5EA6"] = 13] = "\u53D1\u5C04\u5668\u5F39\u9053\u89D2\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u53D1\u5C04\u5668\u5F39\u9053\u6570\u91CF"] = 14] = "\u53D1\u5C04\u5668\u5F39\u9053\u6570\u91CF";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u6301\u7EED\u65F6\u95F4"] = 15] = "\u5B50\u5F39\u6301\u7EED\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u901F\u5EA6"] = 16] = "\u5B50\u5F39\u901F\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u52A0\u901F\u5EA6"] = 17] = "\u5B50\u5F39\u52A0\u901F\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 18] = "\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411"] = 19] = "\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807"] = 20] = "\u5B50\u5F39\u8FFD\u8E2A\u76EE\u6807";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u53EF\u7834\u574F"] = 21] = "\u5B50\u5F39\u53EF\u7834\u574F";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F"] = 22] = "\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u5916\u89C2\u56FE\u7247"] = 23] = "\u5B50\u5F39\u5916\u89C2\u56FE\u7247";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u7F29\u653E"] = 24] = "\u5B50\u5F39\u7F29\u653E";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u989C\u8272R"] = 25] = "\u5B50\u5F39\u989C\u8272R";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u989C\u8272G"] = 26] = "\u5B50\u5F39\u989C\u8272G";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u989C\u8272B"] = 27] = "\u5B50\u5F39\u989C\u8272B";
        eEmitterConditionCn[eEmitterConditionCn["\u5B50\u5F39\u5916\u89C2\u521D\u59CB\u671D\u5411"] = 28] = "\u5B50\u5F39\u5916\u89C2\u521D\u59CB\u671D\u5411";
        eEmitterConditionCn[eEmitterConditionCn["\u5173\u5361\u5DF2\u6301\u7EED\u65F6\u95F4"] = 29] = "\u5173\u5361\u5DF2\u6301\u7EED\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u5173\u5361\u5DF2\u98DE\u884C\u8DDD\u79BB"] = 30] = "\u5173\u5361\u5DF2\u98DE\u884C\u8DDD\u79BB";
        eEmitterConditionCn[eEmitterConditionCn["\u65E0\u5C3D\u6A21\u5F0F\u5F53\u524D\u7B49\u7EA7"] = 31] = "\u65E0\u5C3D\u6A21\u5F0F\u5F53\u524D\u7B49\u7EA7";
        eEmitterConditionCn[eEmitterConditionCn["\u95EF\u5173\u6A21\u5F0F\u5F53\u524D\u7B49\u7EA7"] = 32] = "\u95EF\u5173\u6A21\u5F0F\u5F53\u524D\u7B49\u7EA7";
        eEmitterConditionCn[eEmitterConditionCn["\u73A9\u5BB6\u8D26\u53F7\u7B49\u7EA7"] = 33] = "\u73A9\u5BB6\u8D26\u53F7\u7B49\u7EA7";
        eEmitterConditionCn[eEmitterConditionCn["\u73A9\u5BB6\u5F53\u524D\u5750\u6807X"] = 34] = "\u73A9\u5BB6\u5F53\u524D\u5750\u6807X";
        eEmitterConditionCn[eEmitterConditionCn["\u73A9\u5BB6\u5F53\u524D\u5750\u6807Y"] = 35] = "\u73A9\u5BB6\u5F53\u524D\u5750\u6807Y";
        eEmitterConditionCn[eEmitterConditionCn["\u73A9\u5BB6\u5F53\u524D\u751F\u547D\u767E\u5206\u6BD4"] = 36] = "\u73A9\u5BB6\u5F53\u524D\u751F\u547D\u767E\u5206\u6BD4";
        eEmitterConditionCn[eEmitterConditionCn["\u73A9\u5BB6\u83B7\u5F97buff"] = 37] = "\u73A9\u5BB6\u83B7\u5F97buff";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u751F\u547D\u503C"] = 38] = "\u5355\u4F4D\u5F53\u524D\u751F\u547D\u503C";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u751F\u547D\u767E\u5206\u6BD4"] = 39] = "\u5355\u4F4D\u5F53\u524D\u751F\u547D\u767E\u5206\u6BD4";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u6301\u7EED\u65F6\u95F4"] = 40] = "\u5355\u4F4D\u5F53\u524D\u6301\u7EED\u65F6\u95F4";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u5750\u6807X"] = 41] = "\u5355\u4F4D\u5F53\u524D\u5750\u6807X";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u5750\u6807Y"] = 42] = "\u5355\u4F4D\u5F53\u524D\u5750\u6807Y";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u901F\u5EA6"] = 43] = "\u5355\u4F4D\u5F53\u524D\u901F\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u901F\u5EA6\u89D2\u5EA6"] = 44] = "\u5355\u4F4D\u5F53\u524D\u901F\u5EA6\u89D2\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u52A0\u901F\u5EA6"] = 45] = "\u5355\u4F4D\u5F53\u524D\u52A0\u901F\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u5F53\u524D\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 46] = "\u5355\u4F4D\u5F53\u524D\u52A0\u901F\u5EA6\u89D2\u5EA6";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u4E0E\u73A9\u5BB6\u7684\u8DDD\u79BB"] = 47] = "\u5355\u4F4D\u4E0E\u73A9\u5BB6\u7684\u8DDD\u79BB";
        eEmitterConditionCn[eEmitterConditionCn["\u5355\u4F4D\u4E0E\u73A9\u5BB6\u7684\u89D2\u5EA6"] = 48] = "\u5355\u4F4D\u4E0E\u73A9\u5BB6\u7684\u89D2\u5EA6";
        return eEmitterConditionCn;
      }({}));

      _export("eBulletConditionCn", eBulletConditionCn = /*#__PURE__*/function (eBulletConditionCn) {
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u6301\u7EED\u65F6\u95F4"] = 100] = "\u5B50\u5F39\u6301\u7EED\u65F6\u95F4";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u5DF2\u8FD0\u884C\u65F6\u95F4"] = 101] = "\u5B50\u5F39\u5DF2\u8FD0\u884C\u65F6\u95F4";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u5750\u6807X"] = 102] = "\u5B50\u5F39\u5750\u6807X";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u5750\u6807Y"] = 103] = "\u5B50\u5F39\u5750\u6807Y";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u4F24\u5BB3"] = 104] = "\u5B50\u5F39\u4F24\u5BB3";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u901F\u5EA6"] = 105] = "\u5B50\u5F39\u901F\u5EA6";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6"] = 106] = "\u5B50\u5F39\u901F\u5EA6\u89D2\u5EA6";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u52A0\u901F\u5EA6"] = 107] = "\u5B50\u5F39\u52A0\u901F\u5EA6";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6"] = 108] = "\u5B50\u5F39\u52A0\u901F\u5EA6\u89D2\u5EA6";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u7F29\u653E"] = 109] = "\u5B50\u5F39\u7F29\u653E";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u989C\u8272R"] = 110] = "\u5B50\u5F39\u989C\u8272R";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u989C\u8272G"] = 111] = "\u5B50\u5F39\u989C\u8272G";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u989C\u8272B"] = 112] = "\u5B50\u5F39\u989C\u8272B";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411"] = 113] = "\u5B50\u5F39\u9762\u5411\u79FB\u52A8\u65B9\u5411";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u53EF\u7834\u574F"] = 114] = "\u5B50\u5F39\u53EF\u7834\u574F";
        eBulletConditionCn[eBulletConditionCn["\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F"] = 115] = "\u5B50\u5F39\u547D\u4E2D\u65F6\u7834\u574F";
        return eBulletConditionCn;
      }({}));

      _crd = false;
    }
  };
});
//# sourceMappingURL=fea54e41ed0b0ea1d57c1baf1d8548050f29ff22.js.map