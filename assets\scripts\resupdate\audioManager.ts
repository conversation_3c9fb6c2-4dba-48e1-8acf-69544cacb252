import {_decorator, AudioClip, AudioSource,Component, assert, warn, clamp01, resources } from "cc";
const { ccclass, property } = _decorator;
import { IMgr } from '../IMgr';
import { MyApp } from 'db://assets/scripts/MyApp';

export class audioManager extends IMgr {

    private static _instance: audioManager;

    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new audioManager();
        return this._instance;
    }

    private _audioSource: AudioSource|null = null;
    private _soundSourcePool: AudioSource[] = [];

    // 音乐音量
    private _musicVolume: number = 1;
    // 音效音量(一般两者会区分开)
    private _soundVolume: number = 1;

    /**管理器初始化*/
    init () {
        var audioSource = MyApp.GetInstance().node.getComponent(AudioSource);
        this._audioSource = audioSource;
        this._musicVolume = this._audioSource?.volume || 1;
    }

      /**
     * 播放音乐
     * @param {Boolean} loop 是否循环播放
     */
    playMusic (loop: boolean) {
        const audioSource = this._audioSource!;
        if (!audioSource) {
            return;
        }

        audioSource.loop = loop;
        if (!audioSource.playing) {
            audioSource.play();
        }
    }

     /**
     * 播放音效
     * @param {audioClip} audioClip 音效名称
     * @param {Number} volumeScale 播放音量倍数
     */
    playSound (audioClip: AudioClip, volumeScale: number = 1 ) {
        if (!this._audioSource) {
            return;
        }

        // 注意：第二个参数 “volumeScale” 是指播放音量的倍数，最终播放的音量为 “audioSource.volume * volumeScale”
        this._audioSource.playOneShot(audioClip, volumeScale);
    }

    // 设置音乐音量
    public setMusicVolume(flag: number) {
        const audioSource = this._audioSource!;
        if (!audioSource) {
            return;
        }

        this._musicVolume = clamp01(flag);
        audioSource.volume = this._musicVolume;
    }

    public getMusicVolume(): number {
        return this._musicVolume;
    }

    // 设置音效音量
    public setSoundVolume(flag: number) {
        this._soundVolume = clamp01(flag);
    }

    public getSoundVolume(): number {
        return this._soundVolume;
    }
}