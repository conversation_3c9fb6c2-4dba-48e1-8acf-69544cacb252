System.register(["__unresolved_0", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14"], function (_export, _context) {
  "use strict";

  var _reporterNs, SingletonBase, GameDataManager, MainPlaneManager, BattleManager, BossManager, EnemyManager, GameRuleManager, HurtEffectManager, GamePlaneManager, StageManager, GameResManager, SceneManager, WaveManager, FColliderManager, _GameIns, _crd, GameIns;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameDataManager(extras) {
    _reporterNs.report("GameDataManager", "./manager/GameDataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneManager(extras) {
    _reporterNs.report("MainPlaneManager", "./manager/MainPlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleManager(extras) {
    _reporterNs.report("BattleManager", "./manager/BattleManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossManager(extras) {
    _reporterNs.report("BossManager", "./manager/BossManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyManager(extras) {
    _reporterNs.report("EnemyManager", "./manager/EnemyManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameRuleManager(extras) {
    _reporterNs.report("GameRuleManager", "./manager/GameRuleManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHurtEffectManager(extras) {
    _reporterNs.report("HurtEffectManager", "./manager/HurtEffectManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePlaneManager(extras) {
    _reporterNs.report("GamePlaneManager", "./manager/GamePlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageManager(extras) {
    _reporterNs.report("StageManager", "./manager/StageManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResManager(extras) {
    _reporterNs.report("GameResManager", "./manager/GameResManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneManager(extras) {
    _reporterNs.report("SceneManager", "./manager/SceneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveManager(extras) {
    _reporterNs.report("WaveManager", "./manager/WaveManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMain(extras) {
    _reporterNs.report("GameMain", "./scenes/GameMain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFColliderManager(extras) {
    _reporterNs.report("FColliderManager", "./collider-system/FColliderManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameDataManager = _unresolved_3.GameDataManager;
    }, function (_unresolved_4) {
      MainPlaneManager = _unresolved_4.MainPlaneManager;
    }, function (_unresolved_5) {
      BattleManager = _unresolved_5.BattleManager;
    }, function (_unresolved_6) {
      BossManager = _unresolved_6.BossManager;
    }, function (_unresolved_7) {
      EnemyManager = _unresolved_7.EnemyManager;
    }, function (_unresolved_8) {
      GameRuleManager = _unresolved_8.GameRuleManager;
    }, function (_unresolved_9) {
      HurtEffectManager = _unresolved_9.HurtEffectManager;
    }, function (_unresolved_10) {
      GamePlaneManager = _unresolved_10.GamePlaneManager;
    }, function (_unresolved_11) {
      StageManager = _unresolved_11.StageManager;
    }, function (_unresolved_12) {
      GameResManager = _unresolved_12.default;
    }, function (_unresolved_13) {
      SceneManager = _unresolved_13.SceneManager;
    }, function (_unresolved_14) {
      WaveManager = _unresolved_14.default;
    }, function (_unresolved_15) {
      FColliderManager = _unresolved_15.default;
    }],
    execute: function () {
      _crd = true;
      ;
      _GameIns = class _GameIns extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.gameMainUI = null;
        }

        get battleManager() {
          return (_crd && BattleManager === void 0 ? (_reportPossibleCrUseOfBattleManager({
            error: Error()
          }), BattleManager) : BattleManager).getInstance(_crd && BattleManager === void 0 ? (_reportPossibleCrUseOfBattleManager({
            error: Error()
          }), BattleManager) : BattleManager);
        }

        get bossManager() {
          return (_crd && BossManager === void 0 ? (_reportPossibleCrUseOfBossManager({
            error: Error()
          }), BossManager) : BossManager).getInstance(_crd && BossManager === void 0 ? (_reportPossibleCrUseOfBossManager({
            error: Error()
          }), BossManager) : BossManager);
        }

        get enemyManager() {
          return (_crd && EnemyManager === void 0 ? (_reportPossibleCrUseOfEnemyManager({
            error: Error()
          }), EnemyManager) : EnemyManager).getInstance(_crd && EnemyManager === void 0 ? (_reportPossibleCrUseOfEnemyManager({
            error: Error()
          }), EnemyManager) : EnemyManager);
        }

        get gameDataManager() {
          return (_crd && GameDataManager === void 0 ? (_reportPossibleCrUseOfGameDataManager({
            error: Error()
          }), GameDataManager) : GameDataManager).getInstance(_crd && GameDataManager === void 0 ? (_reportPossibleCrUseOfGameDataManager({
            error: Error()
          }), GameDataManager) : GameDataManager);
        }

        get gameRuleManager() {
          return (_crd && GameRuleManager === void 0 ? (_reportPossibleCrUseOfGameRuleManager({
            error: Error()
          }), GameRuleManager) : GameRuleManager).getInstance(_crd && GameRuleManager === void 0 ? (_reportPossibleCrUseOfGameRuleManager({
            error: Error()
          }), GameRuleManager) : GameRuleManager);
        }

        get hurtEffectManager() {
          return (_crd && HurtEffectManager === void 0 ? (_reportPossibleCrUseOfHurtEffectManager({
            error: Error()
          }), HurtEffectManager) : HurtEffectManager).getInstance(_crd && HurtEffectManager === void 0 ? (_reportPossibleCrUseOfHurtEffectManager({
            error: Error()
          }), HurtEffectManager) : HurtEffectManager);
        }

        get mainPlaneManager() {
          return (_crd && MainPlaneManager === void 0 ? (_reportPossibleCrUseOfMainPlaneManager({
            error: Error()
          }), MainPlaneManager) : MainPlaneManager).getInstance(_crd && MainPlaneManager === void 0 ? (_reportPossibleCrUseOfMainPlaneManager({
            error: Error()
          }), MainPlaneManager) : MainPlaneManager);
        }

        get gamePlaneManager() {
          return (_crd && GamePlaneManager === void 0 ? (_reportPossibleCrUseOfGamePlaneManager({
            error: Error()
          }), GamePlaneManager) : GamePlaneManager).getInstance(_crd && GamePlaneManager === void 0 ? (_reportPossibleCrUseOfGamePlaneManager({
            error: Error()
          }), GamePlaneManager) : GamePlaneManager);
        }

        get stageManager() {
          return (_crd && StageManager === void 0 ? (_reportPossibleCrUseOfStageManager({
            error: Error()
          }), StageManager) : StageManager).getInstance(_crd && StageManager === void 0 ? (_reportPossibleCrUseOfStageManager({
            error: Error()
          }), StageManager) : StageManager);
        }

        get gameResManager() {
          return (_crd && GameResManager === void 0 ? (_reportPossibleCrUseOfGameResManager({
            error: Error()
          }), GameResManager) : GameResManager).getInstance(_crd && GameResManager === void 0 ? (_reportPossibleCrUseOfGameResManager({
            error: Error()
          }), GameResManager) : GameResManager);
        }

        get sceneManager() {
          return (_crd && SceneManager === void 0 ? (_reportPossibleCrUseOfSceneManager({
            error: Error()
          }), SceneManager) : SceneManager).getInstance(_crd && SceneManager === void 0 ? (_reportPossibleCrUseOfSceneManager({
            error: Error()
          }), SceneManager) : SceneManager);
        }

        get waveManager() {
          return (_crd && WaveManager === void 0 ? (_reportPossibleCrUseOfWaveManager({
            error: Error()
          }), WaveManager) : WaveManager).getInstance(_crd && WaveManager === void 0 ? (_reportPossibleCrUseOfWaveManager({
            error: Error()
          }), WaveManager) : WaveManager);
        }

        get fColliderManager() {
          return (_crd && FColliderManager === void 0 ? (_reportPossibleCrUseOfFColliderManager({
            error: Error()
          }), FColliderManager) : FColliderManager).instance;
        }

      };

      _export("GameIns", GameIns = _GameIns.getInstance(_GameIns));

      _crd = false;
    }
  };
});
//# sourceMappingURL=dff9f1d67215a4b3a6643cacca091f4863ea0232.js.map