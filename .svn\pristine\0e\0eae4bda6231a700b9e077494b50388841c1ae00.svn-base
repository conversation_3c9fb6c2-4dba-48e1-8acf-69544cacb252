'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const path_1 = __importDefault(require("path"));
exports.template = `
<ui-prop type="dump" class="progress"></ui-prop>
<ui-prop type="dump" class="levelPrefab"></ui-prop>
<ui-prop type="dump" class="save"></ui-prop>
<ui-prop type="dump" class="play"></ui-prop>
<ui-prop type="dump" class="levelPrefabUUID"></ui-prop>
<ui-prop type="dump" class="levelStrart"></ui-prop>
<ui-prop type="dump" class="levelEnd"></ui-prop>
<ui-prop >
    <ui-button class="btn-save">保存(ctrl+t)</ui-button>
    <ui-button class="btn-new">新建关卡</ui-button>
    <ui-button class="btn-play">播放</ui-button>
    <ui-button class="btn-play">起始</ui-button>
    <ui-button class="btn-play">末端</ui-button>
</ui-prop>
`;
exports.$ = {
    progress: '.progress',
    levelPrefab: '.levelPrefab',
    save: '.save',
    levelPrefabUUID: '.levelPrefabUUID',
    btnSave: '.btn-save',
    btnNew: '.btn-new',
    btnPlay: '.btn-play',
    play: '.play',
    levelStrart: '.levelStrart',
    levelEnd: '.levelEnd',
};
function update(dump) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump;
    this.$.progress.render(dump.value.progress);
    this.$.levelPrefab.render(dump.value.levelPrefab); // 这个是 levelPrefab 的 uuid，需要转成 JsonAsset 才能用
    this.$.save.dump = dump.value.save;
    this.$.play.dump = dump.value.play;
    this.$.levelPrefabUUID.dump = dump.value.levelPrefabUUID;
    this.$.levelStrart.dump = dump.value.levelStrart;
    this.$.levelEnd.dump = dump.value.levelEnd;
    // this.$.save.render(dump.value.save);
}
function ready() {
    this.$.btnSave.addEventListener('confirm', () => {
        console.log("panel save level");
        // @ts-ignore
        this.dump.value.save.value = true;
        this.$.save.dispatch('change-dump');
    });
    this.$.btnNew.addEventListener('confirm', async () => {
        // @ts-ignore
        console.log("panel new level:", Editor.Project.path);
        // Editor.Panel.open('level-editor.newlevel')
        // @ts-ignore
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "game", "level");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Level', extensions: ['json'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        console.log("panel new level name:", name);
        const filePath = `db://assets/resources/game/level/${name}`;
        // @ts-ignore
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', filePath, "{}");
        console.log("panel new level create asset rsp:", createRsp);
        // @ts-ignore
        this.dump.value.levelPrefabUUID.value = createRsp === null || createRsp === void 0 ? void 0 : createRsp.uuid;
        this.$.levelPrefabUUID.dispatch('change-dump');
        // Editor.Message.send('level-editor', 'new-level', name)
    });
    this.$.btnPlay.addEventListener('confirm', () => {
        console.log("panel play level");
        // @ts-ignore
        this.dump.value.play.value = true;
        this.$.play.dispatch('change-dump');
    });
    this.$.levelStrart.addEventListener('confirm', () => {
        console.log("panel levelStrart");
        // @ts-ignore
        this.dump.value.levelProgress.value = 0;
        this.$.levelStrart.dispatch('change-dump');
    });
    this.$.levelEnd.addEventListener('confirm', () => {
        console.log("panel levelEnd");
        // @ts-ignore
        this.dump.value.levelProgress.value = 1;
        this.$.levelEnd.dispatch('change-dump');
    });
}
//# sourceMappingURL=data:application/json;base64,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