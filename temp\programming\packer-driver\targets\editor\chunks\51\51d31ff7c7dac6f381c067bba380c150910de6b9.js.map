{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/GameFunc.ts"], "names": ["GameFunc", "UITransform", "GameConst", "uuid", "_uuid", "wxLoadErr", "setImage", "sprite", "spriteName", "atlas", "adjustSize", "spriteFrame", "getSpriteFrame", "node", "getComponent", "width", "height", "fromTo", "startFrame", "endFrame", "frameTime", "ActionFrameTime", "loadErr"], "mappings": ";;;4FAGaA,Q;;;;;;;;;;;;;;AAHiBC,MAAAA,W,OAAAA,W;;AACrBC,MAAAA,S,iBAAAA,S;;;;;;;0BAEIF,Q,GAAN,MAAMA,QAAN,CAAe;AAIlB;AACJ;AACA;AACA;AACmB,mBAAJG,IAAI,GAAG;AACd,eAAKC,KAAL;AACA,iBAAO,KAAKA,KAAZ;AACH;;AAEe,eAATC,SAAS,GAAG,CAElB;;AAEc,eAARC,QAAQ,CAACC,MAAD,EAAiBC,UAAjB,EAAqCC,KAArC,EAAyDC,UAAmB,GAAG,KAA/E,EAA4F;AACvG,cAAID,KAAJ,EAAW;AACPF,YAAAA,MAAM,CAACI,WAAP,GAAqBF,KAAK,CAACG,cAAN,CAAqBJ,UAArB,CAArB;;AACA,gBAAIE,UAAU,IAAIH,MAAM,CAACI,WAAzB,EAAsC;AAClCJ,cAAAA,MAAM,CAACM,IAAP,CAAYC,YAAZ,CAAyBb,WAAzB,EAAuCc,KAAvC,GAAgD,IAAI,CAAL,GAAUR,MAAM,CAACI,WAAP,CAAmBI,KAA5E;AACAR,cAAAA,MAAM,CAACM,IAAP,CAAYC,YAAZ,CAAyBb,WAAzB,EAAuCe,MAAvC,GAAiD,IAAI,CAAL,GAAUT,MAAM,CAACI,WAAP,CAAmBK,MAA7E;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACiB,eAANC,MAAM,CAACC,UAAD,EAAqBC,QAArB,EAA+C;AACxD,gBAAMC,SAAS,GAAG;AAAA;AAAA,sCAAUC,eAA5B,CADwD,CACX;;AAC7C,iBAAO,CAACF,QAAQ,GAAGD,UAAZ,IAA0BE,SAAjC;AACH;;AApCiB,O;;AAATpB,MAAAA,Q,CACFsB,O,GAAmB,K;AADjBtB,MAAAA,Q,CAEFI,K,GAAgB,C", "sourcesContent": ["import { Sprite, SpriteAtlas, UITransform } from \"cc\";\r\nimport { GameConst } from \"./const/GameConst\";\r\n\r\nexport class GameFunc {\r\n    static loadErr: boolean = false;\r\n    static _uuid: number = 0;\r\n\r\n    /**\r\n     * 获取唯一的 UUID\r\n     * @returns {number} 返回一个自增的唯一标识符\r\n     */\r\n    static get uuid() {\r\n        this._uuid++;\r\n        return this._uuid;\r\n    }\r\n\r\n    static wxLoadErr() {\r\n\r\n    }\r\n\r\n    static setImage(sprite: Sprite, spriteName: string, atlas: SpriteAtlas, adjustSize: boolean = false): void {\r\n        if (atlas) {\r\n            sprite.spriteFrame = atlas.getSpriteFrame(spriteName);\r\n            if (adjustSize && sprite.spriteFrame) {\r\n                sprite.node.getComponent(UITransform)!.width = (2 / 3) * sprite.spriteFrame.width;\r\n                sprite.node.getComponent(UITransform)!.height = (2 / 3) * sprite.spriteFrame.height;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 根据帧范围计算动画持续时间\r\n     * @param startFrame 起始帧\r\n     * @param endFrame 结束帧\r\n     * @returns 动画持续时间（秒）\r\n     */\r\n    static fromTo(startFrame: number, endFrame: number): number {\r\n        const frameTime = GameConst.ActionFrameTime; // 每帧的时间（秒）\r\n        return (endFrame - startFrame) * frameTime;\r\n    }\r\n}"]}