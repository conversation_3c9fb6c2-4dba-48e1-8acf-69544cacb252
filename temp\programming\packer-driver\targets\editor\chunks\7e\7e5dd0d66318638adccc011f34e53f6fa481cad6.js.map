{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts"], "names": ["_decorator", "instantiate", "Color", "Component", "RichText", "Rect", "Vec3", "Graphics", "assetManager", "EDITOR", "Emitter", "BulletSystem", "PlaneBase", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "EmitterEditor", "visible", "displayName", "type", "override", "_updateInEditor", "_graphicsCom", "_player<PERSON>os", "targetFrameRate", "fixedDelta", "value", "graphics", "getComponent", "addComponent", "resetInEditor", "onFocusInEditor", "init", "<PERSON><PERSON><PERSON>", "player<PERSON>ode", "node", "walk", "emitter", "setIsActive", "drawWorldBounds", "onLostFocusInEditor", "reset", "start", "frameCount", "frameTimeInMilliseconds", "destroy", "x", "y", "z", "setPosition", "clear", "console", "log", "update", "dt", "updateInfoText", "milli_dt", "tick", "richText", "string", "toFixed", "allEmitters", "length", "allBullets", "allEventGroups", "strokeColor", "RED", "lineWidth", "bounds", "worldBounds", "rect", "width", "height", "stroke", "instantiatePrefab", "prefabUuid", "loadAny", "uuid", "err", "prefab", "parent", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Y,OAAAA,Y;;AACjFC,MAAAA,M,UAAAA,M;;AACgBC,MAAAA,O,iBAAAA,O;;AAChBC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA;AAAvE,O,GAAiFlB,U;;+BAO1EmB,a,WALZN,OAAO,CAAC,eAAD,C,UACPK,IAAI,CAAC,aAAD,C,UACJJ,WAAW,CAAC,IAAD,C,UACXC,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAEZD,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRJ,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UASRL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAElB,QAAP;AAAiBmB,QAAAA,QAAQ,EAAE,IAA3B;AAAiCF,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBD,QAAAA,WAAW,EAAC;AAA9B,OAAD,C,kGArBb,MAKaF,aALb,SAKmChB,SALnC,CAK6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsBjCqB,eAtBiC,GAsBN,KAtBM;AAAA,eAuBjCC,YAvBiC,GAuBH,IAvBG;AAAA,eA+BzCC,UA/ByC,GA+BtB,IAAIpB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CA/BsB;AAAA;;AAEd;AAGD,YAAfqB,eAAe,GAAW;AACjC,iBAAO,OAAO,KAAKC,UAAnB;AACH;;AAEyB,YAAfD,eAAe,CAACE,KAAD,EAAgB;AACtC,eAAKD,UAAL,GAAkB,OAAOC,KAAzB;AACH;;AAakB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKL,YAAV,EAAwB;AACpB,iBAAKA,YAAL,GAAoB,KAAKM,YAAL,CAAkBxB,QAAlB,KAA+B,KAAKyB,YAAL,CAAkBzB,QAAlB,CAAnD;AACH;;AACD,iBAAO,KAAKkB,YAAZ;AACH;;AAIDQ,QAAAA,aAAa,GAAG;AACZ,eAAKT,eAAL,GAAuB,IAAvB,CADY,CAEZ;AACH;;AAEDU,QAAAA,eAAe,GAAG;AACd,eAAKV,eAAL,GAAuB,IAAvB,CADc,CAEd;AAEA;AACA;;AAEA;AAAA;AAAA,4CAAaW,IAAb,CAAkB,IAAI9B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,GAAf,EAAoB,IAApB,CAAlB,EAPc,CAQd;;AACA;AAAA;AAAA,4CAAa+B,WAAb,GAA2B,KAAKC,UAAhC,CATc,CAUd;AAEA;;AACA,eAAKC,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,kBAAME,OAAO,GAAGF,IAAI,CAACP,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAIS,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACC,WAAR,CAAoB,IAApB;AACH;AACJ,WALD;AAOA,eAAKC,eAAL;AACH;;AAEDC,QAAAA,mBAAmB,GAAS;AACxB,eAAKnB,eAAL,GAAuB,KAAvB;AACA,eAAKoB,KAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKD,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAG;AACJzB,UAAAA,aAAa,CAAC2B,UAAd,GAA2B,CAA3B;AACA3B,UAAAA,aAAa,CAAC4B,uBAAd,GAAwC,CAAxC;AACA;AAAA;AAAA,4CAAaC,OAAb,CAAqB,IAArB,EAHI,CAIJ;;AACA,eAAKV,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,kBAAME,OAAO,GAAGF,IAAI,CAACP,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAIS,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACI,KAAR;AACH;AACJ,WALD;AAMA,eAAKlB,UAAL,CAAgBuB,CAAhB,GAAoB,KAAKvB,UAAL,CAAgBwB,CAAhB,GAAoB,KAAKxB,UAAL,CAAgByB,CAAhB,GAAoB,CAA5D;;AACA,cAAI,KAAKd,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBc,WAArB,CAAiC,KAAK1B,UAAtC;AACH;;AACD,eAAKI,QAAL,CAAcuB,KAAd;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAK/B,eAA5B;AACH;;AAEDgC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAIhD,MAAM,IAAI,KAAKe,eAAnB,EAAoC;AAChC,iBAAKkC,cAAL;AACA,kBAAMC,QAAQ,GAAGF,EAAE,GAAG,IAAtB;AACAtC,YAAAA,aAAa,CAAC2B,UAAd,IAA4B,CAA5B;AACA3B,YAAAA,aAAa,CAAC4B,uBAAd,IAAyCY,QAAzC;AACA;AAAA;AAAA,8CAAaC,IAAb,CAAkBH,EAAlB,EALgC,CAMhC;AACH;AACJ;;AAEDC,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKG,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcC,MAAd,GAAwB,SAAQ3C,aAAa,CAAC4B,uBAAd,CAAsCgB,OAAtC,CAA8C,CAA9C,CAAiD,cAAa;AAAA;AAAA,8CAAaC,WAAb,CAAyBC,MAAO,aAAY;AAAA;AAAA,8CAAaC,UAAb,CAAwBD,MAAO,cAAa;AAAA;AAAA,8CAAaE,cAAb,CAA4BF,MAAO,EAAzN;AACH;AACJ;;AAEDvB,QAAAA,eAAe,GAAG;AACd,eAAKZ,QAAL,CAAcuB,KAAd;AACA,eAAKvB,QAAL,CAAcsC,WAAd,GAA4BlE,KAAK,CAACmE,GAAlC;AACA,eAAKvC,QAAL,CAAcwC,SAAd,GAA0B,EAA1B;AACA,gBAAMC,MAAM,GAAG;AAAA;AAAA,4CAAaC,WAA5B;AACA,eAAK1C,QAAL,CAAc2C,IAAd,CAAmBF,MAAM,CAACtB,CAAP,GAAWsB,MAAM,CAACG,KAAP,GAAa,CAA3C,EAA8CH,MAAM,CAACrB,CAAP,GAAWqB,MAAM,CAACI,MAAP,GAAc,CAAvE,EAA0EJ,MAAM,CAACG,KAAjF,EAAwFH,MAAM,CAACI,MAA/F;AACA,eAAK7C,QAAL,CAAc8C,MAAd;AACH,SAjHwC,CAmHzC;;;AACOC,QAAAA,iBAAiB,CAACC,UAAD,EAAqB;AACzC;AACA;AACAtE,UAAAA,YAAY,CAACuE,OAAb,CAAqB;AAACC,YAAAA,IAAI,EAAEF;AAAP,WAArB,EAAyC,CAACG,GAAD,EAAMC,MAAN,KAAiB;AACtD,gBAAID,GAAJ,EAAS;AACL3B,cAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsC0B,GAAtC;AACA;AACH;;AACD,kBAAM3C,IAAI,GAAGrC,WAAW,CAACiF,MAAD,CAAxB;AACA,kBAAMC,MAAM,GAAG,KAAK7C,IAApB;AACA6C,YAAAA,MAAM,CAAEC,QAAR,CAAiB9C,IAAjB;AACH,WARD;AASH,SAhIwC,CAkIzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAlJyC,O,UAmBlCQ,U,GAAqB,C,UACrBC,uB,GAAkC,C;;;;;iBAlBrB,K;;;;;;;iBAYC,I;;;;;;;iBAGQ,I", "sourcesContent": ["import { _decorator, misc, instantiate, Color, Component, RichText, Rect, Vec3, Graphics, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { eEmitterStatus, Emitter } from 'db://assets/scripts/game/bullet/Emitter';\r\nimport { BulletSystem } from 'db://assets/scripts/game/bullet/BulletSystem';\r\nimport PlaneBase from \"db://assets/scripts/game/ui/plane/PlaneBase\";\r\nimport FColliderManager from 'db://assets/scripts/game/collider-system/FColliderManager';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\n\r\n@ccclass('EmitterEditor')\r\n@menu('子弹系统/发射器编辑器')\r\n@playOnFocus(true)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class EmitterEditor extends Component {\r\n    @property({visible:false})\r\n    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒\r\n\r\n    @property({displayName: \"目标帧率\"})\r\n    public get targetFrameRate(): number {\r\n        return 1000 / this.fixedDelta;\r\n    }\r\n\r\n    public set targetFrameRate(value: number) {\r\n        this.fixedDelta = 1000 / value;\r\n    }\r\n\r\n    @property({type: RichText, override: true, displayName: \"信息显示\"})\r\n    richText: RichText = null!;\r\n\r\n    @property({type: PlaneBase, displayName:\"玩家节点\"})\r\n    playerNode: PlaneBase|null = null;\r\n\r\n    static frameCount: number = 0;\r\n    static frameTimeInMilliseconds: number = 0;\r\n\r\n    private _updateInEditor: boolean = false;\r\n    private _graphicsCom: Graphics|null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphicsCom) {\r\n            this._graphicsCom = this.getComponent(Graphics) || this.addComponent(Graphics);\r\n        }\r\n        return this._graphicsCom!;\r\n    }\r\n\r\n    _playerPos: Vec3 = new Vec3(0, 0, 0);\r\n\r\n    resetInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('resetInEditor');\r\n    }\r\n\r\n    onFocusInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('onFocusInEditor');\r\n        \r\n        // @ts-ignore\r\n        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));\r\n        \r\n        BulletSystem.init(new Rect(0, 0, 750, 1334));\r\n        // fake player plane\r\n        BulletSystem.playerPlane = this.playerNode;\r\n        // FColliderManager.instance.enable = true;\r\n\r\n        // loop all children to find emitters\r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.setIsActive(true);\r\n            }\r\n        });\r\n\r\n        this.drawWorldBounds();\r\n    }\r\n\r\n    onLostFocusInEditor(): void {\r\n        this._updateInEditor = false;\r\n        this.reset();\r\n    }\r\n\r\n    start() {\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        EmitterEditor.frameCount = 0;\r\n        EmitterEditor.frameTimeInMilliseconds = 0;\r\n        BulletSystem.destroy(true);\r\n        // FColliderManager.instance.enable = false; \r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.reset();\r\n            }\r\n        });\r\n        this._playerPos.x = this._playerPos.y = this._playerPos.z = 0;\r\n        if (this.playerNode) {\r\n            this.playerNode.node.setPosition(this._playerPos);\r\n        }\r\n        this.graphics.clear();\r\n        console.log('reset: ', this._updateInEditor);\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (EDITOR && this._updateInEditor) {\r\n            this.updateInfoText();\r\n            const milli_dt = dt * 1000;\r\n            EmitterEditor.frameCount += 1;\r\n            EmitterEditor.frameTimeInMilliseconds += milli_dt;\r\n            BulletSystem.tick(dt);\r\n            // FColliderManager.instance.update(dt);\r\n        }\r\n    }\r\n\r\n    updateInfoText() {\r\n        if (this.richText) {\r\n            this.richText.string = `当前时间: ${EmitterEditor.frameTimeInMilliseconds.toFixed(2)}\\n当前发射器数量: ${BulletSystem.allEmitters.length}\\n当前子弹数量: ${BulletSystem.allBullets.length}\\n当前事件组数量: ${BulletSystem.allEventGroups.length}`;\r\n        }\r\n    }\r\n\r\n    drawWorldBounds() {\r\n        this.graphics.clear();\r\n        this.graphics.strokeColor = Color.RED;\r\n        this.graphics.lineWidth = 20;\r\n        const bounds = BulletSystem.worldBounds;\r\n        this.graphics.rect(bounds.x - bounds.width/2, bounds.y - bounds.height/2, bounds.width, bounds.height);\r\n        this.graphics.stroke();\r\n    }\r\n\r\n    // 编辑器方法\r\n    public instantiatePrefab(prefabUuid: string) {\r\n        // replace db://assets/resources/game/prefabs/emitter/ with assets/resources/game/prefabs/emitter/\r\n        //prefabUrl = prefabUrl.replace('db://', '');\r\n        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {\r\n            if (err) {\r\n                console.log('Failed to load prefab:', err);\r\n                return;\r\n            }\r\n            const node = instantiate(prefab!);\r\n            const parent = this.node;\r\n            parent!.addChild(node);\r\n        });\r\n    }\r\n\r\n    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {\r\n    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        \r\n    //     return new Promise<string>((resolve, reject) => {\r\n    //         const scene = director.getScene();\r\n    //         const target = scene!.getChildByUuid(nodeUuid);\r\n    //         if (!target) {\r\n    //             console.error(\"node not found:\", nodeUuid);\r\n    //             reject();\r\n    //             return;\r\n    //         }\r\n    //         const json = cce.Utils.serialize(target);\r\n    //         // 将节点保存为 Prefab\r\n    //         // _utils.applyTargetOverrides(target as Node);\r\n    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);\r\n    //         resolve(json);\r\n    //     });\r\n    // }\r\n}\r\n"]}