System.register(["__unresolved_0", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, EventActionBase, EmitterActionBase, EmitterAction_Active, EmitterAction_InitialDelay, EmitterAction_Prewarm, EmitterAction_PrewarmDuration, EmitterAction_Duration, EmitterAction_ElapsedTime, EmitterAction_Loop, EmitterAction_LoopInterval, EmitterAction_EmitInterval, EmitterAction_PerEmitCount, EmitterAction_PerEmitInterval, EmitterAction_PerEmitOffsetX, EmitterAction_Angle, EmitterAction_Count, EmitterAction_BulletDuration, EmitterAction_BulletDamage, EmitterAction_BulletSpeed, EmitterAction_BulletSpeedAngle, EmitterAction_BulletAcceleration, EmitterAction_BulletAccelerationAngle, EmitterAction_BulletScale, EmitterAction_BulletColorR, EmitterAction_BulletColorG, EmitterAction_BulletColorB, EmitterAction_BulletFacingMoveDir, EmitterAction_BulletTrackingTarget, EmitterAction_BulletDestructive, EmitterAction_BulletDestructiveOnHit, _crd;

  function _reportPossibleCrUseOfEventActionBase(extras) {
    _reporterNs.report("EventActionBase", "./IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  _export({
    EmitterActionBase: void 0,
    EmitterAction_Active: void 0,
    EmitterAction_InitialDelay: void 0,
    EmitterAction_Prewarm: void 0,
    EmitterAction_PrewarmDuration: void 0,
    EmitterAction_Duration: void 0,
    EmitterAction_ElapsedTime: void 0,
    EmitterAction_Loop: void 0,
    EmitterAction_LoopInterval: void 0,
    EmitterAction_EmitInterval: void 0,
    EmitterAction_PerEmitCount: void 0,
    EmitterAction_PerEmitInterval: void 0,
    EmitterAction_PerEmitOffsetX: void 0,
    EmitterAction_Angle: void 0,
    EmitterAction_Count: void 0,
    EmitterAction_BulletDuration: void 0,
    EmitterAction_BulletDamage: void 0,
    EmitterAction_BulletSpeed: void 0,
    EmitterAction_BulletSpeedAngle: void 0,
    EmitterAction_BulletAcceleration: void 0,
    EmitterAction_BulletAccelerationAngle: void 0,
    EmitterAction_BulletScale: void 0,
    EmitterAction_BulletColorR: void 0,
    EmitterAction_BulletColorG: void 0,
    EmitterAction_BulletColorB: void 0,
    EmitterAction_BulletFacingMoveDir: void 0,
    EmitterAction_BulletTrackingTarget: void 0,
    EmitterAction_BulletDestructive: void 0,
    EmitterAction_BulletDestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      EventActionBase = _unresolved_2.EventActionBase;
    }],
    execute: function () {
      _crd = true;

      _export("EmitterActionBase", EmitterActionBase = class EmitterActionBase extends (_crd && EventActionBase === void 0 ? (_reportPossibleCrUseOfEventActionBase({
        error: Error()
      }), EventActionBase) : EventActionBase) {// this was intentionally left blank
      }); // 修改发射器启用状态


      _export("EmitterAction_Active", EmitterAction_Active = class EmitterAction_Active extends EmitterActionBase {
        canLerp() {
          return false;
        }

        executeInternal(context, value) {
          context.emitter.isActive.value = value === 1;
        }

      }); // 修改发射器初始延迟时间


      _export("EmitterAction_InitialDelay", EmitterAction_InitialDelay = class EmitterAction_InitialDelay extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.initialDelay.value;
        }

        executeInternal(context, value) {
          context.emitter.initialDelay.value = value;
        }

      });

      _export("EmitterAction_Prewarm", EmitterAction_Prewarm = class EmitterAction_Prewarm extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.isPreWarm.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.isPreWarm.value = value === 1;
        }

      });

      _export("EmitterAction_PrewarmDuration", EmitterAction_PrewarmDuration = class EmitterAction_PrewarmDuration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.preWarmDuration.value;
        }

        executeInternal(context, value) {
          context.emitter.preWarmDuration.value = value;
        }

      }); // 修改发射器持续时间


      _export("EmitterAction_Duration", EmitterAction_Duration = class EmitterAction_Duration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.emitDuration.value;
        }

        executeInternal(context, value) {
          context.emitter.emitDuration.value = value;
        }

      }); // 修改发射器已运行时间


      _export("EmitterAction_ElapsedTime", EmitterAction_ElapsedTime = class EmitterAction_ElapsedTime extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.totalElapsedTime.value;
        }

        executeInternal(context, value) {
          context.emitter.totalElapsedTime.value = value;
        }

      });

      _export("EmitterAction_Loop", EmitterAction_Loop = class EmitterAction_Loop extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.isLoop.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.isLoop.value = value === 1;
        }

      });

      _export("EmitterAction_LoopInterval", EmitterAction_LoopInterval = class EmitterAction_LoopInterval extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.loopInterval.value;
        }

        executeInternal(context, value) {
          context.emitter.loopInterval.value = value;
        }

      });

      _export("EmitterAction_EmitInterval", EmitterAction_EmitInterval = class EmitterAction_EmitInterval extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.emitInterval.value;
        }

        executeInternal(context, value) {
          context.emitter.emitInterval.value = value;
        }

      });

      _export("EmitterAction_PerEmitCount", EmitterAction_PerEmitCount = class EmitterAction_PerEmitCount extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.perEmitCount.value;
        }

        executeInternal(context, value) {
          context.emitter.perEmitCount.value = value;
        }

      });

      _export("EmitterAction_PerEmitInterval", EmitterAction_PerEmitInterval = class EmitterAction_PerEmitInterval extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.perEmitInterval.value;
        }

        executeInternal(context, value) {
          context.emitter.perEmitInterval.value = value;
        }

      });

      _export("EmitterAction_PerEmitOffsetX", EmitterAction_PerEmitOffsetX = class EmitterAction_PerEmitOffsetX extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.perEmitOffsetX.value;
        }

        executeInternal(context, value) {
          context.emitter.perEmitOffsetX.value = value;
        }

      });

      _export("EmitterAction_Angle", EmitterAction_Angle = class EmitterAction_Angle extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.angle.value;
        }

        executeInternal(context, value) {
          context.emitter.angle.value = value;
        }

      });

      _export("EmitterAction_Count", EmitterAction_Count = class EmitterAction_Count extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.count.value;
        }

        executeInternal(context, value) {
          context.emitter.count.value = value;
        }

      }); // 以下是发射器修改子弹属性的部分


      _export("EmitterAction_BulletDuration", EmitterAction_BulletDuration = class EmitterAction_BulletDuration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.duration.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.duration.value = value;
        }

      });

      _export("EmitterAction_BulletDamage", EmitterAction_BulletDamage = class EmitterAction_BulletDamage extends EmitterActionBase {
        resetStartValue(context) {// this._startValue = context.emitter!.bulletProp.damage.value;
        }

        executeInternal(context, value) {// context.emitter!.bulletProp.damage.value = value;
        }

      });

      _export("EmitterAction_BulletSpeed", EmitterAction_BulletSpeed = class EmitterAction_BulletSpeed extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.speed.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.speed.value = value;
        }

      });

      _export("EmitterAction_BulletSpeedAngle", EmitterAction_BulletSpeedAngle = class EmitterAction_BulletSpeedAngle extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.speedAngle.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.speedAngle.value = value;
        }

      });

      _export("EmitterAction_BulletAcceleration", EmitterAction_BulletAcceleration = class EmitterAction_BulletAcceleration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.acceleration.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.acceleration.value = value;
        }

      });

      _export("EmitterAction_BulletAccelerationAngle", EmitterAction_BulletAccelerationAngle = class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.accelerationAngle.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.accelerationAngle.value = value;
        }

      });

      _export("EmitterAction_BulletScale", EmitterAction_BulletScale = class EmitterAction_BulletScale extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.scale.value;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.scale.value = value;
        }

      });

      _export("EmitterAction_BulletColorR", EmitterAction_BulletColorR = class EmitterAction_BulletColorR extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.r;
        }

        executeInternal(context, value) {
          var color = context.emitter.bulletProp.color.value;
          color.r = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletColorG", EmitterAction_BulletColorG = class EmitterAction_BulletColorG extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.g;
        }

        executeInternal(context, value) {
          var color = context.emitter.bulletProp.color.value;
          color.g = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletColorB", EmitterAction_BulletColorB = class EmitterAction_BulletColorB extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.b;
        }

        executeInternal(context, value) {
          var color = context.emitter.bulletProp.color.value;
          color.b = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletFacingMoveDir", EmitterAction_BulletFacingMoveDir = class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isFacingMoveDir.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.isFacingMoveDir.value = value === 1;
        }

      });

      _export("EmitterAction_BulletTrackingTarget", EmitterAction_BulletTrackingTarget = class EmitterAction_BulletTrackingTarget extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isTrackingTarget.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.isTrackingTarget.value = value === 1;
        }

      });

      _export("EmitterAction_BulletDestructive", EmitterAction_BulletDestructive = class EmitterAction_BulletDestructive extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isDestructive.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.isDestructive.value = value === 1;
        }

      });

      _export("EmitterAction_BulletDestructiveOnHit", EmitterAction_BulletDestructiveOnHit = class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isDestructiveOnHit.value ? 1 : 0;
        }

        executeInternal(context, value) {
          context.emitter.bulletProp.isDestructiveOnHit.value = value === 1;
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=7b276b8703b77f7ea3cc6a3d37ad14f893706757.js.map