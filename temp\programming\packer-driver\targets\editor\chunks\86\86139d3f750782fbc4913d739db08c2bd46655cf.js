System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EventGroupData, ExpressionValue, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _crd, ccclass, property, BulletData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "./EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "./ExpressionValue", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      EventGroupData = _unresolved_2.EventGroupData;
    }, function (_unresolved_3) {
      ExpressionValue = _unresolved_3.ExpressionValue;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 子弹数据
       * 所有时间相关的，单位都是毫秒(ms)
       */

      _export("BulletData", BulletData = (_dec = ccclass("BulletData"), _dec2 = property({
        displayName: '是否朝向行进方向',
        group: '基础属性'
      }), _dec3 = property({
        displayName: '是否追踪目标',
        group: '基础属性'
      }), _dec4 = property({
        displayName: '是否离屏自动销毁',
        group: '基础属性'
      }), _dec5 = property({
        displayName: '是否可被破坏',
        group: '基础属性'
      }), _dec6 = property({
        displayName: '命中时是否被销毁',
        group: '基础属性'
      }), _dec7 = property({
        visible: false
      }), _dec8 = property({
        displayName: '子弹基础缩放',
        group: '基础属性'
      }), _dec9 = property({
        visible: false
      }), _dec10 = property({
        displayName: '子弹持续时间',
        group: '基础属性'
      }), _dec11 = property({
        visible: false
      }), _dec12 = property({
        displayName: '延迟销毁时间',
        group: '基础属性'
      }), _dec13 = property({
        visible: false
      }), _dec14 = property({
        displayName: '子弹速度',
        group: '基础属性'
      }), _dec15 = property({
        visible: false
      }), _dec16 = property({
        displayName: '子弹加速度',
        group: '基础属性'
      }), _dec17 = property({
        visible: false
      }), _dec18 = property({
        displayName: '加速度方向',
        group: '基础属性'
      }), _dec19 = property({
        type: [_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
          error: Error()
        }), EventGroupData) : EventGroupData],
        displayName: '事件组',
        group: '事件组'
      }), _dec(_class = (_class2 = class BulletData {
        constructor() {
          // 这些数据在子弹表格里
          // @property({displayName: '子弹伤害'})
          // damage : number = 1;                      // 子弹伤害
          // @property({displayName: '子弹Prefab'})
          // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等
          // 是否朝向行进方向
          _initializerDefineProperty(this, "isFacingMoveDir", _descriptor, this);

          // 是否追踪目标
          _initializerDefineProperty(this, "isTrackingTarget", _descriptor2, this);

          // 是否离开屏幕自动销毁
          _initializerDefineProperty(this, "isDestroyOutScreen", _descriptor3, this);

          // 是否可被破坏
          _initializerDefineProperty(this, "isDestructive", _descriptor4, this);

          // 命中时是否被销毁
          _initializerDefineProperty(this, "isDestructiveOnHit", _descriptor5, this);

          // 子弹基础缩放
          _initializerDefineProperty(this, "scale", _descriptor6, this);

          // 子弹持续时间(超出后销毁回收)
          _initializerDefineProperty(this, "duration", _descriptor7, this);

          // 延迟销毁时间
          _initializerDefineProperty(this, "delayDestroy", _descriptor8, this);

          // 子弹速度
          _initializerDefineProperty(this, "speed", _descriptor9, this);

          // 子弹加速度
          _initializerDefineProperty(this, "acceleration", _descriptor10, this);

          // 加速度方向(角度) 0表示朝向移动方向, 90表示朝向发射方向
          _initializerDefineProperty(this, "accelerationAngle", _descriptor11, this);

          _initializerDefineProperty(this, "eventGroupData", _descriptor12, this);
        }

        get scaleStr() {
          return this.scale.raw;
        }

        set scaleStr(value) {
          this.scale.raw = value;
        }

        get durationStr() {
          return this.duration.raw;
        }

        set durationStr(value) {
          this.duration.raw = value;
        }

        get delayDestroyStr() {
          return this.delayDestroy.raw;
        }

        set delayDestroyStr(value) {
          this.delayDestroy.raw = value;
        }

        get speedStr() {
          return this.speed.raw;
        }

        set speedStr(value) {
          this.speed.raw = value;
        }

        get accelerationStr() {
          return this.acceleration.raw;
        }

        set accelerationStr(value) {
          this.acceleration.raw = value;
        }

        get accelerationAngleStr() {
          return this.accelerationAngle.raw;
        }

        set accelerationAngleStr(value) {
          this.accelerationAngle.raw = value;
        }

        static fromJSON(json) {
          const data = new BulletData();

          if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map((_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
              error: Error()
            }), EventGroupData) : EventGroupData).fromJSON);
          }

          return data;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isFacingMoveDir", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isTrackingTarget", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isDestroyOutScreen", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "isDestructive", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "isDestructiveOnHit", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "scale", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "scaleStr", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "scaleStr"), _class2.prototype), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "duration", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('10000');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "durationStr", [_dec10], Object.getOwnPropertyDescriptor(_class2.prototype, "durationStr"), _class2.prototype), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "delayDestroy", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "delayDestroyStr", [_dec12], Object.getOwnPropertyDescriptor(_class2.prototype, "delayDestroyStr"), _class2.prototype), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('600');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "speedStr", [_dec14], Object.getOwnPropertyDescriptor(_class2.prototype, "speedStr"), _class2.prototype), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "acceleration", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "accelerationStr", [_dec16], Object.getOwnPropertyDescriptor(_class2.prototype, "accelerationStr"), _class2.prototype), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "accelerationAngle", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "accelerationAngleStr", [_dec18], Object.getOwnPropertyDescriptor(_class2.prototype, "accelerationAngleStr"), _class2.prototype), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "eventGroupData", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=86139d3f750782fbc4913d739db08c2bd46655cf.js.map