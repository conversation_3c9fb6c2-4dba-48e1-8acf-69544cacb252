{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts"], "names": ["GameType", "GameState", "EnemyAction", "EnemyType", "EnemyCollideLevel", "EnemyDestroyType", "BossAction", "Enemy<PERSON>uff", "EnemyAttr", "GameEnum"], "mappings": ";;;;;;;;;;;;;YAMgBA,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;;YAUAC,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;;YAcAC,W,0BAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;iBAAAA,W;;;;;YAcAC,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;;YAuBAC,iB,0BAAAA,iB;AAAAA,UAAAA,iB,CAAAA,iB;AAAAA,UAAAA,iB,CAAAA,iB;AAAAA,UAAAA,iB,CAAAA,iB;iBAAAA,iB;;;;;YAQAC,gB,0BAAAA,gB;AAAAA,UAAAA,gB,CAAAA,gB;AAAAA,UAAAA,gB,CAAAA,gB;AAAAA,UAAAA,gB,CAAAA,gB;AAAAA,UAAAA,gB,CAAAA,gB;iBAAAA,gB;;;;;YAUAC,U,0BAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;iBAAAA,U;;;;;YAcAC,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;;YASAC,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;SA3GCC,Q,wBAAAA,Q", "sourcesContent": ["\r\nexport namespace GameEnum {\r\n\r\n    /**\r\n * 游戏类型\r\n */\r\n    export enum GameType {\r\n        Common = 0,\r\n        Expedition = 1,\r\n        Gold = 2,\r\n        Boss = 3,\r\n    }\r\n\r\n    /**\r\n     * 游戏状态\r\n     */\r\n    export enum GameState {\r\n        Idle = 0,\r\n        Ready = 1,\r\n        Sortie = 2,\r\n        Battle = 3,\r\n        Pause = 4,\r\n        WillOver = 5,\r\n        Over = 6,\r\n        BossIn = 7,\r\n    }\r\n\r\n    /**\r\n     * 敌人行为类型\r\n     */\r\n    export enum EnemyAction {\r\n        Sneak = 0,// 潜行行为\r\n        GoUp = 1,//上浮\r\n        Track = 2,// 跟踪行为\r\n        Transform = 3,// 变形行为\r\n        AttackPrepare = 4,// 准备攻击行为\r\n        AttackIng = 5,// 攻击中行为\r\n        AttackOver = 6,// 攻击结束行为\r\n        Leave = 7,//离开\r\n    }\r\n\r\n    /**\r\n     * 敌人类型\r\n     */\r\n    export enum EnemyType {\r\n        Normal = 0,\r\n        Missile = 1,\r\n        Turret = 2,\r\n        Ligature = 3,\r\n        LigatureLine = 4,\r\n        LigatureUnit = 5,\r\n        Build = 6,\r\n        Ship = 7,\r\n        ShipHeart = 8,\r\n        Train = 9,\r\n        ParkourItem = 10,\r\n        GoldShip = 11,\r\n        GoldBox = 12,\r\n        BossLigature = 13,\r\n        BossUnit = 10001,\r\n        BossNormal = 20001,\r\n        BossSnake = 21001,\r\n        BossUFO = 22001,\r\n    }\r\n    /**\r\n     * 敌人碰撞等级\r\n     */\r\n    export enum EnemyCollideLevel {\r\n        None = 0,\r\n        MainBullet = 1,\r\n        Main = 2,\r\n    }\r\n    /**\r\n     * 敌人销毁类型\r\n     */\r\n    export enum EnemyDestroyType {\r\n        Die = 0,\r\n        Leave = 1,\r\n        TrackOver = 2,\r\n        TimeOver = 3,\r\n    }\r\n\r\n    /**\r\n     * Boss 行为类型\r\n     */\r\n    export enum BossAction {\r\n        Normal = 0,\r\n        Appear = 1,\r\n        Transform = 2,\r\n        AttackPrepare = 3,\r\n        AttackIng = 4,\r\n        AttackOver = 5,\r\n        Switch = 6,\r\n        Blast = 7,\r\n    }\r\n\r\n    /**\r\n     * 敌人 Buff 类型\r\n     */\r\n    export enum EnemyBuff {\r\n        Ice = 1,\r\n        Fire = 2,\r\n        Treat = 100,\r\n    }\r\n\r\n    /**\r\n     * 敌人属性类型\r\n     */\r\n    export enum EnemyAttr {\r\n        Doctor = 1,\r\n        Shield = 2,\r\n    }\r\n}\r\n"]}