import { logInfo, logWarn } from "db://assets/scripts/utils/Logger";
import BaseComp from "../../base/BaseComp";
import { MyApp } from "db://assets/scripts/MyApp";
import { ResBuffer } from "db://assets/scripts/autogen/luban/schema";
import PlaneBase from "../PlaneBase";
import SkillComp from "./SkillComp";
import { random, randomRange } from "cc";

export class Buff {
    id:number;
    res: ResBuffer
    time = 0;
    duration = 0;
    cycleTimes = 0;
    static incID = 1;
    constructor(data: ResBuffer) {
        this.id = Buff.incID++;
        this.res = data;
        this.duration = this.res.duration == -1 ? -1 : this.res.duration + randomRange(0, this.res.durationBonus);
    }
}

export default class BuffComp extends BaseComp {
    private buffs: Map<number, Buff[]> = new Map();
    ApplyBuff(buffID: number) {
        logInfo("Buff", `apply buff ${buffID}`)
        let buffData = MyApp.lubanTables.TbResBuffer.get(buffID);
        if (!buffData) {
            logWarn("Buff", `apply buff ${buffID} but config not found`)
            return;
        }
        let buff: Buff|null
        if (buffData.duration != 0) {
            buff = new Buff(buffData)!;
            let buffs = this.buffs.get(buffID)
            if (!buffs) {
                buffs = [];
                this.buffs.set(buffID, buffs);
            }
            let stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;
            if (buffData.refreshType && buff.res.duration != -1) {
                buffs.forEach((b) => {
                    b.duration = b.time + buff!.duration;
                })
            }
            buffs.push(buff);
            while (buffs.length > stack) {
                this.removeBuff(buffs[0], buffs, 0, buffID);
            }
        }
        buffData.effects.forEach((applyEffect) => {
            SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                entity.ApplyBuffEffect(buff, applyEffect);
            })
        })
    }
    update(dt: number): void {
        this.buffs.forEach((buffs, buffID) => {
            buffs.forEach((buff, index) => {
                buff.time += dt*1000;
                if (buff.res.cycle > 0 && 
                    buff.time >= (buff.cycleTimes+1) * buff.res.cycle &&
                    (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes)
                ) {
                    buff.cycleTimes++;
                    buff.res.effects.forEach((applyEffect) => {
                        SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                            entity.ApplyBuffEffect(buff, applyEffect);
                        })
                    })
                }
                if (buff.duration != -1 &&buff.time >= buff.duration) {
                    this.removeBuff(buff, buffs, index, buffID);
                }
            })
        })
    }

    private removeBuff(buff: Buff, buffs: Buff[], index: number, buffID: number) {
        buff.res.effects.forEach((applyEffect) => {
            // 这个地方和加的时候查出来的target会不同
            // 1. 需要保证查出来的target只多不少
            // 2. remove接口里面需要判断时候是这个buff的效果
            SkillComp.forEachByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                entity.RemoveBuffEffect(buff, applyEffect);
            })
        })
        buffs.splice(index, 1);
        if (buffs.length === 0) {
            this.buffs.delete(buffID);
        }    
    }
}