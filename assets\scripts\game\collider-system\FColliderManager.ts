import { _decorator, v2, mat4, Rect, Mat4, Vec2, rect, Graphics, director, Scheduler, game, Node, view, Color, Director, log, Component} from 'cc';
import FBoxCollider from "./FBoxCollider";
import FCircleCollider from "./FCircleCollider";
import FCollider, { ColliderGroupType, ColliderType } from "./FCollider";
import FPolygonCollider from "./FPolygonCollider";
import { Intersection } from "./Intersection";
import { QuadTree } from "./QuadTree";
let tempVec2 = v2();
let tempMat4 = mat4();
let tempArr: any[] = [];
let collisionState: number = 0;

// 定义函数类型
type ColliderTriggerFunc = (self: FCollider, other: FCollider) => boolean;

// 显式声明 ColliderTriggerFuncs 的类型
export const ColliderTriggerFuncs: ColliderTriggerFunc[] = [];

ColliderTriggerFuncs[ColliderType.Circle | ColliderType.Circle] = (self: FCollider, other: FCollider) => {
    let selfCircle = self as FCircleCollider;
    let otherCircle = other as FCircleCollider;
    return Intersection.circleCircle(selfCircle, otherCircle);
}
ColliderTriggerFuncs[ColliderType.Circle | ColliderType.Box] = (self: FCollider, other: FCollider) => {
    let selfCircle = self as FCircleCollider;
    let otherBox = other as FBoxCollider;
    return Intersection.polygonCircle(otherBox.worldPoints, selfCircle);
}
ColliderTriggerFuncs[ColliderType.Circle | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {
    let selfCircle = self as FCircleCollider;
    let otherPolygon = other as FPolygonCollider;
    return Intersection.polygonCircle(otherPolygon.worldPoints, selfCircle);
}
ColliderTriggerFuncs[ColliderType.Box | ColliderType.Box] = (self: FCollider, other: FCollider) => {
    let selfBox = self as FBoxCollider;
    let otherBox = other as FBoxCollider;
    if (selfBox.node.angle === 0 && otherBox.node.angle == 0) {
        return Intersection.rectRect(selfBox.aabb, otherBox.aabb);
    } else {
        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
    }
}
ColliderTriggerFuncs[ColliderType.Box | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {
    let selfBox = self as FBoxCollider;
    let otherBox = other as FPolygonCollider;
    if (!otherBox.isConvex) {
        return Intersection.polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);
    }
    else {
        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
    }
}
ColliderTriggerFuncs[ColliderType.Polygon | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {
    let selfBox = self as FPolygonCollider;
    let otherBox = other as FPolygonCollider;
    if (!otherBox.isConvex || !selfBox.isConvex) {
        return Intersection.polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);
    }
    else {
        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
    }
}

function obbApplyMatrix(rect: Rect, mat4: Mat4, out_bl: Vec2, out_tl: Vec2, out_tr: Vec2, out_br: Vec2) {
    let x = rect.x;
    let y = rect.y;
    let width = rect.width;
    let height = rect.height;

    // 初始化一个长度为16的数组来存储矩阵元素
    let mat4m = new Array(16);
    // 或者使用 Float32Array(16) 如果你需要TypedArray
    
    // 将矩阵数据提取到数组中
    Mat4.toArray(mat4m, mat4);
    let m00 = mat4m[0], m01 = mat4m[1], m04 = mat4m[4], m05 = mat4m[5];
    let m12 = mat4m[12], m13 = mat4m[13];

    let tx = m00 * x + m04 * y + m12;
    let ty = m01 * x + m05 * y + m13;
    let xa = m00 * width;
    let xb = m01 * width;
    let yc = m04 * height;
    let yd = m05 * height;

    out_tl.x = tx;
    out_tl.y = ty;
    out_tr.x = xa + tx;
    out_tr.y = xb + ty;
    out_bl.x = yc + tx;
    out_bl.y = yd + ty;
    out_br.x = xa + yc + tx;
    out_br.y = xb + yd + ty;
}

enum CollisionType {
    onEnter = "onCollisionEnter",
    onStay = "onCollisionStay",
    onExit = "onCollisionExit"
}
export interface IColliderPair {
    id: number,
    colliderA: FCollider,
    colliderB: FCollider,
    frameId: number,
    state: CollisionType,
}

let CollisionMatrix = {
    [`${ColliderGroupType.BULLET_SELF}_${ColliderGroupType.ENEMY_NORMAL}`]: true,
    [`${ColliderGroupType.BULLET_ENEMY}_${ColliderGroupType.PLAYER}`]: true,
}

export default class FColliderManager {
    private static _instance: FColliderManager|null = null;

    public static get instance() {
        if (!this._instance) {
            this._instance = new FColliderManager();
        }
        return this._instance;
    };

    private _onCollisionEnter: ((collider1: FCollider, collider2: FCollider) => void) | null = null;
    private _onCollisionStay: ((collider1: FCollider, collider2: FCollider) => void) | null = null;
    private _onCollisionExit: ((collider1: FCollider, collider2: FCollider) => void) | null = null;

    private _tree: QuadTree<FCollider>;
    private _frameId: number = 0;
    private constructor() {
        this._frameId = 0;
        this._tree = new QuadTree<FCollider>(this._treeRect, 0, this._maxDepth, this._maxChildren);
    }
    //是否要重新建树
    private _treeDirty: boolean = true;

    private _maxDepth: number = 6;
    public get maxDepth(): number {
        return this._maxDepth;
    }
    public set maxDepth(value: number) {
        if (value != this._maxDepth) {
            this._maxDepth = value;
            this._treeDirty = true;
        }
    }

    private _maxChildren: number = 12;
    public get maxChildren(): number {
        return this._maxChildren;
    }
    public set maxChildren(value: number) {
        if (this._maxChildren != value) {
            this._maxChildren = value;
            this._treeDirty = true;
        }
    }

    private _treeRect: Rect = rect(0, 0, view.getVisibleSize().width, view.getVisibleSize().height);
    public get treeRect(): Rect {
        return this._treeRect;
    }
    //设置四叉树大小
    public set treeRect(value: Rect) {
        if (this._treeRect) {
            if (this._treeRect.equals(value)) return;
        }
        this._treeRect.set(value);
        this._treeDirty = false;
    }

    private _enable: boolean = false;
    public get enable(): boolean {
        return this._enable;
    }
    public set enable(value: boolean) {
        this._enable = value;
        if (value) {
            // director.getScheduler().enableForTarget(this);
            // director.getScheduler().scheduleUpdate(this, Scheduler.PRIORITY_NON_SYSTEM, false);
            // director.on(Director.EVENT_BEFORE_UPDATE, this.update, this);
        }
    }

    private collisionPairs: Map<number, IColliderPair> = new Map();
    private _colliders: FCollider[] = [];
    public addCollider(collider: FCollider) {
        let colliders = this._colliders;
        this.initCollider(collider);
        colliders.push(collider);
    }
    public removeCollider(collider: FCollider) {
        for (let i = this._colliders.length - 1; i >= 0; i--) {
            let c = this._colliders[i];
            if (collider.colliderId === c.colliderId) {
                this._colliders.splice(i, 1);
            }
        }
    }

    public initCollider(collider: FCollider) {
        collider.initCollider();
    }

    setGlobalColliderEnterCall(func: (collider1: FCollider, collider2: FCollider) => void) {
        this._onCollisionEnter = func;
    }

    setGlobalColliderStayCall(func: (collider1: FCollider, collider2: FCollider) => void) {
        this._onCollisionStay = func;
    }

    setGlobalColliderExitCall(func: (collider1: FCollider, collider2: FCollider) => void) {
        this._onCollisionExit = func;
    }

    public updateCollider(c: FCollider) {
        c.node.getWorldMatrix(tempMat4);
        if (c.type === ColliderType.Box) {
            let collider = c as FBoxCollider;
            let size = collider.size;
            collider.aabb.x = collider.offset.x - size.width / 2;
            collider.aabb.y = collider.offset.y - size.height / 2;
            collider.aabb.width = size.width;
            collider.aabb.height = size.height;
            let wps = collider.worldPoints;
            let wp0 = wps[0], wp1 = wps[1],
                wp2 = wps[2], wp3 = wps[3];
            obbApplyMatrix(collider.aabb, tempMat4, wp0, wp1, wp2, wp3);
            let minx = Math.min(wp0.x, wp1.x, wp2.x, wp3.x);
            let miny = Math.min(wp0.y, wp1.y, wp2.y, wp3.y);
            let maxx = Math.max(wp0.x, wp1.x, wp2.x, wp3.x);
            let maxy = Math.max(wp0.y, wp1.y, wp2.y, wp3.y);
            let worldEdge = collider.worldEdge;
            for (let i = 0, l = wps.length; i < l; i++) {
                if (!worldEdge[i]) worldEdge[i] = v2();
                Vec2.subtract(worldEdge[i], wps[(i + 1) % l], wps[i]);
            }
            collider.aabb.x = minx;
            collider.aabb.y = miny;
            collider.aabb.width = maxx - minx;
            collider.aabb.height = maxy - miny;
        } else if (c.type == ColliderType.Circle) {
            let collider = c as FCircleCollider;
            Vec2.transformMat4(tempVec2, collider.offset, tempMat4);
            collider.worldPosition.x = tempVec2.x;
            collider.worldPosition.y = tempVec2.y;

            let mm = new Array(16); // Create an array to hold the matrix data
            Mat4.toArray(mm, tempMat4); // Extract the matrix data into the array

            let tempx = mm[12], tempy = mm[13];
            mm[12] = mm[13] = 0;

            Mat4.fromArray(tempMat4, mm); 


            tempVec2.x = collider.radius;
            tempVec2.y = 0;

            Vec2.transformMat4(tempVec2, tempVec2, tempMat4);
            let d = Math.sqrt(tempVec2.x * tempVec2.x + tempVec2.y * tempVec2.y);

            collider.worldRadius = d;

            collider.aabb.x = collider.worldPosition.x - d;
            collider.aabb.y = collider.worldPosition.y - d;
            collider.aabb.width = d * 2;
            collider.aabb.height = d * 2;

            mm[12] = tempx;
            mm[13] = tempy;
        } else if (c.type == ColliderType.Polygon) {
            let collider = c as FPolygonCollider;
            let points = collider.points;
            let worldPoints = collider.worldPoints;
            let worldEdge = collider.worldEdge;
            worldPoints.length = points.length;

            let minx = Number.MAX_SAFE_INTEGER, miny = Number.MAX_SAFE_INTEGER, maxx = -Number.MAX_SAFE_INTEGER, maxy = -Number.MAX_SAFE_INTEGER;
            for (let i = 0, l = points.length; i < l; i++) {
                if (!worldPoints[i]) {
                    worldPoints[i] = v2();
                }
                tempVec2.x = points[i].x + collider.offset.x;
                tempVec2.y = points[i].y + collider.offset.y;
                Vec2.transformMat4(tempVec2, tempVec2, tempMat4);

                let x = tempVec2.x;
                let y = tempVec2.y;

                worldPoints[i].set(tempVec2);

                if (x > maxx) maxx = x;
                if (x < minx) minx = x;
                if (y > maxy) maxy = y;
                if (y < miny) miny = y;
            }
            if (c.isConvex) {
                for (let i = 0, l = worldPoints.length; i < l; i++) {
                    if (!worldEdge[i]) worldEdge[i] = v2();
                    Vec2.subtract(worldEdge[i], worldPoints[(i + 1) % l], worldPoints[i]);
                }
            }

            collider.aabb.x = minx;
            collider.aabb.y = miny;
            collider.aabb.width = maxx - minx;
            collider.aabb.height = maxy - miny;
        }
    }

    public shouldCollide(c1: FCollider, c2: FCollider) {
        // if (c1.groupType === ColliderGroupType.DEFAULT || c2.groupType === ColliderGroupType.DEFAULT) {
        //     return true;
        // }

        return CollisionMatrix[`${c1.groupType}_${c2.groupType}`] || CollisionMatrix[`${c2.groupType}_${c1.groupType}`];
    }

    isOutOfScreen(aabb: Rect): boolean {
        const visibleSize = view.getVisibleSize();
        const screenLeft = -200;
        const screenRight = visibleSize.width + 200;
        const screenTop = visibleSize.height + 200;
        const screenBottom = -200;
    
        // 判断是否超出屏幕边界
        return (
            aabb.x + aabb.width < screenLeft || // 超出屏幕左边
            aabb.x > screenRight ||            // 超出屏幕右边
            aabb.y + aabb.height < screenBottom || // 超出屏幕下边
            aabb.y > screenTop                 // 超出屏幕上边
        );
    }

    public update(dt: number) {
        if (!this.enable) return;
        // console.time("碰撞");
        this.oneTest(dt);
        // console.timeEnd("碰撞");
    }

    /**
     * 1.清空四叉树
     * 2.更新Collider
     * 3.插入四叉树
     * 4.筛选重复碰撞组(暂无)
     * 5.碰撞检测
     * @param dt 
     * @returns 
     */
    public oneTest(dt: number) {
        let timeNow = Date.now();
        this._frameId++;

        if (this._treeDirty) {
            this._tree = new QuadTree<FCollider>(this._treeRect, 0, this._maxDepth, this._maxChildren);
            this._treeDirty = false;
        }
        this._tree.clear();
        
        for (let i = this._colliders.length - 1; i >= 0; i--) {
            let collider = this._colliders[i];
            // console.log("collider:", collider.entity?.node.name," isEnable = ", collider.isEnable);
            if (!collider || !collider.isValid) {
                this._colliders.splice(i, 1);
                continue;
            }
            if (!collider.isEnable){
                continue;
            }
            this.updateCollider(this._colliders[i]);
            if (this.isOutOfScreen(collider.aabb)){
                collider.entity?.onOutScreen?.();
                continue;
            }

            this._tree.insert(this._colliders[i]);
        }

        tempArr.length = 0;
        this._tree.getAllNeedTestColliders(tempArr);
        let collisionState = false;
        for (let k = 0, klen = tempArr.length; k < klen; k++) {
            let _colliders = tempArr[k];
            for (let i = 0, len = _colliders.length; i < len; i++) {
                let icollider = _colliders[i] as FCollider;
                for (let j = i + 1; j < len; j++) {
                    let jcollider = _colliders[j] as FCollider;
                    if (!this.shouldCollide(icollider, jcollider)){
                        continue;
                    }
                    if (icollider.type < jcollider.type) {
                        collisionState = ColliderTriggerFuncs[icollider.type | jcollider.type](icollider, jcollider);
                    } else {
                        collisionState = ColliderTriggerFuncs[jcollider.type | icollider.type](jcollider, icollider);
                    }
                    if (collisionState) {
                        let id = 0, aid = icollider.colliderId, bid = jcollider.colliderId;
                        if (aid < bid) { id = aid; aid = bid; bid = id; }
                        id = (aid * (aid + 1) >> 1) + bid - 1;//NOTE: 康拓配对函数
                        let pairs = this.collisionPairs;
                        let data = pairs.get(id);
                        if (data !== undefined) {
                            data.frameId = this._frameId;
                            data.state = CollisionType.onStay;
                        } else {
                            data = {
                                id: id,
                                colliderA: icollider,
                                colliderB: jcollider,
                                frameId: this._frameId,
                                state: CollisionType.onEnter,
                            }
                            pairs.set(id, data);
                        }
                        this._doCollide(icollider, jcollider, data.state);
                    }
                }
            }
        }
        this.endTrigger();
        this.drawColliders();
        this.drawQuadTree();
        // log("每帧碰撞检测耗时:",Date.now() - timeNow + "毫秒");
    }

    private endTrigger() {
        const curFrameId = this._frameId;
        let len = this.collisionPairs.size;
        let entries = this.collisionPairs.values();
        let waitToDelete = [];
        for (let i = 0; i < len; i++) {
            const value = entries.next().value as IColliderPair;
            let icollider = value.colliderA;
            let jcollider = value.colliderB;
            if (value.frameId !== curFrameId || !jcollider.node || !icollider.node || !jcollider.node.isValid || !icollider.node.isValid) {
                this._doCollide(icollider, jcollider, CollisionType.onExit);
                waitToDelete.push(value.id);
            }
        }
        len = waitToDelete.length - 1;
        while (len >= 0) {
            this.collisionPairs.delete(waitToDelete[len]);
            len--;
        }
        waitToDelete.length = 0;
    }

    /**
     *更新两个碰撞体之前的碰撞信息,延迟到发生碰撞才创建
     *
     * @private
     * @param {*} collider1
     * @param {*} collider2
     * @memberof _ColliderManager
     */

    private _doCollide(collider1: FCollider, collider2: FCollider, type: CollisionType) {
        if (!collider1 || !collider1.node) return;
        //@ts-ignore
        let comps1 = collider1.node._components;
        let comp: Component | undefined;
        for (let i = 0, l = comps1.length; i < l; i++) {
            comp = comps1[i];
            if ((comp as any)[type]) {
                (comp as any)[type](collider2, collider1);
            }
        }
        if (this._onCollisionEnter && type === CollisionType.onEnter) {
            this._onCollisionEnter?.(collider1, collider2);
        }
        else if (this._onCollisionStay && type === CollisionType.onStay) {
            this._onCollisionStay?.(collider1, collider2);
        }
        else if (this._onCollisionExit && type === CollisionType.onExit) {
            this._onCollisionExit?.(collider1, collider2);
        }
    }

    private _enableDebugDraw: boolean = false;
    public get enableDebugDraw(): boolean {
        return this._enableDebugDraw;
    }
    public set enableDebugDraw(value: boolean) {
        if (value && !this._enableDebugDraw) {
            this._checkDebugDrawValid();
            this._debugDrawer!.node.active = true;
        }
        else if (!value && this._enableDebugDraw) {
            this._debugDrawer!.clear();
            this._debugDrawer!.node.active = false;
        }
        this._enableDebugDraw = value;
    }

    private _enableQuadTreeDraw: boolean = false;
    public get enableQuadTreeDraw(): boolean {
        return this._enableQuadTreeDraw;
    }
    public set enableQuadTreeDraw(value: boolean) {
        if (value && !this._enableQuadTreeDraw) {
            this._checkDebugDrawValid();
            this._debugDrawer!.node.active = true;
        }
        else if (!value && this._enableQuadTreeDraw) {
            this._debugDrawer!.clear();
            this._debugDrawer!.node.active = false;
        }
        this._enableQuadTreeDraw = value;
    }

    _debugDrawer: Graphics|null = null;
    private _checkDebugDrawValid() {
        if (!this._debugDrawer || !this._debugDrawer.isValid) {
            let node = new Node('FCOLLISION_MANAGER_DEBUG_DRAW');
            // node.setSli;
            director.addPersistRootNode(node);
            this._debugDrawer = node.addComponent(Graphics);
            this._debugDrawer.lineWidth = 5;
        }
    }
    drawColliders() {
        if (!this._enableDebugDraw) {
            return;
        }
        this._checkDebugDrawValid();

        let debugDrawer = this._debugDrawer;
        debugDrawer!.clear();

        let colliders = this._colliders;

        for (let i = 0, l = colliders.length; i < l; i++) {
            let collider = colliders[i];
            debugDrawer!.strokeColor = Color.RED;
            if (collider.type === ColliderType.Box || collider.type === ColliderType.Polygon) {
                //@ts-ignore
                let ps = collider.worldPoints;
                if (ps.length > 0) {
                    Vec2.set(tempVec2, ps[0].x, ps[0].y);
                    debugDrawer!.moveTo(tempVec2.x, tempVec2.y);
                    for (let j = 1; j < ps.length; j++) {
                        Vec2.set(tempVec2, ps[j].x, ps[j].y);
                        debugDrawer!.lineTo(tempVec2.x, tempVec2.y);
                    }
                    debugDrawer!.close();
                    debugDrawer!.stroke();
                }
            }
            else if (collider.type === ColliderType.Circle) {
                //@ts-ignore
                debugDrawer.circle(collider.worldPosition.x, collider.worldPosition.y, collider.worldRadius);
                // console.log(collider.worldPosition.toString(),collider.worldRadius)
                debugDrawer!.stroke();
            }
        }
    }

    drawQuadTree() {
        if (!this._enableQuadTreeDraw) {
            return;
        }
        this._checkDebugDrawValid();

        let debugDrawer = this._debugDrawer;
        if (!this._enableDebugDraw) debugDrawer!.clear();
        this._tree.render(debugDrawer!);
    }
}
