{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneBase.ts"], "names": ["_decorator", "GameIns", "GameEnum", "PlaneBase", "Bullet", "Plane", "ccclass", "property", "EnemyPlaneBase", "removeAble", "bullets", "die", "destroyType", "to<PERSON><PERSON>", "colliderEnabled", "onDie", "will<PERSON><PERSON><PERSON>", "EnemyDestroyType", "Die", "Leave", "TrackOver", "TimeOver", "onCollide", "collider", "isDead", "entity", "attack", "getAttack", "hurtEffectManager", "createHurtNumByType", "node", "getPosition", "hurt", "_checkRemoveAble", "deltaTime", "addBullet", "bullet", "push", "removeBullet", "index", "indexOf", "splice", "setPos", "x", "y", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AAEEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAGTQ,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,yB,2BAFb,MACqBC,cADrB;AAAA;AAAA,kCACsD;AAAA;AAAA;;AAAA;;AAAA,eAIlDC,UAJkD,GAI7B,KAJ6B;AAAA,eAKlDC,OALkD,GAK9B,EAL8B;AAAA;;AAOlDC,QAAAA,GAAG,CAACC,WAAD,EAAyC;AACxC,cAAI,CAAC,MAAMC,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AACD,eAAKC,eAAL,GAAuB,KAAvB;AAEA,eAAKC,KAAL,CAAWH,WAAX;AACH;;AAEDG,QAAAA,KAAK,CAACH,WAAD,EAAsB;AACvB,eAAKI,UAAL;;AAEA,kBAAQJ,WAAR;AACI,iBAAK;AAAA;AAAA,sCAASK,gBAAT,CAA0BC,GAA/B;AACI;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0BE,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASF,gBAAT,CAA0BG,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAASH,gBAAT,CAA0BI,QAA/B;AACI;AARR;AAUH;;AAGDC,QAAAA,SAAS,CAACC,QAAD,EAAsB;AAC3B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,gBAAID,QAAQ,CAACE,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,oBAAMC,MAAM,GAAGH,QAAQ,CAACE,MAAT,CAAgBE,SAAhB,EAAf;AACA;AAAA;AAAA,sCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8CN,QAAQ,CAACE,MAAT,CAAgBK,IAAhB,CAAqBC,WAArB,EAA9C,EAAkFL,MAAlF;AACA,mBAAKM,IAAL,CAAUN,MAAV;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIV,QAAAA,UAAU,GAAG,CAEZ;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,gBAAgB,CAACC,SAAD,EAAoB;AAChC,eAAKzB,UAAL,GAAkB,IAAlB;AACH;;AAED0B,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAK1B,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa2B,IAAb,CAAkBD,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,MAAD,EAAiB;AACzB,cAAI,KAAK1B,OAAT,EAAkB;AACd,kBAAM6B,KAAK,GAAG,KAAK7B,OAAL,CAAa8B,OAAb,CAAqBJ,MAArB,CAAd;;AACA,gBAAIG,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAK7B,OAAL,CAAa+B,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAEDG,QAAAA,MAAM,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAKd,IAAL,CAAUe,WAAV,CAAsBF,CAAtB,EAAyBC,CAAzB;AACH;;AA9EiD,O;;;;;iBAE5B,I", "sourcesContent": ["import { _decorator} from 'cc';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport PlaneBase from '../PlaneBase';\r\nimport FCollider from '../../../collider-system/FCollider';\r\nimport { Bullet } from '../../../bullet/Bullet';\r\nimport { Plane } from 'db://assets/bundles/common/script/ui/Plane';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlaneBase')\r\nexport default class EnemyPlaneBase extends PlaneBase {\r\n    @property(Plane)//敌机显示组件\r\n    plane: Plane | null = null;\r\n\r\n    removeAble:boolean = false;\r\n    bullets: Bullet[] = [];\r\n\r\n    die(destroyType: GameEnum.EnemyDestroyType) {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        this.onDie(destroyType);\r\n    }\r\n\r\n    onDie(destroyType: number) {\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                // this.playDieAnim();\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                break;\r\n        }\r\n    }\r\n\r\n\r\n    onCollide(collider: FCollider) {\r\n        if (!this.isDead) {\r\n            if (collider.entity instanceof Bullet) {\r\n                const attack = collider.entity.getAttack();\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);\r\n                this.hurt(attack)\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以移除\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _checkRemoveAble(deltaTime: number) {\r\n        this.removeAble = true;\r\n    }\r\n\r\n    addBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    setPos(x: number, y: number) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n}"]}