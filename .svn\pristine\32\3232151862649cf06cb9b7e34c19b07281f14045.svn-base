import { _decorator, Component, EventTouch, Label, Node } from 'cc';
import { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';
import { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logError } from 'db://assets/scripts/utils/Logger';
import { UIMgr } from "db://assets/scripts/ui/UIMgr";
import { PlaneUIEvent } from '../../../../event/PlaneUIEvent';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { TopBlockInputUI } from '../../../common/TopBlockInputUI';
import { PlaneCombineResultUI } from '../../PlaneCombineResultUI';
import { TabStatus } from '../../PlaneTypes';

const { ccclass, property } = _decorator;

@ccclass('CombineDisplay')
export class CombineDisplay extends Component {
    @property(Node)
    resultGrid: Node | null = null
    @property(Node)
    materialGridParentNode: Node | null = null
    @property(Label)
    tip: Label | null = null
    @property(ButtonPlus)
    combineOnceBtn: ButtonPlus | null = null
    @property(ButtonPlus)
    combineAllBtn: ButtonPlus | null = null
    private _tips: string[] = [
        "选择你想合成的装备",
        "还需要2件相同装备",
        "还需要1件相同装备",
        "一切准备就绪!",
    ]

    onLoad(): void {
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)
        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this, 1)
        this.materialGridParentNode!.children.forEach(gridsNode => {
            gridsNode!.getComponentsInChildren(ButtonPlus).forEach(btn => {
                btn.addClick(this.onMatGridClick, this)
            })
        });
        this.combineOnceBtn!.addClick(this.onCombineOnceClick, this)
        this.combineAllBtn!.addClick(this.onCombineAllClick, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this);
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)
    }

    private onTabChange(tabStatus: TabStatus) {
        if (tabStatus == TabStatus.Bag) {
            this.node.active = false;
            return
        }
        this.node.active = true;
        DataMgr.equip.eqCombine.reset();
        this.refreshDisplay();
    }

    private refreshDisplay() {
        let combineSize = Math.max(DataMgr.equip.eqCombine.size(), 1)
        this.materialGridParentNode!.children.forEach(gridsNode => {
            gridsNode.active = false
        })
        const combineGridsNode = this.materialGridParentNode!.children[combineSize - 1]
        combineGridsNode.active = true
        combineGridsNode.getComponentsInChildren(Label).forEach((label, index) => {
            const info = DataMgr.equip.eqCombine.getByPos(index)
            if (info) {
                const equipInfo = MyApp.lubanTables.TbResEquip.get(info.item.item_id!)
                if (equipInfo) {
                    label.string = equipInfo.name + "(品质:" + equipInfo.quality + ")"
                } else {
                    const itemInfo = MyApp.lubanTables.TbResItem.get(info.item.item_id!)
                    if (itemInfo) {
                        label.string = itemInfo.name + "(品质:" + info.item.count + ")"
                    } else {
                        label.string = "未知"
                    }
                }
            } else {
                label.string = "合成材料"
            }
        });
        this.tip!.string = this._tips[DataMgr.equip.eqCombine.currentNum()]
        if (DataMgr.equip.eqCombine.isFull()) {
            const nextLev = DataMgr.equip.eqCombine.getCombineResult()
            if (nextLev) {
                this.resultGrid!.getComponentInChildren(Label)!.string = nextLev.name + "(品质:" + nextLev.quality + ")"
            } else {
                logError("PlaneUI", `cant get merge result no pos1 equip info`)
            }
        } else {
            this.resultGrid!.getComponentInChildren(Label)!.string = "合成结果"
        }
        this.combineOnceBtn!.node.active = DataMgr.equip.eqCombine.isFull();
        this.combineAllBtn!.node.active = !this.combineOnceBtn!.node.active;
    }

    private onBagItemClick(item: csproto.cs.ICSItem) {
        if (!this.node.active) return
        const info = DataMgr.equip.eqCombine.getByGuid(item.guid!)
        if (info) {
            DataMgr.equip.eqCombine.deleteByPos(info.pos)
        } else {
            if (!DataMgr.equip.eqCombine.add(item)) return
        }
        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)
        this.refreshDisplay()
    }

    private onMatGridClick(event: EventTouch) {
        const nd = event.target as Node
        const pos = parseInt(nd.name)
        if (!DataMgr.equip.eqCombine.getByPos(pos)) return
        DataMgr.equip.eqCombine.deleteByPos(pos)
        this.refreshDisplay()
        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)
    }

    private onCombineResultMsg(msg: csproto.cs.IS2CMsg) {
        DataMgr.bag.refreshItems();
        DataMgr.equip.eqCombine.reset();
        this.refreshDisplay()
        UIMgr.hideUI(TopBlockInputUI)
        UIMgr.openUI(PlaneCombineResultUI, msg.body?.equip_combine?.results)
    }

    private onCombineOnceClick() {
        if (!DataMgr.equip.eqCombine.isFull()) return
        UIMgr.openUI(TopBlockInputUI)
        DataMgr.equip.eqCombine.combine();
    }

    private onCombineAllClick() {
        if (DataMgr.equip.eqCombine.isFull()) return
        if (DataMgr.equip.eqCombine.combineAll()) {
            UIMgr.openUI(TopBlockInputUI)
        }
    }
}

