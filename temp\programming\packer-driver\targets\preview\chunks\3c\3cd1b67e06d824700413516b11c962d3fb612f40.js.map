{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts"], "names": ["Intersection", "isOverlay", "proA", "proB", "min", "max", "v2", "Vec2", "temp1arr", "temp2arr", "temp3arr", "temp4arr", "polygonCircle", "polygon", "circle", "position", "worldPosition", "pointInPolygon", "sqrtratiud", "worldRadius", "i", "len", "length", "start", "end", "pointLineDistanceSqr", "point", "isSegment", "dx", "x", "dy", "y", "d", "t", "p", "set", "circleCircle", "a", "b", "subtract", "dissqrt", "lengthSqr", "lineLine", "a1", "a2", "b1", "b2", "ua_t", "ub_t", "u_b", "ua", "ub", "lineRect", "yMax", "xMax", "linePolygon", "rectRect", "a_min_x", "a_min_y", "a_max_x", "width", "a_max_y", "height", "b_min_x", "b_min_y", "b_max_x", "b_max_y", "rectPolygon", "l", "contains", "inside", "j", "xi", "yi", "xj", "yj", "intersect", "getNearestPoint", "center", "rp", "minDis", "squaredDistance", "p2", "sd", "satPolygonPolygon", "aside", "bside", "getProjectionPolygon", "ilen", "asix", "Number", "MAX_SAFE_INTEGER", "ai", "al", "pro", "dot", "Math", "polygonPolygon", "getPoint", "p1", "p3", "p4", "out", "x1", "y1", "x2", "y2", "x3", "y3", "x4", "y4", "isConcavePolygon", "pos", "line", "linep", "push", "preValue", "cross", "current", "next", "currentValue"], "mappings": ";;;iEAuBaA,Y;;AAhBb,WAASC,SAAT,CAAmBC,IAAnB,EAAuDC,IAAvD,EAA2F;AACvF,QAAIC,GAAJ,EAASC,GAAT;;AACA,QAAIH,IAAI,CAACE,GAAL,GAAWD,IAAI,CAACC,GAApB,EAAyB;AACrBA,MAAAA,GAAG,GAAGF,IAAI,CAACE,GAAX;AACH,KAFD,MAEO;AACHA,MAAAA,GAAG,GAAGD,IAAI,CAACC,GAAX;AACH;;AAED,QAAIF,IAAI,CAACG,GAAL,GAAWF,IAAI,CAACE,GAApB,EAAyB;AACrBA,MAAAA,GAAG,GAAGH,IAAI,CAACG,GAAX;AACH,KAFD,MAEO;AACHA,MAAAA,GAAG,GAAGF,IAAI,CAACE,GAAX;AACH;;AAED,WAAQH,IAAI,CAACG,GAAL,GAAWH,IAAI,CAACE,GAAjB,IAAyBD,IAAI,CAACE,GAAL,GAAWF,IAAI,CAACC,GAAzC,IAAgDC,GAAG,GAAGD,GAA7D;AACH;;;;;;;;AAtBoBE,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;;;;;;AAErBC,MAAAA,Q,GAAWF,EAAE,E;AACbG,MAAAA,Q,GAAWH,EAAE,E;AACbI,MAAAA,Q,GAAWJ,EAAE,E;AACbK,MAAAA,Q,GAAWL,EAAE,E;;8BAkBJN,Y,GAAN,MAAMA,YAAN,CAAmB;AACF,eAAbY,aAAa,CAACC,OAAD,EAAkBC,MAAlB,EAAwE;AACxF,cAAIC,QAAQ,GAAGD,MAAM,CAACE,aAAtB;;AACA,cAAIhB,YAAY,CAACiB,cAAb,CAA4BF,QAA5B,EAAsCF,OAAtC,CAAJ,EAAoD;AAChD,mBAAO,IAAP;AACH;;AACD,cAAIK,UAAU,GAAGJ,MAAM,CAACK,WAAP,GAAqBL,MAAM,CAACK,WAA7C;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGR,OAAO,CAACS,MAA9B,EAAsCF,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;AAChD,gBAAIG,KAAK,GAAGH,CAAC,KAAK,CAAN,GAAUP,OAAO,CAACA,OAAO,CAACS,MAAR,GAAiB,CAAlB,CAAjB,GAAwCT,OAAO,CAACO,CAAC,GAAG,CAAL,CAA3D;AACA,gBAAII,GAAG,GAAGX,OAAO,CAACO,CAAD,CAAjB;;AACA,gBAAIpB,YAAY,CAACyB,oBAAb,CAAkCV,QAAlC,EAA4CQ,KAA5C,EAAmDC,GAAnD,EAAwD,IAAxD,IAAgEN,UAApE,EAAgF;AAC5E,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;;AAGiC,eAApBO,oBAAoB,CAACC,KAAD,EAAcH,KAAd,EAA2BC,GAA3B,EAAsCG,SAAtC,EAA0D;AACxF,cAAIC,EAAE,GAAGJ,GAAG,CAACK,CAAJ,GAAQN,KAAK,CAACM,CAAvB;AACA,cAAIC,EAAE,GAAGN,GAAG,CAACO,CAAJ,GAAQR,KAAK,CAACQ,CAAvB;AACA,cAAIC,CAAC,GAAGJ,EAAE,GAAGA,EAAL,GAAUE,EAAE,GAAGA,EAAvB;AACA,cAAIG,CAAC,GAAG,CAAC,CAACP,KAAK,CAACG,CAAN,GAAUN,KAAK,CAACM,CAAjB,IAAsBD,EAAtB,GAA2B,CAACF,KAAK,CAACK,CAAN,GAAUR,KAAK,CAACQ,CAAjB,IAAsBD,EAAlD,IAAwDE,CAAhE;AACA,cAAIE,CAAJ;;AAEA,cAAI,CAACP,SAAL,EAAgB;AACZO,YAAAA,CAAC,GAAG3B,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmBe,KAAK,CAACM,CAAN,GAAUI,CAAC,GAAGL,EAAjC,EAAqCL,KAAK,CAACQ,CAAN,GAAUE,CAAC,GAAGH,EAAnD,CAAJ;AACH,WAFD,MAGK;AACD,gBAAIE,CAAJ,EAAO;AACH,kBAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGX,KAAJ,CAAX,KACK,IAAIU,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGV,GAAJ,CAAX,KACAU,CAAC,GAAG3B,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmBe,KAAK,CAACM,CAAN,GAAUI,CAAC,GAAGL,EAAjC,EAAqCL,KAAK,CAACQ,CAAN,GAAUE,CAAC,GAAGH,EAAnD,CAAJ;AACR,aAJD,MAKK;AACDI,cAAAA,CAAC,GAAGX,KAAJ;AACH;AACJ;;AAEDK,UAAAA,EAAE,GAAGF,KAAK,CAACG,CAAN,GAAUK,CAAC,CAACL,CAAjB;AACAC,UAAAA,EAAE,GAAGJ,KAAK,CAACK,CAAN,GAAUG,CAAC,CAACH,CAAjB;AACA,iBAAOH,EAAE,GAAGA,EAAL,GAAUE,EAAE,GAAGA,EAAtB;AACH;;AAEyB,eAAZM,YAAY,CAACC,CAAD,EAAkDC,CAAlD,EAAmG;AACzH/B,UAAAA,IAAI,CAACgC,QAAL,CAAc5B,QAAd,EAAwB0B,CAAC,CAACrB,aAA1B,EAAyCsB,CAAC,CAACtB,aAA3C;AACA,cAAIwB,OAAO,GAAG7B,QAAQ,CAAC8B,SAAT,EAAd;AACA,iBAAOD,OAAO,GAAG,CAACH,CAAC,CAAClB,WAAF,GAAgBmB,CAAC,CAACnB,WAAnB,KAAmC,CAApD;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AAC0B,eAARuB,QAAQ,CAACC,EAAD,EAAWC,EAAX,EAAqBC,EAArB,EAA+BC,EAA/B,EAAyC;AAC3D,cAAIC,IAAI,GAAG,CAACD,EAAE,CAACjB,CAAH,GAAOgB,EAAE,CAAChB,CAAX,KAAiBc,EAAE,CAACZ,CAAH,GAAOc,EAAE,CAACd,CAA3B,IAAgC,CAACe,EAAE,CAACf,CAAH,GAAOc,EAAE,CAACd,CAAX,KAAiBY,EAAE,CAACd,CAAH,GAAOgB,EAAE,CAAChB,CAA3B,CAA3C;AACA,cAAImB,IAAI,GAAG,CAACJ,EAAE,CAACf,CAAH,GAAOc,EAAE,CAACd,CAAX,KAAiBc,EAAE,CAACZ,CAAH,GAAOc,EAAE,CAACd,CAA3B,IAAgC,CAACa,EAAE,CAACb,CAAH,GAAOY,EAAE,CAACZ,CAAX,KAAiBY,EAAE,CAACd,CAAH,GAAOgB,EAAE,CAAChB,CAA3B,CAA3C;AACA,cAAIoB,GAAG,GAAG,CAACH,EAAE,CAACf,CAAH,GAAOc,EAAE,CAACd,CAAX,KAAiBa,EAAE,CAACf,CAAH,GAAOc,EAAE,CAACd,CAA3B,IAAgC,CAACiB,EAAE,CAACjB,CAAH,GAAOgB,EAAE,CAAChB,CAAX,KAAiBe,EAAE,CAACb,CAAH,GAAOY,EAAE,CAACZ,CAA3B,CAA1C;;AAEA,cAAIkB,GAAG,KAAK,CAAZ,EAAe;AACX,gBAAIC,EAAE,GAAGH,IAAI,GAAGE,GAAhB;AACA,gBAAIE,EAAE,GAAGH,IAAI,GAAGC,GAAhB;;AAEA,gBAAI,KAAKC,EAAL,IAAWA,EAAE,IAAI,CAAjB,IAAsB,KAAKC,EAA3B,IAAiCA,EAAE,IAAI,CAA3C,EAA8C;AAC1C,qBAAO,IAAP;AACH;AACJ;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AAC0B,eAARC,QAAQ,CAACT,EAAD,EAAWC,EAAX,EAAqBN,CAArB,EAA8B;AAChD/B,UAAAA,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmB8B,CAAC,CAACT,CAArB,EAAwBS,CAAC,CAACP,CAA1B;AACAxB,UAAAA,IAAI,CAAC4B,GAAL,CAAS1B,QAAT,EAAmB6B,CAAC,CAACT,CAArB,EAAwBS,CAAC,CAACe,IAA1B;AACA9C,UAAAA,IAAI,CAAC4B,GAAL,CAASzB,QAAT,EAAmB4B,CAAC,CAACgB,IAArB,EAA2BhB,CAAC,CAACe,IAA7B;AACA9C,UAAAA,IAAI,CAAC4B,GAAL,CAASxB,QAAT,EAAmB2B,CAAC,CAACgB,IAArB,EAA2BhB,CAAC,CAACP,CAA7B;;AAEA,cAAI/B,YAAY,CAAC0C,QAAb,CAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BpC,QAA9B,EAAwCC,QAAxC,CAAJ,EAAuD;AACnD,mBAAO,IAAP;AACH;;AACD,cAAIT,YAAY,CAAC0C,QAAb,CAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BnC,QAA9B,EAAwCC,QAAxC,CAAJ,EAAuD;AACnD,mBAAO,IAAP;AACH;;AACD,cAAIV,YAAY,CAAC0C,QAAb,CAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BlC,QAA9B,EAAwCC,QAAxC,CAAJ,EAAuD;AACnD,mBAAO,IAAP;AACH;;AACD,cAAIX,YAAY,CAAC0C,QAAb,CAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BjC,QAA9B,EAAwCH,QAAxC,CAAJ,EAAuD;AACnD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACsB,eAAX+C,WAAW,CAACZ,EAAD,EAAWC,EAAX,EAAqBN,CAArB,EAAgC;AAC9C,cAAIhB,MAAM,GAAGgB,CAAC,CAAChB,MAAf;;AACA,eAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,MAApB,EAA4B,EAAEF,CAA9B,EAAiC;AAC7B,gBAAIyB,EAAE,GAAGP,CAAC,CAAClB,CAAD,CAAV;AACA,gBAAI0B,EAAE,GAAGR,CAAC,CAAC,CAAClB,CAAC,GAAG,CAAL,IAAUE,MAAX,CAAV;AACA,gBAAItB,YAAY,CAAC0C,QAAb,CAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BC,EAA9B,EAAkCC,EAAlC,CAAJ,EACI,OAAO,IAAP;AACP;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACmB,eAARU,QAAQ,CAACnB,CAAD,EAAUC,CAAV,EAAmB;AAC9B,cAAImB,OAAO,GAAGpB,CAAC,CAACR,CAAhB;AACA,cAAI6B,OAAO,GAAGrB,CAAC,CAACN,CAAhB;AACA,cAAI4B,OAAO,GAAGtB,CAAC,CAACR,CAAF,GAAMQ,CAAC,CAACuB,KAAtB;AACA,cAAIC,OAAO,GAAGxB,CAAC,CAACN,CAAF,GAAMM,CAAC,CAACyB,MAAtB;AAEA,cAAIC,OAAO,GAAGzB,CAAC,CAACT,CAAhB;AACA,cAAImC,OAAO,GAAG1B,CAAC,CAACP,CAAhB;AACA,cAAIkC,OAAO,GAAG3B,CAAC,CAACT,CAAF,GAAMS,CAAC,CAACsB,KAAtB;AACA,cAAIM,OAAO,GAAG5B,CAAC,CAACP,CAAF,GAAMO,CAAC,CAACwB,MAAtB;AAEA,iBAAOL,OAAO,IAAIQ,OAAX,IACHN,OAAO,IAAII,OADR,IAEHL,OAAO,IAAIQ,OAFR,IAGHL,OAAO,IAAIG,OAHf;AAKH;;AAEwB,eAAXG,WAAW,CAAC9B,CAAD,EAAUC,CAAV,EAAqB;AAC1C/B,UAAAA,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmB6B,CAAC,CAACR,CAArB,EAAwBQ,CAAC,CAACN,CAA1B;AACAxB,UAAAA,IAAI,CAAC4B,GAAL,CAAS1B,QAAT,EAAmB4B,CAAC,CAACR,CAArB,EAAwBQ,CAAC,CAACgB,IAA1B;AACA9C,UAAAA,IAAI,CAAC4B,GAAL,CAASzB,QAAT,EAAmB2B,CAAC,CAACiB,IAArB,EAA2BjB,CAAC,CAACgB,IAA7B;AACA9C,UAAAA,IAAI,CAAC4B,GAAL,CAASxB,QAAT,EAAmB0B,CAAC,CAACiB,IAArB,EAA2BjB,CAAC,CAACN,CAA7B,EAJ0C,CAM1C;;AACA,cAAI/B,YAAY,CAACuD,WAAb,CAAyB/C,QAAzB,EAAmCC,QAAnC,EAA6C6B,CAA7C,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACuD,WAAb,CAAyB9C,QAAzB,EAAmCC,QAAnC,EAA6C4B,CAA7C,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACuD,WAAb,CAAyB7C,QAAzB,EAAmCC,QAAnC,EAA6C2B,CAA7C,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACuD,WAAb,CAAyB5C,QAAzB,EAAmCH,QAAnC,EAA6C8B,CAA7C,CAAJ,EACI,OAAO,IAAP,CAjBsC,CAmB1C;;AACA,eAAK,IAAIlB,CAAC,GAAG,CAAR,EAAWgD,CAAC,GAAG9B,CAAC,CAAChB,MAAtB,EAA8BF,CAAC,GAAGgD,CAAlC,EAAqC,EAAEhD,CAAvC,EAA0C;AACtC,gBAAIiB,CAAC,CAACgC,QAAF,CAAW/B,CAAC,CAAClB,CAAD,CAAZ,CAAJ,EACI,OAAO,IAAP;AACP,WAvByC,CAyB1C;;;AACA,cAAIpB,YAAY,CAACiB,cAAb,CAA4BT,QAA5B,EAAsC8B,CAAtC,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACiB,cAAb,CAA4BR,QAA5B,EAAsC6B,CAAtC,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACiB,cAAb,CAA4BP,QAA5B,EAAsC4B,CAAtC,CAAJ,EACI,OAAO,IAAP;AAEJ,cAAItC,YAAY,CAACiB,cAAb,CAA4BN,QAA5B,EAAsC2B,CAAtC,CAAJ,EACI,OAAO,IAAP;AAEJ,iBAAO,KAAP;AACH;;AAE2B,eAAdrB,cAAc,CAACS,KAAD,EAAcb,OAAd,EAA+B;AACvD,cAAIyD,MAAM,GAAG,KAAb;AACA,cAAIzC,CAAC,GAAGH,KAAK,CAACG,CAAd;AACA,cAAIE,CAAC,GAAGL,KAAK,CAACK,CAAd,CAHuD,CAKvD;AACA;;AACA,cAAIT,MAAM,GAAGT,OAAO,CAACS,MAArB;;AAEA,eAAK,IAAIF,CAAC,GAAG,CAAR,EAAWmD,CAAC,GAAGjD,MAAM,GAAG,CAA7B,EAAgCF,CAAC,GAAGE,MAApC,EAA4CiD,CAAC,GAAGnD,CAAC,EAAjD,EAAqD;AACjD,gBAAIoD,EAAE,GAAG3D,OAAO,CAACO,CAAD,CAAP,CAAWS,CAApB;AAAA,gBAAuB4C,EAAE,GAAG5D,OAAO,CAACO,CAAD,CAAP,CAAWW,CAAvC;AAAA,gBACI2C,EAAE,GAAG7D,OAAO,CAAC0D,CAAD,CAAP,CAAW1C,CADpB;AAAA,gBACuB8C,EAAE,GAAG9D,OAAO,CAAC0D,CAAD,CAAP,CAAWxC,CADvC;AAAA,gBAEI6C,SAAS,GAAKH,EAAE,GAAG1C,CAAN,KAAc4C,EAAE,GAAG5C,CAApB,IAA4BF,CAAC,GAAG,CAAC6C,EAAE,GAAGF,EAAN,KAAazC,CAAC,GAAG0C,EAAjB,KAAwBE,EAAE,GAAGF,EAA7B,IAAmCD,EAFnF;AAIA,gBAAII,SAAJ,EAAeN,MAAM,GAAG,CAACA,MAAV;AAClB;;AAED,iBAAOA,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACiC,eAAfO,eAAe,CAACC,MAAD,EAAezC,CAAf,EAA0B;AACnD,cAAI0C,EAAE,GAAG1C,CAAC,CAAC,CAAD,CAAV;AAAA,cAAe2C,MAAM,GAAGzE,IAAI,CAAC0E,eAAL,CAAqBH,MAArB,EAA6BC,EAA7B,CAAxB;;AACA,eAAK,IAAI3D,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGgB,CAAC,CAACf,MAAxB,EAAgCF,CAAC,GAAGC,GAApC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,gBAAI8D,EAAE,GAAG7C,CAAC,CAACjB,CAAD,CAAV;AAAA,gBAAe+D,EAAE,GAAG5E,IAAI,CAAC0E,eAAL,CAAqBH,MAArB,EAA6BI,EAA7B,CAApB;;AACA,gBAAIC,EAAE,GAAGH,MAAT,EAAiB;AACbA,cAAAA,MAAM,GAAGG,EAAT;AACAJ,cAAAA,EAAE,GAAGG,EAAL;AACH;AACJ;;AACD,iBAAOH,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACmC,eAAjBK,iBAAiB,CAAC/C,CAAD,EAAYC,CAAZ,EAAuB+C,KAAvB,EAAsCC,KAAtC,EAAqD;AAChF,eAAK,IAAIlE,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGgE,KAAK,CAAC/D,MAA5B,EAAoCF,CAAC,GAAGC,GAAxC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9Cb,YAAAA,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmB6E,KAAK,CAACjE,CAAD,CAAL,CAASW,CAA5B,EAA+B,CAACsD,KAAK,CAACjE,CAAD,CAAL,CAASS,CAAzC;AACA,gBAAI3B,IAAI,GAAGF,YAAY,CAACuF,oBAAb,CAAkClD,CAAlC,EAAqC7B,QAArC,CAAX;AACA,gBAAIL,IAAI,GAAGH,YAAY,CAACuF,oBAAb,CAAkCjD,CAAlC,EAAqC9B,QAArC,CAAX;AACA,gBAAIP,SAAS,CAACC,IAAD,EAAOC,IAAP,CAAb,EAA2B,OAAO,KAAP;AAC9B;;AACD,eAAK,IAAIiB,EAAC,GAAG,CAAR,EAAWoE,IAAI,GAAGF,KAAK,CAAChE,MAA7B,EAAqCF,EAAC,GAAGoE,IAAzC,EAA+CpE,EAAC,EAAhD,EAAoD;AAChDb,YAAAA,IAAI,CAAC4B,GAAL,CAAS3B,QAAT,EAAmB8E,KAAK,CAAClE,EAAD,CAAL,CAASW,CAA5B,EAA+B,CAACuD,KAAK,CAAClE,EAAD,CAAL,CAASS,CAAzC;;AACA,gBAAI3B,KAAI,GAAGF,YAAY,CAACuF,oBAAb,CAAkClD,CAAlC,EAAqC7B,QAArC,CAAX;;AACA,gBAAIL,KAAI,GAAGH,YAAY,CAACuF,oBAAb,CAAkCjD,CAAlC,EAAqC9B,QAArC,CAAX;;AACA,gBAAIP,SAAS,CAACC,KAAD,EAAOC,KAAP,CAAb,EAA2B,OAAO,KAAP;AAC9B;;AACD,iBAAO,IAAP;AACH;;AAEkC,eAApBoF,oBAAoB,CAAClD,CAAD,EAAYoD,IAAZ,EAAuB;AACtD,cAAIrF,GAAG,GAAGsF,MAAM,CAACC,gBAAjB;AAAA,cAAmCtF,GAAG,GAAG,CAACqF,MAAM,CAACC,gBAAjD;;AACA,eAAK,IAAIC,EAAE,GAAG,CAAT,EAAYC,EAAE,GAAGxD,CAAC,CAACf,MAAxB,EAAgCsE,EAAE,GAAGC,EAArC,EAAyCD,EAAE,EAA3C,EAA+C;AAC3C,gBAAIE,GAAG,GAAGzD,CAAC,CAACuD,EAAD,CAAD,CAAMG,GAAN,CAAUN,IAAV,CAAV;AACArF,YAAAA,GAAG,GAAG4F,IAAI,CAAC5F,GAAL,CAASA,GAAT,EAAc0F,GAAd,CAAN;AACAzF,YAAAA,GAAG,GAAG2F,IAAI,CAAC3F,GAAL,CAASA,GAAT,EAAcyF,GAAd,CAAN;AACH;;AACD,iBAAO;AAAE1F,YAAAA,GAAF;AAAOC,YAAAA;AAAP,WAAP;AACH;;AAE2B,eAAd4F,cAAc,CAAC5D,CAAD,EAAYC,CAAZ,EAAuB;AAC/C,cAAIlB,CAAJ,EAAOgD,CAAP,CAD+C,CAG/C;;AACA,eAAKhD,CAAC,GAAG,CAAJ,EAAOgD,CAAC,GAAG/B,CAAC,CAACf,MAAlB,EAA0BF,CAAC,GAAGgD,CAA9B,EAAiC,EAAEhD,CAAnC,EAAsC;AAClC,gBAAIuB,EAAE,GAAGN,CAAC,CAACjB,CAAD,CAAV;AACA,gBAAIwB,EAAE,GAAGP,CAAC,CAAC,CAACjB,CAAC,GAAG,CAAL,IAAUgD,CAAX,CAAV;AAEA,gBAAIpE,YAAY,CAACuD,WAAb,CAAyBZ,EAAzB,EAA6BC,EAA7B,EAAiCN,CAAjC,CAAJ,EACI,OAAO,IAAP;AACP,WAV8C,CAY/C;;;AACA,eAAKlB,CAAC,GAAG,CAAJ,EAAOgD,CAAC,GAAG9B,CAAC,CAAChB,MAAlB,EAA0BF,CAAC,GAAGgD,CAA9B,EAAiC,EAAEhD,CAAnC,EAAsC;AAClC,gBAAIpB,YAAY,CAACiB,cAAb,CAA4BqB,CAAC,CAAClB,CAAD,CAA7B,EAAkCiB,CAAlC,CAAJ,EACI,OAAO,IAAP;AACP,WAhB8C,CAkB/C;;;AACA,eAAKjB,CAAC,GAAG,CAAJ,EAAOgD,CAAC,GAAG/B,CAAC,CAACf,MAAlB,EAA0BF,CAAC,GAAGgD,CAA9B,EAAiC,EAAEhD,CAAnC,EAAsC;AAClC,gBAAIpB,YAAY,CAACiB,cAAb,CAA4BoB,CAAC,CAACjB,CAAD,CAA7B,EAAkCkB,CAAlC,CAAJ,EACI,OAAO,IAAP;AACP;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC0B,eAAR4D,QAAQ,CAACC,EAAD,EAAWjB,EAAX,EAAqBkB,EAArB,EAA+BC,EAA/B,EAAyCC,GAAzC,EAAqD;AACvEA,UAAAA,GAAG,GAAGA,GAAG,IAAIhG,EAAE,EAAf;AACA,cAAIiG,EAAE,GAAGJ,EAAE,CAACtE,CAAZ;AAAA,cAAe2E,EAAE,GAAGL,EAAE,CAACpE,CAAvB;AACA,cAAI0E,EAAE,GAAGvB,EAAE,CAACrD,CAAZ;AAAA,cAAe6E,EAAE,GAAGxB,EAAE,CAACnD,CAAvB;AACA,cAAI4E,EAAE,GAAGP,EAAE,CAACvE,CAAZ;AAAA,cAAe+E,EAAE,GAAGR,EAAE,CAACrE,CAAvB;AACA,cAAI8E,EAAE,GAAGR,EAAE,CAACxE,CAAZ;AAAA,cAAeiF,EAAE,GAAGT,EAAE,CAACtE,CAAvB;AACA,cAAIE,CAAC,GAAG,CAAC,CAACwE,EAAE,GAAGF,EAAN,KAAaK,EAAE,GAAGJ,EAAlB,IAAwB,CAACG,EAAE,GAAGJ,EAAN,KAAaG,EAAE,GAAGF,EAAlB,CAAzB,KAAmD,CAACC,EAAE,GAAGF,EAAN,KAAaK,EAAE,GAAGE,EAAlB,IAAwB,CAACH,EAAE,GAAGE,EAAN,KAAaH,EAAE,GAAGF,EAAlB,CAA3E,CAAR;AACA,iBAAOjG,IAAI,CAAC4B,GAAL,CAASmE,GAAT,EAAcK,EAAE,GAAG1E,CAAC,IAAI4E,EAAE,GAAGF,EAAT,CAApB,EAAkCC,EAAE,GAAG3E,CAAC,IAAI6E,EAAE,GAAGF,EAAT,CAAxC,CAAP;AACH,SA9SqB,CAgTtB;;;AAC8B,eAAhBG,gBAAgB,CAACC,GAAD,EAAc;AACxC,cAAIC,IAAY,GAAG,EAAnB;;AACA,eAAK,IAAI7F,CAAC,GAAG,CAAR,EAAWgD,CAAC,GAAG4C,GAAG,CAAC1F,MAAxB,EAAgCF,CAAC,GAAGgD,CAApC,EAAuC,EAAEhD,CAAzC,EAA4C;AACxC,gBAAIuB,EAAE,GAAGqE,GAAG,CAAC5F,CAAD,CAAZ;AACA,gBAAIwB,EAAE,GAAGoE,GAAG,CAAC,CAAC5F,CAAC,GAAG,CAAL,IAAUgD,CAAX,CAAZ;AACA,gBAAI8C,KAAK,GAAG5G,EAAE,EAAd;AACAC,YAAAA,IAAI,CAACgC,QAAL,CAAc2E,KAAd,EAAqBvE,EAArB,EAAyBC,EAAzB;AACAqE,YAAAA,IAAI,CAACE,IAAL,CAAUD,KAAV;AACH;;AACD,cAAIE,QAAQ,GAAGH,IAAI,CAAC,CAAD,CAAJ,CAAQI,KAAR,CAAcJ,IAAI,CAAC,CAAD,CAAlB,KAA0B,CAA1B,GAA8B,CAA9B,GAAkC,CAAC,CAAlD;AAEA,cAAIK,OAAJ,EAAkBC,IAAlB;AACA,cAAIlG,GAAG,GAAG4F,IAAI,CAAC3F,MAAf;;AACA,eAAK,IAAIF,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG6F,IAAI,CAAC3F,MAAzB,EAAiCF,GAAC,EAAlC,EAAsC;AAClCkG,YAAAA,OAAO,GAAGL,IAAI,CAAC7F,GAAD,CAAd;AACAmG,YAAAA,IAAI,GAAGN,IAAI,CAAC,CAAC7F,GAAC,GAAG,CAAL,IAAUC,GAAX,CAAX;AACA,gBAAImG,YAAY,GAAGF,OAAO,CAACD,KAAR,CAAcE,IAAd,KAAuB,CAAvB,GAA2B,CAA3B,GAA+B,CAAC,CAAnD;AACA,gBAAIH,QAAQ,IAAII,YAAhB,EAA8B,OAAO,IAAP;AAC9BJ,YAAAA,QAAQ,GAAGI,YAAX;AACH;;AACD,iBAAO,KAAP;AACH;;AAtUqB,O", "sourcesContent": ["import { _decorator, v2, Vec2, Rect } from 'cc';\n\nlet temp1arr = v2();\nlet temp2arr = v2();\nlet temp3arr = v2();\nlet temp4arr = v2();\n\nfunction isOverlay(proA: { min: number, max: number }, proB: { min: number, max: number }) {\n    let min, max;\n    if (proA.min < proB.min) {\n        min = proA.min;\n    } else {\n        min = proB.min;\n    }\n\n    if (proA.max > proB.max) {\n        max = proA.max;\n    } else {\n        max = proB.max;\n    }\n\n    return (proA.max - proA.min) + (proB.max - proB.min) < max - min;\n}\nexport class Intersection {\n    static polygonCircle(polygon: Vec2[], circle: { worldPosition: Vec2, worldRadius: number }) {\n        let position = circle.worldPosition;\n        if (Intersection.pointInPolygon(position, polygon)) {\n            return true;\n        }\n        let sqrtratiud = circle.worldRadius * circle.worldRadius;\n        for (let i = 0, len = polygon.length; i < len; i++) {\n            let start = i === 0 ? polygon[polygon.length - 1] : polygon[i - 1];\n            let end = polygon[i];\n            if (Intersection.pointLineDistanceSqr(position, start, end, true) < sqrtratiud) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n\n    public static pointLineDistanceSqr(point: Vec2, start: Vec2, end: Vec2, isSegment: boolean) {\n        let dx = end.x - start.x;\n        let dy = end.y - start.y;\n        let d = dx * dx + dy * dy;\n        let t = ((point.x - start.x) * dx + (point.y - start.y) * dy) / d;\n        let p;\n\n        if (!isSegment) {\n            p = Vec2.set(temp1arr, start.x + t * dx, start.y + t * dy);\n        }\n        else {\n            if (d) {\n                if (t < 0) p = start;\n                else if (t > 1) p = end;\n                else p = Vec2.set(temp1arr, start.x + t * dx, start.y + t * dy);\n            }\n            else {\n                p = start;\n            }\n        }\n\n        dx = point.x - p.x;\n        dy = point.y - p.y;\n        return dx * dx + dy * dy;\n    }\n\n    public static circleCircle(a: { worldPosition: Vec2, worldRadius: number }, b: { worldPosition: Vec2, worldRadius: number }) {\n        Vec2.subtract(temp4arr, a.worldPosition, b.worldPosition);\n        let dissqrt = temp4arr.lengthSqr();\n        return dissqrt < (a.worldRadius + b.worldRadius) ** 2;\n    }\n\n    /**\n     * 线段与线段相交\n     * @param  {Vec2} a1\n     * @param  {Vec2} a2\n     * @param  {Vec2} b1\n     * @param  {Vec2} b2\n     */\n    public static lineLine(a1: Vec2, a2: Vec2, b1: Vec2, b2: Vec2) {\n        let ua_t = (b2.x - b1.x) * (a1.y - b1.y) - (b2.y - b1.y) * (a1.x - b1.x);\n        let ub_t = (a2.x - a1.x) * (a1.y - b1.y) - (a2.y - a1.y) * (a1.x - b1.x);\n        let u_b = (b2.y - b1.y) * (a2.x - a1.x) - (b2.x - b1.x) * (a2.y - a1.y);\n\n        if (u_b !== 0) {\n            let ua = ua_t / u_b;\n            let ub = ub_t / u_b;\n\n            if (0 <= ua && ua <= 1 && 0 <= ub && ub <= 1) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * 线段与矩形相交\n     * @param  {Vec2} a1\n     * @param  {Vec2} a2\n     * @param  {Rect} b\n     */\n    public static lineRect(a1: Vec2, a2: Vec2, b: Rect) {\n        Vec2.set(temp1arr, b.x, b.y);\n        Vec2.set(temp2arr, b.x, b.yMax);\n        Vec2.set(temp3arr, b.xMax, b.yMax);\n        Vec2.set(temp4arr, b.xMax, b.y);\n\n        if (Intersection.lineLine(a1, a2, temp1arr, temp2arr)) {\n            return true;\n        }\n        if (Intersection.lineLine(a1, a2, temp2arr, temp3arr)) {\n            return true;\n        }\n        if (Intersection.lineLine(a1, a2, temp3arr, temp4arr)) {\n            return true;\n        }\n        if (Intersection.lineLine(a1, a2, temp4arr, temp1arr)) {\n            return true;\n        }\n        return false;\n    }\n\n    /**\n     * 线段与多边形相交\n     * @param  {Vec2} a1\n     * @param  {Vec2} a2\n     * @param  {Vec2[]} b\n     */\n    static linePolygon(a1: Vec2, a2: Vec2, b: Vec2[]) {\n        let length = b.length;\n        for (let i = 0; i < length; ++i) {\n            let b1 = b[i];\n            let b2 = b[(i + 1) % length];\n            if (Intersection.lineLine(a1, a2, b1, b2))\n                return true;\n        }\n        return false;\n    }\n\n    /**\n     * !#en Test rect and rect\n     * !#zh 测试矩形与矩形是否相交\n     * @method rectRect\n     * @param {Rect} a - The first rect\n     * @param {Rect} b - The second rect\n     * @return {boolean}\n     */\n    static rectRect(a: Rect, b: Rect) {\n        let a_min_x = a.x;\n        let a_min_y = a.y;\n        let a_max_x = a.x + a.width;\n        let a_max_y = a.y + a.height;\n\n        let b_min_x = b.x;\n        let b_min_y = b.y;\n        let b_max_x = b.x + b.width;\n        let b_max_y = b.y + b.height;\n\n        return a_min_x <= b_max_x &&\n            a_max_x >= b_min_x &&\n            a_min_y <= b_max_y &&\n            a_max_y >= b_min_y\n            ;\n    }\n\n    public static rectPolygon(a: Rect, b: Vec2[]) {\n        Vec2.set(temp1arr, a.x, a.y);\n        Vec2.set(temp2arr, a.x, a.yMax);\n        Vec2.set(temp3arr, a.xMax, a.yMax);\n        Vec2.set(temp4arr, a.xMax, a.y);\n\n        // intersection check\n        if (Intersection.linePolygon(temp1arr, temp2arr, b))\n            return true;\n\n        if (Intersection.linePolygon(temp2arr, temp3arr, b))\n            return true;\n\n        if (Intersection.linePolygon(temp3arr, temp4arr, b))\n            return true;\n\n        if (Intersection.linePolygon(temp4arr, temp1arr, b))\n            return true;\n\n        // check if a contains b\n        for (let i = 0, l = b.length; i < l; ++i) {\n            if (a.contains(b[i]))\n                return true;\n        }\n\n        // check if b contains a\n        if (Intersection.pointInPolygon(temp1arr, b))\n            return true;\n\n        if (Intersection.pointInPolygon(temp2arr, b))\n            return true;\n\n        if (Intersection.pointInPolygon(temp3arr, b))\n            return true;\n\n        if (Intersection.pointInPolygon(temp4arr, b))\n            return true;\n\n        return false;\n    }\n\n    public static pointInPolygon(point: Vec2, polygon: Vec2[]) {\n        let inside = false;\n        let x = point.x;\n        let y = point.y;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        let length = polygon.length;\n\n        for (let i = 0, j = length - 1; i < length; j = i++) {\n            let xi = polygon[i].x, yi = polygon[i].y,\n                xj = polygon[j].x, yj = polygon[j].y,\n                intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n\n            if (intersect) inside = !inside;\n        }\n\n        return inside;\n    }\n\n    /**\n     * 求圆心与多边形最近点\n     * @param center \n     * @param a \n     */\n    public static getNearestPoint(center: Vec2, a: Vec2[]) {\n        let rp = a[0], minDis = Vec2.squaredDistance(center, rp);\n        for (let i = 1, len = a.length; i < len; i++) {\n            let p2 = a[i], sd = Vec2.squaredDistance(center, p2);\n            if (sd < minDis) {\n                minDis = sd;\n                rp = p2;\n            }\n        }\n        return rp;\n    }\n\n    /**\n     *分离轴算法，只适合凸多边形\n     *\n     * @static\n     * @param {Vec2[]} a\n     * @param {Vec2[]} b\n     * @param {Vec2[]} aside\n     * @param {Vec2[]} bside\n     * @return {*} \n     * @memberof Intersection\n     */\n    public static satPolygonPolygon(a: Vec2[], b: Vec2[], aside: Vec2[], bside: Vec2[]) {\n        for (let i = 0, len = aside.length; i < len; i++) {\n            Vec2.set(temp1arr, aside[i].y, -aside[i].x);\n            let proA = Intersection.getProjectionPolygon(a, temp1arr);\n            let proB = Intersection.getProjectionPolygon(b, temp1arr);\n            if (isOverlay(proA, proB)) return false;\n        }\n        for (let i = 0, ilen = bside.length; i < ilen; i++) {\n            Vec2.set(temp1arr, bside[i].y, -bside[i].x);\n            let proA = Intersection.getProjectionPolygon(a, temp1arr);\n            let proB = Intersection.getProjectionPolygon(b, temp1arr);\n            if (isOverlay(proA, proB)) return false;\n        }\n        return true;\n    }\n\n    private static getProjectionPolygon(a: Vec2[], asix:Vec2) {\n        let min = Number.MAX_SAFE_INTEGER, max = -Number.MAX_SAFE_INTEGER;\n        for (let ai = 0, al = a.length; ai < al; ai++) {\n            let pro = a[ai].dot(asix);\n            min = Math.min(min, pro);\n            max = Math.max(max, pro);\n        }\n        return { min, max };\n    }\n\n    public static polygonPolygon(a: Vec2[], b: Vec2[]) {\n        let i, l;\n\n        // check if a intersects b\n        for (i = 0, l = a.length; i < l; ++i) {\n            let a1 = a[i];\n            let a2 = a[(i + 1) % l];\n\n            if (Intersection.linePolygon(a1, a2, b))\n                return true;\n        }\n\n        // check if a contains b\n        for (i = 0, l = b.length; i < l; ++i) {\n            if (Intersection.pointInPolygon(b[i], a))\n                return true;\n        }\n\n        // check if b contains a\n        for (i = 0, l = a.length; i < l; ++i) {\n            if (Intersection.pointInPolygon(a[i], b))\n                return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * 获取线段相交的点\n     * @param  {Vec2} p1\n     * @param  {Vec2} p2\n     * @param  {Vec2} p3\n     * @param  {Vec2} p4\n     * @param  {Vec2} out?\n     */\n    public static getPoint(p1: Vec2, p2: Vec2, p3: Vec2, p4: Vec2, out?: Vec2) {\n        out = out || v2();\n        let x1 = p1.x, y1 = p1.y;\n        let x2 = p2.x, y2 = p2.y;\n        let x3 = p3.x, y3 = p3.y;\n        let x4 = p4.x, y4 = p4.y;\n        let t = ((x2 - x1) * (y3 - y1) - (x3 - x1) * (y2 - y1)) / ((x2 - x1) * (y3 - y4) - (x3 - x4) * (y2 - y1));\n        return Vec2.set(out, x3 + t * (x4 - x3), y3 + t * (y4 - y3));\n    }\n\n    //判断是否为凹多边形状\n    public static isConcavePolygon(pos: Vec2[]) {\n        let line: Vec2[] = [];\n        for (let i = 0, l = pos.length; i < l; ++i) {\n            let a1 = pos[i];\n            let a2 = pos[(i + 1) % l];\n            let linep = v2();\n            Vec2.subtract(linep, a1, a2);\n            line.push(linep);\n        }\n        let preValue = line[0].cross(line[1]) >= 0 ? 1 : -1;\n\n        let current:Vec2, next:Vec2;\n        let len = line.length;\n        for (let i = 1; i < line.length; i++) {\n            current = line[i];\n            next = line[(i + 1) % len];\n            let currentValue = current.cross(next) >= 0 ? 1 : -1;\n            if (preValue != currentValue) return true;\n            preValue = currentValue;\n        }\n        return false;\n    }\n}\n"]}