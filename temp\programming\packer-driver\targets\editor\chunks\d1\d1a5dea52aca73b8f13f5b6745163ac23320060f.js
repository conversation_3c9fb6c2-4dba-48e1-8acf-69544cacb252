System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, MyApp, error, AttributeData, AttributeConst, EnemyData, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemy(extras) {
    _reporterNs.report("Enemy", "../../autogen/luban/schema", _context.meta, extras);
  }

  _export("EnemyData", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      AttributeData = _unresolved_3.AttributeData;
    }, function (_unresolved_4) {
      AttributeConst = _unresolved_4.AttributeConst;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['error']);

      _export("EnemyData", EnemyData = class EnemyData extends (_crd && AttributeData === void 0 ? (_reportPossibleCrUseOfAttributeData({
        error: Error()
      }), AttributeData) : AttributeData) {
        constructor(...args) {
          super(...args);
          this._planeId = 0;
          //飞机id
          this.config = null;
        }

        //飞机静态配置
        get planeId() {
          return this._planeId;
        }

        set planeId(value) {
          if (value != this._planeId) {
            this._planeId = value;
            this.config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbEnemy.get(this._planeId);
            this.updateData();
          }
        }

        get recourseSpine() {
          if (!this.config) {
            return "";
          }
        }

        updateData() {
          if (!this.config) {
            error("enemyPlane lv config is null, cannot update attributes.");
            return;
          }

          this.setBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHP, this.config.hp);
          this.setBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).Attack, this.config.atk);
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=d1a5dea52aca73b8f13f5b6745163ac23320060f.js.map