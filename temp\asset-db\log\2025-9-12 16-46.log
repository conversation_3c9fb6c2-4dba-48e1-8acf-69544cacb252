2025-9-12 16:46:22-debug: start **** info
2025-9-12 16:46:23-log: Cannot access game frame or container.
2025-9-12 16:46:23-debug: asset-db:require-engine-code (393ms)
2025-9-12 16:46:23-log: meshopt wasm decoder initialized
2025-9-12 16:46:23-log: [bullet]:bullet wasm lib loaded.
2025-9-12 16:46:23-log: [box2d]:box2d wasm lib loaded.
2025-9-12 16:46:23-log: Cocos Creator v3.8.6
2025-9-12 16:46:23-log: Using legacy pipeline
2025-9-12 16:46:23-log: Forward render pipeline initialized.
2025-9-12 16:46:23-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.84MB, end 80.06MB, increase: 49.22MB
2025-9-12 16:46:23-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.97MB, end 84.31MB, increase: 3.34MB
2025-9-12 16:46:24-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.35MB, end 290.60MB, increase: 206.25MB
2025-9-12 16:46:24-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.82MB, end 288.90MB, increase: 208.09MB
2025-9-12 16:46:24-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 288.93MB, increase: 208.85MB
2025-9-12 16:46:24-debug: run package(honor-mini-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(huawei-agc) handler(enable) success!
2025-9-12 16:46:24-debug: run package(honor-mini-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(huawei-quick-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(huawei-agc) handler(enable) start
2025-9-12 16:46:24-debug: run package(ios) handler(enable) start
2025-9-12 16:46:24-debug: run package(linux) handler(enable) start
2025-9-12 16:46:24-debug: run package(ios) handler(enable) success!
2025-9-12 16:46:24-debug: run package(mac) handler(enable) start
2025-9-12 16:46:24-debug: run package(linux) handler(enable) success!
2025-9-12 16:46:24-debug: run package(mac) handler(enable) success!
2025-9-12 16:46:24-debug: run package(native) handler(enable) success!
2025-9-12 16:46:24-debug: run package(migu-mini-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(native) handler(enable) start
2025-9-12 16:46:24-debug: run package(migu-mini-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(ohos) handler(enable) start
2025-9-12 16:46:24-debug: run package(ohos) handler(enable) success!
2025-9-12 16:46:24-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-12 16:46:24-debug: run package(oppo-mini-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-12 16:46:24-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(taobao-mini-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(web-desktop) handler(enable) start
2025-9-12 16:46:24-debug: run package(vivo-mini-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(web-desktop) handler(enable) success!
2025-9-12 16:46:24-debug: run package(web-mobile) handler(enable) start
2025-9-12 16:46:24-debug: run package(web-mobile) handler(enable) success!
2025-9-12 16:46:24-debug: run package(wechatprogram) handler(enable) start
2025-9-12 16:46:24-debug: run package(wechatprogram) handler(enable) success!
2025-9-12 16:46:24-debug: run package(wechatgame) handler(enable) start
2025-9-12 16:46:24-debug: run package(wechatgame) handler(enable) success!
2025-9-12 16:46:24-debug: run package(windows) handler(enable) start
2025-9-12 16:46:24-debug: run package(windows) handler(enable) success!
2025-9-12 16:46:24-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-12 16:46:24-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-12 16:46:24-debug: run package(cocos-service) handler(enable) success!
2025-9-12 16:46:24-debug: run package(im-plugin) handler(enable) success!
2025-9-12 16:46:24-debug: run package(cocos-service) handler(enable) start
2025-9-12 16:46:24-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-12 16:46:24-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-12 16:46:24-debug: run package(im-plugin) handler(enable) start
2025-9-12 16:46:24-debug: run package(emitter-editor) handler(enable) start
2025-9-12 16:46:24-debug: run package(level-editor) handler(enable) success!
2025-9-12 16:46:24-debug: run package(level-editor) handler(enable) start
2025-9-12 16:46:24-debug: run package(localization-editor) handler(enable) start
2025-9-12 16:46:24-debug: run package(localization-editor) handler(enable) success!
2025-9-12 16:46:24-debug: run package(emitter-editor) handler(enable) success!
2025-9-12 16:46:24-debug: asset-db:worker-init: initPlugin (1093ms)
2025-9-12 16:46:24-debug: run package(wave-editor) handler(enable) start
2025-9-12 16:46:24-debug: run package(wave-editor) handler(enable) success!
2025-9-12 16:46:24-debug: [Assets Memory track]: asset-db:worker-init start:30.83MB, end 298.51MB, increase: 267.68MB
2025-9-12 16:46:24-debug: Run asset db hook programming:beforePreStart success!
2025-9-12 16:46:24-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-12 16:46:24-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-12 16:46:24-debug: Run asset db hook programming:beforePreStart ...
2025-9-12 16:46:24-debug: run package(placeholder) handler(enable) success!
2025-9-12 16:46:24-debug: run package(placeholder) handler(enable) start
2025-9-12 16:46:24-debug: asset-db:worker-init (1626ms)
2025-9-12 16:46:24-debug: asset-db-hook-programming-beforePreStart (50ms)
2025-9-12 16:46:24-debug: asset-db-hook-engine-extends-beforePreStart (50ms)
2025-9-12 16:46:24-debug: Preimport db internal success
2025-9-12 16:46:24-debug: Preimport db assets success
2025-9-12 16:46:24-debug: Run asset db hook programming:afterPreStart ...
2025-9-12 16:46:24-debug: starting packer-driver...
2025-9-12 16:46:29-debug: initialize scripting environment...
2025-9-12 16:46:29-debug: [[Executor]] prepare before lock
2025-9-12 16:46:29-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-12 16:46:29-debug: [[Executor]] prepare after unlock
2025-9-12 16:46:29-debug: Run asset db hook programming:afterPreStart success!
2025-9-12 16:46:29-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-12 16:46:29-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-12 16:46:29-debug: Start up the 'internal' database...
2025-9-12 16:46:29-debug: asset-db-hook-programming-afterPreStart (5374ms)
2025-9-12 16:46:29-debug: asset-db:worker-effect-data-processing (211ms)
2025-9-12 16:46:29-debug: asset-db-hook-engine-extends-afterPreStart (211ms)
2025-9-12 16:46:30-debug: Start up the 'assets' database...
2025-9-12 16:46:30-debug: asset-db:worker-startup-database[internal] (5592ms)
2025-9-12 16:46:30-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.45MB, end 194.89MB, increase: 19.44MB
2025-9-12 16:46:30-debug: lazy register asset handler text
2025-9-12 16:46:30-debug: lazy register asset handler *
2025-9-12 16:46:30-debug: lazy register asset handler directory
2025-9-12 16:46:30-debug: lazy register asset handler spine-data
2025-9-12 16:46:30-debug: lazy register asset handler json
2025-9-12 16:46:30-debug: lazy register asset handler terrain
2025-9-12 16:46:30-debug: lazy register asset handler javascript
2025-9-12 16:46:30-debug: lazy register asset handler typescript
2025-9-12 16:46:30-debug: lazy register asset handler scene
2025-9-12 16:46:30-debug: lazy register asset handler dragonbones
2025-9-12 16:46:30-debug: lazy register asset handler prefab
2025-9-12 16:46:30-debug: lazy register asset handler buffer
2025-9-12 16:46:30-debug: lazy register asset handler sprite-frame
2025-9-12 16:46:30-debug: lazy register asset handler image
2025-9-12 16:46:30-debug: lazy register asset handler dragonbones-atlas
2025-9-12 16:46:30-debug: lazy register asset handler sign-image
2025-9-12 16:46:30-debug: lazy register asset handler tiled-map
2025-9-12 16:46:30-debug: lazy register asset handler alpha-image
2025-9-12 16:46:30-debug: lazy register asset handler texture-cube
2025-9-12 16:46:30-debug: lazy register asset handler render-texture
2025-9-12 16:46:30-debug: lazy register asset handler erp-texture-cube
2025-9-12 16:46:30-debug: lazy register asset handler texture-cube-face
2025-9-12 16:46:30-debug: lazy register asset handler rt-sprite-frame
2025-9-12 16:46:30-debug: lazy register asset handler gltf-mesh
2025-9-12 16:46:30-debug: lazy register asset handler gltf
2025-9-12 16:46:30-debug: lazy register asset handler texture
2025-9-12 16:46:30-debug: lazy register asset handler gltf-skeleton
2025-9-12 16:46:30-debug: lazy register asset handler gltf-scene
2025-9-12 16:46:30-debug: lazy register asset handler gltf-embeded-image
2025-9-12 16:46:30-debug: lazy register asset handler gltf-animation
2025-9-12 16:46:30-debug: lazy register asset handler fbx
2025-9-12 16:46:30-debug: lazy register asset handler gltf-material
2025-9-12 16:46:30-debug: lazy register asset handler physics-material
2025-9-12 16:46:30-debug: lazy register asset handler material
2025-9-12 16:46:30-debug: lazy register asset handler effect
2025-9-12 16:46:30-debug: lazy register asset handler effect-header
2025-9-12 16:46:30-debug: lazy register asset handler animation-clip
2025-9-12 16:46:30-debug: lazy register asset handler audio-clip
2025-9-12 16:46:30-debug: lazy register asset handler animation-graph
2025-9-12 16:46:30-debug: lazy register asset handler animation-graph-variant
2025-9-12 16:46:30-debug: lazy register asset handler ttf-font
2025-9-12 16:46:30-debug: lazy register asset handler bitmap-font
2025-9-12 16:46:30-debug: lazy register asset handler animation-mask
2025-9-12 16:46:30-debug: lazy register asset handler sprite-atlas
2025-9-12 16:46:30-debug: lazy register asset handler particle
2025-9-12 16:46:30-debug: lazy register asset handler auto-atlas
2025-9-12 16:46:30-debug: lazy register asset handler render-pipeline
2025-9-12 16:46:30-debug: lazy register asset handler instantiation-material
2025-9-12 16:46:30-debug: lazy register asset handler label-atlas
2025-9-12 16:46:30-debug: lazy register asset handler instantiation-mesh
2025-9-12 16:46:30-debug: lazy register asset handler instantiation-skeleton
2025-9-12 16:46:30-debug: lazy register asset handler instantiation-animation
2025-9-12 16:46:30-debug: lazy register asset handler render-flow
2025-9-12 16:46:30-debug: lazy register asset handler render-stage
2025-9-12 16:46:30-debug: lazy register asset handler video-clip
2025-9-12 16:46:30-debug: asset-db:worker-startup-database[assets] (5602ms)
2025-9-12 16:46:30-debug: asset-db:start-database (5674ms)
2025-9-12 16:46:30-debug: asset-db:ready (9061ms)
2025-9-12 16:46:30-debug: fix the bug of updateDefaultUserData
2025-9-12 16:46:30-debug: init worker message success
2025-9-12 16:46:30-debug: programming:execute-script (5ms)
2025-9-12 16:46:30-debug: [Build Memory track]: builder:worker-init start:192.52MB, end 210.94MB, increase: 18.42MB
2025-9-12 16:46:30-debug: builder:worker-init (275ms)
2025-9-12 16:47:06-debug: refresh db internal success
2025-9-12 16:47:06-debug: refresh db assets success
2025-9-12 16:47:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 16:47:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 16:47:06-debug: asset-db:refresh-all-database (136ms)
2025-9-12 16:47:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 16:47:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-12 16:47:19-debug: refresh db internal success
2025-9-12 16:47:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 16:47:19-debug: refresh db assets success
2025-9-12 16:47:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 16:47:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 16:47:19-debug: asset-db:refresh-all-database (129ms)
2025-9-12 16:47:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-12 16:50:17-debug: refresh db internal success
2025-9-12 16:50:17-debug: refresh db assets success
2025-9-12 16:50:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 16:50:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 16:50:17-debug: asset-db:refresh-all-database (157ms)
2025-9-12 16:50:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-12 16:50:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
