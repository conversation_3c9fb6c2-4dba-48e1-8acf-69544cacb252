{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts"], "names": ["DYTools", "v2", "Vec2", "misc", "GameConst", "log", "message", "args", "console", "error", "warn", "random_int", "min", "max", "random", "Math", "floor", "getRandomInArray", "array", "remove", "length", "index", "element", "splice", "stringToPoint", "str", "delimiter", "parts", "split", "Number", "ZERO", "stringToNumber", "map", "part", "filter", "num", "isNaN", "arr<PERSON><PERSON><PERSON>", "indexOf", "arrC<PERSON>ain", "addScript", "node", "script", "getComponent", "addComponent", "removeChildByName", "parent", "name", "child", "getChildByName", "destroy", "isPlaneOutScreen", "position", "viewCenterX", "ViewCenter", "x", "viewHeight", "ViewHeight", "y", "getBezier", "p0", "p1", "p2", "p3", "t", "pow", "getStraight", "start", "direction", "distance", "normalizedDir", "subtract", "normalize", "add", "multiplyScalar", "getStraightForDir", "getAngle", "end", "dx", "dy", "sqrt", "angle", "asin", "radiansToDegrees", "getPositionByAngle", "point", "radius", "radian", "atan2", "degreesToRadians", "cos", "sin", "getDir", "x1", "y1", "x2", "y2", "getDegreeForDir", "dir", "reference", "equals", "signAngle", "clearMapForCompArr", "for<PERSON>ach", "compA<PERSON>y", "comp", "clear", "Tools"], "mappings": ";;;+<PERSON><PERSON><PERSON>,O;;;;;;;;;;;;AAHwBC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACrCC,MAAAA,S,iBAAAA,S;;;;;;;AAEHJ,MAAAA,O,GAAN,MAAMA,OAAN,CAAc;AACV;AACJ;AACA;AACIK,QAAAA,GAAG,CAACC,OAAD,EAAkB,GAAGC,IAArB,EAAkC;AACjCC,UAAAA,OAAO,CAACH,GAAR,CAAYC,OAAZ,EAAqB,GAAGC,IAAxB;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,KAAK,CAACH,OAAD,EAAkB,GAAGC,IAArB,EAAkC;AACnCC,UAAAA,OAAO,CAACC,KAAR,CAAcH,OAAd,EAAuB,GAAGC,IAA1B;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,IAAI,CAACJ,OAAD,EAAkB,GAAGC,IAArB,EAAkC;AAClCC,UAAAA,OAAO,CAACE,IAAR,CAAaJ,OAAb,EAAsB,GAAGC,IAAzB;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,UAAU,CAACC,GAAD,EAAcC,GAAd,EAAmC;AACzC,gBAAMC,MAAM,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACD,MAAL,MAAiBD,GAAG,GAAGD,GAAN,GAAY,CAA7B,CAAX,IAA8CA,GAA7D;AACA,iBAAOG,IAAI,CAACF,GAAL,CAASD,GAAT,EAAcG,IAAI,CAACH,GAAL,CAASC,GAAT,EAAcC,MAAd,CAAd,CAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,gBAAgB,CAAIC,KAAJ,EAAgBC,MAAe,GAAG,KAAlC,EAAmD;AAC/D,gBAAMC,MAAM,GAAGF,KAAK,CAACE,MAArB;AACA,cAAIA,MAAM,KAAK,CAAf,EAAkB,OAAO,IAAP;AAElB,gBAAMC,KAAK,GAAG,KAAKV,UAAL,CAAgB,CAAhB,EAAmBS,MAAM,GAAG,CAA5B,CAAd;AACA,gBAAME,OAAO,GAAGJ,KAAK,CAACG,KAAD,CAArB;;AAEA,cAAIF,MAAJ,EAAY;AACRD,YAAAA,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACH;;AAED,iBAAOC,OAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,aAAa,CAACC,GAAD,EAAcC,SAAd,EAAuC;AAChD,gBAAMC,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;;AACA,cAAIC,KAAK,CAACP,MAAN,GAAe,CAAnB,EAAsB;AAClB,mBAAOnB,EAAE,CAAC4B,MAAM,CAACF,KAAK,CAAC,CAAD,CAAN,CAAP,EAAmBE,MAAM,CAACF,KAAK,CAAC,CAAD,CAAN,CAAzB,CAAT;AACH;;AACD,iBAAOzB,IAAI,CAAC4B,IAAZ;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACN,GAAD,EAAcC,SAAd,EAA2C;AACrD,gBAAMC,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;AACA,iBAAOC,KAAK,CAACK,GAAN,CAAWC,IAAD,IAAUJ,MAAM,CAACI,IAAD,CAA1B,EAAkCC,MAAlC,CAA0CC,GAAD,IAAS,CAACC,KAAK,CAACD,GAAD,CAAxD,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,SAAS,CAAInB,KAAJ,EAAgBI,OAAhB,EAAqC;AAC1C,gBAAMD,KAAK,GAAGH,KAAK,CAACoB,OAAN,CAAchB,OAAd,CAAd;;AACA,cAAID,KAAK,IAAI,CAAb,EAAgB;AACZH,YAAAA,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIkB,QAAAA,UAAU,CAAIrB,KAAJ,EAAgBI,OAAhB,EAAqC;AAC3C,iBAAOJ,KAAK,CAACoB,OAAN,CAAchB,OAAd,KAA0B,CAAC,CAAlC;AAAoC;AACvC;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIkB,QAAAA,SAAS,CAAsBC,IAAtB,EAAkCC,MAAlC,EAAkE;AACvE,cAAI,CAACD,IAAD,IAAS,CAACC,MAAd,EAAsB,OAAO,IAAP;AACtB,iBAAOD,IAAI,CAACE,YAAL,CAAkBD,MAAlB,KAA6BD,IAAI,CAACG,YAAL,CAAkBF,MAAlB,CAApC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,MAAD,EAAeC,IAAf,EAA6B;AAC1C,cAAI,CAACD,MAAL,EAAa;AACb,gBAAME,KAAK,GAAGF,MAAM,CAACG,cAAP,CAAsBF,IAAtB,CAAd;;AACA,cAAIC,KAAJ,EAAW;AACPA,YAAAA,KAAK,CAACE,OAAN;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,QAAD,EAA0B;AACtC,gBAAMC,WAAW,GAAG;AAAA;AAAA,sCAAUC,UAAV,CAAqBC,CAArB,GAAyB,EAA7C;AACA,gBAAMC,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAAV,GAAuB,EAA1C;AACA,iBAAOL,QAAQ,CAACG,CAAT,GAAa,CAACF,WAAd,IAA6BD,QAAQ,CAACG,CAAT,GAAaF,WAA1C,IAAyDD,QAAQ,CAACM,CAAT,GAAa,CAACF,UAAvE,IAAqFJ,QAAQ,CAACM,CAAT,GAAa,EAAzG;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,EAAD,EAAaC,EAAb,EAAyBC,EAAzB,EAAqCC,EAArC,EAAiDC,CAAjD,EAAoE;AACzE,iBACIJ,EAAE,GAAG7C,IAAI,CAACkD,GAAL,CAAS,IAAID,CAAb,EAAgB,CAAhB,CAAL,GACA,IAAIH,EAAJ,GAASG,CAAT,GAAajD,IAAI,CAACkD,GAAL,CAAS,IAAID,CAAb,EAAgB,CAAhB,CADb,GAEA,IAAIF,EAAJ,GAAS/C,IAAI,CAACkD,GAAL,CAASD,CAAT,EAAY,CAAZ,CAAT,IAA2B,IAAIA,CAA/B,CAFA,GAGAD,EAAE,GAAGhD,IAAI,CAACkD,GAAL,CAASD,CAAT,EAAY,CAAZ,CAJT;AAMH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,WAAW,CAACC,KAAD,EAAcC,SAAd,EAA+BC,QAA/B,EAAiDL,CAAjD,EAAkE;AACzE,gBAAMM,aAAa,GAAGF,SAAS,CAACG,QAAV,CAAmBJ,KAAnB,EAA0BK,SAA1B,EAAtB;AACA,iBAAOL,KAAK,CAACM,GAAN,CAAUH,aAAa,CAACI,cAAd,CAA6BL,QAAQ,GAAGL,CAAxC,CAAV,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIW,QAAAA,iBAAiB,CAACR,KAAD,EAAcC,SAAd,EAA+BC,QAA/B,EAAiDL,CAAjD,EAAkE;AAC/E,iBAAOG,KAAK,CAACM,GAAN,CAAUL,SAAS,CAACM,cAAV,CAAyBL,QAAQ,GAAGL,CAApC,CAAV,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIY,QAAAA,QAAQ,CAACT,KAAD,EAAcU,GAAd,EAAiC;AACrC,gBAAMC,EAAE,GAAGD,GAAG,CAACtB,CAAJ,GAAQY,KAAK,CAACZ,CAAzB;AACA,gBAAMwB,EAAE,GAAGF,GAAG,CAACnB,CAAJ,GAAQS,KAAK,CAACT,CAAzB;AACA,gBAAMW,QAAQ,GAAGtD,IAAI,CAACiE,IAAL,CAAUF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAzB,CAAjB;AACA,gBAAME,KAAK,GAAGlE,IAAI,CAACmE,IAAL,CAAUJ,EAAE,GAAGT,QAAf,CAAd;AACA,iBAAOU,EAAE,GAAG,CAAL,GAAS,MAAM5E,IAAI,CAACgF,gBAAL,CAAsBF,KAAtB,CAAf,GAA8C9E,IAAI,CAACgF,gBAAL,CAAsBF,KAAtB,CAArD;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,kBAAkB,CAACC,KAAD,EAAcJ,KAAd,EAAmC;AACjD,gBAAMK,MAAM,GAAGvE,IAAI,CAACiE,IAAL,CAAUK,KAAK,CAAC9B,CAAN,GAAU8B,KAAK,CAAC9B,CAAhB,GAAoB8B,KAAK,CAAC3B,CAAN,GAAU2B,KAAK,CAAC3B,CAA9C,CAAf;AACA,gBAAM6B,MAAM,GAAIxE,IAAI,CAACyE,KAAL,CAAWH,KAAK,CAAC3B,CAAjB,EAAoB2B,KAAK,CAAC9B,CAA1B,IAA+BpD,IAAI,CAACsF,gBAAL,CAAsBR,KAAtB,CAA/C;AACA,iBAAOhF,EAAE,CAACc,IAAI,CAAC2E,GAAL,CAASH,MAAT,IAAmBD,MAApB,EAA4BvE,IAAI,CAAC4E,GAAL,CAASJ,MAAT,IAAmBD,MAA/C,CAAT;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIM,QAAAA,MAAM,CAACC,EAAD,EAAaC,EAAb,EAAyBC,EAAzB,EAAqCC,EAArC,EAAuD;AACzD,iBAAO/F,EAAE,CAAC8F,EAAD,EAAKC,EAAL,CAAF,CAAWzB,QAAX,CAAoBtE,EAAE,CAAC4F,EAAD,EAAKC,EAAL,CAAtB,EAAgCtB,SAAhC,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIyB,QAAAA,eAAe,CAACC,GAAD,EAAoB;AAC/B,gBAAMC,SAAS,GAAGlG,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAApB,CAD+B,CACF;;AAC7B,cAAIiG,GAAG,CAACE,MAAJ,CAAWD,SAAX,KAAyBD,GAAG,CAACE,MAAJ,CAAWlG,IAAI,CAAC4B,IAAhB,CAA7B,EAAoD;AAChD,mBAAO,CAAP;AACH;;AACD,gBAAMmD,KAAK,GAAGiB,GAAG,CAACG,SAAJ,CAAcF,SAAd,CAAd;AACA,iBAAOhG,IAAI,CAACgF,gBAAL,CAAsBF,KAAtB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIqB,QAAAA,kBAAkB,CAACtE,GAAD,EAA6B;AAC3C,cAAI,CAACA,GAAL,EAAU;AACVA,UAAAA,GAAG,CAACuE,OAAJ,CAAaC,SAAD,IAAe;AACvBA,YAAAA,SAAS,CAACD,OAAV,CAAmBE,IAAD,IAAU;AACxB,kBAAIA,IAAI,IAAIA,IAAI,CAAChE,IAAjB,EAAuB;AACnBgE,gBAAAA,IAAI,CAAChE,IAAL,CAAUS,OAAV;AACH;AACJ,aAJD;AAKH,WAND;AAOAlB,UAAAA,GAAG,CAAC0E,KAAJ;AACH;;AAlPS,O;;uBAqPDC,K,GAAQ,IAAI3G,OAAJ,E", "sourcesContent": ["import { Component, NodePool, v2, Node, Vec2, misc, Size, size, Sprite, sp, resources, SpriteFrame } from 'cc';\r\nimport { GameConst } from '../const/GameConst';\r\n\r\nclass DYTools {\r\n    /**\r\n     * 打印日志\r\n     */\r\n    log(message: string, ...args: any[]) {\r\n        console.log(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 打印错误日志\r\n     */\r\n    error(message: string, ...args: any[]) {\r\n        console.error(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 打印警告日志\r\n     */\r\n    warn(message: string, ...args: any[]) {\r\n        console.warn(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 生成随机整数\r\n     */\r\n    random_int(min: number, max: number): number {\r\n        const random = Math.floor(Math.random() * (max - min + 1)) + min;\r\n        return Math.max(min, Math.min(max, random));\r\n    }\r\n\r\n    /**\r\n     * 从数组中随机获取一个元素\r\n     */\r\n    getRandomInArray<T>(array: T[], remove: boolean = false): T | null {\r\n        const length = array.length;\r\n        if (length === 0) return null;\r\n\r\n        const index = this.random_int(0, length - 1);\r\n        const element = array[index];\r\n\r\n        if (remove) {\r\n            array.splice(index, 1);\r\n        }\r\n\r\n        return element;\r\n    }\r\n\r\n    /**\r\n     * 将字符串转换为 Vec2\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @returns Vec2 对象\r\n     */\r\n    stringToPoint(str: string, delimiter: string): Vec2 {\r\n        const parts = str.split(delimiter);\r\n        if (parts.length > 1) {\r\n            return v2(Number(parts[0]), Number(parts[1]));\r\n        }\r\n        return Vec2.ZERO;\r\n    }\r\n\r\n\r\n    /**\r\n     * 将字符串转换为数字数组\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @returns 数字数组\r\n     */\r\n    stringToNumber(str: string, delimiter: string): number[] {\r\n        const parts = str.split(delimiter);\r\n        return parts.map((part) => Number(part)).filter((num) => !isNaN(num));\r\n    }\r\n\r\n    /**\r\n     * 从数组中移除某个元素\r\n     * @param array 数组\r\n     * @param element 元素\r\n     * @returns 是否成功移除\r\n     */\r\n    arrRemove<T>(array: T[], element: T): boolean {\r\n        const index = array.indexOf(element);\r\n        if (index >= 0) {\r\n            array.splice(index, 1);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 检查数组是否包含某个元素（与 arrContains 类似）\r\n     * @param array 数组\r\n     * @param element 元素\r\n     * @returns 是否包含\r\n     */\r\n    arrContain<T>(array: T[], element: T): boolean {\r\n        return array.indexOf(element) != -1;;\r\n    }\r\n\r\n    /**\r\n     * 添加脚本组件到节点\r\n     * @param node 节点\r\n     * @param script 脚本类\r\n     * @returns 添加的脚本组件\r\n     */\r\n    addScript<T extends Component>(node: Node, script: { new(): T }): T | null {\r\n        if (!node || !script) return null;\r\n        return node.getComponent(script) || node.addComponent(script);\r\n    }\r\n\r\n    /**\r\n     * 根据名称移除子节点\r\n     * @param parent 父节点\r\n     * @param name 子节点名称\r\n     */\r\n    removeChildByName(parent: Node, name: string) {\r\n        if (!parent) return;\r\n        const child = parent.getChildByName(name);\r\n        if (child) {\r\n            child.destroy();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查飞机是否超出屏幕范围\r\n     * @param position 飞机位置\r\n     * @returns 是否超出屏幕\r\n     */\r\n    isPlaneOutScreen(position: Vec2): boolean {\r\n        const viewCenterX = GameConst.ViewCenter.x + 50;\r\n        const viewHeight = GameConst.ViewHeight + 50;\r\n        return position.x < -viewCenterX || position.x > viewCenterX || position.y < -viewHeight || position.y > 50;\r\n    }\r\n\r\n    /**\r\n     * 获取贝塞尔曲线上的点\r\n     * @param p0 起点\r\n     * @param p1 控制点1\r\n     * @param p2 控制点2\r\n     * @param p3 终点\r\n     * @param t 参数 t（0 到 1）\r\n     * @returns 贝塞尔曲线上的点\r\n     */\r\n    getBezier(p0: number, p1: number, p2: number, p3: number, t: number): number {\r\n        return (\r\n            p0 * Math.pow(1 - t, 3) +\r\n            3 * p1 * t * Math.pow(1 - t, 2) +\r\n            3 * p2 * Math.pow(t, 2) * (1 - t) +\r\n            p3 * Math.pow(t, 3)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * 获取直线上的点\r\n     * @param start 起点\r\n     * @param direction 方向向量\r\n     * @param distance 距离\r\n     * @param t 参数 t（比例）\r\n     * @returns 直线上的点\r\n     */\r\n    getStraight(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {\r\n        const normalizedDir = direction.subtract(start).normalize();\r\n        return start.add(normalizedDir.multiplyScalar(distance * t));\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量上的点\r\n     * @param start 起点\r\n     * @param direction 方向向量\r\n     * @param distance 距离\r\n     * @param t 参数 t（比例）\r\n     * @returns 方向向量上的点\r\n     */\r\n    getStraightForDir(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {\r\n        return start.add(direction.multiplyScalar(distance * t));\r\n    }\r\n\r\n    /**\r\n     * 获取两点之间的角度\r\n     * @param start 起点\r\n     * @param end 终点\r\n     * @returns 角度\r\n     */\r\n    getAngle(start: Vec2, end: Vec2): number {\r\n        const dx = end.x - start.x;\r\n        const dy = end.y - start.y;\r\n        const distance = Math.sqrt(dx * dx + dy * dy);\r\n        const angle = Math.asin(dx / distance);\r\n        return dy < 0 ? 180 - misc.radiansToDegrees(angle) : misc.radiansToDegrees(angle);\r\n    }\r\n\r\n    /**\r\n     * 根据角度获取点的位置\r\n     * @param point 点\r\n     * @param angle 角度\r\n     * @returns 新位置\r\n     */\r\n    getPositionByAngle(point: Vec2, angle: number): Vec2 {\r\n        const radius = Math.sqrt(point.x * point.x + point.y * point.y);\r\n        const radian = (Math.atan2(point.y, point.x) + misc.degreesToRadians(angle));\r\n        return v2(Math.cos(radian) * radius, Math.sin(radian) * radius);\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量\r\n     * @param x1 起点 x 坐标\r\n     * @param y1 起点 y 坐标\r\n     * @param x2 终点 x 坐标\r\n     * @param y2 终点 y 坐标\r\n     * @returns 方向向量\r\n     */\r\n    getDir(x1: number, y1: number, x2: number, y2: number): Vec2 {\r\n        return v2(x2, y2).subtract(v2(x1, y1)).normalize();\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量的角度（以度为单位）\r\n     * @param dir 方向向量\r\n     * @returns 角度\r\n     */\r\n    getDegreeForDir(dir: Vec2): number {\r\n        const reference = v2(0, -1); // 参考向量\r\n        if (dir.equals(reference) || dir.equals(Vec2.ZERO)) {\r\n            return 0;\r\n        }\r\n        const angle = dir.signAngle(reference);\r\n        return misc.radiansToDegrees(angle);\r\n    }\r\n\r\n    /**\r\n     * 清空 Map 中的组件数组\r\n     * @param map 组件数组的 Map\r\n     */\r\n    clearMapForCompArr(map: Map<any, Component[]>) {\r\n        if (!map) return;\r\n        map.forEach((compArray) => {\r\n            compArray.forEach((comp) => {\r\n                if (comp && comp.node) {\r\n                    comp.node.destroy();\r\n                }\r\n            });\r\n        });\r\n        map.clear();\r\n    }\r\n}\r\n\r\nexport const Tools = new DYTools();"]}