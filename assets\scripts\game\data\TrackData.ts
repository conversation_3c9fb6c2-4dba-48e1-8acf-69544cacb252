
import { _decorator, ValueType, Vec2, CCBoolean } from 'cc';
import { Tools } from "../utils/Tools";

export class TrackData {
    trackID = 0; // 轨迹 ID
    type = 0; // 轨迹类型
    startX = 0; // 起点 X 坐标
    startY = 0; // 起点 Y 坐标
    control1X = 0; // 控制点 1 的 X 坐标
    control1Y = 0; // 控制点 1 的 Y 坐标
    control2X = 0; // 控制点 2 的 X 坐标
    control2Y = 0; // 控制点 2 的 Y 坐标
    endX = 0; // 终点 X 坐标
    endY = 0; // 终点 Y 坐标

    /**
     * 加载 JSON 数据并解析轨迹信息
     * @param {Object} data JSON 数据
     */
    loadJson(data:any) {
        if (data.hasOwnProperty('id')) {
            this.trackID = parseInt(data.id);
        }
        if (data.hasOwnProperty('tpe')) {
            this.type = parseInt(data.tpe);
        }
        if (data.hasOwnProperty('value')) {
            const variable = data.value;
            try {
                switch (this.type) {
                    case 2:
                    case 3: {
                        const points = variable.split(';');
                        const start = Tools.stringToPoint(points[0], ',');
                        this.startX = start.x;
                        this.startY = start.y;
                        const end = Tools.stringToPoint(points[1], ',');
                        this.endX = end.x;
                        this.endY = end.y;
                        break;
                    }
                    case 11: {
                        const end = Tools.stringToPoint(variable, ',');
                        this.endX = end.x;
                        this.endY = end.y;
                        break;
                    }
                    default: {
                        const points = variable.split(';');
                        const start = Tools.stringToPoint(points[0], ',');
                        this.startX = start.x;
                        this.startY = start.y;

                        if (this.type === 0 || this.type === 4) {
                            const control1 = Tools.stringToPoint(points[1], ',');
                            this.control1X = control1.x;
                            this.control1Y = control1.y;

                            const control2 = Tools.stringToPoint(points[2], ',');
                            this.control2X = control2.x;
                            this.control2Y = control2.y;

                            const end = Tools.stringToPoint(points[3], ',');
                            this.endX = end.x;
                            this.endY = end.y;
                        } else if (this.type === 1 || this.type === 5) {
                            const end = Tools.stringToPoint(points[1], ',');
                            this.endX = end.x;
                            this.endY = end.y;
                        }
                        break;
                    }
                }
            } catch (error) {
                console.error('Error parsing track data:', error);
            }
        }
    }
}

// // 新的轨迹数据
// export class TrackPoint extends ValueType {
//     public x: number = 0;
//     public y: number = 0;

//     public isOnPath: boolean = false;
// }

// export class TrackNewData {
//     public id: number = 0;
//     public points: TrackPoint[] = [];

//     public fromJson(data: any) {
//         if (data.hasOwnProperty('id')) {
//             this.id = parseInt(data.id);
//         }
//         if (data.hasOwnProperty('value')) {
//             const points = data.value.split(';');
//             for (const point of points) {
//                 if (point !== '') {
//                     const p = new TrackPoint();
//                     p.x = parseFloat(point.split(',')[0]);
//                     p.y = parseFloat(point.split(',')[1]);
//                     this.points.push(p);
//                 }
//             }
//         }
//     }
// }