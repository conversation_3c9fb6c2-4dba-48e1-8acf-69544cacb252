System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EnemyPlaneBase, GameEnum, AttributeConst, ColliderGroupType, FBoxCollider, Tools, TrackComponent, Movable, _dec, _class, _crd, ccclass, property, EnemyPlane;

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "./EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMovable(extras) {
    _reporterNs.report("Movable", "db://assets/scripts/game/move/Movable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      EnemyPlaneBase = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      AttributeConst = _unresolved_4.AttributeConst;
    }, function (_unresolved_5) {
      ColliderGroupType = _unresolved_5.ColliderGroupType;
    }, function (_unresolved_6) {
      FBoxCollider = _unresolved_6.default;
    }, function (_unresolved_7) {
      Tools = _unresolved_7.Tools;
    }, function (_unresolved_8) {
      TrackComponent = _unresolved_8.default;
    }, function (_unresolved_9) {
      Movable = _unresolved_9.Movable;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlane = (_dec = ccclass('EnemyPlane'), _dec(_class = class EnemyPlane extends (_crd && EnemyPlaneBase === void 0 ? (_reportPossibleCrUseOfEnemyPlaneBase({
        error: Error()
      }), EnemyPlaneBase) : EnemyPlaneBase) {
        constructor(...args) {
          super(...args);
          this._roleIndex = 1;
          //当前形态索引
          this._enemyData = null;
          this._curAction = 0;
          this._trackCom = null;
          this._moveCom = null;
        }

        get moveCom() {
          return this._moveCom;
        }

        onLoad() {
          this.enemy = true;
          this._trackCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && TrackComponent === void 0 ? (_reportPossibleCrUseOfTrackComponent({
            error: Error()
          }), TrackComponent) : TrackComponent); // 添加移动组件

          this._moveCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
            error: Error()
          }), Movable) : Movable);
        }

        initPlane(data, trackData) {
          this._enemyData = data;
          super.init();
          this.reset();
          this.refreshProperty();
          this.initTrack(trackData);

          this._initCollide();

          this.startBattle();
        }

        initTrack(trackData) {
          this._trackCom.init(this, trackData.trackData, trackData.trackParams, trackData.offset);

          this._trackCom.setTrackOverCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver);
          });

          this._trackCom.setTrackLeaveCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
          });
        }

        initMove(speed, angle) {
          this._moveCom.speed = speed;
          this._moveCom.speedAngle = angle;

          this._moveCom.setMovable(true);
        }

        initDelayDestroy(delayTime) {
          this._moveCom.onBecomeInvisibleCallback = () => {
            this.scheduleOnce(this._dieWhenOffScreen, delayTime);
          };

          this._moveCom.onBecomeVisibleCallback = () => {
            this.unschedule(this._dieWhenOffScreen);
          };
        }

        _dieWhenOffScreen() {
          this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
        }

        _initCollide() {
          // 添加碰撞组件并初始化
          this.collideComp = this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this);
          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL;
          this.colliderEnabled = false;
        }

        refreshProperty() {
          var _this$_enemyData;

          this.curHp = (_this$_enemyData = this._enemyData) == null ? void 0 : _this$_enemyData.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHP);
          this.maxHp = this.curHp;
        }

        getAttack() {
          var _this$_enemyData2;

          return ((_this$_enemyData2 = this._enemyData) == null ? void 0 : _this$_enemyData2.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).Attack)) || 0;
        }

        reset() {
          this._curAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAction.Track;
        }

        startBattle() {
          this.colliderEnabled = true;
          this.updateHpUI();

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();
        }

        updateGameLogic(deltaTime) {
          if (!this.isDead) {
            // 更新所有组件
            this.m_comps.forEach(comp => {
              comp.update(deltaTime);
            });
          }

          if (this.isDead) {
            this._checkRemoveAble(deltaTime);
          } else {
            this._trackCom.updateGameLogic(deltaTime);

            this.updateAction(deltaTime);
          }
        } //1.敌机出现，并且移动到指定位置
        //2.开始变形
        //3.开始追踪主角，追踪过程，会射击


        setAction(action) {
          if (this._curAction !== action) {
            this._curAction = action; // 停止射击并启用轨迹

            this._trackCom.setTrackAble(true);

            switch (this._curAction) {
              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Sneak:
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Track:
                // 跟踪行为
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Transform:
                // 变形行为
                // this._trackCom!.setTrackAble(false);
                this._roleIndex++; // this.role!.playAnim("transform", () => {
                //     this.role!.playAnim("idle" + this._roleIndex);
                //     this.setAction(GameEnum.EnemyAction.Track);
                //     this._shootCom!.setNextShootAtOnce();
                // }) || (

                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track);
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
                // 准备攻击行为
                this.playAtkAnim(() => {
                  this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                    error: Error()
                  }), GameEnum) : GameEnum).EnemyAction.AttackIng);
                });
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackIng:
                // 攻击中行为
                // this._shootCom!.startShoot();
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackOver:
                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track);
                break;

              default:
                break;
            }
          }
        }
        /**
        * 播放攻击动画
        */


        playAtkAnim(callback) {
          this.plane.playAnim(`atk${this._roleIndex}`, false, () => {
            this.plane.playAnim(`idle${this._roleIndex}`);
            callback == null || callback();
          });
        }
        /**每帧都会检测 */


        updateAction(deltaTime) {
          // this._shootCom!.setNextAble(false);
          switch (this._curAction) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Sneak:
              this.colliderEnabled = false;
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Track:
              // this._shootCom!.setNextAble(
              //     (this._trackCom!.isMoving && this._enemyData!.bMoveAttack) ||
              //     (!this._trackCom!.isMoving && this._enemyData!.bStayAttack)
              // );
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Transform:
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackIng:
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Leave:
              this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver);
              break;
          }
        }

      }) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=0e075f664a7f46e3e9c8b454b52c8229d823e8a1.js.map