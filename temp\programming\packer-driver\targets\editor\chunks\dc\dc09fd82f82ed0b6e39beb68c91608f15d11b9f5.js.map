{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/GamePlaneManager.ts"], "names": ["GamePlaneManager", "SingletonBase", "GameIns", "BattleLayer", "GameConst", "GameEnum", "FCollider", "enemyTarget", "enemyCollider", "frienPlane", "m_planeTable", "enemyTargetPos", "x", "y", "clear", "for<PERSON>ach", "plane", "node", "destroy", "getConfig", "id", "getRecorder", "addFriendPlane", "me", "push", "removeFriendPlane", "index", "indexOf", "splice", "parent", "<PERSON><PERSON><PERSON><PERSON>", "point1", "point2", "Math", "sqrt", "getRandomTargetEnemy", "viewHeight", "ViewHeight", "targets", "EnemyType", "enemyManager", "planes", "enemy", "type", "Normal", "normalCollider", "getComponent", "fColliderManager", "isOutOfScreen", "aabb", "randomIndex", "floor", "random", "length", "getTargetEnemy", "mainPlanePos", "mainPlaneManager", "mainPlane", "position", "closestDistance", "Infinity", "closestEnemy", "closestCollider", "isDead", "colliderEnabled", "enemyPos", "distance", "update", "deltaTime"], "mappings": ";;;yIASaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACEC,MAAAA,S,iBAAAA,S;;AACDC,MAAAA,Q,iBAAAA,Q;;AACDC,MAAAA,S;;;;;;;kCAEMN,gB,GAAN,MAAMA,gBAAN;AAAA;AAAA,0CAA+D;AAAA;AAAA;AAAA,eAClEO,WADkE,GAC/C,IAD+C;AAAA,eAElEC,aAFkE,GAElC,IAFkC;AAAA,eAGlEC,UAHkE,GAG9C,EAH8C;AAAA,eAIlEC,YAJkE;AAAA,eAKlEC,cALkE,GAKjD;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WALiD;AAAA;;AAOlE;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKP,WAAL,GAAmB,IAAnB;AACA,eAAKC,aAAL,GAAqB,IAArB;AACA,eAAKC,UAAL,CAAgBM,OAAhB,CAAyBC,KAAD,IAAW;AAC/B,gBAAIA,KAAK,IAAIA,KAAK,CAACC,IAAnB,EAAyB;AACrBD,cAAAA,KAAK,CAACC,IAAN,CAAWC,OAAX;AACH;AACJ,WAJD;AAKA,eAAKT,UAAL,GAAkB,EAAlB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIU,QAAAA,SAAS,CAACC,EAAD,EAAa;AAClB,iBAAO,KAAKV,YAAL,CAAkBW,WAAlB,CAA8BD,EAA9B,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,cAAc,CAACN,KAAD,EAAa;AACvB;AAAA;AAAA,0CAAYO,EAAZ,CAAeD,cAAf,CAA8BN,KAA9B;AACA,eAAKP,UAAL,CAAgBe,IAAhB,CAAqBR,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,iBAAiB,CAACT,KAAD,EAAa;AAC1B,gBAAMU,KAAK,GAAG,KAAKjB,UAAL,CAAgBkB,OAAhB,CAAwBX,KAAxB,CAAd;;AACA,cAAIU,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKjB,UAAL,CAAgBmB,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;AACH;;AACD,cAAIV,KAAK,IAAIA,KAAK,CAACC,IAAnB,EAAyB;AACrBD,YAAAA,KAAK,CAACC,IAAN,CAAWY,MAAX,GAAoB,IAApB;AACH;AACJ;AAKD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,MAAD,EAAeC,MAAf,EAAqC;AAC1C,iBAAOC,IAAI,CAACC,IAAL,CACH,CAACH,MAAM,CAACnB,CAAP,GAAWoB,MAAM,CAACpB,CAAnB,KAAyBmB,MAAM,CAACnB,CAAP,GAAWoB,MAAM,CAACpB,CAA3C,IACA,CAACmB,MAAM,CAAClB,CAAP,GAAWmB,MAAM,CAACnB,CAAnB,KAAyBkB,MAAM,CAAClB,CAAP,GAAWmB,MAAM,CAACnB,CAA3C,CAFG,CAAP;AAIH;;AAGDsB,QAAAA,oBAAoB,GAAqB;AACrC,gBAAMC,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAA7B;AACA,gBAAMC,OAAoB,GAAG,EAA7B;AAEA,cAAIC,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB,CAJqC,CAKrC;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAArB,CAA4B1B,OAA5B,CAAqC2B,KAAD,IAAW;AAC3C,oBAAQA,KAAK,CAACC,IAAd;AACI,mBAAKJ,SAAS,CAACK,MAAf;AACI,sBAAMC,cAAc,GAAGH,KAAK,CAACI,YAAN;AAAA;AAAA,2CAAvB;;AACA,oBAAI,CAAC;AAAA;AAAA,wCAAQC,gBAAR,CAAyBC,aAAzB,CAAuCH,cAAvC,oBAAuCA,cAAc,CAAEI,IAAvD,CAAL,EAAoE;AAChEX,kBAAAA,OAAO,CAACd,IAAR,CAAaqB,cAAb;AACH;;AACD;AANR;AAQH,WATD,EANqC,CAiBrC;;AACA,gBAAMK,WAAW,GAAGjB,IAAI,CAACkB,KAAL,CAAWlB,IAAI,CAACmB,MAAL,KAAgBd,OAAO,CAACe,MAAnC,CAApB;AACA,iBAAOf,OAAO,CAACY,WAAD,CAAP,IAAwB,IAA/B;AACH;;AAEDI,QAAAA,cAAc,GAAS;AACnB,gBAAMC,YAAY,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,CAAoCxC,IAApC,CAAyCyC,QAA9D;AACA,cAAIC,eAAe,GAAGC,QAAtB;AACA,cAAIC,YAAiB,GAAG,IAAxB;AACA,cAAIC,eAAiC,GAAG,IAAxC;AACA,gBAAM1B,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAA7B;AAEA,cAAIE,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB,CAPmB,CAQnB;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAArB,CAA4B1B,OAA5B,CAAqC2B,KAAD,IAAW;AAC3C,gBAAI,CAACA,KAAK,CAACqB,MAAX,EAAmB;AACf,sBAAQrB,KAAK,CAACC,IAAd;AACI,qBAAKJ,SAAS,CAACK,MAAf;AACI,sBAAIF,KAAK,CAACsB,eAAV,EAA2B;AACvB,0BAAMnB,cAAc,GAAGH,KAAK,CAACI,YAAN;AAAA;AAAA,+CAAvB;;AACA,wBAAI,CAAC;AAAA;AAAA,4CAAQC,gBAAR,CAAyBC,aAAzB,CAAuCH,cAAvC,oBAAuCA,cAAc,CAAEI,IAAvD,CAAL,EAAoE;AAChE,4BAAMgB,QAAQ,GAAGvB,KAAK,CAACzB,IAAN,CAAWyC,QAA5B;AACA,4BAAMQ,QAAQ,GAAG,KAAKpC,SAAL,CAAeyB,YAAf,EAA6BU,QAA7B,CAAjB;;AACA,0BAAIC,QAAQ,GAAGP,eAAf,EAAgC;AAC5BA,wBAAAA,eAAe,GAAGO,QAAlB;AACAL,wBAAAA,YAAY,GAAGnB,KAAf;AACAoB,wBAAAA,eAAe,GAAGpB,KAAK,CAACI,YAAN;AAAA;AAAA,mDAAlB;AACH;AACJ;AACJ;;AACD;AAdR;AAgBH;AACJ,WAnBD,EATmB,CA8BnB;;AACA,cAAIe,YAAJ,EAAkB;AACd,iBAAKtD,WAAL,GAAmBsD,YAAnB;AACA,iBAAKlD,cAAL,CAAoBC,CAApB,GAAwBiD,YAAY,CAAC5C,IAAb,CAAkByC,QAAlB,CAA2B9C,CAAnD;AACA,iBAAKD,cAAL,CAAoBE,CAApB,GAAwBgD,YAAY,CAAC5C,IAAb,CAAkByC,QAAlB,CAA2B7C,CAAnD;AACA,iBAAKL,aAAL,GAAqBsD,eAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,eAAKd,cAAL;AACH;;AAzIiE,O", "sourcesContent": ["\r\nimport { Vec2, Vec3 } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport {GameEnum} from \"../const/GameEnum\";\r\nimport FCollider from \"../collider-system/FCollider\";\r\n\r\nexport class GamePlaneManager extends SingletonBase<GamePlaneManager> {\r\n    enemyTarget: any = null;\r\n    enemyCollider: FCollider|null = null;\r\n    frienPlane: any[] = [];\r\n    m_planeTable: any;\r\n    enemyTargetPos = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * 清理数据\r\n     */\r\n    clear() {\r\n        this.enemyTarget = null;\r\n        this.enemyCollider = null;\r\n        this.frienPlane.forEach((plane) => {\r\n            if (plane && plane.node) {\r\n                plane.node.destroy();\r\n            }\r\n        });\r\n        this.frienPlane = [];\r\n    }\r\n\r\n    /**\r\n     * 获取飞机配置\r\n     * @param id 配置 ID\r\n     * @returns 配置数据\r\n     */\r\n    getConfig(id: number) {\r\n        return this.m_planeTable.getRecorder(id);\r\n    }\r\n\r\n    /**\r\n     * 添加友军飞机\r\n     * @param plane 飞机对象\r\n     */\r\n    addFriendPlane(plane: any) {\r\n        BattleLayer.me.addFriendPlane(plane);\r\n        this.frienPlane.push(plane);\r\n    }\r\n\r\n    /**\r\n     * 移除友军飞机\r\n     * @param plane 飞机对象\r\n     */\r\n    removeFriendPlane(plane: any) {\r\n        const index = this.frienPlane.indexOf(plane);\r\n        if (index >= 0) {\r\n            this.frienPlane.splice(index, 1);\r\n        }\r\n        if (plane && plane.node) {\r\n            plane.node.parent = null;\r\n        }\r\n    }\r\n\r\n\r\n\r\n\r\n    /**\r\n     * 获取两点之间的距离\r\n     * @param point1 点1\r\n     * @param point2 点2\r\n     * @returns 距离\r\n     */\r\n    getLength(point1: Vec3, point2: Vec3): number {\r\n        return Math.sqrt(\r\n            (point1.x - point2.x) * (point1.x - point2.x) +\r\n            (point1.y - point2.y) * (point1.y - point2.y)\r\n        );\r\n    }\r\n\r\n\r\n    getRandomTargetEnemy(): FCollider | null {\r\n        const viewHeight = GameConst.ViewHeight;\r\n        const targets: FCollider[] = [];\r\n\r\n        let EnemyType = GameEnum.EnemyType;\r\n        // 遍历敌机\r\n        GameIns.enemyManager.planes.forEach((enemy) => {\r\n            switch (enemy.type) {\r\n                case EnemyType.Normal:\r\n                    const normalCollider = enemy.getComponent(FCollider);\r\n                    if (!GameIns.fColliderManager.isOutOfScreen(normalCollider?.aabb!)) {\r\n                        targets.push(normalCollider!);\r\n                    }\r\n                    break;\r\n            }\r\n        });\r\n\r\n        // 随机选择一个目标\r\n        const randomIndex = Math.floor(Math.random() * targets.length);\r\n        return targets[randomIndex] || null;\r\n    }\r\n\r\n    getTargetEnemy(): void {\r\n        const mainPlanePos = GameIns.mainPlaneManager.mainPlane!.node.position;\r\n        let closestDistance = Infinity;\r\n        let closestEnemy: any = null;\r\n        let closestCollider: FCollider | null = null;\r\n        const viewHeight = GameConst.ViewHeight;\r\n\r\n        let EnemyType = GameEnum.EnemyType;\r\n        // 遍历敌机\r\n        GameIns.enemyManager.planes.forEach((enemy) => {\r\n            if (!enemy.isDead) {\r\n                switch (enemy.type) {\r\n                    case EnemyType.Normal:\r\n                        if (enemy.colliderEnabled) {\r\n                            const normalCollider = enemy.getComponent(FCollider);\r\n                            if (!GameIns.fColliderManager.isOutOfScreen(normalCollider?.aabb!)) {\r\n                                const enemyPos = enemy.node.position;\r\n                                const distance = this.getLength(mainPlanePos, enemyPos);\r\n                                if (distance < closestDistance) {\r\n                                    closestDistance = distance;\r\n                                    closestEnemy = enemy;\r\n                                    closestCollider = enemy.getComponent(FCollider);\r\n                                }\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n            }\r\n        });\r\n\r\n        // 更新目标敌人和碰撞器\r\n        if (closestEnemy) {\r\n            this.enemyTarget = closestEnemy;\r\n            this.enemyTargetPos.x = closestEnemy.node.position.x;\r\n            this.enemyTargetPos.y = closestEnemy.node.position.y;\r\n            this.enemyCollider = closestCollider;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新函数\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime: number) {\r\n        this.getTargetEnemy();\r\n    }\r\n}"]}