{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts"], "names": ["QuadTree", "Color", "misc", "color", "BLACK", "RED", "BLUE", "GREEN", "catchTree", "constructor", "bounds", "depth", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "nodes", "children", "_bounds", "_depth", "_max<PERSON><PERSON><PERSON>n", "_maxDepth", "halfHeight", "height", "halfWidth", "width", "getAllNeedTestColliders", "out", "length", "push", "i", "len", "node", "render", "pen", "lineWidth", "clampf", "strokeColor", "moveTo", "x", "y", "lineTo", "close", "stroke", "insert", "item", "indexes", "_findIndexs", "subdivide", "j", "k", "klen", "retrieve", "apply", "filter", "index", "indexOf", "pRect", "b", "verticalMidpoint", "horizontalMidpoint", "bottom", "left", "right", "top", "nextLevel", "subWidth", "subHeight", "clear"], "mappings": ";;;oEA4BaA,Q;;;;;;;;AA5BQC,MAAAA,K,OAAAA,K;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;AAiBlCC,MAAAA,K,GAAQ,CACRF,KAAK,CAACG,KADE,EAERH,KAAK,CAACI,GAFE,EAGRJ,KAAK,CAACK,IAHE,EAIR,IAAIL,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,CAJQ,EAKRA,KAAK,CAACM,KALE,EAMRN,KAAK,CAACM,KANE,EAORN,KAAK,CAACM,KAPE,C;AAURC,MAAAA,S,GAAY,E;;0BACHR,Q,GAAN,MAAMA,QAAN,CAAuC;AAYnCS,QAAAA,WAAW,CAACC,MAAD,EAAmBC,KAAnB,EAAkCC,QAAlC,EAAoDC,WAApD,EAAyE;AAX3F;AAW2F,eAVjFC,KAUiF;AAT3F;AAS2F,eARjFC,QAQiF;AAAA,eAPnFC,OAOmF;AAL3F;AAK2F,eAJjFC,MAIiF,GAJxE,CAIwE;AAAA,eAHjFC,YAGiF,GAHlE,CAGkE;AAAA,eAFjFC,SAEiF,GAFrE,CAEqE;AACvF,eAAKH,OAAL,GAAeN,MAAf;AACA,eAAKK,QAAL,GAAgB,EAAhB;AACA,eAAKD,KAAL,GAAa,EAAb;AACA,eAAKI,YAAL,GAAoBL,WAAW,IAAI,EAAnC;AACA,eAAKM,SAAL,GAAiBP,QAAQ,IAAI,CAA7B;AACA,eAAKK,MAAL,GAAcN,KAAK,IAAI,CAAvB;AACA,cAAI,CAAC,KAAKK,OAAL,CAAaI,UAAlB,EAA8B,KAAKJ,OAAL,CAAaI,UAAb,GAA0B,KAAKJ,OAAL,CAAaK,MAAb,GAAsB,CAAhD;AAC9B,cAAI,CAAC,KAAKL,OAAL,CAAaM,SAAlB,EAA6B,KAAKN,OAAL,CAAaM,SAAb,GAAyB,KAAKN,OAAL,CAAaO,KAAb,GAAqB,CAA9C;AAChC;;AAEMC,QAAAA,uBAAuB,CAACC,GAAD,EAAa;AACvC,cAAI,KAAKV,QAAL,CAAcW,MAAlB,EACID,GAAG,CAACE,IAAJ,CAAS,KAAKZ,QAAd;;AACJ,eAAK,IAAIa,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG,KAAKf,KAAL,CAAWY,MAAjC,EAAyCE,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,gBAAIE,IAAI,GAAG,KAAKhB,KAAL,CAAWc,CAAX,CAAX;AACAE,YAAAA,IAAI,CAACN,uBAAL,CAA6BC,GAA7B;AACH;AACJ;;AAEMM,QAAAA,MAAM,CAACC,GAAD,EAAgB;AACzB,eAAK,IAAIJ,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG,KAAKf,KAAL,CAAWY,MAAjC,EAAyCE,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,gBAAIE,IAAI,GAAG,KAAKhB,KAAL,CAAWc,CAAX,CAAX;AACAE,YAAAA,IAAI,IAAIA,IAAI,CAACC,MAAL,CAAYC,GAAZ,CAAR;AACH;;AACDA,UAAAA,GAAG,CAACC,SAAJ,GAAgB/B,IAAI,CAACgC,MAAL,CAAY,IAAI,KAAKjB,MAArB,EAA6B,CAA7B,EAAgC,CAAhC,CAAhB;AACAe,UAAAA,GAAG,CAACG,WAAJ,GAAkBhC,KAAK,CAAC,KAAKc,MAAN,CAAL,IAAsBhB,KAAK,CAACM,KAA9C;AACAyB,UAAAA,GAAG,CAACI,MAAJ,CAAW,KAAKpB,OAAL,CAAaqB,CAAxB,EAA2B,KAAKrB,OAAL,CAAasB,CAAxC;AACAN,UAAAA,GAAG,CAACO,MAAJ,CAAW,KAAKvB,OAAL,CAAaqB,CAAb,GAAiB,KAAKrB,OAAL,CAAaO,KAAzC,EAAgD,KAAKP,OAAL,CAAasB,CAA7D;AACAN,UAAAA,GAAG,CAACO,MAAJ,CAAW,KAAKvB,OAAL,CAAaqB,CAAb,GAAiB,KAAKrB,OAAL,CAAaO,KAAzC,EAAgD,KAAKP,OAAL,CAAasB,CAAb,GAAiB,KAAKtB,OAAL,CAAaK,MAA9E;AACAW,UAAAA,GAAG,CAACO,MAAJ,CAAW,KAAKvB,OAAL,CAAaqB,CAAxB,EAA2B,KAAKrB,OAAL,CAAasB,CAAb,GAAiB,KAAKtB,OAAL,CAAaK,MAAzD;AACAW,UAAAA,GAAG,CAACQ,KAAJ;AACAR,UAAAA,GAAG,CAACS,MAAJ;AACH;;AAEMC,QAAAA,MAAM,CAACC,IAAD,EAAW;AACpB,cAAIC,OAAJ;;AACA,cAAI,KAAK9B,KAAL,CAAWY,MAAf,EAAuB;AACnBkB,YAAAA,OAAO,GAAG,KAAKC,WAAL,CAAiBF,IAAjB,CAAV;;AACA,iBAAK,IAAIf,CAAC,GAAG,CAAR,EAAWC,IAAG,GAAGe,OAAO,CAAClB,MAA9B,EAAsCE,CAAC,GAAGC,IAA1C,EAA+CD,CAAC,EAAhD,EAAoD;AAChD,mBAAKd,KAAL,CAAW8B,OAAO,CAAChB,CAAD,CAAlB,EAAuBc,MAAvB,CAA8BC,IAA9B;AACH;;AACD;AACH;;AACD,eAAK5B,QAAL,CAAcY,IAAd,CAAmBgB,IAAnB;AACA,cAAId,GAAG,GAAG,KAAKd,QAAL,CAAcW,MAAxB;;AACA,cAAI,KAAKT,MAAL,GAAc,KAAKE,SAAnB,IAAgCU,GAAG,GAAG,KAAKX;AAAa;AAA5D,YAAmJ;AAC/I,gBAAI,CAAC,KAAKJ,KAAL,CAAWY,MAAhB,EACI,KAAKoB,SAAL;;AACJ,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,GAApB,EAAyBkB,CAAC,EAA1B,EAA8B;AAC1BH,cAAAA,OAAO,GAAG,KAAKC,WAAL,CAAiB,KAAK9B,QAAL,CAAcgC,CAAd,CAAjB,CAAV;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,IAAI,GAAGL,OAAO,CAAClB,MAA/B,EAAuCsB,CAAC,GAAGC,IAA3C,EAAiDD,CAAC,EAAlD,EAAsD;AAClD,qBAAKlC,KAAL,CAAW8B,OAAO,CAACI,CAAD,CAAlB,EAAuBN,MAAvB,CAA8B,KAAK3B,QAAL,CAAcgC,CAAd,CAA9B;AACH;AACJ;;AACD,iBAAKhC,QAAL,CAAcW,MAAd,GAAuB,CAAvB;AACH;AACJ;;AAEMwB,QAAAA,QAAQ,CAACP,IAAD,EAAUlB,GAAV,EAAoB;AAC/B,cAAImB,OAAO,GAAG,KAAKC,WAAL,CAAiBF,IAAjB,CAAd;;AACA,cAAI,KAAK5B,QAAL,CAAcW,MAAlB,EACID,GAAG,CAACE,IAAJ,CAASwB,KAAT,CAAe1B,GAAf,EAAoB,KAAKV,QAAzB;;AACJ,cAAI,KAAKD,KAAL,CAAWY,MAAf,EAAuB;AACnB,iBAAK,IAAIE,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGe,OAAO,CAAClB,MAA9B,EAAsCE,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;AAChD,mBAAKd,KAAL,CAAW8B,OAAO,CAAChB,CAAD,CAAlB,EAAuBsB,QAAvB,CAAgCP,IAAhC,EAAsClB,GAAtC;AACH;AACJ;;AACDA,UAAAA,GAAG,GAAGA,GAAG,CAAC2B,MAAJ,CAAW,CAACT,IAAD,EAAOU,KAAP,KAAiB;AAC9B,mBAAO5B,GAAG,CAAC6B,OAAJ,CAAYX,IAAZ,KAAqBU,KAA5B;AACH,WAFK,CAAN;AAGH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACWR,QAAAA,WAAW,CAACU,KAAD,EAAW;AACzB,cAAIC,CAAC,GAAG,KAAKxC,OAAb,CADyB,CAEzB;;AACA,cAAIyC,gBAAgB,GAAGD,CAAC,CAACnB,CAAF,GAAMmB,CAAC,CAAClC,SAA/B;AACA,cAAIoC,kBAAkB,GAAGF,CAAC,CAAClB,CAAF,GAAMkB,CAAC,CAACpC,UAAjC;AACA,cAAIuC,MAAM,GAAGJ,KAAK,CAACjB,CAAN,GAAUoB,kBAAvB;AAAA,cACIE,IAAI,GAAGL,KAAK,CAAClB,CAAN,GAAUoB,gBADrB;AAAA,cAEII,KAAK,GAAGN,KAAK,CAAClB,CAAN,GAAUkB,KAAK,CAAChC,KAAhB,GAAwBkC,gBAFpC;AAAA,cAGIK,GAAG,GAAGP,KAAK,CAACjB,CAAN,GAAUiB,KAAK,CAAClC,MAAhB,GAAyBqC,kBAHnC;AAIA,cAAId,OAAO,GAAG,EAAd,CATyB,CAUzB;;AACA,cAAIkB,GAAG,IAAIF,IAAX,EAAiB;AACbhB,YAAAA,OAAO,CAACjB,IAAR,CAAa,CAAb;AACH,WAbwB,CAezB;;;AACA,cAAIgC,MAAM,IAAIC,IAAd,EAAoB;AAChBhB,YAAAA,OAAO,CAACjB,IAAR,CAAa,CAAb;AACH,WAlBwB,CAoBzB;;;AACA,cAAIgC,MAAM,IAAIE,KAAd,EAAqB;AACjBjB,YAAAA,OAAO,CAACjB,IAAR,CAAa,CAAb;AACH,WAvBwB,CAyBzB;;;AACA,cAAImC,GAAG,IAAID,KAAX,EAAkB;AACdjB,YAAAA,OAAO,CAACjB,IAAR,CAAa,CAAb;AACH;;AACD,iBAAOiB,OAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACWE,QAAAA,SAAS,GAAG;AACf,cAAIiB,SAAS,GAAG,KAAK9C,MAAL,GAAc,CAA9B;AAAA,cACI+C,QAAQ,GAAG,KAAKhD,OAAL,CAAaM,SAD5B;AAAA,cAEI2C,SAAS,GAAG,KAAKjD,OAAL,CAAaI,UAF7B;AAAA,cAGIiB,CAAC,GAAG,KAAKrB,OAAL,CAAaqB,CAHrB;AAAA,cAIIC,CAAC,GAAG,KAAKtB,OAAL,CAAasB,CAJrB,CADe,CAMf;;AACA,eAAKxB,KAAL,CAAW,CAAX,IAAgB,IAAId,QAAJ,CAAa;AACzBqC,YAAAA,CAAC,EAAEA,CADsB;AAEzBC,YAAAA,CAAC,EAAEA,CAAC,GAAG2B,SAFkB;AAGzB1C,YAAAA,KAAK,EAAEyC,QAHkB;AAIzB3C,YAAAA,MAAM,EAAE4C;AAJiB,WAAb,EAKbF,SALa,EAKF,KAAK5C,SALH,EAKc,KAAKD,YALnB,CAAhB,CAPe,CAcf;;AACA,eAAKJ,KAAL,CAAW,CAAX,IAAgB,IAAId,QAAJ,CAAa;AACzBqC,YAAAA,CAAC,EAAEA,CADsB;AAEzBC,YAAAA,CAAC,EAAEA,CAFsB;AAGzBf,YAAAA,KAAK,EAAEyC,QAHkB;AAIzB3C,YAAAA,MAAM,EAAE4C;AAJiB,WAAb,EAKbF,SALa,EAKF,KAAK5C,SALH,EAKc,KAAKD,YALnB,CAAhB,CAfe,CAsBf;;AACA,eAAKJ,KAAL,CAAW,CAAX,IAAgB,IAAId,QAAJ,CAAa;AACzBqC,YAAAA,CAAC,EAAEA,CAAC,GAAG2B,QADkB;AAEzB1B,YAAAA,CAAC,EAAEA,CAFsB;AAGzBf,YAAAA,KAAK,EAAEyC,QAHkB;AAIzB3C,YAAAA,MAAM,EAAE4C;AAJiB,WAAb,EAKbF,SALa,EAKF,KAAK5C,SALH,EAKc,KAAKD,YALnB,CAAhB,CAvBe,CA8Bf;;AACA,eAAKJ,KAAL,CAAW,CAAX,IAAgB,IAAId,QAAJ,CAAa;AACzBqC,YAAAA,CAAC,EAAEA,CAAC,GAAG2B,QADkB;AAEzB1B,YAAAA,CAAC,EAAEA,CAAC,GAAG2B,SAFkB;AAGzB1C,YAAAA,KAAK,EAAEyC,QAHkB;AAIzB3C,YAAAA,MAAM,EAAE4C;AAJiB,WAAb,EAKbF,SALa,EAKF,KAAK5C,SALH,EAKc,KAAKD,YALnB,CAAhB;AAMH;;AAEMgD,QAAAA,KAAK,GAAG;AACX,eAAKnD,QAAL,CAAcW,MAAd,GAAuB,CAAvB;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG,KAAKf,KAAL,CAAWY,MAAjC,EAAyCE,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,iBAAKd,KAAL,CAAWc,CAAX,EAAcsC,KAAd;AACH;;AACD,eAAKpD,KAAL,CAAWY,MAAX,GAAoB,CAApB;AACH;;AAtLyC,O", "sourcesContent": ["import { _decorator, Color, Graphics, misc } from 'cc';\n\ninterface LikeRect {\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n    halfWidth?: number,\n    halfHeight?: number,\n}\n\ninterface LikeCollider {\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n}\nlet color = [\n    Color.BLACK,\n    Color.RED,\n    Color.BLUE,\n    new Color(255, 127, 0),\n    Color.GREEN,\n    Color.GREEN,\n    Color.GREEN,\n]\n\nlet catchTree = [];\nexport class QuadTree<T extends LikeCollider> {\n    //subnodes\n    protected nodes: QuadTree<T>[];\n    //children contained directly in the node\n    protected children: T[];\n    private _bounds: LikeRect;\n\n    //read only\n    protected _depth = 0;\n    protected _maxChildren = 6;\n    protected _maxDepth = 6;\n\n    public constructor(bounds: LikeRect, depth: number, maxDepth: number, maxChildren: number) {\n        this._bounds = bounds;\n        this.children = [];\n        this.nodes = [];\n        this._maxChildren = maxChildren || 10;\n        this._maxDepth = maxDepth || 4;\n        this._depth = depth || 0;\n        if (!this._bounds.halfHeight) this._bounds.halfHeight = this._bounds.height / 2;\n        if (!this._bounds.halfWidth) this._bounds.halfWidth = this._bounds.width / 2;\n    }\n\n    public getAllNeedTestColliders(out: T[][]) {\n        if (this.children.length)\n            out.push(this.children);\n        for (let i = 0, len = this.nodes.length; i < len; i++) {\n            let node = this.nodes[i];\n            node.getAllNeedTestColliders(out);\n        }\n    }\n\n    public render(pen: Graphics) {\n        for (let i = 0, len = this.nodes.length; i < len; i++) {\n            let node = this.nodes[i];\n            node && node.render(pen);\n        }\n        pen.lineWidth = misc.clampf(8 - this._depth, 2, 8);\n        pen.strokeColor = color[this._depth] || Color.GREEN;\n        pen.moveTo(this._bounds.x, this._bounds.y);\n        pen.lineTo(this._bounds.x + this._bounds.width, this._bounds.y);\n        pen.lineTo(this._bounds.x + this._bounds.width, this._bounds.y + this._bounds.height);\n        pen.lineTo(this._bounds.x, this._bounds.y + this._bounds.height);\n        pen.close();\n        pen.stroke();\n    }\n\n    public insert(item:any) {\n        let indexes;\n        if (this.nodes.length) {\n            indexes = this._findIndexs(item);\n            for (let i = 0, len = indexes.length; i < len; i++) {\n                this.nodes[indexes[i]].insert(item);\n            }\n            return;\n        }\n        this.children.push(item);\n        let len = this.children.length;\n        if (this._depth < this._maxDepth && len > this._maxChildren /*&& (item.width < this._bounds.halfWidth && item.height < this._bounds.halfHeight)*/) {\n            if (!this.nodes.length)\n                this.subdivide();\n            for (let j = 0; j < len; j++) {\n                indexes = this._findIndexs(this.children[j]);\n                for (let k = 0, klen = indexes.length; k < klen; k++) {\n                    this.nodes[indexes[k]].insert(this.children[j]);\n                }\n            }\n            this.children.length = 0;\n        }\n    }\n\n    public retrieve(item: T, out: T[]) {\n        let indexes = this._findIndexs(item);\n        if (this.children.length)\n            out.push.apply(out, this.children);\n        if (this.nodes.length) {\n            for (let i = 0, len = indexes.length; i < len; i++) {\n                this.nodes[indexes[i]].retrieve(item, out);\n            }\n        }\n        out = out.filter((item, index) => {\n            return out.indexOf(item) >= index;\n        });\n    }\n\n    /**\n     *查找当前碰撞体在那几个象限\n     *\n     * ---------\n     * | 0 | 3 |\n     * ----|----\n     * | 1 | 2 |\n     * ---------\n     *\n     * @param {T} item\n     * @return {*} \n     * @memberof QuadTree\n     */\n    public _findIndexs(pRect: T) {\n        let b = this._bounds;\n        //象限中点\n        let verticalMidpoint = b.x + b.halfWidth!;\n        let horizontalMidpoint = b.y + b.halfHeight!;\n        let bottom = pRect.y < horizontalMidpoint,\n            left = pRect.x < verticalMidpoint,\n            right = pRect.x + pRect.width > verticalMidpoint,\n            top = pRect.y + pRect.height > horizontalMidpoint;\n        let indexes = [];\n        //top-left quad\n        if (top && left) {\n            indexes.push(0);\n        }\n\n        //bottom-left quad\n        if (bottom && left) {\n            indexes.push(1);\n        }\n\n        //bottom-right quad\n        if (bottom && right) {\n            indexes.push(2);\n        }\n\n        //top-right quad\n        if (top && right) {\n            indexes.push(3);\n        }\n        return indexes;\n    }\n\n    /**\n     * ---------\n     * | 0 | 3 |\n     * ----|----\n     * | 1 | 2 |\n     * ---------\n     */\n    public subdivide() {\n        let nextLevel = this._depth + 1,\n            subWidth = this._bounds.halfWidth!,\n            subHeight = this._bounds.halfHeight!,\n            x = this._bounds.x,\n            y = this._bounds.y;\n        //top left node\n        this.nodes[0] = new QuadTree({\n            x: x,\n            y: y + subHeight,\n            width: subWidth,\n            height: subHeight\n        }, nextLevel, this._maxDepth, this._maxChildren);\n\n        //bottom left node\n        this.nodes[1] = new QuadTree({\n            x: x,\n            y: y,\n            width: subWidth,\n            height: subHeight\n        }, nextLevel, this._maxDepth, this._maxChildren);\n\n        //bottom right node\n        this.nodes[2] = new QuadTree({\n            x: x + subWidth,\n            y: y,\n            width: subWidth,\n            height: subHeight\n        }, nextLevel, this._maxDepth, this._maxChildren);\n\n        //top right node\n        this.nodes[3] = new QuadTree({\n            x: x + subWidth,\n            y: y + subHeight,\n            width: subWidth,\n            height: subHeight\n        }, nextLevel, this._maxDepth, this._maxChildren);\n    }\n\n    public clear() {\n        this.children.length = 0;\n        for (let i = 0, len = this.nodes.length; i < len; i++) {\n            this.nodes[i].clear();\n        }\n        this.nodes.length = 0;\n    }\n}\n"]}