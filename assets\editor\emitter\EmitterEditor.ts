import { _decorator, misc, instantiate, Color, Component, RichText, Rect, Vec3, Graphics, assetManager } from 'cc';
import { EDITOR } from 'cc/env';
import { eEmitterStatus, Emitter } from '../../scripts/game/bullet/Emitter';
import { BulletSystem } from '../../scripts/game/bullet/BulletSystem';
import PlaneBase from "db://assets/scripts/game/ui/plane/PlaneBase";
const { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;

@ccclass('EmitterEditor')
@menu('子弹系统/发射器编辑器')
@playOnFocus(true)
@executeInEditMode(true)
@disallowMultiple(true)
export class EmitterEditor extends Component {
    @property({visible:false})
    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒

    @property({displayName: "目标帧率"})
    public get targetFrameRate(): number {
        return 1000 / this.fixedDelta;
    }

    public set targetFrameRate(value: number) {
        this.fixedDelta = 1000 / value;
    }

    @property({type: RichText, override: true, displayName: "信息显示"})
    richText: RichText = null!;

    @property({type: PlaneBase, displayName:"玩家节点"})
    playerNode: PlaneBase|null = null;

    static frameCount: number = 0;
    static frameTimeInMilliseconds: number = 0;

    private _updateInEditor: boolean = false;
    private _graphicsCom: Graphics|null = null;
    public get graphics(): Graphics {
        if (!this._graphicsCom) {
            this._graphicsCom = this.getComponent(Graphics) || this.addComponent(Graphics);
        }
        return this._graphicsCom!;
    }

    _playerPos: Vec3 = new Vec3(0, 0, 0);

    resetInEditor() {
        this._updateInEditor = true;
        // console.log('resetInEditor');
    }

    onFocusInEditor() {
        this._updateInEditor = true;
        // console.log('onFocusInEditor');
        
        // @ts-ignore
        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));
        
        BulletSystem.init(new Rect(0, 0, 750, 1334));
        // fake player plane
        BulletSystem.playerPlane = this.playerNode;

        // loop all children to find emitters
        this.node.walk((node) => {
            const emitter = node.getComponent(Emitter);
            if (emitter) {
                emitter.setIsActive(true);
            }
        });

        this.drawWorldBounds();
    }

    onLostFocusInEditor(): void {
        this._updateInEditor = false;
        this.reset();
    }

    start() {
        this.reset();
    }

    reset() {
        EmitterEditor.frameCount = 0;
        EmitterEditor.frameTimeInMilliseconds = 0;
        BulletSystem.playerPlane = null;
        BulletSystem.destroyAllBullets(true);                
        this.node.walk((node) => {
            const emitter = node.getComponent(Emitter);
            if (emitter) {
                emitter.reset();
            }
        });
        BulletSystem.allEventGroups = [];
        this._playerPos.x = this._playerPos.y = this._playerPos.z = 0;
        if (this.playerNode) {
            this.playerNode.node.setPosition(this._playerPos);
        }
        this.graphics.clear();
        console.log('reset: ', this._updateInEditor);
    }

    update(dt: number) {
        if (EDITOR && this._updateInEditor) {
            this.updateInfoText();
            const milli_dt = dt * 1000;
            EmitterEditor.frameCount += 1;
            EmitterEditor.frameTimeInMilliseconds += milli_dt;
            BulletSystem.tick(dt);
        }
    }

    updateInfoText() {
        if (this.richText) {
            this.richText.string = `当前时间: ${EmitterEditor.frameTimeInMilliseconds.toFixed(2)}\n当前发射器数量: ${BulletSystem.allEmitters.length}\n当前子弹数量: ${BulletSystem.allBullets.length}\n当前事件组数量: ${BulletSystem.allEventGroups.length}`;
        }
    }

    drawWorldBounds() {
        this.graphics.clear();
        this.graphics.strokeColor = Color.RED;
        this.graphics.lineWidth = 20;
        const bounds = BulletSystem.worldBounds;
        this.graphics.rect(bounds.x - bounds.width/2, bounds.y - bounds.height/2, bounds.width, bounds.height);
        this.graphics.stroke();
    }

    // 编辑器方法
    public instantiatePrefab(prefabUuid: string) {
        // replace db://assets/resources/game/prefabs/emitter/ with assets/resources/game/prefabs/emitter/
        //prefabUrl = prefabUrl.replace('db://', '');
        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {
            if (err) {
                console.log('Failed to load prefab:', err);
                return;
            }
            const node = instantiate(prefab!);
            const parent = this.node;
            parent!.addChild(node);
        });
    }

    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {
    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        
    //     return new Promise<string>((resolve, reject) => {
    //         const scene = director.getScene();
    //         const target = scene!.getChildByUuid(nodeUuid);
    //         if (!target) {
    //             console.error("node not found:", nodeUuid);
    //             reject();
    //             return;
    //         }
    //         const json = cce.Utils.serialize(target);
    //         // 将节点保存为 Prefab
    //         // _utils.applyTargetOverrides(target as Node);
    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);
    //         resolve(json);
    //     });
    // }
}
