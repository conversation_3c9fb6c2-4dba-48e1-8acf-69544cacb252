System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, UITransform, GameConst, GameFunc, _crd;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "./const/GameConst", _context.meta, extras);
  }

  _export("GameFunc", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['Sprite', 'SpriteAtlas', 'UITransform']);

      _export("GameFunc", GameFunc = class GameFunc {
        /**
         * 获取唯一的 UUID
         * @returns {number} 返回一个自增的唯一标识符
         */
        static get uuid() {
          this._uuid++;
          return this._uuid;
        }

        static wxLoadErr() {}

        static setImage(sprite, spriteName, atlas, adjustSize = false) {
          if (atlas) {
            sprite.spriteFrame = atlas.getSpriteFrame(spriteName);

            if (adjustSize && sprite.spriteFrame) {
              sprite.node.getComponent(UITransform).width = 2 / 3 * sprite.spriteFrame.width;
              sprite.node.getComponent(UITransform).height = 2 / 3 * sprite.spriteFrame.height;
            }
          }
        }
        /**
         * 根据帧范围计算动画持续时间
         * @param startFrame 起始帧
         * @param endFrame 结束帧
         * @returns 动画持续时间（秒）
         */


        static fromTo(startFrame, endFrame) {
          const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime; // 每帧的时间（秒）

          return (endFrame - startFrame) * frameTime;
        }

      });

      GameFunc.loadErr = false;
      GameFunc._uuid = 0;
      _crd = false;
    }
  };
});
//# sourceMappingURL=51d31ff7c7dac6f381c067bba380c150910de6b9.js.map