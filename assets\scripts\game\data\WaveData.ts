
import { _decorator, error, v2, Vec2, <PERSON><PERSON>b, Enum, CCInteger } from "cc";
import { ExpressionValue } from "./bullet/ExpressionValue";
import { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType } from "./bullet/EventGroupData";
import { eEasing } from "../bullet/Easing";
const { ccclass, property } = _decorator;

export enum eSpawnOrder {
    Sequential = 0,
    Random = 1,
}

export enum eWaveAngleType {
    FacingMoveDir, FacingPlayer, Fixed, 
}

export enum eWaveConditionType {
    Player_Level,       // 玩家等级

    Spawn_Index,        // 当前生成的索引
}

export enum eWaveActionType {
    Speed,
    SpeedAngle,
}

// 和发射器的事件组类似
@ccclass("WaveConditionData")
export class WaveConditionData implements IEventConditionData {
    @property({ type: Enum(eConditionOp), displayName: '条件关系' })
    public op: eConditionOp = eConditionOp.And;

    @property({visible:false})
    public type: eWaveConditionType = eWaveConditionType.Player_Level;

    @property({ type: Enum(eCompareOp), displayName: '比较方式' })
    public compareOp: eCompareOp = eCompareOp.Equal;
    
    // 条件值: 例如持续时间、距离
    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
}

@ccclass("WaveActionData")
export class WaveActionData implements IEventActionData {
    @property({visible:false})
    public type: eWaveActionType = eWaveActionType.Speed;
    
    // 持续时间: 0表示立即执行
    @property({visible:false})
    public duration: ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '持续时间' })
    public get durationStr(): string { return this.duration.raw; }
    public set durationStr(value: string) { this.duration.raw = value; }
    
    @property({visible:false})
    public targetValue: ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
    
    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })
    public targetValueType: eTargetValueType = eTargetValueType.Absolute;
    
    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    public easing: eEasing = eEasing.Linear;
}

@ccclass("WaveEventGroupData")
export class WaveEventGroupData {
    @property({ displayName: '事件组名称' })
    public name: string = "";

    @property({ type: [WaveConditionData], displayName: '条件列表' })
    public conditions: WaveConditionData[] = [];

    @property({ type: [WaveActionData], displayName: '行为列表' })
    public actions: WaveActionData[] = [];
}

/**
 * 波次数据：未来代替现有的EnemyWave
 * 所有时间相关的，单位都是毫秒(ms)
 */
@ccclass("WaveData")
export class WaveData {
    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发
    // 因此这里不再配置触发条件
    @property({visible:false})
    planeList: number[] = [];

    @property({displayName: "飞机列表(,号分隔)", group: "基础属性" })
    public get planeListStr(): string {
        return (this.planeList || []).join(",");
    }
    public set planeListStr(value: string) {
        if (value === "") {
            this.planeList = [];
            return;
        }
        this.planeList = value.split(",").map((item) => parseInt(item));
    }

    @property({type: Enum(eSpawnOrder), displayName: "出生顺序", group: "基础属性"})
    spawnOrder: eSpawnOrder = eSpawnOrder.Sequential;

    @property({displayName: "出生间隔(ms)", group: "基础属性"})
    spawnInterval: number = 1000;

    @property({visible:false})
    public spawnPosX : ExpressionValue = new ExpressionValue('0');
    @property({visible:false})
    public spawnPosY : ExpressionValue = new ExpressionValue('0');
    @property({displayName: "出生位置X", group: "基础属性"})
    public get spawnPosXStr(): string { return this.spawnPosX.raw; }
    public set spawnPosXStr(value: string) { this.spawnPosX.raw = value; }
    @property({displayName: "出生位置Y", group: "基础属性"})
    public get spawnPosYStr(): string { return this.spawnPosY.raw; }
    public set spawnPosYStr(value: string) { this.spawnPosY.raw = value; }

    private _spawnPos: Vec2 = new Vec2();
    public get spawnPos(): Vec2 {
        this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());
        return this._spawnPos;
    }

    @property({visible:false})
    public spawnAngle : ExpressionValue = new ExpressionValue('0');
    @property({displayName: "出生角度", group: "基础属性"})
    public get spawnAngleStr(): string { return this.spawnAngle.raw; }
    public set spawnAngleStr(value: string) { this.spawnAngle.raw = value; }

    @property({type: CCInteger, displayName: "延迟销毁时间", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁', group: "基础属性"})
    delayDestroy: number = 5000;

    @property({type: CCInteger, displayName: "出生速度", group: "基础属性"})
    spawnSpeed: number = 500;

    @property({type: Enum(eWaveAngleType), displayName: "单位朝向类型", group: "基础属性"})
    planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;
    public get isFixedAngleType(): boolean {
        return this.planeAngleType == eWaveAngleType.Fixed;
    }

    @property({type: CCInteger, 
        // visible() { 
        //     return this.isFixedAngleType;
        // },
        displayName: "单位朝向", tooltip: '仅在单位朝向类型为Fixed时有效', group: '基础属性'})
    planeAngleFixed: number = 0;

    @property({type: [WaveEventGroupData], displayName: '事件组', group: '事件组'})
    eventGroupData: WaveEventGroupData[] = [];
}