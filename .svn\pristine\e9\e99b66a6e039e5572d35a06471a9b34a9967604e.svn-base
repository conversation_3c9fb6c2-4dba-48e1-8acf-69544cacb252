// @ts-ignore
import packageJSON from '../package.json';
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
    saveLevel() {
        console.log('saveLevel in main');
        // @ts-ignore
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "saveLevel", args: [] });
    },
    playLevel() {
        console.log('playLevel in main');
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "playLevel", args: [] });
    },
    levelStart() {
        console.log('levelStart in main this',this);
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "levelStart", args: [] });
    },
    levelEnd() {
        console.log('levelEnd in main');
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "levelEnd", args: [] });
    }
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() { }

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() { }
