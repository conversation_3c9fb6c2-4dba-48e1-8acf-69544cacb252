System.register(["cc"], function (_export, _context) {
  "use strict";

  var __checkObsolete__, __checkObsoleteInNamespace__, instantiate, NodePool, ObjectPool, _crd;

  _export("ObjectPool", void 0);

  return {
    setters: [function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
      NodePool = _cc.NodePool;
    }],
    execute: function () {
      _crd = true;

      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */
      __checkObsolete__(['_decorator', 'instantiate', 'Node', 'Prefab', 'NodePool']);

      _export("ObjectPool", ObjectPool = class ObjectPool {
        static getPool(key) {
          if (!this.all_pools[key]) {
            this.all_pools[key] = new NodePool();
          }

          return this.all_pools[key];
        }

        static clearPool(key) {
          const pool = this.all_pools[key];

          if (pool) {
            pool.clear();
          }
        }

        static clearAll() {
          for (const key in this.all_pools) {
            this.clearPool(key);
          }

          this.all_pools = {};
        }

        static getNode(node_parent, prefab) {
          let pool = this.getPool(prefab.uuid);
          let node = null;

          if (pool.size() > 0) {
            // use size method to check if there're nodes available in the pool
            node = pool.get();
          } else {
            // if not enough node in the pool, we call cc.instantiate to create node
            node = instantiate(prefab);
          }

          node.parent = node_parent; // add new enemy node to the node tree
          // @ts-ignore

          node.puuid = prefab.uuid;
          return node;
        }

        static returnNode(node) {
          //@ts-ignore
          if (!node.puuid) {
            console.warn("Node does not have a prefab UUID.");
            node.destroy();
            return;
          } //@ts-ignore


          let pool = this.all_pools[node.puuid];

          if (pool) {
            pool.put(node);
          } else {
            node.destroy();
          }
        }

      });

      ObjectPool.all_pools = {};
      _crd = false;
    }
  };
});
//# sourceMappingURL=70e09bb861e257c594d1f417c92bcfcf688cbf87.js.map