{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossPlane.ts"], "names": ["_decorator", "size", "v2", "TrackComponent", "GameEnum", "Tools", "GameIns", "TrackGroup", "FBoxCollider", "ColliderGroupType", "EnemyPlaneBase", "ccclass", "property", "BossPlane", "_datas", "_data", "_trackCom", "_idleName", "_formIndex", "_formNum", "_posX", "_posY", "_moveToX", "_moveToY", "_moveSpeed", "_bArriveDes", "_transFormMove", "_nextWayPointTime", "_nextWayPointX", "_nextWayPointY", "_nextWayPointInterval", "_nextWaySpeed", "_shootAble", "_atkActions", "_bOrde<PERSON><PERSON><PERSON>ck", "_orderIndex", "_orderAtkArr", "_atkPointDatas", "_action", "_bDamageable", "_b<PERSON><PERSON>ckMove", "_bFirstWayPoint", "transformBattle", "_bRemoveable", "_nextAttackInterval", "_nextAttackTime", "_attackID", "tip", "_hpWhiteTween", "bullets", "initBoss", "datas", "enemy", "init", "length", "_initProperty", "_initTrack", "_initCollide", "setFormIndex", "curHp", "maxHp", "getAttack", "addScript", "node", "setTrackGroupStartCall", "setTrackGroupOverCall", "BossAction", "Appear", "setTrackAble", "setAction", "Transform", "setTrackOverCall", "setTrackLeaveCall", "setTrackStartCall", "track", "collide<PERSON>omp", "getComponent", "addComponent", "groupType", "ENEMY_NORMAL", "colliderEnabled", "action", "Normal", "_playSkel", "bDamageable", "_startAppearTrack", "transformEnd", "AttackPrepare", "scheduleOnce", "AttackIng", "AttackOver", "Blast", "anim<PERSON><PERSON>", "loop", "callback", "plane", "playAnim", "setTip", "battleManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bossFightStart", "index", "i", "attackActions", "push", "point", "attackPoints", "data", "bAvailable", "enterNextForm", "startBattle", "_startNormalTrack", "updateGameLogic", "deltaTime", "isDead", "_processNextWayPoint", "_updateMove", "_processNextAttack", "_udpateShoot", "trackGroup", "loopNum", "trackIDs", "<PERSON><PERSON><PERSON><PERSON>", "speeds", "trackIntervals", "startTrack", "trackGroups", "x", "y", "moveToPos", "speed", "transformMove", "setPos", "update", "setPosition", "getRandomInArray", "wayPointIntervals", "random_int", "wayPointXs", "wayPointYs", "deltaX", "deltaY", "distance", "Math", "sqrt", "moveX", "moveY", "abs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAWC,MAAAA,I,OAAAA,I;AAAKC,MAAAA,E,OAAAA,E;;AAClBC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AAEFC,MAAAA,Y;;AACaC,MAAAA,iB,iBAAAA,iB;;AAEbC,MAAAA,c;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAGTa,S,WADpBF,OAAO,CAAC,WAAD,C,gBAAR,MACqBE,SADrB;AAAA;AAAA,4CACsD;AAAA;AAAA;AAAA,eAElDC,MAFkD,GAE7B,EAF6B;AAAA,eAGlDC,KAHkD,GAGzB,IAHyB;AAAA,eAIlDC,SAJkD,GAIf,IAJe;AAAA,eAKlDC,SALkD,GAK9B,OAL8B;AAAA,eAMlDC,UANkD,GAM7B,CAAC,CAN4B;AAM1B;AAN0B,eAOlDC,QAPkD,GAO/B,CAP+B;AAO7B;AAP6B,eASlDC,KATkD,GASlC,CATkC;AAAA,eAUlDC,KAVkD,GAUlC,CAVkC;AAAA,eAYlDC,QAZkD,GAY/B,CAZ+B;AAAA,eAalDC,QAbkD,GAa/B,CAb+B;AAAA,eAclDC,UAdkD,GAc7B,CAd6B;AAAA,eAelDC,WAfkD,GAe3B,KAf2B;AAerB;AAfqB,eAgBlDC,cAhBkD,GAgBxB,KAhBwB;AAiBlD;AAjBkD,eAkBlDC,iBAlBkD,GAkBtB,CAlBsB;AAAA,eAmBlDC,cAnBkD,GAmBzB,CAnByB;AAAA,eAoBlDC,cApBkD,GAoBzB,CApByB;AAAA,eAqBlDC,qBArBkD,GAqBlB,CArBkB;AAAA,eAsBlDC,aAtBkD,GAsB1B,CAtB0B;AAAA,eAuBlDC,UAvBkD,GAuB5B,IAvB4B;AAAA,eAwBlDC,WAxBkD,GAwB7B,EAxB6B;AAAA,eA0BlDC,aA1BkD,GA0BzB,KA1ByB;AAAA,eA2BlDC,WA3BkD,GA2B5B,CA3B4B;AAAA,eA4BlDC,YA5BkD,GA4BzB,EA5ByB;AAAA,eA8BlDC,cA9BkD,GA8B1B,EA9B0B;AAAA,eAgClDC,OAhCkD,GAgChC,CAAC,CAhC+B;AAAA,eAiClDC,YAjCkD,GAiC1B,KAjC0B;AAAA,eAkClDC,YAlCkD,GAkC1B,KAlC0B;AAAA,eAmClDC,eAnCkD,GAmCvB,KAnCuB;AAAA,eAoClDC,eApCkD,GAoCvB,IApCuB;AAAA,eAqClDC,YArCkD,GAqC1B,KArC0B;AAAA,eAuClDC,mBAvCkD,GAuCpB,CAvCoB;AAAA,eAwClDC,eAxCkD,GAwCxB,CAxCwB;AAAA,eAyClDC,SAzCkD,GAyC9B,CAzC8B;AAAA,eA2ClDC,GA3CkD,GA2CpC,EA3CoC;AAAA,eA4ClDC,aA5CkD,GA4C7B,IA5C6B;AAAA,eA6ClDC,OA7CkD,GA6CjC,EA7CiC;AAAA;;AA+ClD;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,KAAD,EAAoB;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,gBAAMC,IAAN;AACA,eAAKvC,MAAL,GAAcqC,KAAd;AACA,eAAKhC,QAAL,GAAgB,KAAKL,MAAL,CAAYwC,MAA5B;AACA,eAAKb,eAAL,GAAuB,IAAvB;;AACA,eAAKc,aAAL;;AACA,eAAKC,UAAL;;AACA,eAAKC,YAAL;;AACA,eAAKC,YAAL,CAAkB,CAAlB;AACH;;AAGDH,QAAAA,aAAa,GAAE;AACX;AACA,eAAKI,KAAL,GAAa,IAAb;AACA,eAAKC,KAAL,GAAa,KAAKD,KAAlB;AACH;;AAEDE,QAAAA,SAAS,GAAW;AAChB,iBAAO,EAAP;AACH;;AAEDL,QAAAA,UAAU,GAAG;AACT,eAAKxC,SAAL,GAAiB;AAAA;AAAA,8BAAM8C,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;;AACA,eAAK/C,SAAL,CAAgBgD,sBAAhB,CAAuC,MAAM,CAAG,CAAhD;;AACA,eAAKhD,SAAL,CAAgBiD,qBAAhB,CAAsC,MAAM;AACxC,gBAAI,KAAK3B,OAAL,KAAiB;AAAA;AAAA,sCAAS4B,UAAT,CAAoBC,MAAzC,EAAiD;AAC7C,mBAAKnD,SAAL,CAAgBoD,YAAhB,CAA6B,KAA7B;;AACA,mBAAKC,SAAL,CAAe;AAAA;AAAA,wCAASH,UAAT,CAAoBI,SAAnC;AACH;AACJ,WALD;;AAMA,eAAKtD,SAAL,CAAgBuD,gBAAhB,CAAiC,MAAM,CAAG,CAA1C;;AACA,eAAKvD,SAAL,CAAgBwD,iBAAhB,CAAkC,MAAM,CAAG,CAA3C;;AACA,eAAKxD,SAAL,CAAgByD,iBAAhB,CAAmCC,KAAD,IAAgB,CAEjD,CAFD;AAGH;;AAEDjB,QAAAA,YAAY,GAAS;AACjB,eAAKkB,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBtB,IAAlB,CAAuB,IAAvB,EAA6BpD,IAAI,CAAC,GAAD,EAAM,GAAN,CAAjC,EAFiB,CAE6B;;AAC9C,eAAK0E,WAAL,CAAkBG,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAEDX,QAAAA,SAAS,CAACY,MAAD,EAAiB;AACtB,cAAI,KAAK3C,OAAL,KAAiB2C,MAArB,EAA6B;AACzB,iBAAK3C,OAAL,GAAe2C,MAAf;AAEA,gBAAIf,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAK5B,OAAb;AACI,mBAAK4B,UAAU,CAACgB,MAAhB;AACI,qBAAKC,SAAL,CAAe,KAAKlE,SAApB,EAA+B,IAA/B,EAAqC,MAAM,CAAG,CAA9C;;AACA,qBAAKmE,WAAL,GAAmB,IAAnB;AACA;;AAEJ,mBAAKlB,UAAU,CAACC,MAAhB;AACI,qBAAKgB,SAAL,CAAgB,QAAO,KAAKjE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,IAA9C,EAAoD,MAAM,CAAG,CAA7D;;AACA,qBAAKkE,WAAL,GAAmB,KAAnB;;AACA,qBAAKC,iBAAL;;AACA;;AAEJ,mBAAKnB,UAAU,CAACI,SAAhB;AACI,qBAAKa,SAAL,CAAgB,QAAO,KAAKjE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,KAA9C,EAAqD,MAAM;AACvD,uBAAKwB,eAAL,IAAwB,KAAK4C,YAAL,EAAxB;AACH,iBAFD;;AAGA,qBAAKF,WAAL,GAAmB,KAAnB;AACA;;AAEJ,mBAAKlB,UAAU,CAACqB,aAAhB;AACI,qBAAKC,YAAL,CAAkB,MAAM;AACpB,uBAAKnB,SAAL,CAAeH,UAAU,CAACuB,SAA1B;AACH,iBAFD;AAGA,qBAAKL,WAAL,GAAmB,IAAnB;AACA;;AAEJ,mBAAKlB,UAAU,CAACuB,SAAhB;AACA,mBAAKvB,UAAU,CAACwB,UAAhB;AACI,qBAAKN,WAAL,GAAmB,IAAnB;AACA;;AAEJ,mBAAKlB,UAAU,CAACyB,KAAhB;AACI,qBAAKP,WAAL,GAAmB,KAAnB;AACA;;AAEJ;AACI,qBAAKA,WAAL,GAAmB,IAAnB;AApCR;AAsCH;AACJ;;AAEDD,QAAAA,SAAS,CAACS,QAAD,EAAmBC,IAAnB,EAAkCC,QAAlC,EAAsD;AAC3D,eAAKC,KAAL,CAAYC,QAAZ,CAAqBJ,QAArB,EAA+BC,IAA/B,EAAqCC,QAArC;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,MAAM,CAAClD,GAAD,EAAc;AAChB,eAAKA,GAAL,GAAWA,GAAX;AACH;AACD;AACJ;AACA;;;AACIuC,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKvC,GAAL,KAAa,EAAjB,EAAqB;AACjB;AAAA;AAAA,oCAAQmD,aAAR,CAAsBC,gBAAtB,CAAuC,KAAKpD,GAA5C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQmD,aAAR,CAAsBE,cAAtB;AACH;AACJ;AAGD;AACJ;AACA;AACA;;;AACI1C,QAAAA,YAAY,CAAC2C,KAAD,EAAgB;AACxB,cAAI,KAAKnF,UAAL,KAAoBmF,KAAxB,EAA+B;AAC3B,iBAAKnF,UAAL,GAAkBmF,KAAlB;AACA,iBAAKnE,aAAL,GAAqB,IAArB;AACA,iBAAKC,WAAL,GAAmB,CAAnB;AACA,iBAAKpB,KAAL,GAAa,KAAKD,MAAL,CAAY,KAAKI,UAAjB,CAAb;AACA,iBAAKD,SAAL,GAAkB,OAAM,KAAKC,UAAL,GAAkB,CAAE,EAA5C;;AAEA,gBAAImF,KAAK,KAAK,CAAd,EAAiB;AACb,mBAAKhC,SAAL,CAAe;AAAA;AAAA,wCAASH,UAAT,CAAoBC,MAAnC;AACH;;AAED,iBAAK/B,YAAL,GAAoB,EAApB;;AACA,iBAAK,IAAIkE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvF,KAAL,CAAWwF,aAAX,CAAyBjD,MAA7C,EAAqDgD,CAAC,EAAtD,EAA0D;AACtD,mBAAKlE,YAAL,CAAkBoE,IAAlB,CAAuBF,CAAvB;AACH;;AAED,iBAAKjE,cAAL,GAAsB,EAAtB;;AACA,iBAAK,MAAMoE,KAAX,IAAoB,KAAK1F,KAAL,CAAW2F,YAA/B,EAA6C;AACzC,oBAAMC,IAAI,GAAG,CAACF,KAAK,CAACG,UAAP,EAAmBH,KAAnB,CAAb;;AACA,mBAAKpE,cAAL,CAAoBmE,IAApB,CAAyBG,IAAzB;AACH;;AAED,iBAAK1E,WAAL,GAAmB,CAAC,GAAG,KAAKlB,KAAL,CAAWwF,aAAf,CAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACIM,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAK3F,UAAL,GAAkB,KAAKJ,MAAL,CAAYwC,MAAZ,GAAqB,CAA3C,EAA8C;AAC1C,iBAAKpC,UAAL;AACA,iBAAKwC,YAAL,CAAkB,KAAKxC,UAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACI4F,QAAAA,WAAW,GAAG;AACV,eAAKC,iBAAL;;AACA,eAAK1C,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBgB,MAAnC;AACA,eAAKF,eAAL,GAAuB,IAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIgC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,gBAAIhD,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAK5B,OAAb;AACI,mBAAK4B,UAAU,CAACgB,MAAhB;AACI,qBAAKiC,oBAAL,CAA0BF,SAA1B;;AACA,qBAAKG,WAAL,CAAiBH,SAAjB;;AACA,qBAAKI,kBAAL,CAAwBJ,SAAxB;;AACA;;AAEJ,mBAAK/C,UAAU,CAACC,MAAhB;AACI,qBAAKiD,WAAL,CAAiBH,SAAjB;;AACA,oBAAI,KAAKxF,WAAT,EAAsB;AAClB,uBAAK4C,SAAL,CAAeH,UAAU,CAACI,SAA1B;AACH;;AACD;;AAEJ,mBAAKJ,UAAU,CAACI,SAAhB;AACI,oBAAI,KAAK5C,cAAT,EAAyB;AACrB,uBAAK0F,WAAL,CAAiBH,SAAjB;AACH;;AACD;;AAEJ,mBAAK/C,UAAU,CAACqB,aAAhB;AACI,qBAAK4B,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAKzE,YAAT,EAAuB;AACnB,uBAAK4E,WAAL,CAAiBH,SAAjB;AACH;;AACD;;AAEJ,mBAAK/C,UAAU,CAACuB,SAAhB;AACI,qBAAK0B,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAKzE,YAAT,EAAuB;AACnB,uBAAK4E,WAAL,CAAiBH,SAAjB;AACH;;AACD,qBAAKK,YAAL,CAAkBL,SAAlB;;AACA;;AAEJ,mBAAK/C,UAAU,CAACwB,UAAhB;AACI,qBAAKyB,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAKzE,YAAT,EAAuB;AACnB,uBAAK4E,WAAL,CAAiBH,SAAjB;AACH;;AACD,qBAAK5C,SAAL,CAAeH,UAAU,CAACgB,MAA1B;AACA;;AAEJ,mBAAKhB,UAAU,CAACyB,KAAhB;AACI;AA5CR;AA8CH;AACJ;AAED;AACJ;AACA;;;AACIN,QAAAA,iBAAiB,GAAG;AAChB,gBAAMkC,UAAU,GAAG;AAAA;AAAA,yCAAnB;AACAA,UAAAA,UAAU,CAACC,OAAX,GAAqB,CAArB;AACAD,UAAAA,UAAU,CAACE,QAAX,GAAsB,CAAC,KAAK1G,KAAL,CAAY2G,WAAZ,CAAwB,CAAxB,CAAD,CAAtB;AACAH,UAAAA,UAAU,CAACI,MAAX,GAAoB,CAAC,KAAK5G,KAAL,CAAY2G,WAAZ,CAAwB,CAAxB,CAAD,CAApB;AACAH,UAAAA,UAAU,CAACK,cAAX,GAA4B,CAAC,CAAD,CAA5B;;AAEA,eAAK5G,SAAL,CAAgBqC,IAAhB,CAAqB,IAArB,EAA2B,CAACkE,UAAD,CAA3B,EAAyC,EAAzC,EAA6CrH,EAAE,CAAC,KAAKa,KAAL,CAAY2G,WAAZ,CAAwB,CAAxB,CAAD,EAA6B,KAAK3G,KAAL,CAAY2G,WAAZ,CAAwB,CAAxB,CAA7B,CAA/C;;AACA,eAAK1G,SAAL,CAAgBoD,YAAhB,CAA6B,IAA7B;;AACA,eAAKpD,SAAL,CAAgB6G,UAAhB;AACH;AAED;AACJ;AACA;;;AACId,QAAAA,iBAAiB,GAAG;AAChB,eAAK/F,SAAL,CAAgBqC,IAAhB,CAAqB,IAArB,EAA2B,KAAKtC,KAAL,CAAY+G,WAAvC,EAAoD,EAApD,EAAwD5H,EAAE,CAAC,KAAK6D,IAAL,CAAUgE,CAAX,EAAc,KAAKhE,IAAL,CAAUiE,CAAxB,CAA1D;;AACA,eAAKhH,SAAL,CAAgBoD,YAAhB,CAA6B,IAA7B;;AACA,eAAKpD,SAAL,CAAgB6G,UAAhB;;AACA,eAAKxD,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBgB,MAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI+C,QAAAA,SAAS,CAACF,CAAD,EAAYC,CAAZ,EAAuBE,KAAvB,EAAsCC,aAAsB,GAAG,KAA/D,EAAsE;AAC3E,eAAK7G,QAAL,GAAgByG,CAAhB;AACA,eAAKxG,QAAL,GAAgByG,CAAhB;AACA,eAAKxG,UAAL,GAAkB0G,KAAlB;AACA,eAAKzG,WAAL,GAAmB,KAAnB;AACA,eAAKC,cAAL,GAAsByG,aAAtB;AACH;;AAGDC,QAAAA,MAAM,CAACL,CAAD,EAAYC,CAAZ,EAAuBK,MAAe,GAAG,IAAzC,EAA+C;AACjD,eAAKtE,IAAL,CAAUuE,WAAV,CAAsBP,CAAtB,EAAyBC,CAAzB;AACA,eAAK5G,KAAL,GAAa2G,CAAb;AACA,eAAK1G,KAAL,GAAa2G,CAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIb,QAAAA,oBAAoB,CAACF,SAAD,EAAoB;AACpC,cAAI,KAAKxF,WAAL,IAAoB,KAAKV,KAAL,CAAY+G,WAAZ,CAAwBxE,MAAxB,KAAmC,CAA3D,EAA8D;AAC1D,iBAAK3B,iBAAL,IAA0BsF,SAA1B;;AACA,gBAAI,KAAKtF,iBAAL,GAAyB,KAAKG,qBAAlC,EAAyD;AACrD,mBAAKA,qBAAL,GAA6B;AAAA;AAAA,kCAAMyG,gBAAN,CAAuB,KAAKxH,KAAL,CAAYyH,iBAAnC,CAA7B;AACA,mBAAK7G,iBAAL,GAAyB,CAAzB;;AAEA,kBAAI,KAAKc,eAAT,EAA0B;AACtB,qBAAKA,eAAL,GAAuB,KAAvB;AACH,eAFD,MAEO;AACH,sBAAM4D,KAAK,GAAG;AAAA;AAAA,oCAAMoC,UAAN,CAAiB,CAAjB,EAAoB,KAAK1H,KAAL,CAAY2H,UAAZ,CAAuBpF,MAAvB,GAAgC,CAApD,CAAd;AACA,qBAAK1B,cAAL,GAAsB,KAAKb,KAAL,CAAY2H,UAAZ,CAAuBrC,KAAvB,CAAtB;AACA,qBAAKxE,cAAL,GAAsB,KAAKd,KAAL,CAAY4H,UAAZ,CAAuBtC,KAAvB,CAAtB;AACA,qBAAKtE,aAAL,GAAqB;AAAA;AAAA,oCAAMwG,gBAAN,CAAuB,KAAKxH,KAAL,CAAY4G,MAAnC,CAArB;AACA,qBAAKM,SAAL,CAAe,KAAKrG,cAApB,EAAoC,KAAKC,cAAzC,EAAyD,KAAKE,aAA9D;AACH;AACJ;AACJ;AACJ;;AAGDqF,QAAAA,WAAW,CAACH,SAAD,EAAoB;AAC3B,cAAI,KAAK3E,OAAL,KAAiB;AAAA;AAAA,oCAAS4B,UAAT,CAAoBC,MAArC,IAA+C,KAAKpD,KAAL,CAAY+G,WAAZ,CAAwBxE,MAAxB,GAAiC,CAApF,EAAuF;AACnF;AACA,iBAAKtC,SAAL,CAAgBgG,eAAhB,CAAgCC,SAAhC;AACH,WAHD,MAGO,IAAI,CAAC,KAAKxF,WAAV,EAAuB;AAC1B;AACA;AACA;AAEA,kBAAMmH,MAAM,GAAG,KAAKtH,QAAL,GAAgB,KAAKF,KAApC;AACA,kBAAMyH,MAAM,GAAG,KAAKtH,QAAL,GAAgB,KAAKF,KAApC;AACA,kBAAMyH,QAAQ,GAAGC,IAAI,CAACC,IAAL,CAAUJ,MAAM,GAAGA,MAAT,GAAkBC,MAAM,GAAGA,MAArC,CAAjB;AAEA,gBAAII,KAAK,GAAG,CAAZ;AACA,gBAAIC,KAAK,GAAG,CAAZ,CAV0B,CAY1B;;AACA,gBAAIJ,QAAQ,IAAI,KAAKtH,UAArB,EAAiC;AAC7ByH,cAAAA,KAAK,GAAGL,MAAR;AACAM,cAAAA,KAAK,GAAGL,MAAR;AACH,aAHD,CAIA;AAJA,iBAKK;AACDI,cAAAA,KAAK,GAAG,KAAKzH,UAAL,GAAkBoH,MAAlB,GAA2BE,QAAnC;AACAI,cAAAA,KAAK,GAAG,KAAK1H,UAAL,GAAkBqH,MAAlB,GAA2BC,QAAnC;AACH,aArByB,CAuB1B;;;AACA,iBAAK1H,KAAL,IAAc6H,KAAd;AACA,iBAAK5H,KAAL,IAAc6H,KAAd;AACA,iBAAKd,MAAL,CAAY,KAAKhH,KAAjB,EAAwB,KAAKC,KAA7B,EA1B0B,CA4B1B;;AACA,iBAAKI,WAAL,GAAoBsH,IAAI,CAACI,GAAL,CAASF,KAAT,IAAkB,GAAlB,IAAyBF,IAAI,CAACI,GAAL,CAASD,KAAT,IAAkB,GAA/D;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI7B,QAAAA,kBAAkB,CAACJ,SAAD,EAAoB,CAClC;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACsB,cAAZK,YAAY,CAACL,SAAD,EAAoB,CAClC;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACH;;AApciD,O", "sourcesContent": ["import { _decorator,size,v2,} from 'cc';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\nimport { BossData } from '../../../data/BossData';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport { Bullet } from '../../../bullet/Bullet';\r\nimport EnemyPlaneBase from '../enemy/EnemyPlaneBase';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BossPlane\")\r\nexport default class BossPlane extends EnemyPlaneBase {\r\n\r\n    _datas: BossData[] = [];\r\n    _data: BossData | null = null;\r\n    _trackCom: TrackComponent | null = null;\r\n    _idleName: string = \"idle1\";\r\n    _formIndex: number = -1;//形态索引\r\n    _formNum: number = 0;//形态数量\r\n\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n\r\n    _moveToX: number = 0;\r\n    _moveToY: number = 0;\r\n    _moveSpeed: number = 0;\r\n    _bArriveDes: boolean = false;//是否达到目标点\r\n    _transFormMove: boolean = false;\r\n    //下一个航点\r\n    _nextWayPointTime: number = 0;\r\n    _nextWayPointX: number = 0;\r\n    _nextWayPointY: number = 0;\r\n    _nextWayPointInterval: number = 0;\r\n    _nextWaySpeed: number = 0;\r\n    _shootAble: boolean = true;\r\n    _atkActions: any[] = [];\r\n\r\n    _bOrderAttack: boolean = false;\r\n    _orderIndex: number = 0;\r\n    _orderAtkArr: number[] = [];\r\n\r\n    _atkPointDatas: any[] = [];\r\n\r\n    _action: number = -1;\r\n    _bDamageable: boolean = false;\r\n    _bAttackMove: boolean = false;\r\n    _bFirstWayPoint: boolean = false;\r\n    transformBattle: boolean = true;\r\n    _bRemoveable: boolean = false;\r\n\r\n    _nextAttackInterval: number = 0;\r\n    _nextAttackTime: number = 0;\r\n    _attackID: number = 0;\r\n\r\n    tip: string = \"\";\r\n    _hpWhiteTween: any = null;\r\n    bullets: any[] = [];\r\n\r\n    /**\r\n     * 初始化 Boss 数据\r\n     * @param datas Boss 数据数组\r\n     */\r\n    initBoss(datas: BossData[]) {\r\n        this.enemy = true\r\n        super.init();\r\n        this._datas = datas;\r\n        this._formNum = this._datas.length;\r\n        this._bFirstWayPoint = true;\r\n        this._initProperty();\r\n        this._initTrack();\r\n        this._initCollide();\r\n        this.setFormIndex(0);\r\n    }\r\n\r\n\r\n    _initProperty(){\r\n        //暂时写死，后续读取新配表\r\n        this.curHp = 4500;\r\n        this.maxHp = this.curHp;\r\n    }\r\n\r\n    getAttack(): number {\r\n        return 60;\r\n    }\r\n\r\n    _initTrack() {\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._trackCom!.setTrackGroupStartCall(() => { });\r\n        this._trackCom!.setTrackGroupOverCall(() => {\r\n            if (this._action === GameEnum.BossAction.Appear) {\r\n                this._trackCom!.setTrackAble(false);\r\n                this.setAction(GameEnum.BossAction.Transform);\r\n            }\r\n        });\r\n        this._trackCom!.setTrackOverCall(() => { });\r\n        this._trackCom!.setTrackLeaveCall(() => { });\r\n        this._trackCom!.setTrackStartCall((track: any) => {\r\n\r\n        });\r\n    }\r\n\r\n    _initCollide(): void {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(100, 100)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n    setAction(action: number) {\r\n        if (this._action !== action) {\r\n            this._action = action;\r\n\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._playSkel(this._idleName, true, () => { });\r\n                    this.bDamageable = true;\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._playSkel(`enter${this._formIndex + 1}`, true, () => { });\r\n                    this.bDamageable = false;\r\n                    this._startAppearTrack();\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {\r\n                        this.transformBattle && this.transformEnd();\r\n                    });\r\n                    this.bDamageable = false;\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this.scheduleOnce(() => {\r\n                        this.setAction(BossAction.AttackIng);\r\n                    });\r\n                    this.bDamageable = true;\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                case BossAction.AttackOver:\r\n                    this.bDamageable = true;\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    this.bDamageable = false;\r\n                    break;\r\n\r\n                default:\r\n                    this.bDamageable = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    _playSkel(animName: string, loop: boolean, callback: Function) {\r\n        this.plane!.playAnim(animName, loop, callback)\r\n    }\r\n\r\n    /**\r\n     * 设置提示信息\r\n     * @param tip 提示信息\r\n     */\r\n    setTip(tip: string) {\r\n        this.tip = tip;\r\n    }\r\n    /**\r\n    * 变形结束\r\n    */\r\n    transformEnd() {\r\n        if (this.tip !== \"\") {\r\n            GameIns.battleManager.bossChangeFinish(this.tip);\r\n        } else {\r\n            GameIns.battleManager.bossFightStart();\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param index 形态索引\r\n     */\r\n    setFormIndex(index: number) {\r\n        if (this._formIndex !== index) {\r\n            this._formIndex = index;\r\n            this._bOrderAttack = true;\r\n            this._orderIndex = 0;\r\n            this._data = this._datas[this._formIndex];\r\n            this._idleName = `idle${this._formIndex + 1}`;\r\n\r\n            if (index === 0) {\r\n                this.setAction(GameEnum.BossAction.Appear);\r\n            }\r\n\r\n            this._orderAtkArr = [];\r\n            for (let i = 0; i < this._data.attackActions.length; i++) {\r\n                this._orderAtkArr.push(i);\r\n            }\r\n\r\n            this._atkPointDatas = [];\r\n            for (const point of this._data.attackPoints) {\r\n                const data = [point.bAvailable, point];\r\n                this._atkPointDatas.push(data);\r\n            }\r\n\r\n            this._atkActions = [...this._data.attackActions];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 进入下一形态\r\n     */\r\n    enterNextForm() {\r\n        if (this._formIndex < this._datas.length - 1) {\r\n            this._formIndex++;\r\n            this.setFormIndex(this._formIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this._startNormalTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n        this.colliderEnabled = true;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead) {\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    this._updateMove(deltaTime);\r\n                    this._processNextAttack(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._updateMove(deltaTime);\r\n                    if (this._bArriveDes) {\r\n                        this.setAction(BossAction.Transform);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    if (this._transFormMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this._udpateShoot(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.AttackOver:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this.setAction(BossAction.Normal);\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始出现轨迹\r\n     */\r\n    _startAppearTrack() {\r\n        const trackGroup = new TrackGroup();\r\n        trackGroup.loopNum = 1;\r\n        trackGroup.trackIDs = [this._data!.appearParam[2]];\r\n        trackGroup.speeds = [this._data!.appearParam[3]];\r\n        trackGroup.trackIntervals = [0];\r\n\r\n        this._trackCom!.init(this, [trackGroup], [], v2(this._data!.appearParam[0], this._data!.appearParam[1]));\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 开始正常轨迹\r\n     */\r\n    _startNormalTrack() {\r\n        this._trackCom!.init(this, this._data!.trackGroups, [], v2(this.node.x, this.node.y));\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n    }\r\n\r\n    /**\r\n     * 移动到指定位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param speed 移动速度\r\n     * @param transformMove 是否为变形移动\r\n     */\r\n    moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {\r\n        this._moveToX = x;\r\n        this._moveToY = y;\r\n        this._moveSpeed = speed;\r\n        this._bArriveDes = false;\r\n        this._transFormMove = transformMove;\r\n    }\r\n\r\n\r\n    setPos(x: number, y: number, update: boolean = true) {\r\n        this.node.setPosition(x, y);\r\n        this._posX = x;\r\n        this._posY = y;\r\n    }\r\n\r\n    /**\r\n     * 处理下一个路径点\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextWayPoint(deltaTime: number) {\r\n        if (this._bArriveDes && this._data!.trackGroups.length === 0) {\r\n            this._nextWayPointTime += deltaTime;\r\n            if (this._nextWayPointTime > this._nextWayPointInterval) {\r\n                this._nextWayPointInterval = Tools.getRandomInArray(this._data!.wayPointIntervals)!;\r\n                this._nextWayPointTime = 0;\r\n\r\n                if (this._bFirstWayPoint) {\r\n                    this._bFirstWayPoint = false;\r\n                } else {\r\n                    const index = Tools.random_int(0, this._data!.wayPointXs.length - 1);\r\n                    this._nextWayPointX = this._data!.wayPointXs[index];\r\n                    this._nextWayPointY = this._data!.wayPointYs[index];\r\n                    this._nextWaySpeed = Tools.getRandomInArray(this._data!.speeds)!;\r\n                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    _updateMove(deltaTime: number) {\r\n        if (this._action === GameEnum.BossAction.Appear || this._data!.trackGroups.length > 0) {\r\n            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n        } else if (!this._bArriveDes) {\r\n            // 如果未到达目标位置，则更新移动逻辑\r\n            // this._prePosX = this._posX;\r\n            // this._prePosY = this._posY;\r\n\r\n            const deltaX = this._moveToX - this._posX;\r\n            const deltaY = this._moveToY - this._posY;\r\n            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n            let moveX = 0;\r\n            let moveY = 0;\r\n\r\n            // 如果距离小于等于移动速度，则直接到达目标点\r\n            if (distance <= this._moveSpeed) {\r\n                moveX = deltaX;\r\n                moveY = deltaY;\r\n            }\r\n            // 否则按比例移动\r\n            else {\r\n                moveX = this._moveSpeed * deltaX / distance;\r\n                moveY = this._moveSpeed * deltaY / distance;\r\n            }\r\n\r\n            // 更新位置\r\n            this._posX += moveX;\r\n            this._posY += moveY;\r\n            this.setPos(this._posX, this._posY);\r\n\r\n            // 检查是否到达目的地（当移动量很小时认为已到达）\r\n            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理下一次攻击\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextAttack(deltaTime: number) {\r\n        // if (this._shootAble && this._action === GameEnum.BossAction.Normal) {\r\n        //     this._nextAttackTime += deltaTime;\r\n        //     if (this._nextAttackTime > this._nextAttackInterval) {\r\n        //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;\r\n        //         this._nextAttackTime = 0;\r\n\r\n        //         let attackAction = null;\r\n        //         if (this._bOrderAttack) {\r\n        //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;\r\n        //             Tools.arrRemove(this._orderAtkArr, randomIndex);\r\n        //             attackAction = this._atkActions[randomIndex];\r\n        //             this._orderIndex++;\r\n        //             if (this._orderIndex > this._atkActions.length - 1) {\r\n        //                 this._bOrderAttack = false;\r\n        //             }\r\n        //         } else {\r\n        //             attackAction = Tools.getRandomInArray(this._atkActions);\r\n        //         }\r\n\r\n        //         if (attackAction) {\r\n        //             this._bAttackMove = attackAction.bAtkMove;\r\n        //             this._attackID = attackAction.atkActId;\r\n        //             this._attackPoints.splice(0);\r\n\r\n        //             for (const pointId of attackAction.atkPointId) {\r\n        //                 const pointData = this._atkPointDatas[pointId];\r\n        //                 if (pointData[0]) {\r\n        //                     let attackPoint = this._atkPointsPool[pointId]\r\n        //                     if (!attackPoint) {\r\n        //                         const pointNode = new Node();\r\n        //                         this.node.addChild(pointNode);\r\n        //                         attackPoint = pointNode.addComponent(AttackPoint);\r\n        //                         this._atkPointsPool.push(attackPoint);\r\n        //                     }\r\n        //                     attackPoint.initForBoss(pointData[1], this);\r\n        //                     this._attackPoints.push(attackPoint);\r\n        //                 }\r\n        //             }\r\n\r\n        //             if (this._attackPoints.length > 0) {\r\n        //                 this.setAction(GameEnum.BossAction.AttackPrepare);\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    async _udpateShoot(deltaTime: number) {\r\n        // if (this._shootAble) {\r\n        //     let allAttacksOver = true;\r\n\r\n        //     for (const attackPoint of this._attackPoints) {\r\n        //         await attackPoint.updateGameLogic(deltaTime);\r\n        //         if (!attackPoint.isAttackOver()) {\r\n        //             allAttacksOver = false;\r\n        //         }\r\n        //     }\r\n\r\n        //     if (allAttacksOver) {\r\n        //         this.setAction(GameEnum.BossAction.AttackOver);\r\n        //     }\r\n        // }\r\n    }\r\n}"]}