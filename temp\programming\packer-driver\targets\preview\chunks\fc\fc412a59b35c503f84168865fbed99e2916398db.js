System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, SingletonBase, GameIns, MyApp, BattleLayer, Prefab, instantiate, GameResourceList, MainPlane, AttributeConst, MainPlaneData, MainPlaneManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../ui/plane/mainPlane/MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneData(extras) {
    _reporterNs.report("MainPlaneData", "../data/MainPlaneFightData", _context.meta, extras);
  }

  _export("MainPlaneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      BattleLayer = _unresolved_5.default;
    }, function (_unresolved_6) {
      GameResourceList = _unresolved_6.default;
    }, function (_unresolved_7) {
      MainPlane = _unresolved_7.MainPlane;
    }, function (_unresolved_8) {
      AttributeConst = _unresolved_8.AttributeConst;
    }, function (_unresolved_9) {
      MainPlaneData = _unresolved_9.MainPlaneData;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['Prefab', 'Node', 'instantiate']);

      _export("MainPlaneManager", MainPlaneManager = class MainPlaneManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this._planeData = null;
          //飞机数据
          this.mainPlane = null;
          //飞机战斗UI
          this.planeFightData = new (_crd && MainPlaneData === void 0 ? (_reportPossibleCrUseOfMainPlaneData({
            error: Error()
          }), MainPlaneData) : MainPlaneData)();
          //飞机战斗数据
          this.hurtTotal = 0;
        }

        set moveAble(value) {
          if (this.mainPlane) {
            this.mainPlane.setMoveAble(value);
          }
        }

        setPlaneData(planeData) {
          this._planeData = planeData;
        }

        preload() {
          var _this = this;

          return _asyncToGenerator(function* () {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            yield _this.createMainPlane();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();

            _this.reset();
          })();
        }
        /**
         * 创建主飞机
         * @param isTrans 是否为特殊状态
         * @returns 主飞机对象
         */


        createMainPlane() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var _me, _this2$mainPlane;

            if (_this2.mainPlane) {
              return _this2.mainPlane;
            }

            var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).MainPlane, Prefab);
            var planeNode = instantiate(prefab);
            _this2.mainPlane = planeNode.getComponent(_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
              error: Error()
            }), MainPlane) : MainPlane);
            (_me = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me) == null || _me.addMainPlane(); // this.mainPlane!.node.active = false;

            (_this2$mainPlane = _this2.mainPlane) == null || _this2$mainPlane.initPlane(_this2._planeData);
            return _this2.mainPlane;
          })();
        }

        mainReset() {
          if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
          }
        }

        reset() {
          this.planeFightData.die = false;

          if (this.mainPlane) {
            this.mainPlane.battleQuit();
          }

          this.refreshPlaneData();
        }

        refreshPlaneData() {
          var _this$_planeData;

          this.planeFightData.hp = (_this$_planeData = this._planeData) == null ? void 0 : _this$_planeData.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHP);
          this.planeFightData.maxhp = this.planeFightData.hp;
        }

        idToType(id) {
          return Math.floor(id / 100);
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=fc412a59b35c503f84168865fbed99e2916398db.js.map