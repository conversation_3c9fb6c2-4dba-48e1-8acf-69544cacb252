System.register(["__unresolved_0", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, EventConditionBase, Comparer, eCompareOp, BulletConditionBase, BulletCondition_Duration, BulletCondition_ElapsedTime, BulletCondition_PosX, BulletCondition_PosY, BulletCondition_Damage, BulletCondition_Speed, BulletCondition_SpeedAngle, BulletCondition_Acceleration, BulletCondition_AccelerationAngle, BulletCondition_Scale, BulletCondition_ColorR, BulletCondition_ColorG, BulletCondition_ColorB, BulletCondition_FacingMoveDir, BulletCondition_Destructive, BulletCondition_DestructiveOnHit, _crd;

  function _reportPossibleCrUseOfEventConditionBase(extras) {
    _reporterNs.report("EventConditionBase", "./IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfComparer(extras) {
    _reporterNs.report("Comparer", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export({
    BulletConditionBase: void 0,
    BulletCondition_Duration: void 0,
    BulletCondition_ElapsedTime: void 0,
    BulletCondition_PosX: void 0,
    BulletCondition_PosY: void 0,
    BulletCondition_Damage: void 0,
    BulletCondition_Speed: void 0,
    BulletCondition_SpeedAngle: void 0,
    BulletCondition_Acceleration: void 0,
    BulletCondition_AccelerationAngle: void 0,
    BulletCondition_Scale: void 0,
    BulletCondition_ColorR: void 0,
    BulletCondition_ColorG: void 0,
    BulletCondition_ColorB: void 0,
    BulletCondition_FacingMoveDir: void 0,
    BulletCondition_Destructive: void 0,
    BulletCondition_DestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_unresolved_2) {
      EventConditionBase = _unresolved_2.EventConditionBase;
    }, function (_unresolved_3) {
      Comparer = _unresolved_3.Comparer;
    }, function (_unresolved_4) {
      eCompareOp = _unresolved_4.eCompareOp;
    }],
    execute: function () {
      _crd = true;

      _export("BulletConditionBase", BulletConditionBase = class BulletConditionBase extends (_crd && EventConditionBase === void 0 ? (_reportPossibleCrUseOfEventConditionBase({
        error: Error()
      }), EventConditionBase) : EventConditionBase) {});

      _export("BulletCondition_Duration", BulletCondition_Duration = class BulletCondition_Duration extends BulletConditionBase {
        evaluate(context) {
          // Custom evaluation logic for active condition
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.duration.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ElapsedTime", BulletCondition_ElapsedTime = class BulletCondition_ElapsedTime extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.elapsedTime, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_PosX", BulletCondition_PosX = class BulletCondition_PosX extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.position.x, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_PosY", BulletCondition_PosY = class BulletCondition_PosY extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.position.y, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Damage", BulletCondition_Damage = class BulletCondition_Damage extends BulletConditionBase {
        evaluate(context) {
          // return Comparer.compare(context.bullet!.damage.value, this._targetValue, this.data.compareOp);
          return false;
        }

      });

      _export("BulletCondition_Speed", BulletCondition_Speed = class BulletCondition_Speed extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.speed.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_SpeedAngle", BulletCondition_SpeedAngle = class BulletCondition_SpeedAngle extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.speedAngle.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Acceleration", BulletCondition_Acceleration = class BulletCondition_Acceleration extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.acceleration.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_AccelerationAngle", BulletCondition_AccelerationAngle = class BulletCondition_AccelerationAngle extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.accelerationAngle.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Scale", BulletCondition_Scale = class BulletCondition_Scale extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.scale.x, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorR", BulletCondition_ColorR = class BulletCondition_ColorR extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.color.value.r, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorG", BulletCondition_ColorG = class BulletCondition_ColorG extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.color.value.g, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorB", BulletCondition_ColorB = class BulletCondition_ColorB extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.prop.color.value.b, this._targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_FacingMoveDir", BulletCondition_FacingMoveDir = class BulletCondition_FacingMoveDir extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.bullet.prop.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.bullet.prop.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("BulletCondition_Destructive", BulletCondition_Destructive = class BulletCondition_Destructive extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.bullet.prop.isDestructive.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.bullet.prop.isDestructive.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("BulletCondition_DestructiveOnHit", BulletCondition_DestructiveOnHit = class BulletCondition_DestructiveOnHit extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.bullet.prop.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.bullet.prop.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=314a639eb5ccd582bf67a6fd9668a4463b930929.js.map