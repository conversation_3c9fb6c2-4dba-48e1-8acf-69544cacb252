{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Easing.ts"], "names": ["Easing", "eEasing", "lerp", "easing", "start", "end", "t", "Linear", "InSine", "Math", "cos", "PI", "OutSine", "sin", "InOutSine", "InQuad", "OutQuad", "InOutQuad", "pow"], "mappings": ";;;MAOaA,M;;;;;;;;;yBANDC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;;wBAMCD,M,GAAN,MAAMA,MAAN,CAAa;AACL,eAAJE,IAAI,CAACC,MAAD,EAAkBC,KAAlB,EAAiCC,GAAjC,EAA8CC,CAA9C,EAAiE;AACxE,kBAAQH,MAAR;AACI,iBAAKF,OAAO,CAACM,MAAb;AACI,qBAAOH,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,IAAgBE,CAA/B;;AACJ,iBAAKL,OAAO,CAACO,MAAb;AACI,qBAAOJ,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,KAAiB,IAAIK,IAAI,CAACC,GAAL,CAAUJ,CAAC,GAAGG,IAAI,CAACE,EAAV,GAAgB,CAAzB,CAArB,CAAf;;AACJ,iBAAKV,OAAO,CAACW,OAAb;AACI,qBAAOR,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,IAAgBK,IAAI,CAACI,GAAL,CAAUP,CAAC,GAAGG,IAAI,CAACE,EAAV,GAAgB,CAAzB,CAA/B;;AACJ,iBAAKV,OAAO,CAACa,SAAb;AACI,qBAAOV,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,KAAiBE,CAAC,GAAG,GAAJ,GAAU,CAAC,IAAIG,IAAI,CAACC,GAAL,CAASJ,CAAC,GAAGG,IAAI,CAACE,EAAlB,CAAL,IAA8B,CAAxC,GAA4C,CAAC,IAAIF,IAAI,CAACI,GAAL,CAASP,CAAC,GAAGG,IAAI,CAACE,EAAlB,CAAL,IAA8B,CAA3F,CAAf;;AACJ,iBAAKV,OAAO,CAACc,MAAb;AACI,qBAAOX,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,IAAgBE,CAAhB,GAAoBA,CAAnC;;AACJ,iBAAKL,OAAO,CAACe,OAAb;AACI,qBAAOZ,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,KAAiB,IAAI,CAAC,IAAIE,CAAL,KAAW,IAAIA,CAAf,CAArB,CAAf;;AACJ,iBAAKL,OAAO,CAACgB,SAAb;AACI,qBAAOb,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,KAAiBE,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAlB,GAAsB,IAAIG,IAAI,CAACS,GAAL,CAAS,CAAC,CAAD,GAAKZ,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAArE,CAAf;;AACJ;AACI,qBAAOF,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,IAAgBE,CAA/B;AAhBR;AAkBH;;AApBe,O", "sourcesContent": ["\r\nexport enum eEasing {\r\n    Linear,\r\n    InSine, OutSine, InOutSine,\r\n    InQuad, OutQuad, InOutQuad\r\n}\r\n\r\nexport class Easing {\r\n    static lerp(easing: eEasing, start: number, end: number, t: number): number {\r\n        switch (easing) {\r\n            case eEasing.Linear:\r\n                return start + (end - start) * t;\r\n            case eEasing.InSine:\r\n                return start + (end - start) * (1 - Math.cos((t * Math.PI) / 2));\r\n            case eEasing.OutSine:\r\n                return start + (end - start) * Math.sin((t * Math.PI) / 2);\r\n            case eEasing.InOutSine:\r\n                return start + (end - start) * (t < 0.5 ? (1 - Math.cos(t * Math.PI)) / 2 : (1 + Math.sin(t * Math.PI)) / 2);\r\n            case eEasing.InQuad:\r\n                return start + (end - start) * t * t;\r\n            case eEasing.OutQuad:\r\n                return start + (end - start) * (1 - (1 - t) * (1 - t));\r\n            case eEasing.InOutQuad:\r\n                return start + (end - start) * (t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2);\r\n            default:\r\n                return start + (end - start) * t;\r\n        }\r\n    }\r\n}"]}