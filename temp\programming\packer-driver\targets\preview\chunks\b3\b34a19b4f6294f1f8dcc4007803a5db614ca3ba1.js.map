{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts"], "names": ["BaseComp", "m_entity", "m_enabled", "init", "entity", "enabled", "onInit", "value", "remove", "update", "dt"], "mappings": ";;;mBAEqBA,Q;;;;;;;;;;;;;;;yBAAAA,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,eAE1BC,QAF0B,GAEA,IAFA;AAEM;AAFN,eAG1BC,SAH0B,GAGd,IAHc;AAAA;;AAGR;;AAGlB;AACJ;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,eAAKH,QAAL,GAAgBG,MAAhB;AACA,eAAKC,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL;AACH;AAED;AACJ;AACA;AACA;;;AACe,YAAPD,OAAO,GAAG;AACV,iBAAO,KAAKH,SAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACe,YAAPG,OAAO,CAACE,KAAD,EAAQ;AACf,cAAI,KAAKL,SAAL,KAAmBK,KAAvB,EAA8B;AAC1B,iBAAKL,SAAL,GAAiBK,KAAjB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACc,YAANH,MAAM,GAAG;AACT,iBAAO,KAAKH,QAAZ;AACH;AAED;AACJ;AACA;;;AACIO,QAAAA,MAAM,GAAG;AACL,eAAKH,OAAL,GAAe,KAAf;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,MAAM,GAAG,CAAE;;AAEXG,QAAAA,MAAM,CAACC,EAAD,EAAW,CAEhB;;AAxDyB,O", "sourcesContent": ["import Entity from \"./Entity\";\r\n\r\nexport default class BaseComp {\r\n\r\n    m_entity: Entity | null = null; // 绑定的实体\r\n    m_enabled = true; // 是否启用\r\n\r\n\r\n    /**\r\n     * 初始化组件\r\n     * @param {Entity} entity 绑定的实体\r\n     */\r\n    init(entity: Entity) {\r\n        this.m_entity = entity;\r\n        this.enabled = true;\r\n        this.onInit();\r\n    }\r\n\r\n    /**\r\n     * 获取组件是否启用\r\n     * @returns {boolean}\r\n     */\r\n    get enabled() {\r\n        return this.m_enabled;\r\n    }\r\n\r\n    /**\r\n     * 设置组件是否启用\r\n     * @param {boolean} value 是否启用\r\n     */\r\n    set enabled(value) {\r\n        if (this.m_enabled !== value) {\r\n            this.m_enabled = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取绑定的实体\r\n     * @returns {Entity}\r\n     */\r\n    get entity() {\r\n        return this.m_entity;\r\n    }\r\n\r\n    /**\r\n     * 移除组件\r\n     */\r\n    remove() {\r\n        this.enabled = false;\r\n    }\r\n\r\n    /**\r\n     * 初始化时的回调\r\n     */\r\n    onInit() {}\r\n\r\n    update(dt:number){\r\n\r\n    }\r\n}"]}