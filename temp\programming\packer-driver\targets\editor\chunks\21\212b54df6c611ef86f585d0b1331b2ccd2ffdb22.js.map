{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventGroupData.ts"], "names": ["_decorator", "Enum", "eEasing", "eEmitterCondition", "eEmitterConditionCn", "eBulletConditionCn", "eEmitterAction", "eEmitterActionCn", "eBulletActionCn", "ExpressionValue", "ccclass", "property", "eConditionOp", "eCompareOp", "eTargetValueType", "EventConditionData", "type", "displayName", "visible", "emitterConditionType", "value", "bulletConditionType", "targetValueStr", "targetValue", "raw", "fromJSON", "json", "data", "Object", "assign", "And", "Level_Duration", "Equal", "EventActionData", "group", "emitterActionType", "bulletActionType", "durationStr", "duration", "Emitter_Active", "Absolute", "Linear", "EventGroupData", "conditions", "map", "actions"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,O,iBAAAA,O;;AACqBC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,mB,iBAAAA,mB;AAAuCC,MAAAA,kB,iBAAAA,kB;;AAC7DC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,gB,iBAAAA,gB;AAAiCC,MAAAA,e,iBAAAA,e;;AACnEC,MAAAA,e,iBAAAA,e;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;8BAElBY,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;4BAIAC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;kCASAC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;cAIZ;;;oCAeaC,kB,WADZL,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI,CAACW,YAAD,CAAZ;AAA4BK,QAAAA,WAAW,EAAE;AAAzC,OAAD,C,UAGRN,QAAQ,CAAC;AAACO,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRP,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,uDAAZ;AAAmCgB,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,UAIRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,qDAAZ;AAAkCgB,QAAAA,WAAW,EAAE;AAA/C,OAAD,C,UAIRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI,CAACY,UAAD,CAAZ;AAA0BI,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,UAIRN,QAAQ,CAAC;AAACO,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERP,QAAQ,CAAC;AAACM,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BAtBb,MACaF,kBADb,CAC+D;AAAA;AAAA;;AAAA;;AAAA;;AAkB3D;AAlB2D;AAAA;;AAQ5B,YAApBI,oBAAoB,GAAwB;AAAE,iBAAO,KAAKH,IAAZ;AAAqD;;AAC/E,YAApBG,oBAAoB,CAACC,KAAD,EAA6B;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAoD;;AAGpF,YAAnBC,mBAAmB,GAAuB;AAAE,iBAAO,KAAKL,IAAZ;AAAoD;;AAC7E,YAAnBK,mBAAmB,CAACD,KAAD,EAA4B;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAmD;;AAStF,YAAdE,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACF,KAAD,EAAgB;AAAE,eAAKG,WAAL,CAAiBC,GAAjB,GAAuBJ,KAAvB;AAA+B;;AAE3D,eAARK,QAAQ,CAACC,IAAD,EAAgC;AAC3C,gBAAMC,IAAI,GAAG,IAAIZ,kBAAJ,EAAb;AACA,cAAIW,IAAJ,EAAUE,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACV,iBAAOC,IAAP;AACH;;AA7B0D,O;;;;;iBAEjCf,YAAY,CAACkB,G;;;;;;;iBAGJ;AAAA;AAAA,sDAAkBC,c;;;;;;;iBAWtBlB,UAAU,CAACmB,K;;;;;;;iBAIH;AAAA;AAAA,kDAAoB,GAApB,C;;;;iCAa9BC,e,YADZvB,OAAO,CAAC,iBAAD,C,WAEHC,QAAQ,CAAC;AAACO,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAGRP,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,iDAAZ;AAAgCgB,QAAAA,WAAW,EAAE,OAA7C;AAAsDiB,QAAAA,KAAK,EAAE;AAA7D,OAAD,C,WAIRvB,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,+CAAZ;AAA+BgB,QAAAA,WAAW,EAAE,MAA5C;AAAoDiB,QAAAA,KAAK,EAAE;AAA3D,OAAD,C,WAKRvB,QAAQ,CAAC;AAACO,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERP,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRN,QAAQ,CAAC;AAACO,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERP,QAAQ,CAAC;AAACM,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI,CAACa,gBAAD,CAAZ;AAAgCG,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAGRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,+BAAZ;AAAuBgB,QAAAA,WAAW,EAAE;AAApC,OAAD,C,6BA7Bb,MACagB,eADb,CACyD;AAAA;AAAA;;AAYrD;AAZqD;;AAAA;;AAAA;;AAAA;AAAA;;AAKzB,YAAjBE,iBAAiB,GAAqB;AAAE,iBAAO,KAAKnB,IAAZ;AAAkD;;AACzE,YAAjBmB,iBAAiB,CAACf,KAAD,EAA0B;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAiD;;AAG9E,YAAhBgB,gBAAgB,GAAoB;AAAE,iBAAO,KAAKpB,IAAZ;AAAiD;;AACvE,YAAhBoB,gBAAgB,CAAChB,KAAD,EAAyB;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAgD;;AAMhF,YAAXiB,WAAW,GAAW;AAAE,iBAAO,KAAKC,QAAL,CAAclB,KAArB;AAA6B;;AAC1C,YAAXiB,WAAW,CAACjB,KAAD,EAAgB;AAAE,eAAKkB,QAAL,CAAclB,KAAd,GAAsBA,KAAtB;AAA8B;;AAK7C,YAAdE,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACF,KAAD,EAAgB;AAAE,eAAKG,WAAL,CAAiBC,GAAjB,GAAuBJ,KAAvB;AAA+B;;AAQ3D,eAARK,QAAQ,CAACC,IAAD,EAA6B;AACxC,gBAAMC,IAAI,GAAG,IAAIM,eAAJ,EAAb;AACA,cAAIP,IAAJ,EAAUE,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACV,iBAAOC,IAAP;AACH;;AAnCoD,O;;;;;iBAErB;AAAA;AAAA,gDAAeY,c;;;;;;;iBAYX;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMG;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMHzB,gBAAgB,CAAC0B,Q;;;;;;;iBAGlC;AAAA;AAAA,kCAAQC,M;;;;gCAUlBC,c,aADZhC,OAAO,CAAC,gBAAD,C,WAEHC,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAE,CAACD,kBAAD,CAAR;AAA8BE,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,WAGRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAE,CAACiB,eAAD,CAAR;AAA2BhB,QAAAA,WAAW,EAAE;AAAxC,OAAD,C,8BAZb,MACayB,cADb,CAC4B;AAAA;AAAA;;AAIxB;AAJwB;;AAAA;;AAAA;AAAA;;AAcF,eAARjB,QAAQ,CAACC,IAAD,EAA4B;AAC9C,gBAAMC,IAAI,GAAG,IAAIe,cAAJ,EAAb;;AACA,cAAIhB,IAAJ,EAAU;AACN;AACAE,YAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AAEAC,YAAAA,IAAI,CAACgB,UAAL,GAAkB,CAACjB,IAAI,CAACiB,UAAL,IAAmB,EAApB,EAAwBC,GAAxB,CAA4B7B,kBAAkB,CAACU,QAA/C,CAAlB;AACAE,YAAAA,IAAI,CAACkB,OAAL,GAAe,CAACnB,IAAI,CAACmB,OAAL,IAAgB,EAAjB,EAAqBD,GAArB,CAAyBX,eAAe,CAACR,QAAzC,CAAf;AACH;;AACD,iBAAOE,IAAP;AACH;;AAxBuB,O;;;;;iBAEF,E;;;;;;;iBAIQ,C;;;;;;;iBAGY,E;;;;;;;iBAGN,E", "sourcesContent": ["import { _decorator, Enum } from \"cc\";\r\nimport { eEasing } from \"../../bullet/Easing\";\r\nimport { eEventConditionType, eEmitterCondition, eEmitterConditionCn, eBulletCondition, eBulletConditionCn } from \"./EventConditionType\";\r\nimport { eEventActionType, eEmitterAction, eEmitterActionCn, eBulletAction, eBulletActionCn } from \"./EventActionType\";\r\nimport { ExpressionValue } from \"./ExpressionValue\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eConditionOp {\r\n    And, Or\r\n}\r\n\r\nexport enum eCompareOp {\r\n    Equal, // 等于\r\n    NotEqual, // 不等于\r\n    Greater, // 大于\r\n    Less, // 小于\r\n    GreaterEqual, // 大于等于\r\n    LessEqual, // 小于等于\r\n}\r\n\r\nexport enum eTargetValueType {\r\n    Absolute = 1, Relative\r\n}\r\n\r\n// 数据基类(不含具体的type类型)\r\nexport interface IEventConditionData {\r\n    op: eConditionOp;\r\n    compareOp: eCompareOp;\r\n    targetValue: ExpressionValue;\r\n}\r\n\r\nexport interface IEventActionData {\r\n    duration: ExpressionValue;\r\n    targetValue: ExpressionValue;\r\n    targetValueType: eTargetValueType;\r\n    easing: eEasing;\r\n}\r\n\r\n@ccclass('EventConditionData')\r\nexport class EventConditionData implements IEventConditionData {\r\n    @property({ type: Enum(eConditionOp), displayName: '条件关系' })\r\n    public op: eConditionOp = eConditionOp.And;\r\n\r\n    @property({visible:false})\r\n    public type: eEventConditionType = eEmitterCondition.Level_Duration;\r\n\r\n    @property({ type: Enum(eEmitterConditionCn), displayName: '发射器条件' })\r\n    public get emitterConditionType(): eEmitterConditionCn { return this.type as unknown as eEmitterConditionCn; }\r\n    public set emitterConditionType(value: eEmitterConditionCn) { this.type = value as unknown as eEmitterCondition; }\r\n\r\n    @property({ type: Enum(eBulletConditionCn), displayName: '子弹条件' })\r\n    public get bulletConditionType(): eBulletConditionCn { return this.type as unknown as eBulletConditionCn; }\r\n    public set bulletConditionType(value: eBulletConditionCn) { this.type = value as unknown as eBulletCondition; }\r\n\r\n    @property({ type: Enum(eCompareOp), displayName: '比较方式' })\r\n    public compareOp: eCompareOp = eCompareOp.Equal;\r\n\r\n    // 条件值: 例如持续时间、距离\r\n    @property({visible:false})\r\n    public targetValue : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n\r\n    static fromJSON(json: any): EventConditionData {\r\n        const data = new EventConditionData();\r\n        if (json) Object.assign(data, json);\r\n        return data;\r\n    }\r\n}\r\n\r\n@ccclass('EventActionData')\r\nexport class EventActionData implements IEventActionData {\r\n    @property({visible:false})\r\n    public type: eEventActionType = eEmitterAction.Emitter_Active;\r\n    \r\n    @property({ type: Enum(eEmitterActionCn), displayName: '发射器操作', group: '发射器' })\r\n    public get emitterActionType(): eEmitterActionCn { return this.type as unknown as eEmitterActionCn; }\r\n    public set emitterActionType(value: eEmitterActionCn) { this.type = value as unknown as eEmitterAction; }\r\n\r\n    @property({ type: Enum(eBulletActionCn), displayName: '子弹操作', group: '子弹' })\r\n    public get bulletActionType(): eBulletActionCn { return this.type as unknown as eBulletActionCn; }\r\n    public set bulletActionType(value: eBulletActionCn) { this.type = value as unknown as eBulletAction; }\r\n\r\n    // 持续时间: 0表示立即执行\r\n    @property({visible:false})\r\n    public duration : ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '持续时间' })\r\n    public get durationStr(): number { return this.duration.value; }\r\n    public set durationStr(value: number) { this.duration.value = value; }\r\n\r\n    @property({visible:false})\r\n    public targetValue : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n\r\n    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })\r\n    targetValueType: eTargetValueType = eTargetValueType.Absolute;\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    easing : eEasing = eEasing.Linear;\r\n\r\n    static fromJSON(json: any): EventActionData {\r\n        const data = new EventActionData();\r\n        if (json) Object.assign(data, json);\r\n        return data;\r\n    }\r\n}\r\n\r\n@ccclass('EventGroupData')\r\nexport class EventGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    public name: string = \"\";\r\n\r\n    // 重复触发次数(默认1,只能触发一次; -1表示循环触发)\r\n    @property({ displayName: '触发次数' })\r\n    public triggerCount: number = 1;\r\n\r\n    @property({ type: [EventConditionData], displayName: '条件列表' })\r\n    public conditions: EventConditionData[] = [];\r\n\r\n    @property({ type: [EventActionData], displayName: '行为列表' })\r\n    public actions: EventActionData[] = [];\r\n\r\n    public static fromJSON(json: any): EventGroupData {\r\n        const data = new EventGroupData();\r\n        if (json) {\r\n            // Extract conditions and actions before Object.assign\r\n            Object.assign(data, json);\r\n            \r\n            data.conditions = (json.conditions || []).map(EventConditionData.fromJSON);\r\n            data.actions = (json.actions || []).map(EventActionData.fromJSON);\r\n        }\r\n        return data;\r\n    }\r\n}\r\n"]}