{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts"], "names": ["_decorator", "Component", "ccclass", "eEntityTag", "Entity", "new_uuid", "m_comps", "Map", "m_tags", "None", "init", "initComps", "for<PERSON>ach", "comp", "addTag", "tag", "removeTag", "hasTag", "clearTags", "getComp", "type", "get", "getComps", "addComp", "set", "removeComp", "remove", "delete", "removeAllComp", "Array", "from", "values", "clear", "removeOther<PERSON>omps", "keepComp", "onCollide", "collision", "onOutScreen", "getAttack"], "mappings": ";;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;OAIf;AAAEC,QAAAA;AAAF,O,GAAcF,U;;4BAERG,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;yBAUSC,M,WADpBF,OAAO,CAAC,QAAD,C,gBAAR,MACqBE,MADrB,SACoCH,SADpC,CAC8C;AAAA;AAAA;AAAA,eAE1CI,QAF0C,GAE/B,CAF+B;AAAA,eAG1CC,OAH0C,GAGhC,IAAIC,GAAJ,EAHgC;AAGH;AAHG,eAI1CC,MAJ0C,GAIrBL,UAAU,CAACM,IAJU;AAAA;;AAM1CC,QAAAA,IAAI,GAAG;AACH,eAAKC,SAAL;AACH;;AAEDA,QAAAA,SAAS,GAAG;AACR,eAAKL,OAAL,CAAaM,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACH,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;;AAEDI,QAAAA,MAAM,CAACC,GAAD,EAAkB;AACpB,eAAKP,MAAL,IAAeO,GAAf;AACH;;AAEDC,QAAAA,SAAS,CAACD,GAAD,EAAkB;AACvB,eAAKP,MAAL,IAAe,CAACO,GAAhB;AACH;;AAEDE,QAAAA,MAAM,CAACF,GAAD,EAA2B;AAC7B,iBAAO,CAAC,KAAKP,MAAL,GAAcO,GAAf,MAAwB,CAA/B;AACH;;AAEDG,QAAAA,SAAS,GAAG;AACR,eAAKV,MAAL,GAAcL,UAAU,CAACM,IAAzB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIU,QAAAA,OAAO,CAACC,IAAD,EAAc;AACjB,iBAAO,KAAKd,OAAL,CAAae,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,QAAQ,CAACF,IAAD,EAAe;AACnB,iBAAO,KAAKd,OAAL,CAAae,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,OAAO,CAACH,IAAD,EAAeP,IAAf,EAA+B;AAClC,eAAKP,OAAL,CAAakB,GAAb,CAAiBJ,IAAjB,EAAuBP,IAAvB;AACA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIY,QAAAA,UAAU,CAACL,IAAD,EAAe;AACrB,gBAAMP,IAAI,GAAG,KAAKM,OAAL,CAAaC,IAAb,CAAb;;AACA,cAAIP,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACa,MAAL;AACH;;AACD,eAAKpB,OAAL,CAAaqB,MAAb,CAAoBP,IAApB;AACH;AAED;AACJ;AACA;;;AACIQ,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKtB,OAAL,IAAgB,IAApB,EAA0B;AACtBuB,YAAAA,KAAK,CAACC,IAAN,CAAW,KAAKxB,OAAL,CAAayB,MAAb,EAAX,EAAkCnB,OAAlC,CAA2CC,IAAD,IAAU;AAChDA,cAAAA,IAAI,CAACa,MAAL;AACH,aAFD;AAGA,iBAAKpB,OAAL,CAAa0B,KAAb;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,QAAD,EAAqB;AACjC,cAAI,KAAK5B,OAAL,IAAgB,IAApB,EAA0B;AACtB,iBAAKA,OAAL,CAAaM,OAAb,CAAqB,CAACC,IAAD,EAAOO,IAAP,KAAgB;AACjC,kBAAIP,IAAI,KAAKqB,QAAb,EAAuB;AACnB,qBAAK5B,OAAL,CAAaqB,MAAb,CAAoBP,IAApB;AACH;AACJ,aAJD;AAKH;AACJ;;AAEDe,QAAAA,SAAS,CAACC,SAAD,EAAuB,CAE/B;;AAEDC,QAAAA,WAAW,GAAG,CAEb;;AAEDC,QAAAA,SAAS,GAAU;AACf,iBAAO,CAAP;AACH;;AA7GyC,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BaseComp from './BaseComp';\r\nimport FCollider from '../../collider-system/FCollider';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nexport enum eEntityTag {\r\n    None = 0,\r\n    Player = 1,\r\n    Enemy = 2,\r\n    PlayerBullet = 4,\r\n    EnemyBullet = 8,\r\n    Boss = 16,\r\n}\r\n\r\n@ccclass('Entity')\r\nexport default class Entity extends Component {\r\n\r\n    new_uuid = 0;\r\n    m_comps = new Map<string, BaseComp>(); // 存储组件的 Map\r\n    m_tags: eEntityTag = eEntityTag.None;\r\n\r\n    init() {\r\n        this.initComps()\r\n    }\r\n\r\n    initComps() {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    addTag(tag: eEntityTag) {\r\n        this.m_tags |= tag;\r\n    }\r\n\r\n    removeTag(tag: eEntityTag) {\r\n        this.m_tags &= ~tag;\r\n    }\r\n\r\n    hasTag(tag: eEntityTag): boolean {\r\n        return (this.m_tags & tag) !== 0;\r\n    }\r\n\r\n    clearTags() {\r\n        this.m_tags = eEntityTag.None;\r\n    }\r\n\r\n    /**\r\n     * 获取指定类型的组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComp(type:string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 获取指定类型的所有组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComps(type: string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 添加组件\r\n     * @param {string} type 组件类型\r\n     * @param {any} comp 组件实例\r\n     * @returns {any} 添加的组件实例\r\n     */\r\n    addComp(type: string, comp: BaseComp) {\r\n        this.m_comps.set(type, comp);\r\n        return comp;\r\n    }\r\n\r\n    /**\r\n     * 移除指定类型的组件\r\n     * @param {string} type 组件类型\r\n     */\r\n    removeComp(type: string) {\r\n        const comp = this.getComp(type);\r\n        if (comp) {\r\n            comp.remove();\r\n        }\r\n        this.m_comps.delete(type);\r\n    }\r\n\r\n    /**\r\n     * 移除所有组件\r\n     */\r\n    removeAllComp() {\r\n        if (this.m_comps != null) {\r\n            Array.from(this.m_comps.values()).forEach((comp) => {\r\n                comp.remove();\r\n            });\r\n            this.m_comps.clear();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除除指定组件外的其他组件\r\n     * @param {BaseComp} keepComp 要保留的组件\r\n     */\r\n    removeOtherComps(keepComp: BaseComp) {\r\n        if (this.m_comps != null) {\r\n            this.m_comps.forEach((comp, type) => {\r\n                if (comp !== keepComp) {\r\n                    this.m_comps.delete(type);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    onCollide(collision: FCollider) {\r\n        \r\n    }\r\n\r\n    onOutScreen() {\r\n        \r\n    }\r\n\r\n    getAttack():number {\r\n        return 1;\r\n    }\r\n}"]}