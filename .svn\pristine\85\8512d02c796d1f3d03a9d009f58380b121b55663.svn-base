import { _decorator, Button, Component, Sprite } from 'cc';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
const { ccclass, property } = _decorator;

@ccclass('PKHistoryCellUI')
export class PKHistoryCellUI extends Component {

    @property(Sprite)
    icon: Sprite | null = null;

    @property(Button)
    btnClick: Button | null = null;

    public guid: Long | undefined;

    onButtonClick() {
        if (!this.guid) {
            return;
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_REWARD, { game_pvp_get_reward: { guid: this.guid } });
    }

    start() {
        EventMgr.on(DataEvent.GamePvpGetAward, this.getAward, this)
    }
    private getAward() {
    }
    update(deltaTime: number) {

    }

    protected onDestroy(): void {

    }

}


