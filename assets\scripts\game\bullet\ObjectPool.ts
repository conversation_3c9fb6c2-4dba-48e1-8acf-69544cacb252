import { _decorator, instantiate, Node, Prefab, NodePool } from "cc";

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class ObjectPool {
    private static all_pools: { [key: string]: NodePool } = {};

    public static getPool(key: string): NodePool {
        if (!this.all_pools[key]) {
            this.all_pools[key] = new NodePool();
        }
        return this.all_pools[key];
    }

    public static clearPool(key: string): void {
        const pool = this.all_pools[key];
        if (pool) {
            pool.clear();
        }
    }

    public static clearAll(): void {
        for (const key in this.all_pools) {
            this.clearPool(key);
        }
        this.all_pools = {};
    }

    public static getNode(node_parent: Node|null, prefab: Prefab) {
        let pool = this.getPool(prefab.uuid);

        let node = null;
        if (pool.size() > 0) { 
            // use size method to check if there're nodes available in the pool
            node = pool.get();
        } else { 
            // if not enough node in the pool, we call cc.instantiate to create node
            node = instantiate(prefab);
        }

        node!.parent = node_parent; // add new enemy node to the node tree
        // @ts-ignore
        node!.puuid = prefab.uuid;

        return node;
    }

    public static returnNode(node: Node) {
        //@ts-ignore
        if (!node.puuid) {
            console.warn("Node does not have a prefab UUID.");
            node.destroy();
            return;
        }
        
        //@ts-ignore
        let pool = this.all_pools[node.puuid];
        if (pool) {
            pool.put(node);
        }
        else {
            node.destroy();
        }
    }
}
