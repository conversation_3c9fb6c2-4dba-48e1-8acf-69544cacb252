{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts"], "names": ["_decorator", "EnemyPlaneBase", "GameEnum", "AttributeConst", "ColliderGroupType", "FBoxCollider", "Tools", "TrackComponent", "Movable", "ccclass", "property", "EnemyPlane", "_roleIndex", "_enemyData", "_curAction", "_trackCom", "_moveCom", "moveCom", "onLoad", "enemy", "addScript", "node", "initPlane", "data", "trackData", "init", "reset", "refreshProperty", "initTrack", "_initCollide", "startBattle", "trackParams", "offset", "setTrackOverCall", "die", "EnemyDestroyType", "TrackOver", "setTrackLeaveCall", "Leave", "initMove", "speed", "angle", "speedAngle", "setMovable", "initDelayDestroy", "delayTime", "onBecomeInvisibleCallback", "scheduleOnce", "_dieWhenOffScreen", "onBecomeVisibleCallback", "unschedule", "collide<PERSON>omp", "addComponent", "groupType", "ENEMY_NORMAL", "colliderEnabled", "curHp", "getFinalAttributeByKey", "MaxHP", "maxHp", "getAttack", "Attack", "EnemyAction", "Track", "updateHpUI", "setTrackAble", "startTrack", "updateGameLogic", "deltaTime", "isDead", "m_comps", "for<PERSON>ach", "comp", "update", "_checkRemoveAble", "updateAction", "setAction", "action", "<PERSON><PERSON><PERSON>", "Transform", "AttackPrepare", "playAtkAnim", "AttackIng", "AttackOver", "callback", "plane", "playAnim", "TimeOver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,Y;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,c;;AACEC,MAAAA,O,iBAAAA,O;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAGTW,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,4CACuD;AAAA;AAAA;AAAA,eAEnDC,UAFmD,GAEtC,CAFsC;AAEpC;AAFoC,eAGnDC,UAHmD,GAGpB,IAHoB;AAAA,eAInDC,UAJmD,GAI9B,CAJ8B;AAAA,eAMnDC,SANmD,GAMhB,IANgB;AAAA,eAQnDC,QARmD,GAQxB,IARwB;AAAA;;AASjC,YAAPC,OAAO,GAAG;AAAE,iBAAO,KAAKD,QAAZ;AAAuB;;AAEpCE,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKJ,SAAL,GAAiB;AAAA;AAAA,8BAAMK,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB,CAFqB,CAGrB;;AACA,eAAKL,QAAL,GAAgB;AAAA;AAAA,8BAAMI,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,iCAAhB;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAiBC,SAAjB,EAAgC;AACrC,eAAKX,UAAL,GAAkBU,IAAlB;AACA,gBAAME,IAAN;AACA,eAAKC,KAAL;AACA,eAAKC,eAAL;AACA,eAAKC,SAAL,CAAeJ,SAAf;;AACA,eAAKK,YAAL;;AACA,eAAKC,WAAL;AACH;;AAEDF,QAAAA,SAAS,CAACJ,SAAD,EAAgB;AACrB,eAAKT,SAAL,CAAgBU,IAAhB,CAAqB,IAArB,EAA2BD,SAAS,CAACA,SAArC,EAAgDA,SAAS,CAACO,WAA1D,EAAuEP,SAAS,CAACQ,MAAjF;;AAEA,eAAKjB,SAAL,CAAgBkB,gBAAhB,CAAiC,MAAM;AACnC,iBAAKC,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,SAAnC;AACH,WAFD;;AAIA,eAAKrB,SAAL,CAAgBsB,iBAAhB,CAAkC,MAAM;AACpC,iBAAKH,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BG,KAAnC;AACH,WAFD;AAGH;;AAEDC,QAAAA,QAAQ,CAACC,KAAD,EAAgBC,KAAhB,EAA+B;AACnC,eAAKzB,QAAL,CAAewB,KAAf,GAAuBA,KAAvB;AACA,eAAKxB,QAAL,CAAe0B,UAAf,GAA4BD,KAA5B;;AACA,eAAKzB,QAAL,CAAe2B,UAAf,CAA0B,IAA1B;AACH;;AAEDC,QAAAA,gBAAgB,CAACC,SAAD,EAAoB;AAChC,eAAK7B,QAAL,CAAe8B,yBAAf,GAA2C,MAAM;AAC7C,iBAAKC,YAAL,CAAkB,KAAKC,iBAAvB,EAA0CH,SAA1C;AACH,WAFD;;AAIA,eAAK7B,QAAL,CAAeiC,uBAAf,GAAyC,MAAM;AAC3C,iBAAKC,UAAL,CAAgB,KAAKF,iBAArB;AACH,WAFD;AAGH;;AAEDA,QAAAA,iBAAiB,GAAG;AAChB,eAAKd,GAAL,CAAS;AAAA;AAAA,oCAASC,gBAAT,CAA0BG,KAAnC;AACH;;AAEDT,QAAAA,YAAY,GAAS;AACjB;AACA,eAAKsB,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKA,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKD,WAAL,CAAkB1B,IAAlB,CAAuB,IAAvB;AACA,eAAK0B,WAAL,CAAkBE,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAED5B,QAAAA,eAAe,GAAG;AAAA;;AACd,eAAK6B,KAAL,uBAAa,KAAK3C,UAAlB,qBAAa,iBAAiB4C,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,KAAvD,CAAb;AACA,eAAKC,KAAL,GAAa,KAAKH,KAAlB;AACH;;AAEDI,QAAAA,SAAS,GAAU;AAAA;;AACf,iBAAO,2BAAK/C,UAAL,uCAAiB4C,sBAAjB,CAAwC;AAAA;AAAA,gDAAeI,MAAvD,MAAkE,CAAzE;AACH;;AAEDnC,QAAAA,KAAK,GAAG;AACJ,eAAKZ,UAAL,GAAkB;AAAA;AAAA,oCAASgD,WAAT,CAAqBC,KAAvC;AACH;;AAEDjC,QAAAA,WAAW,GAAG;AACV,eAAKyB,eAAL,GAAuB,IAAvB;AACA,eAAKS,UAAL;;AACA,eAAKjD,SAAL,CAAgBkD,YAAhB,CAA6B,IAA7B;;AACA,eAAKlD,SAAL,CAAgBmD,UAAhB;AACH;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd;AACA,iBAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACC,MAAL,CAAYL,SAAZ;AACH,aAFD;AAGH;;AACD,cAAI,KAAKC,MAAT,EAAiB;AACb,iBAAKK,gBAAL,CAAsBN,SAAtB;AACH,WAFD,MAEM;AACF,iBAAKrD,SAAL,CAAgBoD,eAAhB,CAAgCC,SAAhC;;AACA,iBAAKO,YAAL,CAAkBP,SAAlB;AACH;AACJ,SArGkD,CAuGnD;AACA;AACA;;;AACAQ,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAK/D,UAAL,KAAoB+D,MAAxB,EAAgC;AAC5B,iBAAK/D,UAAL,GAAkB+D,MAAlB,CAD4B,CAG5B;;AACA,iBAAK9D,SAAL,CAAgBkD,YAAhB,CAA6B,IAA7B;;AAEA,oBAAQ,KAAKnD,UAAb;AACI,mBAAK;AAAA;AAAA,wCAASgD,WAAT,CAAqBgB,KAA1B;AACI;;AACJ,mBAAK;AAAA;AAAA,wCAAShB,WAAT,CAAqBC,KAA1B;AACI;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqBiB,SAA1B;AACI;AACA;AACA,qBAAKnE,UAAL,GAHJ,CAII;AACA;AACA;AACA;AACA;;AACA,qBAAKgE,SAAL,CAAe;AAAA;AAAA,0CAASd,WAAT,CAAqBC,KAApC;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqBkB,aAA1B;AACI;AACA,qBAAKC,WAAL,CAAiB,MAAM;AACnB,uBAAKL,SAAL,CAAe;AAAA;AAAA,4CAASd,WAAT,CAAqBoB,SAApC;AACH,iBAFD;AAGA;;AAEJ,mBAAK;AAAA;AAAA,wCAASpB,WAAT,CAAqBoB,SAA1B;AACI;AACA;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAASpB,WAAT,CAAqBqB,UAA1B;AACI,qBAAKP,SAAL,CAAe;AAAA;AAAA,0CAASd,WAAT,CAAqBC,KAApC;AACA;;AACJ;AACI;AAhCR;AAkCH;AACJ;AAED;AACJ;AACA;;;AACIkB,QAAAA,WAAW,CAACG,QAAD,EAAsB;AAC7B,eAAKC,KAAL,CAAYC,QAAZ,SAA2B,KAAK1E,UAAhC,EAA8C,KAA9C,EAAqD,MAAM;AACvD,iBAAKyE,KAAL,CAAYC,QAAZ,UAA4B,KAAK1E,UAAjC;AACAwE,YAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX,WAHD;AAIH;AAED;;;AACAT,QAAAA,YAAY,CAACP,SAAD,EAAoB;AAC5B;AAEA,kBAAQ,KAAKtD,UAAb;AACI,iBAAK;AAAA;AAAA,sCAASgD,WAAT,CAAqBgB,KAA1B;AACI,mBAAKvB,eAAL,GAAuB,KAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAASO,WAAT,CAAqBC,KAA1B;AACI;AACA;AACA;AACA;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASD,WAAT,CAAqBiB,SAA1B;AACI;;AAEJ,iBAAK;AAAA;AAAA,sCAASjB,WAAT,CAAqBkB,aAA1B;AACA,iBAAK;AAAA;AAAA,sCAASlB,WAAT,CAAqBoB,SAA1B;AACI;;AACJ,iBAAK;AAAA;AAAA,sCAASpB,WAAT,CAAqBxB,KAA1B;AACI,mBAAKJ,GAAL,CAAS;AAAA;AAAA,wCAASC,gBAAT,CAA0BoD,QAAnC;AACA;AAnBR;AAqBH;;AAzLkD,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport EnemyPlaneBase from './EnemyPlaneBase';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport { EnemyData } from '../../../data/EnemyData';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { Movable } from 'db://assets/scripts/game/move/Movable';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlane')\r\nexport default class EnemyPlane extends EnemyPlaneBase {\r\n\r\n    _roleIndex = 1;//当前形态索引\r\n    _enemyData: EnemyData | null = null;\r\n    _curAction: number = 0;\r\n    \r\n    _trackCom: TrackComponent | null = null;\r\n\r\n    _moveCom: Movable | null = null;\r\n    public get moveCom() { return this._moveCom; }\r\n\r\n    protected onLoad(): void {\r\n        this.enemy = true\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        // 添加移动组件\r\n        this._moveCom = Tools.addScript(this.node, Movable);\r\n    }\r\n\r\n    initPlane(data: EnemyData,trackData:any) {\r\n        this._enemyData = data;\r\n        super.init();\r\n        this.reset();\r\n        this.refreshProperty();\r\n        this.initTrack(trackData);\r\n        this._initCollide();\r\n        this.startBattle();\r\n    }\r\n\r\n    initTrack(trackData:any) {\r\n        this._trackCom!.init(this, trackData.trackData, trackData.trackParams, trackData.offset);\r\n\r\n        this._trackCom!.setTrackOverCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.TrackOver);\r\n        });\r\n\r\n        this._trackCom!.setTrackLeaveCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.Leave);\r\n        });\r\n    }\r\n\r\n    initMove(speed: number, angle: number) {\r\n        this._moveCom!.speed = speed;\r\n        this._moveCom!.speedAngle = angle;\r\n        this._moveCom!.setMovable(true);\r\n    }\r\n\r\n    initDelayDestroy(delayTime: number) {\r\n        this._moveCom!.onBecomeInvisibleCallback = () => {\r\n            this.scheduleOnce(this._dieWhenOffScreen, delayTime);\r\n        };\r\n\r\n        this._moveCom!.onBecomeVisibleCallback = () => {\r\n            this.unschedule(this._dieWhenOffScreen);\r\n        };\r\n    }\r\n    \r\n    _dieWhenOffScreen() {\r\n        this.die(GameEnum.EnemyDestroyType.Leave);\r\n    }\r\n\r\n    _initCollide(): void {\r\n        // 添加碰撞组件并初始化\r\n        this.collideComp = this.addComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this);\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n    refreshProperty() {\r\n        this.curHp = this._enemyData?.getFinalAttributeByKey(AttributeConst.MaxHP)!;\r\n        this.maxHp = this.curHp;\r\n    }\r\n\r\n    getAttack():number {\r\n        return this._enemyData?.getFinalAttributeByKey(AttributeConst.Attack) || 0;\r\n    }\r\n\r\n    reset() {\r\n        this._curAction = GameEnum.EnemyAction.Track;\r\n    }\r\n\r\n    startBattle() {\r\n        this.colliderEnabled = true;\r\n        this.updateHpUI();\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead) {\r\n            // 更新所有组件\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n        }\r\n        if (this.isDead) {\r\n            this._checkRemoveAble(deltaTime);\r\n        } else{\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n            this.updateAction(deltaTime);\r\n        }\r\n    }\r\n\r\n    //1.敌机出现，并且移动到指定位置\r\n    //2.开始变形\r\n    //3.开始追踪主角，追踪过程，会射击\r\n    setAction(action: number) {\r\n        if (this._curAction !== action) {\r\n            this._curAction = action;\r\n\r\n            // 停止射击并启用轨迹\r\n            this._trackCom!.setTrackAble(true);\r\n\r\n            switch (this._curAction) {\r\n                case GameEnum.EnemyAction.Sneak:\r\n                    break;\r\n                case GameEnum.EnemyAction.Track:\r\n                    // 跟踪行为\r\n                    break;\r\n                case GameEnum.EnemyAction.Transform:\r\n                    // 变形行为\r\n                    // this._trackCom!.setTrackAble(false);\r\n                    this._roleIndex++;\r\n                    // this.role!.playAnim(\"transform\", () => {\r\n                    //     this.role!.playAnim(\"idle\" + this._roleIndex);\r\n                    //     this.setAction(GameEnum.EnemyAction.Track);\r\n                    //     this._shootCom!.setNextShootAtOnce();\r\n                    // }) || (\r\n                    this.setAction(GameEnum.EnemyAction.Track)\r\n                    break;\r\n                case GameEnum.EnemyAction.AttackPrepare:\r\n                    // 准备攻击行为\r\n                    this.playAtkAnim(() => {\r\n                        this.setAction(GameEnum.EnemyAction.AttackIng);\r\n                    });\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackIng:\r\n                    // 攻击中行为\r\n                    // this._shootCom!.startShoot();\r\n                    break;\r\n                case GameEnum.EnemyAction.AttackOver:\r\n                    this.setAction(GameEnum.EnemyAction.Track);\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n    * 播放攻击动画\r\n    */\r\n    playAtkAnim(callback?: Function) {\r\n        this.plane!.playAnim(`atk${this._roleIndex}`, false, () => {\r\n            this.plane!.playAnim(`idle${this._roleIndex}`);\r\n            callback?.();\r\n        });\r\n    }\r\n\r\n    /**每帧都会检测 */\r\n    updateAction(deltaTime: number) {\r\n        // this._shootCom!.setNextAble(false);\r\n\r\n        switch (this._curAction) {\r\n            case GameEnum.EnemyAction.Sneak:\r\n                this.colliderEnabled = false;\r\n                break;\r\n            case GameEnum.EnemyAction.Track:\r\n                // this._shootCom!.setNextAble(\r\n                //     (this._trackCom!.isMoving && this._enemyData!.bMoveAttack) ||\r\n                //     (!this._trackCom!.isMoving && this._enemyData!.bStayAttack)\r\n                // );\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Transform:\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.AttackPrepare:\r\n            case GameEnum.EnemyAction.AttackIng:\r\n                break;\r\n            case GameEnum.EnemyAction.Leave:\r\n                this.die(GameEnum.EnemyDestroyType.TimeOver);\r\n                break;\r\n        }\r\n    }\r\n}"]}