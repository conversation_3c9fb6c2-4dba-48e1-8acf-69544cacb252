System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, Font, instantiate, Label, NodePool, ParticleSystem2D, Prefab, SpriteAtlas, Tween, UITransform, v3, warn, SingletonBase, GameIns, Tools, GameConst, ImageSequence, UIAnimMethods, GameResourceList, EnemyEffectLayer, MyApp, HurtEffectManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfImageSequence(extras) {
    _reporterNs.report("ImageSequence", "../ui/base/ImageSequence", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIAnimMethods(extras) {
    _reporterNs.report("UIAnimMethods", "../ui/base/UIAnimMethods", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEffectLayer(extras) {
    _reporterNs.report("EnemyEffectLayer", "../ui/layer/EnemyEffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  _export("HurtEffectManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Font = _cc.Font;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      NodePool = _cc.NodePool;
      ParticleSystem2D = _cc.ParticleSystem2D;
      Prefab = _cc.Prefab;
      SpriteAtlas = _cc.SpriteAtlas;
      Tween = _cc.Tween;
      UITransform = _cc.UITransform;
      v3 = _cc.v3;
      warn = _cc.warn;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      GameConst = _unresolved_5.GameConst;
    }, function (_unresolved_6) {
      ImageSequence = _unresolved_6.default;
    }, function (_unresolved_7) {
      UIAnimMethods = _unresolved_7.default;
    }, function (_unresolved_8) {
      GameResourceList = _unresolved_8.default;
    }, function (_unresolved_9) {
      EnemyEffectLayer = _unresolved_9.default;
    }, function (_unresolved_10) {
      MyApp = _unresolved_10.MyApp;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['director', 'Font', 'game', 'instantiate', 'Label', 'Node', 'NodePool', 'ParticleSystem2D', 'Prefab', 'SpriteAtlas', 'SpriteFrame', 'Tween', 'UITransform', 'v3', 'Vec2', 'Vec3', 'warn']);

      _export("HurtEffectManager", HurtEffectManager = class HurtEffectManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.coolTime = 5;
          this.ratio = 0.8;
          this.hurtNum = null;
          this.hurtEffect = null;
          this.m_hurtEffects = new Map();
          this.m_hurtAtlas = null;
          this.m_spriteFrames = [];
          this.m_spriteCounts = [];
          this.m_spriteNameAndCounts = new Map();
          this.m_nameAndImgs = new Map();
          this.m_hurtParticlePF = new Map();
          this.m_hurtParticles = new Map();
          this.m_hurtNums = new Map();
          this.m_hurtFont = new Map();
          this.m_bulletDieAnim = new NodePool();
        }

        /**
         * 预加载资源
         */
        preLoad() {
          this.m_hurtParticles.clear();
          this.m_hurtParticlePF.clear();
          this.m_bulletDieAnim.clear();
          this.m_spriteNameAndCounts.clear();
          this.m_nameAndImgs.clear();

          if (!this.m_hurtAtlas) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).atlas_hurtEffects, SpriteAtlas, (error, atlas) => {
              this.m_hurtAtlas = atlas;
              this.m_spriteFrames = this.m_hurtAtlas.getSpriteFrames();
              const uniqueNames = [];

              for (let i = 1; i < this.m_spriteFrames.length; i++) {
                const name = this.m_spriteFrames[i].name.split("_")[0];

                if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).arrContain(uniqueNames, name)) {
                  uniqueNames.push(name);
                }
              }

              this.m_spriteCounts = [];

              for (const name of uniqueNames) {
                let count = 0;

                for (const frame of this.m_spriteFrames) {
                  if (name === frame.name.split("_")[0]) {
                    count++;
                  }
                }

                this.m_spriteNameAndCounts.set(name, count);
              }

              this.m_hurtEffects.clear();
              this.m_spriteNameAndCounts.forEach((count, name) => {
                const frames = [];

                for (let i = 1; i <= count; i++) {
                  frames.push(this.m_hurtAtlas.getSpriteFrame(`${name}_${i}`));
                }

                const effectInstances = [];
                const effect = instantiate(this.hurtEffect).getComponent(_crd && ImageSequence === void 0 ? (_reportPossibleCrUseOfImageSequence({
                  error: Error()
                }), ImageSequence) : ImageSequence);
                effect.setData(frames);
                effectInstances.push(effect);
                this.m_hurtEffects.set(name, effectInstances);
                this.m_nameAndImgs.set(name, frames);
              });
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.checkLoadFinish();
            });
          }

          if (!this.hurtEffect) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).HurtEffect, Prefab, (error, prefab) => {
              this.hurtEffect = prefab;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.checkLoadFinish();
            });
          }

          if (!this.hurtNum) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).HurtNum, Prefab, (error, prefab) => {
              this.hurtNum = prefab;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.checkLoadFinish();
            });
          }

          if (!this.m_hurtFont) {
            this.m_hurtFont = new Map();
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadDir((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).font_hurtNum, Font, (error, fonts) => {
            fonts.forEach(font => {
              if (font) {
                this.m_hurtFont.set(font.name, font);
              }
            });
            this.m_hurtNums.clear();
            this.m_hurtFont.forEach((font, name) => {
              const instances = [];

              for (let i = 0; i < 3; i++) {
                if (!this.hurtNum) continue;
                const labelNode = instantiate(this.hurtNum);
                const label = labelNode.getComponent(Label);
                label.string = "";
                label.font = font;
                instances.push(label);
              }

              this.m_hurtNums.set(name, instances);
            });
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).Hurt0, Prefab, (error, prefab) => {
            this.m_hurtParticlePF.set("default", prefab);
            const particles = [];
            this.m_hurtParticles.set("default", particles);

            for (let i = 0; i < 10; i++) {
              const particleNode = instantiate(prefab);
              let com = particleNode.getComponent(ParticleSystem2D);
              particles.push(com);
            }

            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }
        /**
         * 清理资源
         */


        clear() {
          if (this.m_hurtAtlas && !(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).Cache) {
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.releaseAssetByForce(this.m_hurtAtlas);
            this.m_hurtAtlas = null;
          }

          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearMapForCompArr(this.m_hurtEffects);
          this.m_spriteFrames = [];
          this.m_spriteCounts = [];
          this.m_spriteNameAndCounts.clear();
          this.m_nameAndImgs.clear();
          this.m_hurtParticlePF.clear();
          this.m_hurtParticles.forEach(particles => {
            for (const particle of particles) {
              if (particle && particle.node) {
                particle.node.destroy();
              }
            }
          });
          this.m_hurtParticles.clear();
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearMapForCompArr(this.m_hurtNums);
          this.m_bulletDieAnim.clear();
        }

        createHurtEffect(pos, particleType, scale = 1) {
          const particle = this.getHurtParticle(particleType);

          if (particle) {
            pos = (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
              error: Error()
            }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtEffectLayer.getComponent(UITransform).convertToNodeSpaceAR(pos);
            particle.node.parent = (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
              error: Error()
            }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtEffectLayer;
            particle.node.setPosition(pos);
            particle.node.setScale(scale, scale);
            particle.node.active = true;

            if (particle.stopped) {
              particle.resetSystem();
            }

            particle.scheduleOnce(() => {
              if (particle && particle.node) {
                particle.stopSystem();
                this.pushHurtParticle(particle, particleType);
              }
            }, 0.12);
          }
        }

        pushHurtParticle(particle, particleType = "default") {
          setTimeout(() => {
            if (particle && particle.node) {
              if (!this.m_hurtParticles.has(particleType)) {
                particleType = "default";
              }

              const particles = this.m_hurtParticles.get(particleType);

              if (particles) {
                particles.push(particle);
                particle.node.active = false;
              } else {
                particle.node.destroy();
              }
            }
          }, 200);
        }
        /**
         * 获取伤害粒子
         * @param {string} particleType 粒子类型
         * @returns {ParticleSystem2D|null} 粒子系统
         */


        getHurtParticle(particleType = "default") {
          if (!this.m_hurtParticles.has(particleType)) {
            particleType = "default";
          }

          const particles = this.m_hurtParticles.get(particleType);

          if (particles && particles.length > 0) {
            const particle = particles.shift();

            if (particle && particle.node) {
              particle.node.active = true;
              return particle;
            }
          }

          const prefab = this.m_hurtParticlePF.get(particleType);

          if (prefab) {
            const newParticle = instantiate(prefab).getComponent(ParticleSystem2D);
            return newParticle;
          }

          return null;
        }
        /**
         * 播放子弹死亡动画
         * @param {Vec2} position 动画位置
         */


        playBulletDieAnim(position) {// if (Tools.isPlaneOutScreen(position)) return;
          // let animNode = this.m_bulletDieAnim.get();
          // if (!animNode) {
          //     animNode = instantiate(GameIns.gameResManager.frameAnim!);
          //     const animComponent = animNode.getComponent(PfFrameAnim)!;
          //     animComponent.init(
          //         GameIns.bulletManager.enemyComAtlas!,
          //         "die_",
          //         7,
          //         GameConst.ActionFrameTime,
          //         () => {
          //             animComponent.stop();
          //             this.m_bulletDieAnim.put(animNode!);
          //         }
          //     );
          // }
          // animNode.parent = EnemyEffectLayer.me.hurtEffectLayer;
          // animNode.setPosition(position.x, position.y);
          // const animComponent = animNode.getComponent(PfFrameAnim);
          // if (animComponent) {
          //     animComponent.reset();
          // }
        }
        /**
         * 根据类型创建伤害数字
         * @param {Object} collider 碰撞体
         * @param {Object} entity 实体对象
         * @param {number} damage 伤害值
         * @param {Object} offset 偏移量
         */


        createHurtNumByType(position, damage, isCirt = false) {
          if (damage <= 0) return;
          const fontType = isCirt ? "yellowHurtNum" : "whiteHurtNum";
          const lab = this.GetHurtNumsByCount(fontType, damage);

          if (lab && (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
            error: Error()
          }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtNumLayer) {
            lab.node.parent = (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
              error: Error()
            }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtNumLayer;
            lab.node.setPosition(position.x, position.y - 30);
            this.startHurtAni(lab, fontType);
          }
        }

        GetHurtNumsByCount(fontType, damage) {
          let hurtNum = null;
          const pool = this.m_hurtNums.get(fontType);

          if (pool) {
            if (pool.length > 0) {
              hurtNum = pool.pop();
            } else {
              hurtNum = instantiate(this.hurtNum).getComponent(Label);
              hurtNum.font = this.m_hurtFont.get(fontType);
            }
          }

          hurtNum.node.opacity = 255;
          hurtNum.node.active = true;
          hurtNum.string = Math.ceil(damage).toString();
          return hurtNum;
        }

        startHurtAni(hurtNum, fontType) {
          const ratio = this.ratio;
          Tween.stopAllByTarget(hurtNum.node);
          let tween;

          switch (fontType) {
            case "whiteHurtNum":
              hurtNum.node.setScale(0.15, 0.15);
              tween = new Tween(hurtNum.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(1, 6), {
                scale: v3(1.53 * ratio, 1.53 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(6, 11), {
                scale: v3(0.47 * ratio, 0.47 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(11, 32), {
                position: v3(hurtNum.node.x, hurtNum.node.y + 13),
                scale: v3(0.47 * ratio, 0.47 * ratio)
              });
              break;

            case "yellowHurtNum":
              hurtNum.node.setScale(0.16, 0.16);
              tween = new Tween(hurtNum.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(1, 6), {
                scale: v3(1.75 * ratio, 1.75 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(6, 9), {
                scale: v3(0.44 * ratio, 0.44 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(9, 12), {
                scale: v3(0.52 * ratio, 0.52 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(12, 31), {
                position: v3(hurtNum.node.x, hurtNum.node.y + 21),
                scale: v3(0.52 * ratio, 0.52 * ratio)
              });
              break;

            default:
              warn("Unknown font type in createHurtNumInTarget");
          }

          tween.call(() => {
            this.pushHurtNums(fontType, hurtNum);
          }).start();
        }

        pushHurtNums(fontType, hurtNum) {
          if (hurtNum && hurtNum.node) {
            hurtNum.string = "";
            hurtNum.node.active = false;
            const pool = this.m_hurtNums.get(fontType);

            if (pool) {
              pool.push(hurtNum);
            } else {
              hurtNum.node.destroy();
            }
          }
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=2c76053ce1b9f314c40aec53b0833627dc0f2acc.js.map