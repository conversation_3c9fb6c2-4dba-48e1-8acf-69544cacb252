"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    saveLevel() {
        console.log('saveLevel in main');
        // @ts-ignore
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "saveLevel", args: [] });
    },
    playLevel() {
        console.log('playLevel in main');
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "playLevel", args: [] });
    },
    levelStart() {
        console.log('levelStart in main this', this);
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "levelStart", args: [] });
    },
    levelEnd() {
        console.log('levelEnd in main');
        Editor.Message.send('scene', "execute-scene-script", { name: "level-editor", method: "levelEnd", args: [] });
    }
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() { }
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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