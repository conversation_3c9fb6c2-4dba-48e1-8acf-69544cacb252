import { MyApp } from "db://assets/scripts/MyApp";
import { GameIns } from "../../../GameIns";
import BaseComp from "../../base/BaseComp";
import { logInfo, logWarn } from "db://assets/scripts/utils/Logger";
import Entity from "../../base/Entity";
import { TargetType } from "db://assets/scripts/autogen/luban/schema";
import Plane from "../PlaneBase";

export default class SkillComp extends BaseComp {
    Cast(caster:Plane, skillID:number) {
        logInfo("Skill", `cast skill ${skillID}`);
        let skillData = MyApp.lubanTables.TbResSkill.get(skillID);
        if (!skillData) {
            logWarn("Skill", `cast skill ${skillID} but config not found`)
            return;
        }
        skillData.ApplyBuffs.forEach((applyBuff) => {
            SkillComp.forEachByTargetType(caster, applyBuff.target, (entity) => {
                (entity as Plane).buffComp.ApplyBuff(applyBuff.buffID);
            })
        })
    }
    
    static forEachByTargetType(caster:Plane, targetType: TargetType, callback: (entity: Plane) => void) {
        switch (targetType) {
            case TargetType.Self:
                callback(caster as Plane);
                break;
            case TargetType.Main:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case TargetType.MainFriendly:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case TargetType.Enemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case TargetType.BossEnemy:
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case TargetType.NormalEnemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                break;
            default:
                break;
        }
    }
}