{"__type__": "cc.Json<PERSON>set", "_name": "tbresgamemode", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"ID": 101, "modeType": 0, "chapterID": 0, "order": 1, "resourceID": 1, "description": "主游戏模式，打分", "conList": [], "cycle": 0, "times": 1, "monType": 3, "costParam1": 5, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 1, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 102, "modeType": 4, "chapterID": 0, "order": 1, "resourceID": 2, "description": "邀请好友PK", "conList": [{"con": 1, "param": 10}], "cycle": 0, "times": 5, "monType": 3, "costParam1": 5, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 2, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 103, "modeType": 3, "chapterID": 0, "order": 1, "resourceID": 3, "description": "金币PK", "conList": [{"con": 1, "param": 10}], "cycle": 0, "times": 10, "monType": 1, "costParam1": 1000, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 3, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 104, "modeType": 3, "chapterID": 0, "order": 2, "resourceID": 4, "description": "钻石PK", "conList": [{"con": 1, "param": 10}], "cycle": 0, "times": 10, "monType": 2, "costParam1": 200, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 4, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 105, "modeType": 3, "chapterID": 0, "order": 3, "resourceID": 5, "description": "高级钻石PK", "conList": [{"con": 1, "param": 10}], "cycle": 0, "times": 10, "monType": 2, "costParam1": 500, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 5, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 2001, "modeType": 1, "chapterID": 10001, "order": 1, "resourceID": 6, "description": "第一章", "conList": [], "cycle": 0, "times": 0, "monType": 3, "costParam1": 5, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10000, "rogueID": 6, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 2002, "modeType": 1, "chapterID": 10002, "order": 2, "resourceID": 7, "description": "第二章", "conList": [{"con": 2, "param": 2001}, {"con": 1, "param": 5}], "cycle": 0, "times": 0, "monType": 3, "costParam1": 5, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 20000, "rogueID": 7, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 2003, "modeType": 1, "chapterID": 0, "order": 3, "resourceID": 8, "description": "第三章", "conList": [{"con": 2, "param": 2001}, {"con": 1, "param": 8}], "cycle": 0, "times": 0, "monType": 3, "costParam1": 5, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30000, "rogueID": 8, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 5, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 1, "modeType": 2, "chapterID": 0, "order": 1, "resourceID": 9, "description": "第一关", "conList": [{"con": 1, "param": 20}], "cycle": 1, "times": 2, "monType": 4, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 9, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 2, "modeType": 2, "chapterID": 0, "order": 2, "resourceID": 10, "description": "第二关", "conList": [{"con": 2, "param": 2001}], "cycle": 1, "times": 2, "monType": 4, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 10, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 3, "modeType": 2, "chapterID": 0, "order": 3, "resourceID": 11, "description": "第三关", "conList": [{"con": 2, "param": 2001}], "cycle": 1, "times": 2, "monType": 4, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 11, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 4, "modeType": 2, "chapterID": 0, "order": 4, "resourceID": 12, "description": "第四关", "conList": [{"con": 2, "param": 2001}], "cycle": 1, "times": 2, "monType": 4, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 12, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}, {"ID": 5, "modeType": 2, "chapterID": 0, "order": 5, "resourceID": 13, "description": "第五关", "conList": [{"con": 2, "param": 2001}], "cycle": 1, "times": 2, "monType": 4, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 13, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID1": 0, "rewardID2": 0, "ratingList": []}]}