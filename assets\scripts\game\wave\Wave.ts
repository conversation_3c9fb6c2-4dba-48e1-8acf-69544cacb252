import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';
import { WaveData, eSpawnOrder } from '../data/WaveData';
import { GameIns } from 'db://assets/scripts/game/GameIns';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('WaveTrack')
export class WaveTrack {
    @property(CCInteger)
    public id = 0;
    @property(CCFloat)
    public speed = 0;
    @property(CCFloat)
    public accelerate = 0;
    @property(CCFloat)
    public Interval = 0;
}

@ccclass('WaveTrackGroup')
export class WaveTrackGroup {
    @property(CCInteger)
    public type = 0;
    @property(CCInteger)
    public loopNum = 0;
    @property(CCInteger)
    public formIndex = 0;
    @property([WaveTrack])
    public tracks: WaveTrack[] = [];
}

@ccclass('Wave')
@executeInEditMode()
export class Wave extends Component {
    
    @property({type:WaveData})
    readonly waveData: WaveData = new WaveData();

    /*
     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
     */
    private _isCompleted: boolean = false;
    // 当前波次是否已完成
    public get isCompleted() { return this._isCompleted; }
    private _waveElapsedTime: number = 0;
    private _nextSpawnTime: number = 0;
    private _nextSpawnIndex: number = 0;
    private _spawnQueue: number[] = [];

    private _reset() {
        this._isCompleted = false;
        this._waveElapsedTime = 0;
        this._nextSpawnTime = 0;
        this._nextSpawnIndex = 0;
        this._spawnQueue = this.waveData.planeList;
    }

    trigger() {
        this._reset();
        // shuffle spawn queue
        if (this.waveData.spawnOrder === eSpawnOrder.Random) {
            this._spawnQueue = this._spawnQueue.sort(() => Math.random() - 0.5);
        }
    }

    // tick wave
    tick(dtInMiliseconds: number) {
        if (this._isCompleted) return;

        this._waveElapsedTime += dtInMiliseconds;
        if (this._waveElapsedTime >= this._nextSpawnTime) {
            if (!this.spawn()) {
                this._isCompleted = true;
            }
        }
    }

    private spawn(): boolean {        
        if (this._nextSpawnIndex >= this._spawnQueue.length) {
            return false;
        }

        this.spawnSingle(this._nextSpawnIndex++);
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval;
        return true;
    }

    private spawnSingle(index: number): void {
        if (index >= this._spawnQueue.length) {
            return;
        }

        let spawnPos = this.waveData.spawnPos;
        let spawnAngle = this.waveData.spawnAngle.eval();
        let spawnSpeed = this.waveData.spawnSpeed;

        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle, spawnSpeed);
    }

    private async createPlane(planeId: number, pos: Vec2, angle: number, speed: number) {
        let enemy = await GameIns.enemyManager.addPlane(planeId, null);
        if (enemy) {
            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);
            // enemy.setStandByTime(0);
            console.log("createPlane", planeId, pos, angle, speed);
            enemy.setPos(pos.x, pos.y);
            enemy.initMove(speed, angle);
            enemy.initDelayDestroy(this.waveData.delayDestroy);
        }
    }

}