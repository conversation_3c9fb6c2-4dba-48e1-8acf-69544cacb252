import { _decorator, director, Label, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/Bundle';
import { ResGameMode } from 'db://assets/scripts/autogen/luban/schema';
import { MyApp } from 'db://assets/scripts/MyApp';
import { LoadingUI } from 'db://assets/scripts/ui/LoadingUI';
import { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/ui/UIMgr';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { HomeUI } from '../home/<USER>';
import { PopupUI } from '../home/<USER>';
import { BuidingUI } from './BuidingUI';
import { BuildingInfoUI } from './BuildingInfoUI';


const { ccclass, property } = _decorator;

@ccclass("StoryUI")
export class StoryUI extends BaseUI {
    @property(ButtonPlus)
    btnMap: ButtonPlus | null = null;
    @property(Label)
    lblBattle: Label | null = null;;
    @property(Node)
    buidings: Node | null = null;
    private index: any = 0;
    private imageUrls: string[] = ["item_7_1", "item_7_2", "item_7_3", "item_7_4", "item_7_5", "item_7_6", "item_7_8"];
    gameMode: ResGameMode | undefined = undefined;
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    public static getUrl(): string { return "prefab/ui/StoryUI" };
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomeStory }
    protected onLoad(): void {
        EventMgr.on(DataEvent.BattleItemClick, this.onBattleItemClick, this);

        this.gameMode = MyApp.lubanTables.TbResGameMode.get(2001);
        //let list: GameMode[] = MyApp.lubanTables.TbResGameMode.getDataList().filter(element => element.modeType == ModeType.STORY);
        let list: ResGameMode[] = MyApp.lubanTables.TbResGameMode.getDataList();
        let row = 0;
        const bagItems = this.buidings!.children.forEach(element => {
            element!.getComponent(BuidingUI)!.setNewFrame(this.imageUrls[row]);
            element!.getComponent(BuidingUI)!.setTitle(list[row].ID, `第${(row + 1)}关`);
            row++;
        });
        this.btnClose!.addClick(this.closeUI, this);
    }
    async closeUI() {
        //await UIMgr.openUI(HomeUI)
        UIMgr.closeUI(StoryUI)
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }
    private onBattleItemClick(index: number, item: string) {
        this.index = index;
        this.lblBattle!.string = `${item}(${index})`;
        UIMgr.openUI(BuildingInfoUI);
    }
    async onShow(...args: any[]): Promise<void> {
        this.btnMap!.addClick(this.onMapClick, this);
    }

    async onHide(...args: any[]): Promise<void> { }

    async onClose(...args: any[]): Promise<void> { }

    onListRender(listItem: Node, row: number) {
        listItem.name = `listItem${row}`
        listItem.getComponentInChildren(Label)!.string = `第${(row + 1)}关`;
    }

    async onMapClick() {
        if (this.index == 0) {
            UIMgr.openUI(PopupUI, "未选择地图");
            return;
        }
        await UIMgr.openUI(LoadingUI)
        EventMgr.emit(HomeUIEvent.Leave)

        director.preloadScene("Game", async () => {
            director.loadScene("Game")
        })
    }
}
