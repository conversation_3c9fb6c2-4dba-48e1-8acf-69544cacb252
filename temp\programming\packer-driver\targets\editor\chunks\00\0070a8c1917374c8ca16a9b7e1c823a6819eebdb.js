System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Node, UITransform, view, LevelLayerUI, MyApp, _dec, _class, _dec2, _class3, _dec3, _class5, _crd, ccclass, property, BackgroundsNodeName, LevelLayer, LevelBackgroundLayer, LevelBaseUI;

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayerUI(extras) {
    _reporterNs.report("LevelLayerUI", "./LevelLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
      view = _cc.view;
    }, function (_unresolved_2) {
      LevelLayerUI = _unresolved_2.LevelLayerUI;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Node', 'Prefab', 'UITransform', 'view']);

      ({
        ccclass,
        property
      } = _decorator);
      BackgroundsNodeName = "backgrounds";
      LevelLayer = (_dec = ccclass('LevelLayer'), _dec(_class = class LevelLayer {
        constructor() {
          this.node = null;
          this.speed = 0;
        }

      }) || _class);
      LevelBackgroundLayer = (_dec2 = ccclass('LevelBackgroundLayer'), _dec2(_class3 = class LevelBackgroundLayer extends LevelLayer {
        constructor(...args) {
          super(...args);
          this.backgrounds = [];
          this.backgroundsNode = null;
        }

      }) || _class3);

      _export("LevelBaseUI", LevelBaseUI = (_dec3 = ccclass('LevelBaseUI'), _dec3(_class5 = class LevelBaseUI extends Component {
        constructor(...args) {
          super(...args);
          this._curLevelIndex = -1;
          // 当前关卡索引
          this._totalTime = 10;
          // 当前关卡的时长
          this._preLevelHeight = 0;
          // 上一关的关卡高度
          this._preLevelOffsetY = 0;
          // 上一关的关卡偏移量
          this._backgroundLayerNode = null;
          this._floorLayersNode = null;
          this._skyLayersNode = null;
          this._backgroundLayer = null;
          this._floorLayers = [];
          this._skyLayers = [];
          this._lastLevelUpdate = null;
        }

        get floorLayers() {
          return this._floorLayers;
        }

        get skyLayers() {
          return this._skyLayers;
        }

        get backgroundLayer() {
          if (!this._backgroundLayer) {
            throw new Error("backgroundLayer is not initialized");
          }

          return this._backgroundLayer;
        }

        get TotalTime() {
          return this._totalTime;
        }

        getLevelTotalHeightByIndex(index) {
          var totalHeight = 0;

          if (this._backgroundLayerNode) {
            const levelNode = this._backgroundLayerNode.getChildByName(`level_${index}`);

            if (levelNode) {
              var preBgNode = levelNode.getChildByName(`layer_0`);

              if (preBgNode) {
                const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);

                if (backgroundsNode) {
                  backgroundsNode.children.forEach(bg => {
                    var height = bg.getComponent(UITransform).contentSize.height;
                    totalHeight += height;
                  });
                }
              }
            }
          }

          return totalHeight;
        }

        onLoad() {}

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        async levelPrefab(levelData, levelInfo, bFristLevel = false) {
          this._backgroundLayerNode = this._getOrAddNode(this.node, "BackgroundLayer");
          this._floorLayersNode = this._getOrAddNode(this.node, "FloorLayers");
          this._skyLayersNode = this._getOrAddNode(this.node, "SkyLayers");

          if (bFristLevel) {
            await this._initByLevelData(levelData, levelInfo);
          } else {
            this._initByLevelData(levelData, levelInfo);
          } // 如果是最后一关，设置无限循环滚动逻辑

          /*if (levelInfo.levelIndex + 1 >= levelInfo.levelCount) {
              this._setupInfiniteScroll();
              this._setupLastLevelUpdate(); 
          }*/

        }

        switchLevel(speed, time, levelIndex) {
          this.backgroundLayer.speed = speed;
          this._totalTime = time; // 释放上一关资源

          this._removeNode(this._backgroundLayerNode, `level_${this._curLevelIndex}`);

          this._removeNode(this._floorLayersNode, `level_${this._curLevelIndex}`);

          this._removeNode(this._skyLayersNode, `level_${this._curLevelIndex}`);

          this._curLevelIndex = levelIndex;
        }

        _removeNode(parentNode, name) {
          var node = parentNode.getChildByName(name);

          if (node) {
            node.removeFromParent();
          }
        }

        async _initByLevelData(data, levelInfo) {
          const levelBackground = this._getOrAddNode(this._backgroundLayerNode, `level_${levelInfo.levelIndex}`);

          const levelFloor = this._getOrAddNode(this._floorLayersNode, `level_${levelInfo.levelIndex}`);

          const levelSky = this._getOrAddNode(this._skyLayersNode, `level_${levelInfo.levelIndex}`);

          await this._initBackgroundLayer(levelBackground, data, levelInfo);

          this._initLayers(levelFloor, this.floorLayers, data.floorLayers);

          this._initLayers(levelSky, this.skyLayers, data.skyLayers);
        }

        _initLayers(parentNode, layers, dataLayers) {
          dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).initByLevelData(layer, this._preLevelOffsetY);
            layers.push(levelLayer);
          });
        }

        async _initBackgroundLayer(parentNode, data, levelInfo) {
          if (data.backgroundLayer.backgrounds.length > 0) {
            if (this._backgroundLayer === null) {
              this._backgroundLayer = new LevelBackgroundLayer();
              this._backgroundLayer.backgrounds = [];
            }

            this._backgroundLayer.speed = data.backgroundLayer.speed;
            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1334);
            const loadPromises = data.backgroundLayer.backgrounds.map(backgroundLayer => {
              return new Promise((resolve, reject) => {
                const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, backgroundLayer);
                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                  if (err) {
                    console.error('LevelBaseUI', "initByLevelData load background prefab err", err);
                    reject(err);
                    return;
                  }

                  this._backgroundLayer.backgrounds.push(prefab);

                  resolve();
                });
              });
            });
            await Promise.all(loadPromises); // 节点设置偏移

            var offsetY = 0;
            this._preLevelHeight = 0;

            if (this.backgroundLayer) {
              const levelNode = this._backgroundLayerNode;
              const childrenCount = levelNode.children.length; // 获取上一关的背景层

              if (childrenCount > 1) {
                const preLevel = this._backgroundLayerNode.children[childrenCount - 2];

                if (preLevel) {
                  const preBgNode = preLevel.getChildByName(`layer_0`);

                  if (preBgNode) {
                    offsetY = preBgNode.getPosition().y;
                    const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);

                    if (backgroundsNode) {
                      backgroundsNode.children.forEach(bg => {
                        const height = bg.getComponent(UITransform).contentSize.height;
                        this._preLevelHeight += height;
                      });
                    }
                  }
                }
              }
            }

            this._preLevelOffsetY = this._preLevelHeight + offsetY;
            console.log('LevelBaseUI', "_initBackgroundLayer _preLevelHeight", this._preLevelHeight, "offsetY", offsetY);
            this.backgroundLayer.node = this._addLayer(parentNode, `layer_0`).node;
            this.backgroundLayer.backgroundsNode = this._getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
            this.backgroundLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).initByLevelData(data.backgroundLayer, this._preLevelOffsetY);
            this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
            var pos = 0;

            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode.children.length) {
              var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode.children.length % this._backgroundLayer.backgrounds.length]);
              const height = bg.getComponent(UITransform).contentSize.height;
              bg.setPosition(0, pos, 0);
              pos += height;

              this._backgroundLayer.backgroundsNode.addChild(bg);
            }
          }
        }

        _addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
            error: Error()
          }), LevelLayerUI) : LevelLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        tick(deltaTime) {
          this._backgroundLayerNode.children.forEach(node => {
            node.children.forEach(child => {
              const layerUI = child.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
                error: Error()
              }), LevelLayerUI) : LevelLayerUI);

              if (layerUI) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              }
            });
          });

          this.floorLayers.forEach(layer => {
            var _layer$node;

            const layerUI = (_layer$node = layer.node) == null ? void 0 : _layer$node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI);

            if (layerUI) {
              if (layerUI.TrackBackground) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              } else {
                layerUI.tick(deltaTime, layer.speed);
              }
            }
          });
          this.skyLayers.forEach(layer => {
            var _layer$node2;

            const layerUI = (_layer$node2 = layer.node) == null ? void 0 : _layer$node2.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI);

            if (layerUI) {
              if (layerUI.TrackBackground) {
                layerUI.tick(deltaTime, this.backgroundLayer.speed);
              } else {
                layerUI.tick(deltaTime, layer.speed);
              }
            }
          });
        }

        _setupInfiniteScroll() {
          if (!this._backgroundLayerNode) return;
          this.schedule(() => {
            const lastLevelNode = this._backgroundLayerNode.getChildByName(`level_${this._curLevelIndex}`);

            if (!lastLevelNode) return;
            const lastBgNode = lastLevelNode.getChildByName(`layer_${this._curLevelIndex}`);
            if (!lastBgNode) return;
            const bgPosY = lastBgNode.getPosition().y;
            const screenHeight = view.getVisibleSize().height; // 当原始节点完全滚出屏幕时，重置其位置到副本下方，并移除副本

            if (bgPosY < -screenHeight * 2) {
              lastBgNode.setPosition(0, bgPosY + screenHeight * 2, 0);

              const lastFloorNode = this._floorLayersNode.getChildByName(`floor_${this._curLevelIndex}`);

              if (lastFloorNode) {
                lastFloorNode.setPosition(0, bgPosY + screenHeight * 2, 0);
              }

              const lastSkyNode = this._skyLayersNode.getChildByName(`sky_${this._curLevelIndex}`);

              if (lastSkyNode) {
                lastSkyNode.setPosition(0, bgPosY + screenHeight * 2, 0);
              }
            }
          }, 0.1);
        }

        _setupLastLevelUpdate() {
          this._lastLevelUpdate = () => {
            this.tick(0.016);
          };

          this.schedule(this._lastLevelUpdate, 0.016);
        }

        onDestroy() {
          this.unschedule(this._setupInfiniteScroll);

          if (this._lastLevelUpdate) {
            this.unschedule(this._lastLevelUpdate);
          }
        }

      }) || _class5));

      _crd = false;
    }
  };
});
//# sourceMappingURL=0070a8c1917374c8ca16a9b7e1c823a6819eebdb.js.map