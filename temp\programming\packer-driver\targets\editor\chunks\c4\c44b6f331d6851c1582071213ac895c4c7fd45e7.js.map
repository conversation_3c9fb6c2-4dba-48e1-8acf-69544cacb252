{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts"], "names": ["_decorator", "Component", "Node", "GameIns", "ccclass", "property", "BattleLayer", "onLoad", "me", "addBullet", "bullet", "enemy", "node", "parent", "enemyBullet<PERSON>ayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addMainPlane", "mainPlaneManager", "mainPlane", "selfPlane<PERSON><PERSON><PERSON>", "addFriendPlane", "plane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEnemy", "zIndex", "enemyPlane<PERSON><PERSON>er", "setSiblingIndex", "addMissile", "addMissileWarn"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACvBC,MAAAA,O,iBAAAA,O;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,W,WADpBF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,sCAlBb,MACqBI,WADrB,SACyCL,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAsB/CM,QAAAA,MAAM,GAAG;AACLD,UAAAA,WAAW,CAACE,EAAZ,GAAiB,IAAjB;AACH;;AAEDC,QAAAA,SAAS,CAACC,MAAD,EAAyC;AAC9C,cAAIA,MAAM,CAACC,KAAX,EAAkB;AACdD,YAAAA,MAAM,CAACE,IAAP,CAAYC,MAAZ,GAAqB,KAAKC,gBAA1B;AACH,WAFD,MAEO;AACHJ,YAAAA,MAAM,CAACE,IAAP,CAAYC,MAAZ,GAAqB,KAAKE,eAA1B;AACH;AACJ;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,CAAoCN,IAApC,CAAyCC,MAAzC,GAAkD,KAAKM,cAAvD;AACH;;AAEDC,QAAAA,cAAc,CAACC,KAAD,EAAwB;AAClCA,UAAAA,KAAK,CAACT,IAAN,CAAWC,MAAX,GAAoB,KAAKS,QAAzB;AACH;;AAEDC,QAAAA,QAAQ,CAACX,IAAD,EAAaY,MAAc,GAAG,CAA9B,EAAiC;AACrCZ,UAAAA,IAAI,CAACC,MAAL,GAAc,KAAKY,eAAnB;AACAb,UAAAA,IAAI,CAACc,eAAL,CAAqBF,MAArB;AACH;;AAEDG,QAAAA,UAAU,CAACf,IAAD,EAAa;AACnBA,UAAAA,IAAI,CAACC,MAAL,GAAc,KAAKC,gBAAnB;AACH;;AAEDc,QAAAA,cAAc,CAAChB,IAAD,EAAa;AACvBA,UAAAA,IAAI,CAACC,MAAL,GAAc,KAAKY,eAAnB;AACH;;AArD8C,O,UAoBxCjB,E;;;;;iBAjBwB,I;;;;;;;iBAGC,I;;;;;;;iBAGA,I;;;;;;;iBAGF,I;;;;;;;iBAGN,I;;;;;;;iBAGO,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BattleLayer')\r\nexport default class BattleLayer extends Component {\r\n\r\n    @property(Node)\r\n    enemyPlaneLayer: Node | null = null;\r\n\r\n    @property(Node)\r\n    enemyBulletLayer: Node | null = null;\r\n\r\n    @property(Node)\r\n    enemyEffectLayer: Node | null = null;\r\n\r\n    @property(Node)\r\n    selfPlaneLayer: Node | null = null;\r\n\r\n    @property(Node)\r\n    dunLayer: Node | null = null;\r\n\r\n    @property(Node)\r\n    selfBulletLayer: Node | null = null;\r\n\r\n    static me: BattleLayer;\r\n\r\n    onLoad() {\r\n        BattleLayer.me = this;\r\n    }\r\n\r\n    addBullet(bullet: { enemy: boolean; node: Node }) {\r\n        if (bullet.enemy) {\r\n            bullet.node.parent = this.enemyBulletLayer;\r\n        } else {\r\n            bullet.node.parent = this.selfBulletLayer;\r\n        }\r\n    }\r\n\r\n    addMainPlane() {\r\n        GameIns.mainPlaneManager.mainPlane!.node.parent = this.selfPlaneLayer;\r\n    }\r\n\r\n    addFriendPlane(plane: { node: Node }) {\r\n        plane.node.parent = this.dunLayer;\r\n    }\r\n\r\n    addEnemy(node: Node, zIndex: number = 0) {\r\n        node.parent = this.enemyPlaneLayer;\r\n        node.setSiblingIndex(zIndex);\r\n    }\r\n\r\n    addMissile(node: Node) {\r\n        node.parent = this.enemyBulletLayer;\r\n    }\r\n\r\n    addMissileWarn(node: Node) {\r\n        node.parent = this.enemyPlaneLayer;\r\n    }\r\n}"]}