{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "assetManager", "EDITOR", "BulletProperty", "Bullet", "EmitterData", "BulletData", "ObjectPool", "BulletSystem", "EventGroupContext", "PropertyContainerComponent", "MyApp", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "Emitter", "displayName", "type", "editor<PERSON><PERSON><PERSON>", "onBulletCreatedCallback", "onEmitterStatusChangedCallback", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitBulletID", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "totalElapsedTime", "bulletProp", "eventGroups", "_status", "None", "_statusElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_entity", "_bulletConfig", "undefined", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "bulletConfig", "onLoad", "createProperties", "createEventGroups", "resetProperties", "onLostFocusInEditor", "updatePropertiesInEditor", "emitterData", "value", "bulletID", "eval", "notifyAll", "setIsActive", "active", "notify", "reset", "changeStatus", "length", "for<PERSON>ach", "group", "setEntity", "entity", "getEntity", "clear", "addProperty", "IsActive", "TotalElapsedTime", "EmitBulletID", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PreWarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "GetInstance", "lubanTables", "TbResBullet", "get", "resMgr", "load", "prefab", "error", "console", "onCreateEmitter", "onDestroyEmitter", "eventGroupData", "ctx", "emitter", "<PERSON><PERSON><PERSON>", "eventGroup", "createEmitterEventGroup", "resetFromData", "bulletData", "oldStatus", "Emitting", "Prewarm", "tryStart", "tryStop", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "bulletPrefab", "createBulletInEditor", "warn", "bullet", "instantiateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "prop", "speedAngle", "atan2", "speed", "resetEventGroups", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAoCC,MAAAA,M,OAAAA,M;AAA2BC,MAAAA,Y,OAAAA,Y;;AAC3EC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,M,iBAAAA,M;;AAChBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,0B,iBAAAA,0B;;AAEVC,MAAAA,K,iBAAAA,K;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAoElB,U;OACpE;AAAEmB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCnB,I;;gCAEnCoB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;yBAgBCC,O,WALZT,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAACvB,MAAN;AAAcsB,QAAAA,WAAW,EAAE,iBAA3B;AAA8CE,QAAAA,UAAU,EAAE;AAA1D,OAAD,C,UAGRV,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBD,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,mFAlBb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAgBlE;AAhBkE,eAiBlEI,uBAjBkE,GAiBR,IAjBQ;AAAA,eAkBlEC,8BAlBkE,GAkBM,IAlBN;AAoBlE;AApBkE,eAqB3DC,QArB2D;AAAA,eAsB3DC,cAtB2D;AAAA,eAuB3DC,SAvB2D;AAAA,eAwB3DC,MAxB2D;AAAA,eAyB3DC,YAzB2D;AAAA,eA0B3DC,eA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,YA5B2D;AAAA,eA6B3DC,YA7B2D;AAAA,eA8B3DC,SA9B2D;AAAA,eA+B3DC,YA/B2D;AAAA,eAgC3DC,YAhC2D;AAAA,eAiC3DC,eAjC2D;AAAA,eAkC3DC,cAlC2D;AAAA,eAmC3DC,KAnC2D;AAAA,eAoC3DC,KApC2D;AAAA,eAqC3DC,GArC2D;AAAA,eAsC3DC,MAtC2D;AAAA,eAuC3DC,gBAvC2D;AAwClE;AAxCkE,eAyC3DC,UAzC2D;AA2ClE;AA3CkE,eA4C3DC,WA5C2D,GA4C/B,EA5C+B;AA8ClE;AA9CkE,eA+CxDC,OA/CwD,GA+C9B7B,cAAc,CAAC8B,IA/Ce;AAAA,eAgDxDC,kBAhDwD,GAgD3B,CAhD2B;AAAA,eAiDxDC,WAjDwD,GAiDjC,KAjDiC;AAAA,eAkDxDC,aAlDwD,GAkDhC,CAlDgC;AAAA,eAmDxDC,aAnDwD,GAmD3B,IAnD2B;AAAA,eAoDxDC,oBApDwD,GAoDpB,IApDoB;AAAA,eAqDxDC,iBArDwD,GAqDvB,IArDuB;AAAA,eAsDxDC,OAtDwD,GAsDjC,IAtDiC;AAAA,eAuDxDC,aAvDwD,GAuDnBC,SAvDmB;AAyDlE;AAzDkE,eA0DxDC,mBA1DwD,GA0DgC,EA1DhC;AAAA;;AA4DpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKT,WAAZ;AAA0B;;AAC5C,YAANU,MAAM,GAAmB;AAAE,iBAAO,KAAKb,OAAZ;AAAsB;;AAChC,YAAjBc,iBAAiB,GAAW;AAAE,iBAAO,KAAKZ,kBAAZ;AAAiC;;AACnD,YAAZa,YAAY,GAAwB;AAAE,iBAAO,KAAKN,aAAZ;AAA4B;;AAE5DO,QAAAA,MAAM,GAAU;AACtB,eAAKC,gBAAL;AACA,eAAKC,iBAAL,GAFsB,CAItB;;AACA,eAAKC,eAAL;AACH,SAvEiE,CAyElE;;;AACOC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKH,iBAAL;AACH,SA7EiE,CA+ElE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEOG,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKC,WAAV,EAAuB;AAEvB,eAAK3C,QAAL,CAAc4C,KAAd,GAAsB,IAAtB;AACA,eAAKtC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,QAA/B;AACA,eAAK5C,cAAL,CAAoB2C,KAApB,GAA4B,KAAKD,WAAL,CAAiB1C,cAA7C;AACA,eAAKC,SAAL,CAAe0C,KAAf,GAAuB,KAAKD,WAAL,CAAiBzC,SAAxC;AACA,eAAKC,MAAL,CAAYyC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBxC,MAArC;AAEA,eAAKC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBvC,YAAjB,CAA8B0C,IAA9B,EAA1B;AACA,eAAKzC,eAAL,CAAqBuC,KAArB,GAA6B,KAAKD,WAAL,CAAiBtC,eAAjB,CAAiCyC,IAAjC,EAA7B;AACA,eAAKvC,YAAL,CAAkBqC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBpC,YAAjB,CAA8BuC,IAA9B,EAA1B;AACA,eAAKtC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBnC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,SAAL,CAAemC,KAAf,GAAuB,KAAKD,WAAL,CAAiBlC,SAAjB,CAA2BqC,IAA3B,EAAvB;AACA,eAAKpC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBjC,YAAjB,CAA8BoC,IAA9B,EAA1B;AACA,eAAKnC,YAAL,CAAkBiC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBhC,YAAjB,CAA8BmC,IAA9B,EAA1B;AACA,eAAKlC,eAAL,CAAqBgC,KAArB,GAA6B,KAAKD,WAAL,CAAiB/B,eAAjB,CAAiCkC,IAAjC,EAA7B;AACA,eAAKjC,cAAL,CAAoB+B,KAApB,GAA4B,KAAKD,WAAL,CAAiB9B,cAAjB,CAAgCiC,IAAhC,EAA5B;AACA,eAAKhC,KAAL,CAAW8B,KAAX,GAAmB,KAAKD,WAAL,CAAiB7B,KAAjB,CAAuBgC,IAAvB,EAAnB;AACA,eAAK/B,KAAL,CAAW6B,KAAX,GAAmB,KAAKD,WAAL,CAAiB5B,KAAjB,CAAuB+B,IAAvB,EAAnB;AACA,eAAK9B,GAAL,CAAS4B,KAAT,GAAiB,KAAKD,WAAL,CAAiB3B,GAAjB,CAAqB8B,IAArB,EAAjB;AACA,eAAK7B,MAAL,CAAY2B,KAAZ,GAAoB,KAAKD,WAAL,CAAiB1B,MAAjB,CAAwB6B,IAAxB,EAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH,SAjHiE,CAkHlE;AAEA;;;AACOC,QAAAA,WAAW,CAACC,MAAD,EAAkB;AAChC,eAAKjD,QAAL,CAAc4C,KAAd,GAAsBK,MAAtB;AACA,eAAKjD,QAAL,CAAckD,MAAd;AACH,SAxHiE,CA0HlE;;;AACOC,QAAAA,KAAK,GAAG;AACX,eAAK3B,WAAL,GAAmB,KAAnB;AACA,eAAK4B,YAAL,CAAkB5D,cAAc,CAAC8B,IAAjC;AACA,eAAKkB,eAAL;;AACA,cAAI,KAAKpB,WAAL,CAAiBiC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,iBAAKjC,WAAL,CAAiBkC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACJ,KAAN,EAAlC;AACH;AACJ;;AAEMK,QAAAA,SAAS,CAACC,MAAD,EAAiB;AAC7B,eAAK5B,OAAL,GAAe4B,MAAf;AACH;;AAEMC,QAAAA,SAAS,GAAkB;AAC9B,iBAAO,KAAK7B,OAAZ;AACH;;AAESS,QAAAA,gBAAgB,GAAG;AACzB,eAAKqB,KAAL;AAEA,eAAK3D,QAAL,GAAgB,KAAK4D,WAAL,CAAiBnE,YAAY,CAACoE,QAA9B,EAAwC,KAAxC,CAAhB;AACA,eAAK3C,gBAAL,GAAwB,KAAK0C,WAAL,CAAiBnE,YAAY,CAACqE,gBAA9B,EAAgD,CAAhD,CAAxB;AACA,eAAKxD,YAAL,GAAoB,KAAKsD,WAAL,CAAiBnE,YAAY,CAACsE,YAA9B,EAA4C,KAAKlB,QAAjD,CAApB;AACA,eAAK5C,cAAL,GAAsB,KAAK2D,WAAL,CAAiBnE,YAAY,CAACuE,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAK9D,SAAL,GAAiB,KAAK0D,WAAL,CAAiBnE,YAAY,CAACwE,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAK9D,MAAL,GAAc,KAAKyD,WAAL,CAAiBnE,YAAY,CAACyE,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAK9D,YAAL,GAAoB,KAAKwD,WAAL,CAAiBnE,YAAY,CAAC0E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK9D,eAAL,GAAuB,KAAKuD,WAAL,CAAiBnE,YAAY,CAAC2E,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAK7D,YAAL,GAAoB,KAAKqD,WAAL,CAAiBnE,YAAY,CAAC4E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK7D,YAAL,GAAoB,KAAKoD,WAAL,CAAiBnE,YAAY,CAAC6E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK7D,SAAL,GAAiB,KAAKmD,WAAL,CAAiBnE,YAAY,CAAC8E,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAK7D,YAAL,GAAoB,KAAKkD,WAAL,CAAiBnE,YAAY,CAAC+E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK7D,YAAL,GAAoB,KAAKiD,WAAL,CAAiBnE,YAAY,CAACgF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK7D,eAAL,GAAuB,KAAKgD,WAAL,CAAiBnE,YAAY,CAACiF,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAK7D,cAAL,GAAsB,KAAK+C,WAAL,CAAiBnE,YAAY,CAACkF,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAK7D,KAAL,GAAa,KAAK8C,WAAL,CAAiBnE,YAAY,CAACmF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAK7D,KAAL,GAAa,KAAK6C,WAAL,CAAiBnE,YAAY,CAACoF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAK7D,GAAL,GAAW,KAAK4C,WAAL,CAAiBnE,YAAY,CAACqF,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAK7D,MAAL,GAAc,KAAK2C,WAAL,CAAiBnE,YAAY,CAACsF,MAA9B,EAAsC,CAAtC,CAAd,CAtByB,CAwBzB;;AACA,eAAK5D,UAAL,GAAkB;AAAA;AAAA,iDAAlB,CAzByB,CA2BzB;;AACA,eAAKb,YAAL,CAAkB0E,EAAlB,CAAsBpC,KAAD,IAAW;AAC5B,gBAAIA,KAAK,GAAG,CAAR,IAAa;AAAA;AAAA,gCAAMqC,WAAN,EAAjB,EAAsC;AAClC,mBAAKnD,aAAL,GAAqB;AAAA;AAAA,kCAAMoD,WAAN,CAAkBC,WAAlB,CAA8BC,GAA9B,CAAkCxC,KAAlC,CAArB;;AACA,kBAAI,KAAKd,aAAT,EAAwB;AACpB;AAAA;AAAA,oCAAMuD,MAAN,CAAaC,IAAb,CAAkB,KAAKxD,aAAL,CAAmByD,MAArC,EAA6ClH,MAA7C,EAAqD,CAACmH,KAAD,EAAaD,MAAb,KAAgC;AACjF,sBAAIC,KAAJ,EAAW;AACPC,oBAAAA,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACA;AACH;;AACD,uBAAK9D,aAAL,GAAqB6D,MAArB;AACH,iBAND;AAOH;AACJ;AACJ,WAbD;AAcA,eAAKvF,QAAL,CAAcgF,EAAd,CAAkBpC,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW;AACP;AAAA;AAAA,gDAAa8C,eAAb,CAA6B,IAA7B;AACH,aAFD,MAEO;AACH;AAAA;AAAA,gDAAaC,gBAAb,CAA8B,IAA9B;AACH;AACJ,WAND;AAOH;;AAESpD,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKI,WAAN,IAAqB,KAAKA,WAAL,CAAiBiD,cAAjB,CAAgCvC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAKjC,WAAL,GAAmB,EAAnB;AACA,cAAIyE,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;AACAD,UAAAA,GAAG,CAACE,WAAJ,GAAkB;AAAA;AAAA,4CAAaA,WAA/B;;AACA,eAAK,IAAMC,UAAX,IAAyB,KAAKrD,WAAL,CAAiBiD,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCJ,GAArC,EAA0CG,UAA1C;AACH;AACJ,SAzMiE,CA2MlE;;;AACUxD,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKG,WAAV,EAAuB;AAEvB,eAAK3C,QAAL,CAAc4C,KAAd,GAAsB,KAAtB;AACA,eAAK1B,gBAAL,CAAsB0B,KAAtB,GAA8B,CAA9B;AACA,eAAKtC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,QAA/B;AACA,eAAK5C,cAAL,CAAoB2C,KAApB,GAA4B,KAAKD,WAAL,CAAiB1C,cAA7C;AACA,eAAKC,SAAL,CAAe0C,KAAf,GAAuB,KAAKD,WAAL,CAAiBzC,SAAxC;AACA,eAAKC,MAAL,CAAYyC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBxC,MAArC;AAEA,eAAKC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBvC,YAAjB,CAA8B0C,IAA9B,EAA1B;AACA,eAAKzC,eAAL,CAAqBuC,KAArB,GAA6B,KAAKD,WAAL,CAAiBtC,eAAjB,CAAiCyC,IAAjC,EAA7B;AACA,eAAKvC,YAAL,CAAkBqC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBpC,YAAjB,CAA8BuC,IAA9B,EAA1B;AACA,eAAKtC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBnC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,SAAL,CAAemC,KAAf,GAAuB,KAAKD,WAAL,CAAiBlC,SAAjB,CAA2BqC,IAA3B,EAAvB;AACA,eAAKpC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBjC,YAAjB,CAA8BoC,IAA9B,EAA1B;AACA,eAAKnC,YAAL,CAAkBiC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBhC,YAAjB,CAA8BmC,IAA9B,EAA1B;AACA,eAAKlC,eAAL,CAAqBgC,KAArB,GAA6B,KAAKD,WAAL,CAAiB/B,eAAjB,CAAiCkC,IAAjC,EAA7B;AACA,eAAKjC,cAAL,CAAoB+B,KAApB,GAA4B,KAAKD,WAAL,CAAiB9B,cAAjB,CAAgCiC,IAAhC,EAA5B;AACA,eAAKhC,KAAL,CAAW8B,KAAX,GAAmB,KAAKD,WAAL,CAAiB7B,KAAjB,CAAuBgC,IAAvB,EAAnB;AACA,eAAK/B,KAAL,CAAW6B,KAAX,GAAmB,KAAKD,WAAL,CAAiB5B,KAAjB,CAAuB+B,IAAvB,EAAnB;AACA,eAAK9B,GAAL,CAAS4B,KAAT,GAAiB,KAAKD,WAAL,CAAiB3B,GAAjB,CAAqB8B,IAArB,EAAjB;AACA,eAAK7B,MAAL,CAAY2B,KAAZ,GAAoB,KAAKD,WAAL,CAAiB1B,MAAjB,CAAwB6B,IAAxB,EAApB;AAEA,eAAK3B,UAAL,CAAgB+E,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAKpD,SAAL,CAAe,IAAf;AACH;AAED;AACJ;AACA;;;AACIK,QAAAA,YAAY,CAAClB,MAAD,EAAyB;AACjC,cAAI,KAAKb,OAAL,KAAiBa,MAArB,EAA6B;AAE7B,cAAMkE,SAAS,GAAG,KAAK/E,OAAvB;AACA,eAAKA,OAAL,GAAea,MAAf;AACA,eAAKX,kBAAL,GAA0B,CAA1B;AACA,eAAKE,aAAL,GAAqB,CAArB,CANiC,CAOjC;;AACA,eAAKO,mBAAL,GAA2B,EAA3B;;AAEA,cAAIE,MAAM,KAAK1C,cAAc,CAAC6G,QAA1B,IAAsCnE,MAAM,KAAK1C,cAAc,CAAC8G,OAApE,EAA6E;AACzE,gBAAI,KAAKlF,WAAL,CAAiBiC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKjC,WAAL,CAAiBkC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACgD,QAAN,EAAlC;AACH;AACJ,WAJD,MAKK;AACD,gBAAI,KAAKnF,WAAL,CAAiBiC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKjC,WAAL,CAAiBkC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACiD,OAAN,EAAlC;AACH;AACJ;;AAED,cAAI,KAAKzG,8BAAL,IAAuC,IAA3C,EACA;AACI,iBAAKA,8BAAL,CAAoC,IAApC,EAA0CqG,SAA1C,EAAqDlE,MAArD;AACH;AACJ;;AAESuE,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKjG,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBnC,YAAjB,CAA8BsC,IAA9B,EAA1B,CAFyB,CAIzB;;AACA,eAAKrB,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,KAAKf,YAAL,CAAkBoC,KAAjE;AACH;;AAES8D,QAAAA,aAAa,GAAG;AACtB,eAAKlF,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAESmF,QAAAA,YAAY,GAAG;AACrB,eAAKnF,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKQ,mBAAL,GAA2B,EAA3B;AACA,eAAK4E,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA,eAAKnG,YAAL,CAAkBiC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBhC,YAAjB,CAA8BmC,IAA9B,EAA1B;;AAEA,cAAI,KAAKlC,eAAL,CAAqBgC,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpG,YAAL,CAAkBiC,KAAtC,EAA6CmE,CAAC,EAA9C,EAAkD;AAC9C,mBAAKnG,eAAL,CAAqBgC,KAArB,GAA6B,KAAKD,WAAL,CAAiB/B,eAAjB,CAAiCkC,IAAjC,EAA7B;AACA,kBAAMkE,UAAU,GAAG,KAAKzF,kBAAL,GAA2B,KAAKX,eAAL,CAAqBgC,KAArB,GAA6BmE,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlG,KAAL,CAAW6B,KAA/B,EAAsCqE,CAAC,EAAvC,EAA2C;AACvC,qBAAKjF,mBAAL,CAAyBkF,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKlG,KAAL,CAAW6B,KAA/B,EAAsCqE,EAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKpG,YAAL,CAAkBiC,KAAtC,EAA6CmE,EAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,EAAhB,EAAmBF,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKtF,mBAAL,CAAyBqB,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAMkE,UAAU,GAAG,KAAKvF,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKT,kBAAL,IAA2BgG,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAKhF,mBAAL,CAAyBwF,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAeC,YAAf,EAAqC;AACrD,cAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,cAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,cAAMY,WAAW,GAAG,KAAKhH,KAAL,CAAW6B,KAAX,GAAmB,CAAnB,GAAwB,KAAK5B,GAAL,CAAS4B,KAAT,IAAkB,KAAK7B,KAAL,CAAW6B,KAAX,GAAmB,CAArC,CAAD,GAA4CuE,KAA5C,GAAoD,KAAKnG,GAAL,CAAS4B,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,cAAMoF,MAAM,GAAG1I,gBAAgB,CAAC,KAAKwB,KAAL,CAAW8B,KAAX,GAAmBmF,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,cAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuBzG,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAApB,EAAuB,OAAO,CAAP;AACvB,gBAAM4H,QAAQ,GAAG1H,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAIyG,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,eAAe,GAAIN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAzB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,gBAAe,GAAIN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAzB;;AACA,uBAAOoB,gBAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,iBAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,iBAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAK1H,cAAL,CAAoB+B,KAApB,GAA4B,KAAKD,WAAL,CAAiB9B,cAAjB,CAAgCiC,IAAhC,EAA5B;AACA,cAAMjC,cAAc,GAAGyH,cAAc,CAAClB,YAAD,EAAe,KAAKzG,YAAL,CAAkBiC,KAAjC,EAAwC,KAAK/B,cAAL,CAAoB+B,KAA5D,CAArC;;AAEA,cAAI,KAAK3B,MAAL,CAAY2B,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAEqF,cAAAA,CAAC,EAAEpH,cAAL;AAAqBuH,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,iBAAO;AACHc,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKhH,MAAL,CAAY2B,KAA1B,GAAkC/B,cADlC;AAEHuH,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKnH,MAAL,CAAY2B;AAF1B,WAAP;AAIH;;AAEDkF,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAKlG,aAAV,EAAyB;AACrB,gBAAI,KAAKiH,YAAT,EAAuB;AACnB,mBAAKjH,aAAL,GAAqB,KAAKiH,YAA1B;AACH,aAFD,MAGK;AACD,kBAAIpK,MAAJ,EAAY;AACR,qBAAKqK,oBAAL,CAA0BlB,SAA1B,EAAqCE,QAArC;AACH,eAFD,MAGK;AACDnC,gBAAAA,OAAO,CAACoD,IAAR,CAAa,oCAAb;AACH;;AACD;AACH;AACJ;;AAED,cAAMC,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa,OAjB2E,CAmBxF;;AACA,cAAME,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAJ,UAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAKA;AAAA;AAAA,4CAAaC,cAAb,CAA4B,IAA5B,EAAkCP,MAAlC;AACAA,UAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuB3G,KAAvB,GAA+BrD,gBAAgB,CAAC2I,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,UAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkB7G,KAAlB,IAA2B,KAAKnC,SAAL,CAAemC,KAA1C;AACAkG,UAAAA,MAAM,CAACQ,IAAP,CAAYvG,SAAZ,GA7BwF,CA8BxF;AACA;;AACA+F,UAAAA,MAAM,CAACY,gBAAP;;AAEA,cAAI,KAAK5J,uBAAL,IAAgC,IAApC,EACA;AACI,iBAAKA,uBAAL,CAA6BgJ,MAA7B;AACH;AACJ;;AAEeF,QAAAA,oBAAoB,CAAClB,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAAA;AAC1G;AACA,gBAAM+B,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,YAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAkB;AACpB1L,cAAAA,YAAY,CAAC2L,OAAb,CAAqB;AAACD,gBAAAA,IAAI,EAAEA;AAAP,eAArB,EAAmC,CAACE,GAAD,EAAM3E,MAAN,KAAiB;AAChD,oBAAI2E,GAAJ,EAAS;AACLzE,kBAAAA,OAAO,CAACD,KAAR,CAAc0E,GAAd;AACA;AACH;;AACD,gBAAA,KAAI,CAACxI,aAAL,GAAqB6D,MAArB;;AACA,oBAAMuD,MAAM,GAAG,KAAI,CAACC,iBAAL,EAAf;;AACA,oBAAI,CAACD,MAAL,EAAa,OAPmC,CAShD;;AACA,oBAAME,UAAU,GAAG,KAAI,CAACC,IAAL,CAAUC,gBAAV,EAAnB;;AACAJ,gBAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAKA;AAAA;AAAA,kDAAaC,cAAb,CAA4B,KAA5B,EAAkCP,MAAlC;AACAA,gBAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuB3G,KAAvB,GAA+BrD,gBAAgB,CAAC2I,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,gBAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkB7G,KAAlB,IAA2B,KAAI,CAACnC,SAAL,CAAemC,KAA1C;AACAkG,gBAAAA,MAAM,CAACQ,IAAP,CAAYvG,SAAZ;AAEA+F,gBAAAA,MAAM,CAACY,gBAAP;AACH,eAtBD;AAuBH,aAzBL;AAJ0G;AA8B7G;;AAESX,QAAAA,iBAAiB,GAAkB;AACzC,cAAMoB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAK3I,aAAnD,CAAnB;;AACA,cAAI,CAACyI,UAAL,EAAiB;AACb1E,YAAAA,OAAO,CAACD,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALwC,CAOzC;;;AACA,cAAMsD,MAAM,GAAGqB,UAAU,CAACG,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACxB,MAAL,EAAa;AACTrD,YAAAA,OAAO,CAACD,KAAR,CAAc,uDAAd;AACA2E,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIhM,MAAJ,EAAY;AACR4L,YAAAA,UAAU,CAACK,IAAX,GAAkB9K,OAAO,CAAC+K,mBAA1B;AACH;;AAED,iBAAO3B,MAAP;AACH;;AAED4B,QAAAA,UAAU,CAACnF,MAAD,EAAiBqC,QAAjB,EAAiC+C,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACrF,MAAL,EAAa;AAEb,cAAMsF,UAAU,GAAG;AAAA;AAAA,wCAAWT,OAAX,CAAmB,KAAKnB,IAAxB,EAA8B1D,MAA9B,CAAnB;AACA,cAAI,CAACsF,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAAC1B,gBAAX,CAA4BvB,QAA5B;AACAiD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKnL,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAc4C,KAArC,EAA4C;AACxC;AACH;;AAED,kBAAQ,KAAKvB,OAAb;AAEI,iBAAK7B,cAAc,CAAC8B,IAApB;AACI,mBAAK8J,gBAAL;AACA;;AACJ,iBAAK5L,cAAc,CAAC8G,OAApB;AACI,mBAAK+E,mBAAL;AACA;;AACJ,iBAAK7L,cAAc,CAAC6G,QAApB;AACI,mBAAKiF,oBAAL;AACA;;AACJ,iBAAK9L,cAAc,CAAC+L,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKhM,cAAc,CAACiM,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;;AAqBA,eAAKnK,kBAAL,IAA2B4J,SAA3B;AACA,eAAKjK,gBAAL,CAAsB0B,KAAtB,IAA+BuI,SAA/B;AAEA,eAAKpI,SAAL;AACH;;AAESqI,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK7J,kBAAL,IAA2B,KAAKnB,YAAL,CAAkBwC,KAAjD,EAAwD;AACpD,iBAAKQ,YAAL,CAAkB5D,cAAc,CAAC8G,OAAjC;AACH;AACJ;;AAES+E,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKnL,SAAL,CAAe0C,KAApB,EACI,KAAKQ,YAAL,CAAkB5D,cAAc,CAAC6G,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK9E,kBAAL,IAA2B,KAAKlB,eAAL,CAAqBuC,KAApD,EAA2D;AACvD,mBAAKQ,YAAL,CAAkB5D,cAAc,CAAC6G,QAAjC;AACH;AACJ;AACJ;;AAESiF,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAK/J,kBAAL,GAA0B,KAAKhB,YAAL,CAAkBqC,KAAhD,EAAuD;AACnD,iBAAK+D,YAAL;AACA,gBAAI,KAAKxG,MAAT,EACI,KAAKiD,YAAL,CAAkB5D,cAAc,CAAC+L,cAAjC,EADJ,KAGI,KAAKnI,YAAL,CAAkB5D,cAAc,CAACiM,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKjK,WAAV,EAAuB;AACnB,iBAAKkF,aAAL;AACH,WAFD,MAGK,IAAI,KAAKnF,kBAAL,IAA2B,KAAKE,aAApC,EAAmD;AACpD,iBAAKgG,OAAL;;AACA,gBAAI,KAAK7G,eAAL,CAAqBgC,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAK6D,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAKhF,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAIoK,WAAW,GAAG,KAAK3J,mBAAL,CAAyBqB,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAKiE,mBAAL;;AACA,cAAIqE,WAAW,IAAI,KAAK3J,mBAAL,CAAyBqB,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAKoD,gBAAL;AACH;AACJ;;AAES+E,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKjK,kBAAL,IAA2B,KAAKb,YAAL,CAAkBkC,KAAjD,EAAwD;AACpD,iBAAKQ,YAAL,CAAkB5D,cAAc,CAAC8G,OAAjC;AACH;AACJ;;AAESoF,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAK1L,QAAL,CAAc4C,KAAd,GAAsB,KAAtB;AACA,eAAK5C,QAAL,CAAckD,MAAd;AACH;;AAtnBiE,O,UAE3DuH,mB,GAA6B,U;;;;;iBAGR,C;;;;;;;;;;;;iBAMQ;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Component, Prefab, Color, Vec3, Quat, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletProperty, Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport Entity from 'db://assets/scripts/game/ui/base/Entity';\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\nimport { ResBullet } from 'db://assets/scripts/autogen/luban/schema';\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, \r\n    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX, \r\n    Angle, Count, Arc, Radius,\r\n    TotalElapsedTime, \r\n}\r\n\r\nexport type onBulletCreatedDelegate = (bullet: Bullet) => void;\r\nexport type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;\r\n\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({displayName: \"子弹ID\"})\r\n    readonly bulletID: number = 0;\r\n\r\n    @property({type:Prefab, displayName: \"子弹Prefab(仅编辑器下)\", editorOnly: true})\r\n    readonly bulletPrefab!: Prefab;\r\n\r\n    @property({type: EmitterData, displayName: \"发射器属性\"})\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({type: BulletData, displayName: \"子弹属性\"})\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // callbacks\r\n    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;\r\n    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitBulletID!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public totalElapsedTime!: Property<number>;\r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _bulletPrefab: Prefab|null = null;\r\n    protected _prewarmEffectPrefab: Prefab|null = null;\r\n    protected _emitEffectPrefab: Prefab|null = null;\r\n    protected _entity: Entity|null = null;\r\n    protected _bulletConfig: ResBullet|undefined = undefined;\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n    get bulletConfig(): ResBullet|undefined { return this._bulletConfig; }\r\n\r\n    protected onLoad() : void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n\r\n        // 更新属性\r\n        this.resetProperties();\r\n    }\r\n\r\n    //#region \"Editor Region\"\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    // public resetInEditor() {\r\n    //     this.resetProperties();\r\n    //     this.changeStatus(eEmitterStatus.None);\r\n    //     if (this.eventGroups.length > 0) {\r\n    //         this.eventGroups.forEach(group => group.reset());\r\n    //     }\r\n    //     this._isEmitting = false;\r\n    //     this.totalElapsedTime.value = 0;\r\n    // }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.isActive.value = true;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.notifyAll(true);\r\n    }\r\n    //#endregion \"Editor Region\"\r\n\r\n    // 通过这个接口来启用和禁用发射器\r\n    public setIsActive(active: boolean) {\r\n        this.isActive.value = active;\r\n        this.isActive.notify();\r\n    }\r\n\r\n    // 这个接口清理发射器的状态，全部从头开始\r\n    public reset() {\r\n        this._isEmitting = false;\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this.resetProperties();\r\n        if (this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.reset());\r\n        }\r\n    }\r\n\r\n    public setEntity(entity: Entity) {\r\n        this._entity = entity;\r\n    }\r\n\r\n    public getEntity(): Entity | null {\r\n        return this._entity;\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n        \r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, false);\r\n        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);\r\n        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        // 子弹表->Prefab路径\r\n        this.emitBulletID.on((value) => {\r\n            if (value > 0 && MyApp.GetInstance()) {\r\n                this._bulletConfig = MyApp.lubanTables.TbResBullet.get(value);\r\n                if (this._bulletConfig) {\r\n                    MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {\r\n                        if (error) {\r\n                            console.error(\"Emitter load bullet prefab err\", error);\r\n                            return;\r\n                        }\r\n                        this._bulletPrefab = prefab;\r\n                    });\r\n                }\r\n            }\r\n        });\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                BulletSystem.onCreateEmitter(this);\r\n            } else {\r\n                BulletSystem.onDestroyEmitter(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        ctx.playerPlane = BulletSystem.playerPlane;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.isActive.value = false;\r\n        this.totalElapsedTime.value = 0;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        if (this._status === status) return;\r\n\r\n        const oldStatus = this._status;\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n\r\n        if (status === eEmitterStatus.Emitting || status === eEmitterStatus.Prewarm) {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStart());\r\n            }\r\n        }\r\n        else {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStop());\r\n            }\r\n        }\r\n\r\n        if (this.onEmitterStatusChangedCallback != null)\r\n        {\r\n            this.onEmitterStatusChangedCallback(this, oldStatus, status);\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // re-eval\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        \r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n\r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle =  Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle =  Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n        \r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius.value + perEmitOffsetX,\r\n            y: direction.y * this.radius.value\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            if (this.bulletPrefab) {\r\n                this._bulletPrefab = this.bulletPrefab;\r\n            }\r\n            else {\r\n                if (EDITOR) {\r\n                    this.createBulletInEditor(direction, position);\r\n                }\r\n                else {\r\n                    console.warn(\"Emitter: No bullet prefab assigned\");\r\n                }\r\n                return;\r\n            }\r\n        }\r\n        \r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        bullet.prop.notifyAll();\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.resetEventGroups();\r\n\r\n        if (this.onBulletCreatedCallback != null)\r\n        {\r\n            this.onBulletCreatedCallback(bullet);\r\n        }\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid: string) => {\r\n                assetManager.loadAny({uuid: uuid}, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.prop.notifyAll();\r\n\r\n                    bullet.resetEventGroups();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): Bullet | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime.value += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n        \r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}