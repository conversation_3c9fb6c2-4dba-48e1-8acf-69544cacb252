import { Emitter } from "./Emitter";
import { Bullet } from "./Bullet";
import { eEmitterCondition, eBulletCondition } from "../data/bullet/EventConditionType";
import { eBulletAction, eEmitterAction } from "../data/bullet/EventActionType";
import { IEventCondition } from "./conditions/IEventCondition";
import { IEventAction } from "./actions/IEventAction";
import { eConditionOp, eCompareOp, EventGroupData, EventActionData, EventConditionData } from "../data/bullet/EventGroupData";
import * as emitter_cond from "./conditions/EmitterEventConditions";
import * as bullet_cond from "./conditions/BulletEventConditions";
import * as emitter_act from "./actions/EmitterEventActions";
import * as bullet_act from "./actions/BulletEventActions";
import { BulletSystem } from "./BulletSystem";
import PlaneBase from "db://assets/scripts/game/ui/plane/PlaneBase";

// context for running condition & action
export class EventGroupContext {
    emitter: Emitter | null = null;
    bullet: Bullet | null = null;
    playerPlane: PlaneBase | null = null;
    // TODO: add level 

    reset(): void {
        this.emitter = null;
        this.bullet = null;
    }
}

// Condition chain with operators
class ConditionChain {
    conditions: Array<IEventCondition> = [];

    evaluate(context: EventGroupContext): boolean {
        if (this.conditions.length === 0) return true;
        let result = this.conditions[0].evaluate(context);
        
        for (let i = 1; i < this.conditions.length; i++) {
            const condition = this.conditions[i];
            const conditionResult = condition.evaluate(context);
            
            if (condition.data.op === eConditionOp.And) {
                result = result && conditionResult;
            } else if (condition.data.op === eConditionOp.Or) {
                result = result || conditionResult;
            }
        }
        
        return result;
    }
}

// Updated EventGroup
export enum eEventGroupStatus {
    Idle,       // not active
    Waiting,    // waiting for conditions to be met
    Active,     // conditions are met, now ticking actions
    Stopped     // stopped
}

export class EventGroup {
    readonly data: EventGroupData;

    context: EventGroupContext;
    conditionChain: ConditionChain;
    actions: IEventAction[];

    private _triggerCount: number = 0;
    private _status: eEventGroupStatus = eEventGroupStatus.Idle;
    get status(): eEventGroupStatus {
        return this._status;
    }
    
    constructor(ctx: EventGroupContext, data: EventGroupData) {
        this.context = ctx;
        this.data = data;
        this.conditionChain = this.buildConditionChain(data.conditions);
        this.actions = data.actions.map(actionData => {
            let action = ActionFactory.create(actionData);
            return action;
        });
        this._triggerCount = 0;

        this.changeStatus(eEventGroupStatus.Idle);
    }

    start(): void {
        BulletSystem.onCreateEventGroup(this);
        this.changeStatus(eEventGroupStatus.Waiting);
    }

    stop(): void {
        // both stop and idle will do the trick
        BulletSystem.onDestroyEventGroup(this);
        this.changeStatus(eEventGroupStatus.Stopped); 
    }

    canExecute(): boolean {
        return this.conditionChain.evaluate(this.context);
    }
    
    tick(dt: number): void {
        
        switch (this._status) {
            case eEventGroupStatus.Idle:
                // not active
                break;
            case eEventGroupStatus.Waiting:
                // waiting for conditions to be met
                if (this.canExecute()) {
                    // TODO: 考虑这里检测增加时间间隔来减少消耗
                    this.changeStatus(eEventGroupStatus.Active);
                }
                break;
            case eEventGroupStatus.Active:
                // conditions are met, now ticking actions
                this.tickActive(dt);
                break;
            case eEventGroupStatus.Stopped:
                // stopped
                break;
        }
    }

    private changeStatus(newStatus: eEventGroupStatus) {
        if (this._status === newStatus) return;

        this._status = newStatus;
    
        switch (this._status) {
            case eEventGroupStatus.Waiting:
                // reset actions by onLoad
                break;
            case eEventGroupStatus.Active:
                // 启用时，重置action的初始参数
                this.actions.forEach(action => action.onLoad(this.context));
                break;
            case eEventGroupStatus.Stopped:
                break;
            default: break;
        }
    }

    private buildConditionChain(conditions: EventConditionData[]): ConditionChain {
        const chain = new ConditionChain();
        conditions.forEach((condData, index) => {
            const condition = ConditionFactory.create(condData);
            if (condition) {
                condition.onLoad(this.context);
                chain.conditions.push(condition);
            }
        });
        return chain;
    }

    private tickActive(dt: number): void {
        let isAllFinished = true;

        for (const action of this.actions) {
            if (action.isCompleted()) continue;
            action.onExecute(this.context, dt);
            isAllFinished = false;
        }
        
        if (isAllFinished) {
            this._triggerCount++;
            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {
                // restart
                this.changeStatus(eEventGroupStatus.Waiting);
            }
            else {
                this.stop();
            }
        }
    }
}

// 提供一个静态函数帮助比较value
export class Comparer {
    static compare(a: number, b: number, op: eCompareOp): boolean {
        switch (op) {
            case eCompareOp.Equal:
                return a === b;
            case eCompareOp.NotEqual:
                return a !== b;
            case eCompareOp.Greater:
                return a > b;
            case eCompareOp.GreaterEqual:
                return a >= b;
            case eCompareOp.Less:
                return a < b;
            case eCompareOp.LessEqual:
                return a <= b;
            default:
                throw new Error(`Unknown compare operator: ${op}`);
        }
    }
}

// Factory pattern for conditions & actions
class ConditionFactory {
    static create(data: EventConditionData): IEventCondition {
        switch (data.type) {
            case eEmitterCondition.Emitter_Active:
                return new emitter_cond.EmitterCondition_Active(data);
            case eEmitterCondition.Emitter_InitialDelay:
                return new emitter_cond.EmitterCondition_InitialDelay(data);
            case eEmitterCondition.Emitter_Prewarm:
                return new emitter_cond.EmitterCondition_Prewarm(data);
            case eEmitterCondition.Emitter_PrewarmDuration:
                return new emitter_cond.EmitterCondition_PrewarmDuration(data);
            case eEmitterCondition.Emitter_Duration:
                return new emitter_cond.EmitterCondition_Duration(data);
            case eEmitterCondition.Emitter_ElapsedTime:
                return new emitter_cond.EmitterCondition_ElapsedTime(data);
            case eEmitterCondition.Emitter_Loop:
                return new emitter_cond.EmitterCondition_Loop(data);
            case eEmitterCondition.Emitter_LoopInterval:
                return new emitter_cond.EmitterCondition_LoopInterval(data);
            case eEmitterCondition.Emitter_EmitInterval:
                return new emitter_cond.EmitterCondition_EmitInterval(data);
            case eEmitterCondition.Emitter_PerEmitCount:
                return new emitter_cond.EmitterCondition_PerEmitCount(data);
            case eEmitterCondition.Emitter_PerEmitInterval:
                return new emitter_cond.EmitterCondition_PerEmitInterval(data);
            case eEmitterCondition.Emitter_PerEmitOffsetX:
                return new emitter_cond.EmitterCondition_PerEmitOffsetX(data);
            case eEmitterCondition.Emitter_Angle:
                return new emitter_cond.EmitterCondition_Angle(data);
            case eEmitterCondition.Emitter_Count:
                return new emitter_cond.EmitterCondition_Count(data);
            case eEmitterCondition.Bullet_Duration:
                return new emitter_cond.EmitterCondition_BulletDuration(data);
            case eEmitterCondition.Bullet_Speed:
                return new emitter_cond.EmitterCondition_BulletSpeed(data);
            case eEmitterCondition.Bullet_Acceleration:
                return new emitter_cond.EmitterCondition_BulletAcceleration(data);
            case eEmitterCondition.Bullet_AccelerationAngle:
                return new emitter_cond.EmitterCondition_BulletAccelerationAngle(data);
            case eEmitterCondition.Bullet_FacingMoveDir:
                return new emitter_cond.EmitterCondition_BulletFacingMoveDir(data);
            case eEmitterCondition.Bullet_TrackingTarget:
                return new emitter_cond.EmitterCondition_BulletTrackingTarget(data);
            case eEmitterCondition.Bullet_Destructive:
                return new emitter_cond.EmitterCondition_BulletDestructive(data);
            case eEmitterCondition.Bullet_DestructiveOnHit:
                return new emitter_cond.EmitterCondition_BulletDestructiveOnHit(data);
            // case eEmitterCondition.Bullet_Sprite:
            //     return new emitter_cond.EmitterCondition_BulletSprite(data);
            case eEmitterCondition.Bullet_Scale:
                return new emitter_cond.EmitterCondition_BulletScale(data);
            case eEmitterCondition.Bullet_ColorR:
                return new emitter_cond.EmitterCondition_BulletColorR(data);
            case eEmitterCondition.Bullet_ColorG:
                return new emitter_cond.EmitterCondition_BulletColorG(data);
            case eEmitterCondition.Bullet_ColorB:
                return new emitter_cond.EmitterCondition_BulletColorB(data);
            case eEmitterCondition.Bullet_DefaultFacing:
                return new emitter_cond.EmitterCondition_BulletDefaultFacing(data);
            case eEmitterCondition.Player_ActLevel:
                return new emitter_cond.EmitterCondition_PlayerActLevel(data);  
            case eEmitterCondition.Player_PosX:
                return new emitter_cond.EmitterCondition_PlayerPosX(data);  
            case eEmitterCondition.Player_PosY:
                return new emitter_cond.EmitterCondition_PlayerPosY(data);
            case eEmitterCondition.Player_LifePercent:
                return new emitter_cond.EmitterCondition_PlayerLifePercent(data);
            case eEmitterCondition.Player_GainBuff:
                return new emitter_cond.EmitterCondition_PlayerGainBuff(data);
            
            // ... bullet cases
            case eBulletCondition.Bullet_Duration:
                return new bullet_cond.BulletCondition_Duration(data);
            case eBulletCondition.Bullet_ElapsedTime:
                return new bullet_cond.BulletCondition_ElapsedTime(data);
            case eBulletCondition.Bullet_PosX:
                return new bullet_cond.BulletCondition_PosX(data);
            case eBulletCondition.Bullet_PosY:
                return new bullet_cond.BulletCondition_PosY(data);
            case eBulletCondition.Bullet_Speed:
                return new bullet_cond.BulletCondition_Speed(data);
            case eBulletCondition.Bullet_SpeedAngle:
                return new bullet_cond.BulletCondition_SpeedAngle(data);
            case eBulletCondition.Bullet_Acceleration:
                return new bullet_cond.BulletCondition_Acceleration(data);
            case eBulletCondition.Bullet_AccelerationAngle:
                return new bullet_cond.BulletCondition_AccelerationAngle(data);
            case eBulletCondition.Bullet_Scale:
                return new bullet_cond.BulletCondition_Scale(data);
            case eBulletCondition.Bullet_ColorR:
                return new bullet_cond.BulletCondition_ColorR(data);
            case eBulletCondition.Bullet_ColorG:
                return new bullet_cond.BulletCondition_ColorG(data);
            case eBulletCondition.Bullet_ColorB:
                return new bullet_cond.BulletCondition_ColorB(data);
            case eBulletCondition.Bullet_FacingMoveDir:
                return new bullet_cond.BulletCondition_FacingMoveDir(data);
            case eBulletCondition.Bullet_Destructive:
                return new bullet_cond.BulletCondition_Destructive(data);
            case eBulletCondition.Bullet_DestructiveOnHit:
                return new bullet_cond.BulletCondition_DestructiveOnHit(data);
            default:
                throw new Error(`Unknown condition type: ${data.type}`);
        }
    }
}

class ActionFactory {
    static create(data: EventActionData): IEventAction {
        switch (data.type) {
            case eEmitterAction.Emitter_Active:
                return new emitter_act.EmitterAction_Active(data);
            case eEmitterAction.Emitter_InitialDelay:
                return new emitter_act.EmitterAction_InitialDelay(data);
            case eEmitterAction.Emitter_Prewarm:
                return new emitter_act.EmitterAction_Prewarm(data);
            case eEmitterAction.Emitter_PrewarmDuration:
                return new emitter_act.EmitterAction_PrewarmDuration(data);
            case eEmitterAction.Emitter_Duration:
                return new emitter_act.EmitterAction_Duration(data);
            case eEmitterAction.Emitter_ElapsedTime:
                return new emitter_act.EmitterAction_ElapsedTime(data);
            case eEmitterAction.Emitter_Loop:
                return new emitter_act.EmitterAction_Loop(data);
            case eEmitterAction.Emitter_LoopInterval:
                return new emitter_act.EmitterAction_LoopInterval(data);
            case eEmitterAction.Emitter_EmitInterval:
                return new emitter_act.EmitterAction_EmitInterval(data);
            case eEmitterAction.Emitter_PerEmitCount:
                return new emitter_act.EmitterAction_PerEmitCount(data);
            case eEmitterAction.Emitter_PerEmitInterval:
                return new emitter_act.EmitterAction_PerEmitInterval(data);
            case eEmitterAction.Emitter_PerEmitOffsetX:
                return new emitter_act.EmitterAction_PerEmitOffsetX(data);
            case eEmitterAction.Emitter_Angle:
                return new emitter_act.EmitterAction_Angle(data);
            case eEmitterAction.Emitter_Count:
                return new emitter_act.EmitterAction_Count(data);
            case eEmitterAction.Bullet_Duration:
                return new emitter_act.EmitterAction_BulletDuration(data);
            case eEmitterAction.Bullet_Damage:
                return new emitter_act.EmitterAction_BulletDamage(data);
            case eEmitterAction.Bullet_Speed:
                return new emitter_act.EmitterAction_BulletSpeed(data);
            case eEmitterAction.Bullet_SpeedAngle:
                return new emitter_act.EmitterAction_BulletSpeedAngle(data);
            case eEmitterAction.Bullet_Acceleration:
                return new emitter_act.EmitterAction_BulletAcceleration(data);
            case eEmitterAction.Bullet_AccelerationAngle:
                return new emitter_act.EmitterAction_BulletAccelerationAngle(data);
            case eEmitterAction.Bullet_Scale:
                return new emitter_act.EmitterAction_BulletScale(data);
            case eEmitterAction.Bullet_ColorR:
                return new emitter_act.EmitterAction_BulletColorR(data);
            case eEmitterAction.Bullet_ColorG:
                return new emitter_act.EmitterAction_BulletColorG(data);
            case eEmitterAction.Bullet_ColorB:
                return new emitter_act.EmitterAction_BulletColorB(data);
            case eEmitterAction.Bullet_FacingMoveDir:
                return new emitter_act.EmitterAction_BulletFacingMoveDir(data);
            case eEmitterAction.Bullet_TrackingTarget:
                return new emitter_act.EmitterAction_BulletTrackingTarget(data);
            case eEmitterAction.Bullet_Destructive:
                return new emitter_act.EmitterAction_BulletDestructive(data);
            case eEmitterAction.Bullet_DestructiveOnHit:
                return new emitter_act.EmitterAction_BulletDestructiveOnHit(data);
            // ... bullet cases
            case eBulletAction.Bullet_Duration:
                return new bullet_act.BulletAction_Duration(data);
            case eBulletAction.Bullet_ElapsedTime:
                return new bullet_act.BulletAction_ElapsedTime(data);
            case eBulletAction.Bullet_PosX:
                return new bullet_act.BulletAction_PosX(data);
            case eBulletAction.Bullet_PosY:
                return new bullet_act.BulletAction_PosY(data);
            case eBulletAction.Bullet_Speed:
                return new bullet_act.BulletAction_Speed(data);
            case eBulletAction.Bullet_SpeedAngle:
                return new bullet_act.BulletAction_SpeedAngle(data);
            case eBulletAction.Bullet_Acceleration:
                return new bullet_act.BulletAction_Acceleration(data);
            case eBulletAction.Bullet_AccelerationAngle:
                return new bullet_act.BulletAction_AccelerationAngle(data);
            case eBulletAction.Bullet_Scale:
                return new bullet_act.BulletAction_Scale(data);
            case eBulletAction.Bullet_ColorR:
                return new bullet_act.BulletAction_ColorR(data);
            case eBulletAction.Bullet_ColorG:
                return new bullet_act.BulletAction_ColorG(data);
            case eBulletAction.Bullet_ColorB:
                return new bullet_act.BulletAction_ColorB(data);
            case eBulletAction.Bullet_FacingMoveDir:
                return new bullet_act.BulletAction_FacingMoveDir(data);
            case eBulletAction.Bullet_Destructive:
                return new bullet_act.BulletAction_Destructive(data);
            case eBulletAction.Bullet_DestructiveOnHit:
                return new bullet_act.BulletAction_DestructiveOnHit(data);
            default:
                throw new Error(`Unknown action type: ${data.type}`);
        }
    }
}
