{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts"], "names": ["GameDataManager", "SingletonBase", "curMainStage", "curSubStage", "reviveCount", "isHardMode", "resetBattleData"], "mappings": ";;;kCAKaA,e;;;;;;;;;;;;AAJJC,MAAAA,a,iBAAAA,a;;;;;iCAIID,e,GAAN,MAAMA,eAAN;AAAA;AAAA,0CAA6D;AAAA;AAAA;AAAA,eAEhEE,YAFgE,GAEzC,CAFyC;AAEtC;AAFsC,eAGhEC,WAHgE,GAG1C,CAH0C;AAGvC;AAHuC,eAIhEC,WAJgE,GAI1C,CAJ0C;AAIvC;AAJuC,eAKhEC,UALgE,GAK1C,KAL0C;AAAA;;AAKnC;AAE7BC,QAAAA,eAAe,GAAG,CAEjB;;AAT+D,O", "sourcesContent": ["\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\n\r\n\r\n\r\nexport class GameDataManager extends SingletonBase<GameDataManager> {\r\n\r\n    curMainStage: number = 1; // 当前主关卡\r\n    curSubStage: number = 1; // 当前子关卡\r\n    reviveCount: number = 0; // 当前子关卡\r\n    isHardMode: boolean = false; // 是否为困难模式\r\n\r\n    resetBattleData() {\r\n\r\n    }\r\n}\r\n\r\n"]}