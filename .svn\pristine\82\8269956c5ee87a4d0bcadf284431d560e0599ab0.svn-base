

import { error } from "cc";
import { Tools } from "../utils/Tools";
import { ResStage } from "../../autogen/luban/schema";


class StageData {
    id = 0;
    mainStage = 0;
    subStage = 0;
    type = 0;
    enemyNorIDs:number[] = [];
    enemyNorInterval = 0;
    enemyNorRate:number[] = [];


    loadJson(data:ResStage) {
        this.mainStage = data.mainStage;
        this.subStage = data.subStage;
        this.type = data.type;
        this.enemyNorIDs = Tools.stringToNumber(data.enemyGroupID, ',');
        this.enemyNorInterval = Number(data.delay);

        if (data.hasOwnProperty('enemyNorRate') && data.enemyNorRate !== '') {
            this.enemyNorRate = Tools.stringToNumber(data.enemyNorRate, ',');
        }
    }
}

export { StageData};