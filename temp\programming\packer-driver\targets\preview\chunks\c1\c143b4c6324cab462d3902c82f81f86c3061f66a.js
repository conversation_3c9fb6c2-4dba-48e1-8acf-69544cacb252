System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, EventGroupData, ExpressionValue, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _dec23, _dec24, _dec25, _dec26, _dec27, _dec28, _dec29, _dec30, _dec31, _dec32, _dec33, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _crd, ccclass, property, EmitterData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "./EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "./ExpressionValue", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      EventGroupData = _unresolved_2.EventGroupData;
    }, function (_unresolved_3) {
      ExpressionValue = _unresolved_3.ExpressionValue;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 发射器数据
       * 所有时间相关的，单位都是毫秒(ms)
       */

      _export("EmitterData", EmitterData = (_dec = ccclass("EmitterData"), _dec2 = property({
        displayName: '是否仅在屏幕内发射',
        group: '基础属性'
      }), _dec3 = property({
        displayName: '是否预热',
        tooltip: '预热xxx',
        group: '基础属性'
      }), _dec4 = property({
        displayName: '是否循环',
        group: '基础属性'
      }), _dec5 = property({
        visible: false
      }), _dec6 = property({
        displayName: '初始延迟',
        group: '基础属性'
      }), _dec7 = property({
        visible: false
      }), _dec8 = property({
        displayName: '预热持续时长'
      }), _dec9 = property({
        type: Prefab,
        displayName: '预热特效',
        group: '基础属性'
      }), _dec10 = property({
        visible: false
      }), _dec11 = property({
        displayName: '发射器持续时间',
        group: '基础属性'
      }), _dec12 = property({
        visible: false
      }), _dec13 = property({
        displayName: '发射间隔',
        group: '基础属性'
      }), _dec14 = property({
        visible: false
      }), _dec15 = property({
        displayName: '发射速度',
        group: '基础属性'
      }), _dec16 = property({
        visible: false
      }), _dec17 = property({
        displayName: '循环间隔',
        group: '基础属性'
      }), _dec18 = property({
        visible: false
      }), _dec19 = property({
        displayName: '单次发射数量',
        group: '基础属性'
      }), _dec20 = property({
        visible: false
      }), _dec21 = property({
        displayName: '单次发射多个子弹时的间隔',
        group: '基础属性'
      }), _dec22 = property({
        visible: false
      }), _dec23 = property({
        displayName: '单次发射多个子弹时的x偏移',
        group: '基础属性'
      }), _dec24 = property({
        visible: false
      }), _dec25 = property({
        displayName: '发射方向',
        group: '基础属性'
      }), _dec26 = property({
        visible: false
      }), _dec27 = property({
        displayName: '发射条数',
        group: '基础属性'
      }), _dec28 = property({
        visible: false
      }), _dec29 = property({
        displayName: '发射范围',
        group: '基础属性'
      }), _dec30 = property({
        visible: false
      }), _dec31 = property({
        displayName: '发射半径',
        group: '基础属性'
      }), _dec32 = property({
        type: Prefab,
        displayName: '发射特效',
        group: '基础属性'
      }), _dec33 = property({
        type: [_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
          error: Error()
        }), EventGroupData) : EventGroupData],
        displayName: '事件组',
        group: '事件组'
      }), _dec(_class = (_class2 = class EmitterData {
        constructor() {
          // @property({displayName: '发射器名称'})
          // name : string = '';                // uid 
          _initializerDefineProperty(this, "isOnlyInScreen", _descriptor, this);

          // 仅在屏幕内才发射
          _initializerDefineProperty(this, "isPreWarm", _descriptor2, this);

          // 是否预热
          _initializerDefineProperty(this, "isLoop", _descriptor3, this);

          // 是否循环
          _initializerDefineProperty(this, "initialDelay", _descriptor4, this);

          _initializerDefineProperty(this, "preWarmDuration", _descriptor5, this);

          // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)
          _initializerDefineProperty(this, "preWarmEffect", _descriptor6, this);

          // @property({displayName: '预热音效'})
          // preWarmSound : string;             // 预热音效
          _initializerDefineProperty(this, "emitDuration", _descriptor7, this);

          _initializerDefineProperty(this, "emitInterval", _descriptor8, this);

          // 用来修改子弹初始速度的乘数(备用)
          _initializerDefineProperty(this, "emitPower", _descriptor9, this);

          // 循环间隔
          _initializerDefineProperty(this, "loopInterval", _descriptor10, this);

          // 单次发射数量
          _initializerDefineProperty(this, "perEmitCount", _descriptor11, this);

          // 单次发射多个子弹时的间隔
          _initializerDefineProperty(this, "perEmitInterval", _descriptor12, this);

          // 单次发射多个子弹时的x偏移    
          _initializerDefineProperty(this, "perEmitOffsetX", _descriptor13, this);

          // 发射方向: 0朝右, 90朝上, 180朝左, 270朝下(or -90)
          _initializerDefineProperty(this, "angle", _descriptor14, this);

          // 发射条数(弹道数量)
          _initializerDefineProperty(this, "count", _descriptor15, this);

          // 发射范围(弧度范围)
          _initializerDefineProperty(this, "arc", _descriptor16, this);

          // 发射半径
          _initializerDefineProperty(this, "radius", _descriptor17, this);

          _initializerDefineProperty(this, "emitEffect", _descriptor18, this);

          // 发射特效(多个的话建议做到prefab上?) 包含音效?
          _initializerDefineProperty(this, "eventGroupData", _descriptor19, this);
        }

        get initialDelayStr() {
          return this.initialDelay.raw;
        }

        set initialDelayStr(value) {
          this.initialDelay.raw = value;
        }

        get preWarmDurationStr() {
          return this.preWarmDuration.raw;
        }

        set preWarmDurationStr(value) {
          this.preWarmDuration.raw = value;
        }

        get emitDurationStr() {
          return this.emitDuration.raw;
        }

        set emitDurationStr(value) {
          this.emitDuration.raw = value;
        }

        get emitIntervalStr() {
          return this.emitInterval.raw;
        }

        set emitIntervalStr(value) {
          this.emitInterval.raw = value;
        }

        get emitPowerStr() {
          return this.emitPower.raw;
        }

        set emitPowerStr(value) {
          this.emitPower.raw = value;
        }

        get loopIntervalStr() {
          return this.loopInterval.raw;
        }

        set loopIntervalStr(value) {
          this.loopInterval.raw = value;
        }

        get perEmitCountStr() {
          return this.perEmitCount.raw;
        }

        set perEmitCountStr(value) {
          this.perEmitCount.raw = value;
        }

        get perEmitIntervalStr() {
          return this.perEmitInterval.raw;
        }

        set perEmitIntervalStr(value) {
          this.perEmitInterval.raw = value;
        }

        get perEmitOffsetXStr() {
          return this.perEmitOffsetX.raw;
        }

        set perEmitOffsetXStr(value) {
          this.perEmitOffsetX.raw = value;
        }

        get angleStr() {
          return this.angle.raw;
        }

        set angleStr(value) {
          this.angle.raw = value;
        }

        get countStr() {
          return this.count.raw;
        }

        set countStr(value) {
          this.count.raw = value;
        }

        get arcStr() {
          return this.arc.raw;
        }

        set arcStr(value) {
          this.arc.raw = value;
        }

        get radiusStr() {
          return this.radius.raw;
        }

        set radiusStr(value) {
          this.radius.raw = value;
        }

        static fromJSON(json) {
          var data = new EmitterData();

          if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map((_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
              error: Error()
            }), EventGroupData) : EventGroupData).fromJSON);
          }

          return data;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isOnlyInScreen", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isPreWarm", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isLoop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "initialDelay", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "initialDelayStr", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "initialDelayStr"), _class2.prototype), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "preWarmDuration", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "preWarmDurationStr", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "preWarmDurationStr"), _class2.prototype), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "preWarmEffect", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "emitDuration", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('10000');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "emitDurationStr", [_dec11], Object.getOwnPropertyDescriptor(_class2.prototype, "emitDurationStr"), _class2.prototype), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "emitInterval", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1000');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "emitIntervalStr", [_dec13], Object.getOwnPropertyDescriptor(_class2.prototype, "emitIntervalStr"), _class2.prototype), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "emitPower", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1.0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "emitPowerStr", [_dec15], Object.getOwnPropertyDescriptor(_class2.prototype, "emitPowerStr"), _class2.prototype), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "loopInterval", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0.0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "loopIntervalStr", [_dec17], Object.getOwnPropertyDescriptor(_class2.prototype, "loopIntervalStr"), _class2.prototype), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "perEmitCount", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "perEmitCountStr", [_dec19], Object.getOwnPropertyDescriptor(_class2.prototype, "perEmitCountStr"), _class2.prototype), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "perEmitInterval", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0.0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "perEmitIntervalStr", [_dec21], Object.getOwnPropertyDescriptor(_class2.prototype, "perEmitIntervalStr"), _class2.prototype), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "perEmitOffsetX", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0.0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "perEmitOffsetXStr", [_dec23], Object.getOwnPropertyDescriptor(_class2.prototype, "perEmitOffsetXStr"), _class2.prototype), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "angle", [_dec24], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('90');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "angleStr", [_dec25], Object.getOwnPropertyDescriptor(_class2.prototype, "angleStr"), _class2.prototype), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "count", [_dec26], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "countStr", [_dec27], Object.getOwnPropertyDescriptor(_class2.prototype, "countStr"), _class2.prototype), _descriptor16 = _applyDecoratedDescriptor(_class2.prototype, "arc", [_dec28], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('60');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "arcStr", [_dec29], Object.getOwnPropertyDescriptor(_class2.prototype, "arcStr"), _class2.prototype), _descriptor17 = _applyDecoratedDescriptor(_class2.prototype, "radius", [_dec30], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1.0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "radiusStr", [_dec31], Object.getOwnPropertyDescriptor(_class2.prototype, "radiusStr"), _class2.prototype), _descriptor18 = _applyDecoratedDescriptor(_class2.prototype, "emitEffect", [_dec32], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class2.prototype, "eventGroupData", [_dec33], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class2)) || _class));

      _crd = false;
    }
  };
});
//# sourceMappingURL=c143b4c6324cab462d3902c82f81f86c3061f66a.js.map