import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Vec2 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;
import { LayerType, LevelData, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataScroll } from 'db://assets/scripts/leveldata/leveldata';
import { LevelEditorLayerUI } from './LevelEditorLayerUI';
import { LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI, LevelScrollLayerUI } from './utils';
import { RandTerrain } from 'db://assets/scripts/game/randTerrain/RandTerrain';

const BackgroundsNodeName = "backgrounds";

@ccclass('LevelEditorBaseUI')
@executeInEditMode()
export class LevelEditorBaseUI extends Component {
    @property(CCString)
    public levelname: string = "";
    @property({type:CCFloat, displayName:"关卡时长"})
    public totalTime: number = 10;
    private _totalHeight: number = 0;

    @property(LevelBackgroundLayer)
    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();
    @property({type:[LevelLayer], displayName:"地面层"})
    public floorLayers: LevelLayer[] = [];
    @property({type:[LevelLayer], displayName:"天空层"})
    public skyLayers: LevelLayer[] = [];

    private backgroundLayerNode:Node|null = null;
    private floorLayersNode:Node|null = null;
    private skyLayersNode:Node|null = null;

    private _play: boolean = false;
    private drawNode: Node | null = null;
    private graphics: Graphics | null = null;

    onLoad():void {
        console.log(`LevelEditorBaseUI start.`);
        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, "BackgroundLayer");
        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, "FloorLayers");
        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, "SkyLayers");

        this.drawNode = LevelEditorUtils.getOrAddNode(this.node, "DrawNode");
        if (!this.graphics) {
            this.graphics = this.drawNode.getComponent(Graphics) || this.drawNode.addComponent(Graphics);
        }
        
        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);
    }
    update(dt:number):void {
        this.checkLayerNode(this.floorLayersNode!, this.floorLayers);
        this.checkLayerNode(this.skyLayersNode!, this.skyLayers);
    }
    private setBackgroundNodePosition(node:Node, yOff:number):number {
        const height = node.getComponent(UITransform)!.contentSize.height;
        node.setPosition(0, yOff-view.getVisibleSize().height/2+height/2);
        return height

    }
    public tick(progress: number):void {
        let yOff = 0
        for (let i = 0; i < this.backgroundLayer.backgroundsNode!.children.length; i++) {
            var bg = this.backgroundLayer.backgroundsNode!.children[i]
            yOff += this.setBackgroundNodePosition(bg, yOff)
        }
        while(this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {
            let bg:Node|null = null;
            let bgIndex = this.backgroundLayer.backgroundsNode!.children.length % this.backgroundLayer.backgrounds.length;
            const prefab = this.backgroundLayer.backgrounds[bgIndex]
            if (prefab != null) {
                bg = instantiate(prefab)
            } 
            if (bg == null) {
                bg = new Node("empty");
                bg.addComponent(UITransform).height = 1024;
            }
            this.backgroundLayer.backgroundsNode!.addChild(bg);
            yOff += this.setBackgroundNodePosition(bg, yOff)
        }
        for (let i = this.backgroundLayer.backgroundsNode!.children.length - 1; i >= 0; i--) {
            const bg = this.backgroundLayer.backgroundsNode!.children[i]
            if (bg.position.y - bg.getComponent(UITransform)!.height/2 > this._totalHeight) {
                bg.removeFromParent()
            } else {
                break;
            }
        }

        this.backgroundLayer!.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(
            progress, this.totalTime, this.backgroundLayer.speed);
        this.floorLayers.forEach((layer) => {
            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);
        });
        this.skyLayers.forEach((layer) => {
            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);
        });
    }

    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    private checkLayerNode(parentNode: Node, layers: LevelLayer[]):void {
        var removeLayerNodes: Node[] = []
        parentNode.children.forEach(node => {
            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
            if (layerCom == null) {
                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null"`);
                removeLayerNodes.push(node)
                return;
            }
            if (layers.find((layer) => layer.node == node) == null) {
                console.log(`Level checkLayerNode remove ${node.name} because not in layers"`);
                removeLayerNodes.push(node)
                return;
            }
        });
        removeLayerNodes.forEach(element => {
            element.removeFromParent();    
        });
        layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
                console.log(`Level checkLayerNode add because layer == null`);
                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; 
            }

            if (layer.type === LayerType.Scroll) {
                this._checkScrollNode(layer, layer!.node);
            } else if (layer.type === LayerType.Random) {
                this._checkRandTerrainNode(layer, layer!.node);
            }
        });
    }

    private _checkScrollNode(data: LevelLayer, parentNode: Node):void {
        const scrollsNode = LevelEditorUtils.getOrAddNode(parentNode, "scrolls");
        if (data.type != LayerType.Scroll) {
            scrollsNode.removeAllChildren();
            return;
        }

        const isCountMatch = scrollsNode.children.length === data.scrollLayers.length;
        let isUUIDMatch = true;
        for (let i = 0; i < scrollsNode.children.length; i++) {
            const scrollNode = scrollsNode.children[i];
            var scrollPrefabUUID = "";
            var firstChild;
            if (scrollNode != null) {
                firstChild = scrollNode.children[0];  
                if (firstChild != null) {
                    // @ts-ignore
                    scrollPrefabUUID = firstChild._prefab?.asset?._uuid;
                } 
            }
            // @ts-ignore
            const scrollUUID = data.scrollLayers[i]?.scrollPrefab?._uuid;

            if (scrollPrefabUUID != scrollUUID) {
                console.log("LevelEditorBaseUI _checkScrollNode scrollPrefabUUID != scrollUUID", scrollPrefabUUID, scrollUUID);
                isUUIDMatch = false;
                break;
            }
        }

        if (!isCountMatch || !isUUIDMatch) {
            scrollsNode.removeAllChildren();
            data.scrollLayers.forEach((scroll, index) => { 
                if (scroll.scrollPrefab != null) {
                        assetManager.loadAny({uuid:scroll.scrollPrefab?.uuid}, (err, prefab:Prefab) => { 
                        if (err) {
                            console.error("LevelEditorBaseUI _checkScrollNode load scroll prefab err", err);
                            return;
                        }
                        const scrollNode = LevelEditorUtils.getOrAddNode(scrollsNode, `scroll_${index}`);
                        
                        var totalHeight = data.speed * this.totalTime;
                        console.log("LevelEditorBaseUI totalHeight", totalHeight);
                        var childCount = totalHeight / data.speed;
                        var posOffsetY = 0;
                        for (let i = 0; i < childCount; i++) {
                            const child = instantiate(prefab);
                            child.setPosition(0, posOffsetY, 0);
                            var offY = child.getComponent(UITransform)!.contentSize.height;
                            scrollNode!.addChild(child);
                            posOffsetY += offY;
                        }
                    });
                }
            });
        }
    }

    private _checkRandTerrainNode(data: LevelLayer, parentNode: Node):void {
        const dynamicNode = LevelEditorUtils.getOrAddNode(parentNode, "dynamic");

        // 删除所有多余的dyna节点
        const currentDynaNodes = dynamicNode.children;
        for (let i = currentDynaNodes.length - 1; i >= 0; i--) {
            const node = currentDynaNodes[i];
            const match = node.name.match(/^dyna_(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index >= data.randomLayers.length) {
                    node.removeFromParent();
                }
            }
        }

        if (data.type != LayerType.Random || data.randomLayers.length === 0) {
            dynamicNode.removeAllChildren();
            return;
        }

        let needRebuild = false;
        const rebuildList: number[] = [];
        
        for (let i = 0; i < data.randomLayers.length; i++) {
            const randTerrains = data.randomLayers[i];
            const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${i}`);
            
            // 计算该dyna节点应有的总地形元素数量
            let expectedChildCount = 0;
            for (const terrains of randTerrains.dynamicTerrains) {
                expectedChildCount += terrains.dynamicTerrain.length;
            }
            
            // 检查子节点数量是否匹配
            if (dynaNode.children.length !== expectedChildCount) {
                needRebuild = true;
                rebuildList.push(i);
                continue;
            }
            
            // 检查每个子节点对应的预制体UUID是否匹配
            let childIndex = 0;
            let isUUIDMatch = true;
            
            for (const terrains of randTerrains.dynamicTerrains) {
                for (const terrain of terrains.dynamicTerrain) {
                    const childNode = dynaNode.children[childIndex];
                    // @ts-ignore
                    const childPrefabUUID = childNode._prefab?.asset?._uuid;
                    const terrainUUID = terrain?.terrainElement?.uuid;
                    
                    if (childPrefabUUID !== terrainUUID) {
                        isUUIDMatch = false;
                        break;
                    }
                    childIndex++;
                }
                if (!isUUIDMatch) break;
            }
            
            if (!isUUIDMatch) {
                needRebuild = true;
                rebuildList.push(i);
            }
        }

        if (needRebuild) {
            //console.log("LevelEditorBaseUI _checkRandTerrainNode need rebuild");

            for (const index of rebuildList) {
                const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${index}`);
                dynaNode.removeAllChildren();
                const randTerrains = data.randomLayers[index];
                // 遍历所有地形组
                for (let j = 0; j < randTerrains.dynamicTerrains.length; j++) {
                    const terrains = randTerrains.dynamicTerrains[j];
                    
                    // 遍历地形组中的每个地形元素
                    for (let k = 0; k < terrains.dynamicTerrain.length; k++) {
                        const terrain = terrains.dynamicTerrain[k];
                        assetManager.loadAny({ uuid: terrain?.terrainElement?.uuid }, (err, prefab: Prefab) => { 
                            if (err) {
                                //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);
                                return;
                            }

                            const node = instantiate(prefab);
                            dynaNode.addChild(node);
                        });
                    }
                }
            }
        }
    }

    public initByLevelData(data: LevelData):void {
        this.levelname = data.name;
        this.totalTime = data.totalTime

        this.backgroundLayerNode!.removeAllChildren()
        this.backgroundLayer = new LevelBackgroundLayer();
        this.backgroundLayer.backgrounds = [];
        data.backgroundLayer?.backgrounds?.forEach((background) => {
            assetManager.loadAny({uuid:background}, (err: Error, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                    return
                } 
                this.backgroundLayer.backgrounds.push(prefab);
            });
        });
        this.backgroundLayer.speed = data.backgroundLayer?.speed;
        this.backgroundLayer.remark = data.backgroundLayer?.remark;
        this._totalHeight = this.backgroundLayer.speed * this.totalTime;
        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode!, "layer").node;
        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
        this.backgroundLayer.backgroundsNode.setSiblingIndex(0); 
        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.initByLevelData(data.backgroundLayer);
        if (data.backgroundLayer && data.backgroundLayer.scrolls) {
            data.backgroundLayer.scrolls.forEach((scrollData) => {
                const scrollLayer = new LevelScrollLayerUI();
                assetManager.loadAny({uuid: scrollData.uuid}, (err: Error, prefab:Prefab) => {
                    if (err) {
                        console.error("LevelEditorBaseUI initByLevelData load background scroll layer prefab err", err);
                        return;
                    }
                    scrollLayer.scrollPrefab = prefab;
                    this.backgroundLayer.scrollLayers.push(scrollLayer);
                });
            });
        }
    
        this.floorLayers = []
        this.skyLayers = []
        LevelEditorBaseUI.initLayers(this.floorLayersNode!, this.floorLayers, data.floorLayers);
        LevelEditorBaseUI.initLayers(this.skyLayersNode!, this.skyLayers, data.skyLayers);
    }

    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        parentNode.removeAllChildren()
        dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.type = layer.type;
            levelLayer.remark = layer.remark;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            const levelEditorLayerUI = levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!;
            if (layer.type === LayerType.Scroll) {
                levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);
            } else if (layer.type === LayerType.Random) {
                levelLayer.randomLayers = [];
                var randomLayers = new LevelRandTerrainsLayersUI();
                var randomLayer = new LevelRandTerrainsLayerUI();
                randomLayer.dynamicTerrain = [];
                layer.dynamics.forEach((dynamic) => {
                    randomLayer.weight = dynamic.weight;
                    dynamic.terrains.forEach((terrain) => {
                        var dynamicTerrain = new LevelRandTerrainUI();
                        dynamicTerrain.weight = terrain.weight;
                        assetManager.loadAny({uuid: terrain.uuid}, (err: Error, prefab:Prefab) => {
                            if (err) {
                                return;
                            } 
                            dynamicTerrain.terrainElement = prefab;
                            randomLayer.dynamicTerrain.push(dynamicTerrain);
                        });

                    });
                });
                randomLayers.dynamicTerrains.push(randomLayer);
                levelLayer.randomLayers.push(randomLayers);
            }
            levelEditorLayerUI.initByLevelData(layer);
            layers.push(levelLayer);
        });
    }

    private static fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {
        dataLayer.speed = layer.speed;
        dataLayer.type = layer.type;
        dataLayer.remark = layer.remark;
        if (layer.type === LayerType.Scroll) {
            layer.scrollLayers.forEach((scrollLayer) => {       
                var data = new LevelDataScroll();
                data.uuid = scrollLayer.scrollPrefab?.uuid!;
                data.weight = scrollLayer.weight;
                data.position = Vec2.ZERO;
                data.scale = Vec2.ONE;
                data.rotation = 0;
                dataLayer.scrolls.push(data);
                console.log("LevelEditorBaseUI fill scrollLayersData", dataLayer);
            });
        } else if (layer.type === LayerType.Random) {
            layer.randomLayers.forEach((randomLayer) => {    
                randomLayer.dynamicTerrains.forEach((terrains) => {
                    var data = new LevelDataRandTerrains();
                    data.terrains = [];
                    data.weight = terrains.weight;
                    terrains.dynamicTerrain.forEach((terrainElement) => {
                        var terrainData: LevelDataRandTerrain = {
                            weight: terrainElement.weight,
                            uuid: terrainElement.terrainElement?.uuid!
                        };
                        data.terrains.push(terrainData);
                    });
                    dataLayer.dynamics.push(data);
                }); 
            });
        }
        layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.fillLevelData(dataLayer);
    }

    private static fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        layers.forEach((layer) => {
            var levelLayer = new LevelDataLayer();
            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
        });
    }
    public fillLevelData(data: LevelData):void {
        data.name = this.levelname;
        data.totalTime = this.totalTime;

        data.backgroundLayer = new LevelDataBackgroundLayer();
        for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {
            const prefab = this.backgroundLayer.backgrounds[i];
            if (prefab == null) {
                continue;
            }
            data.backgroundLayer.backgrounds.push(prefab.uuid);
        }
        LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);

        data.floorLayers = []
        data.skyLayers = []
        LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);
        LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);
    }

    public set play(bPlay: boolean) {
        if (this._play === bPlay) {
            return;
        }
        
        this._play = bPlay;
        console.log("LevelEditorBaseUI set play", bPlay);
        if (bPlay) {
            this._drawViewport();
        } else {
            this._drawClear();
        }
        this._randLayerActive(this.floorLayers, this.floorLayersNode!, this._play);
        this._randLayerActive(this.skyLayers, this.skyLayersNode!, this._play);
    }

    private _randLayerActive(layers: LevelLayer[], parentNode: Node, bPlay: boolean):void {
        layers.forEach((layer) => {
            if (layer.type === LayerType.Random) {
                parentNode.children.forEach(layerNode => {
                    const dynamicNode = layerNode.getChildByName("dynamic")!;
                
                    if (bPlay) {
                        // 先激活所有dyna_x节点
                        dynamicNode.children.forEach(dynaNode => dynaNode.active = true);
                        
                        // 遍历所有dyna_x节点（每个对应一个地形策略组）
                        dynamicNode.children.forEach((dynaNode, groupIndex) => {
                            // 获取对应的地形策略组配置
                            const terrainGroup = layer.randomLayers[groupIndex]?.dynamicTerrains[0];
                            if (!terrainGroup) return;
                            
                            // 计算该组的总权重
                            const totalWeight = terrainGroup.dynamicTerrain.reduce(
                                (sum, terrain) => sum + terrain.weight, 0
                            );
                            
                            // 随机选择地形
                            let randomWeight = Math.random() * totalWeight;
                            let accumulatedWeight = 0;
                            let selectedIndex = -1;
                            
                            for (let i = 0; i < terrainGroup.dynamicTerrain.length; i++) {
                                accumulatedWeight += terrainGroup.dynamicTerrain[i].weight;
                                if (randomWeight <= accumulatedWeight) {
                                    selectedIndex = i;
                                    break;
                                }
                            }
                            
                            // 显示选中的地形，隐藏其他
                            dynaNode.children.forEach((terrainNode, terrainIndex) => {
                                const active = (terrainIndex === selectedIndex);
                                terrainNode.active = active;
                                const terrain = terrainNode.getComponent(RandTerrain)!;
                                terrain.play(active);
                            });
                        });
                    } else {
                        dynamicNode.children.forEach(dynaNode => {
                            dynaNode.active = true;
                            dynaNode.children.forEach(terrainNode => {
                                terrainNode.active = true;
                                const terrain = terrainNode.getComponent(RandTerrain)!;
                                terrain.play(false);
                            });
                        });
                    }  
                });
            } else if (layer.type === LayerType.Scroll) {
                parentNode.children.forEach(layerNode => {
                    // 滚动层逻辑
                    const scrollsNode = layerNode.getChildByName("scrolls")!;
                    scrollsNode.children.forEach(layerNode => layerNode.active = true);
                    
                    if (bPlay) {
                        // 计算总权重
                        let totalWeight = 0;
                        for (const scrollLayer of layer.scrollLayers) {
                            totalWeight += scrollLayer.weight;
                        }
                        
                        // 随机选择要显示的滚动体
                        let randomWeight = Math.random() * totalWeight;
                        let selectedIndex = -1;
                        for (let i = 0; i < layer.scrollLayers.length; i++) {
                            randomWeight -= layer.scrollLayers[i].weight;
                            if (randomWeight <= 0) {
                                selectedIndex = i;
                                console.log("LevelEditorBase selectedIndex", selectedIndex);
                                break;
                            }
                        }
                        
                        scrollsNode.children.forEach((child, index) => {
                            child.active = (index === selectedIndex);
                        });
                    } else {
                        scrollsNode.children.forEach(child => {
                            child.active = true;
                        });
                    }
                })
            }
        });
    }

    private _drawViewport(): void {
        if (!this._play || !this.graphics) return;
            
            const drawTransform = this.drawNode!.getComponent(UITransform)!;

            // 计算视口矩形
            const viewport = new Rect(
                this.drawNode!.getPosition().x - drawTransform.contentSize.width / 2,
                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2,
                drawTransform.contentSize.width,
                drawTransform.contentSize.height
            );
            
            // Draw viewport rectangle
            this.graphics.strokeColor = Color.RED;
            this.graphics.lineWidth = 10;
            this.graphics.rect(viewport.x, viewport.y, viewport.width, viewport.height);
            this.graphics.stroke();

            // 绘制4个填充矩形表示视口边界
            const maskWidth = 10000;
            const maskHeight = drawTransform.contentSize.height;
            this.graphics.fillColor = Color.BLACK;
            
            // 顶部矩形
            this.graphics.fillRect(
                -maskWidth / 2,
                this.drawNode!.getPosition().y + drawTransform.contentSize.height - maskHeight / 2, 
                maskWidth, 
                maskHeight
            );
            
            // 底部矩形
            this.graphics.fillRect(
                -maskWidth / 2,
                this.drawNode!.getPosition().y - drawTransform.contentSize.height - maskHeight / 2,  
                maskWidth, 
                maskHeight
            );
            
            // 左侧矩形
            this.graphics.fillRect(
                -maskWidth - drawTransform.contentSize.width / 2, 
                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2, 
                maskWidth, 
                maskHeight
            );
            
            // 右侧矩形
            this.graphics.fillRect(
                drawTransform.contentSize.width / 2, 
                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2, 
                maskWidth, 
                maskHeight
            );
    }

    private _drawClear(): void {
        if (!this.graphics) return;
        this.graphics.clear();
    }
}