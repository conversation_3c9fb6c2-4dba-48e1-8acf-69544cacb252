System.register([], function (_export, _context) {
  "use strict";

  var _crd, GameResourceList;

  return {
    setters: [],
    execute: function () {
      _crd = true;
      GameResourceList = {
        MainPlane: "prefabs/mainPlane/MainPlane",
        PrefabBoss: "prefabs/boss/BossPlane",
        FrameAnim: "prefabs/FrameAnim",
        Bullet: "prefabs/Bullet",
        EnemyPlane: "prefabs/enemy/EnemyPlane",
        HurtEffect: "prefabs/HurtEffect",
        HurtNum: "prefabs/HurtNum",
        Hurt0: "prefabs/effect/Hurt",
        EmitterPrefabPath: "prefabs/emitter/",
        font_hurtNum: "font/hurtNum",
        atlas_mainPlane: "texture/mainPlane/package_mainPlane_trans_",
        atlas_enemyBullet: "texture/enemy/enemyBullet",
        atlas_mainBullet: "texture/mainPlane/mainBullet",
        atlas_hurtEffects: "texture/hurtEffect/hurtEffects",
        atlas_enemyBullet1: "texture/enemy/1/enemyBullet1",
        atlas_package_enemy1: "texture/enemy/1/package_enemy1",
        atlas_package_turret1: "texture/enemy/1/package_turret1",
        atlas_boss_unit: "texture/boss/boss_unit",
        texture_map_mask: "texture/mask/mask1/spriteFrame",
        spine_boss_smoke: "spine/skel_boss_smoke",
        spine_mainfire: "spine/mainPlane/firePoint/skel_mainfire",
        GameMap_1: "normal/chapter_1/GameMap_1"
      }; // Add "game/" prefix to all values

      (() => {
        for (var key in GameResourceList) {
          GameResourceList[key] = "game/" + GameResourceList[key];
        }
      })();

      _export("default", GameResourceList);

      _crd = false;
    }
  };
});
//# sourceMappingURL=ef621ee973d6b08e625c883a4d62571eb8215ad3.js.map