{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/EmitterEventConditions.ts"], "names": ["EmitterConditionBase", "EmitterCondition_Active", "EmitterCondition_InitialDelay", "EmitterCondition_Prewarm", "EmitterCondition_PrewarmDuration", "EmitterCondition_Duration", "EmitterCondition_ElapsedTime", "EmitterCondition_Loop", "EmitterCondition_LoopInterval", "EmitterCondition_EmitInterval", "EmitterCondition_PerEmitCount", "EmitterCondition_PerEmitInterval", "EmitterCondition_PerEmitOffsetX", "EmitterCondition_Angle", "EmitterCondition_Count", "EmitterCondition_BulletDuration", "EmitterCondition_BulletSpeed", "EmitterCondition_BulletAcceleration", "EmitterCondition_BulletAccelerationAngle", "EmitterCondition_BulletFacingMoveDir", "EmitterCondition_BulletTrackingTarget", "EmitterCondition_BulletDestructive", "EmitterCondition_BulletDestructiveOnHit", "EmitterCondition_BulletHitEffect", "EmitterCondition_BulletScale", "EmitterCondition_BulletColorR", "EmitterCondition_BulletColorG", "EmitterCondition_BulletColorB", "EmitterCondition_BulletDefaultFacing", "EmitterCondition_PlayerActLevel", "EmitterCondition_PlayerPosX", "EmitterCondition_PlayerPosY", "EmitterCondition_PlayerLifePercent", "EmitterCondition_PlayerGainBuff", "Vec3", "EventConditionBase", "Comparer", "eCompareOp", "evaluate", "context", "emitter", "isActive", "value", "compare", "initialDelay", "_targetValue", "data", "compareOp", "Equal", "isPreWarm", "NotEqual", "preWarmDuration", "emitDuration", "totalElapsedTime", "isLoop", "loopInterval", "emitInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "_evalValue", "onLoad", "bulletProp", "duration", "speed", "acceleration", "accelerationAngle", "isFacingMoveDir", "isTrackingTarget", "isDestructive", "isDestructiveOnHit", "scale", "color", "r", "g", "b", "defaultFacing", "_player<PERSON>os", "ZERO", "<PERSON><PERSON><PERSON>", "node", "getPosition", "x", "y", "hp_ratio", "curHp", "maxHp", "buff<PERSON><PERSON>p", "<PERSON><PERSON><PERSON>"], "mappings": ";;;+HAKaA,oB,EAOAC,uB,EAQAC,6B,EAMAC,wB,EAaAC,gC,EAOAC,yB,EAOAC,4B,EAMAC,qB,EAaAC,6B,EAMAC,6B,EAMAC,6B,EAMAC,gC,EAMAC,+B,EAMAC,sB,EAMAC,sB,EASAC,+B,EAaAC,4B,EAaAC,mC,EAaAC,wC,EAaAC,oC,EAaAC,qC,EAaAC,kC,EAaAC,uC,EAaAC,gC,EAaAC,4B,EAaAC,6B,EAaAC,6B,EAaAC,6B,EAaAC,oC,EAiBAC,+B,EAIAC,2B,EAaAC,2B,EAWAC,kC,EAWAC,+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtVQC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,kB,iBAAAA,kB;;AACmBC,MAAAA,Q,iBAAAA,Q;;AACCC,MAAAA,U,iBAAAA,U;;;;;;;;;sCAEhBrC,oB,GAAN,MAAMA,oBAAN;AAAA;AAAA,oDAAsD,E,GAG7D;AACA;AACA;AACA;;;yCACaC,uB,GAAN,MAAMA,uBAAN,SAAsCD,oBAAtC,CAA2D;AACvDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAOA,OAAO,CAACC,OAAR,CAAiBC,QAAjB,CAA0BC,KAAjC;AACH;;AAJ6D,O,GAOlE;;;+CACaxC,6B,GAAN,MAAMA,6BAAN,SAA4CF,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBI,YAAjB,CAA8BF,KAA/C,EAAsD,KAAKG,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;0CAM3D5C,wB,GAAN,MAAMA,wBAAN,SAAuCH,oBAAvC,CAA4D;AACxDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBS,SAAjB,CAA2BP,KAA3B,MAAsC,KAAKG,YAAL,KAAsB,CAA5D,IAAiE,IAAjE,GAAwE,KAA/E;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBS,SAAjB,CAA2BP,KAA3B,MAAsC,KAAKG,YAAL,KAAsB,CAA5D,IAAiE,IAAjE,GAAwE,KAA/E;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV8D,O;;kDAatDzC,gC,GAAN,MAAMA,gCAAN,SAA+CJ,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBW,eAAjB,CAAiCT,KAAlD,EAAyD,KAAKG,YAA9D,EAA4E,KAAKC,IAAL,CAAUC,SAAtF,CAAP;AACH;;AAHsE,O,GAM3E;;;2CACa1C,yB,GAAN,MAAMA,yBAAN,SAAwCL,oBAAxC,CAA6D;AACzDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBY,YAAjB,CAA8BV,KAA/C,EAAsD,KAAKG,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAH+D,O,GAMpE;;;8CACazC,4B,GAAN,MAAMA,4BAAN,SAA2CN,oBAA3C,CAAgE;AAC5DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBa,gBAAjB,CAAkCX,KAAnD,EAA0D,KAAKG,YAA/D,EAA6E,KAAKC,IAAL,CAAUC,SAAvF,CAAP;AACH;;AAHkE,O;;uCAM1DxC,qB,GAAN,MAAMA,qBAAN,SAAoCP,oBAApC,CAAyD;AACrDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBc,MAAjB,CAAwBZ,KAAxB,MAAmC,KAAKG,YAAL,KAAsB,CAAzD,IAA8D,IAA9D,GAAqE,KAA5E;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBc,MAAjB,CAAwBZ,KAAxB,MAAmC,KAAKG,YAAL,KAAsB,CAAzD,IAA8D,IAA9D,GAAqE,KAA5E;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV2D,O;;+CAanDrC,6B,GAAN,MAAMA,6BAAN,SAA4CR,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBe,YAAjB,CAA8Bb,KAA/C,EAAsD,KAAKG,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;+CAM3DtC,6B,GAAN,MAAMA,6BAAN,SAA4CT,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBgB,YAAjB,CAA8Bd,KAA/C,EAAsD,KAAKG,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;+CAM3DrC,6B,GAAN,MAAMA,6BAAN,SAA4CV,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBiB,YAAjB,CAA8Bf,KAA/C,EAAsD,KAAKG,YAA3D,EAAyE,KAAKC,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;kDAM3DpC,gC,GAAN,MAAMA,gCAAN,SAA+CX,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBkB,eAAjB,CAAiChB,KAAlD,EAAyD,KAAKG,YAA9D,EAA4E,KAAKC,IAAL,CAAUC,SAAtF,CAAP;AACH;;AAHsE,O;;iDAM9DnC,+B,GAAN,MAAMA,+BAAN,SAA8CZ,oBAA9C,CAAmE;AAC/DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBmB,cAAjB,CAAgCjB,KAAjD,EAAwD,KAAKG,YAA7D,EAA2E,KAAKC,IAAL,CAAUC,SAArF,CAAP;AACH;;AAHqE,O;;wCAM7DlC,sB,GAAN,MAAMA,sBAAN,SAAqCb,oBAArC,CAA0D;AACtDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBoB,KAAjB,CAAuBlB,KAAxC,EAA+C,KAAKG,YAApD,EAAkE,KAAKC,IAAL,CAAUC,SAA5E,CAAP;AACH;;AAH4D,O;;wCAMpDjC,sB,GAAN,MAAMA,sBAAN,SAAqCd,oBAArC,CAA0D;AACtDsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAiBqB,KAAjB,CAAuBnB,KAAxC,EAA+C,KAAKG,YAApD,EAAkE,KAAKC,IAAL,CAAUC,SAA5E,CAAP;AACH;;AAH4D,O,GAMjE;AACA;AACA;;;iDACahC,+B,GAAN,MAAMA,+BAAN,SAA8Cf,oBAA9C,CAAmE;AAAA;AAAA;AAAA,eAC9D8D,UAD8D,GACzC,CADyC;AAAA;;AAG/DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BC,QAA5B,CAAqCvB,KAAvD;AACH;;AAEMJ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVqE,O;;8CAa7D/B,4B,GAAN,MAAMA,4BAAN,SAA2ChB,oBAA3C,CAAgE;AAAA;AAAA;AAAA,eAC3D8D,UAD2D,GACtC,CADsC;AAAA;;AAG5DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BE,KAA5B,CAAkCxB,KAApD;AACH;;AAEMJ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVkE,O;;qDAa1D9B,mC,GAAN,MAAMA,mCAAN,SAAkDjB,oBAAlD,CAAuE;AAAA;AAAA;AAAA,eAClE8D,UADkE,GAC7C,CAD6C;AAAA;;AAGnEC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BG,YAA5B,CAAyCzB,KAA3D;AACH;;AAEMJ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVyE,O;;0DAajE7B,wC,GAAN,MAAMA,wCAAN,SAAuDlB,oBAAvD,CAA4E;AAAA;AAAA;AAAA,eACvE8D,UADuE,GAClD,CADkD;AAAA;;AAGxEC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BI,iBAA5B,CAA8C1B,KAAhE;AACH;;AAEMJ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAV8E,O;;sDAatE5B,oC,GAAN,MAAMA,oCAAN,SAAmDnB,oBAAnD,CAAwE;AACpEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BK,eAA5B,CAA4C3B,KAA5C,MAAuD,KAAKG,YAAL,KAAsB,CAA7E,IAAkF,IAAlF,GAAyF,KAAhG;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BK,eAA5B,CAA4C3B,KAA5C,MAAuD,KAAKG,YAAL,KAAsB,CAA7E,IAAkF,IAAlF,GAAyF,KAAhG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV0E,O;;uDAalEzB,qC,GAAN,MAAMA,qCAAN,SAAoDpB,oBAApD,CAAyE;AACrEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BM,gBAA5B,CAA6C5B,KAA7C,MAAwD,KAAKG,YAAL,KAAsB,CAA9E,IAAmF,IAAnF,GAA0F,KAAjG;;AACA,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACA,qBAAOX,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BM,gBAA5B,CAA6C5B,KAA7C,MAAwD,KAAKG,YAAL,KAAsB,CAA9E,IAAmF,IAAnF,GAA0F,KAAjG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV2E,O;;oDAanExB,kC,GAAN,MAAMA,kCAAN,SAAiDrB,oBAAjD,CAAsE;AAClEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BO,aAA5B,CAA0C7B,KAA1C,MAAqD,KAAKG,YAAL,KAAsB,CAA3E,IAAgF,IAAhF,GAAuF,KAA9F;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BO,aAA5B,CAA0C7B,KAA1C,MAAqD,KAAKG,YAAL,KAAsB,CAA3E,IAAgF,IAAhF,GAAuF,KAA9F;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVwE,O;;yDAahEvB,uC,GAAN,MAAMA,uCAAN,SAAsDtB,oBAAtD,CAA2E;AACvEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BQ,kBAA5B,CAA+C9B,KAA/C,MAA0D,KAAKG,YAAL,KAAsB,CAAhF,IAAqF,IAArF,GAA4F,KAAnG;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BQ,kBAA5B,CAA+C9B,KAA/C,MAA0D,KAAKG,YAAL,KAAsB,CAAhF,IAAqF,IAArF,GAA4F,KAAnG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV6E,O;;kDAarEtB,gC,GAAN,MAAMA,gCAAN,SAA+CvB,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB,CADJ,CAEQ;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,QAAhB,CAHJ,CAIQ;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVsE,O;;8CAa9D1B,4B,GAAN,MAAMA,4BAAN,SAA2CxB,oBAA3C,CAAgE;AAAA;AAAA;AAAA,eAC3D8D,UAD2D,GACtC,CADsC;AAAA;;AAG5DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BS,KAA5B,CAAkC/B,KAApD;AACH;;AAEMJ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVkE,O;;+CAa1DtB,6B,GAAN,MAAMA,6BAAN,SAA4CzB,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BU,KAA5B,CAAkChC,KAAlC,CAAwCiC,CAA1D;AACH;;AAEMrC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;+CAa3DrB,6B,GAAN,MAAMA,6BAAN,SAA4C1B,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BU,KAA5B,CAAkChC,KAAlC,CAAwCkC,CAA1D;AACH;;AAEMtC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;+CAa3DpB,6B,GAAN,MAAMA,6BAAN,SAA4C3B,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAmC;AAC5C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4BU,KAA5B,CAAkChC,KAAlC,CAAwCmC,CAA1D;AACH;;AAEMvC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiB,KAAKmB,UAAtB,EAAkC,KAAKjB,YAAvC,EAAqD,KAAKC,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;sDAa3DnB,oC,GAAN,MAAMA,oCAAN,SAAmD5B,oBAAnD,CAAwE;AACpEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKO,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOT,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4Bc,aAA5B,CAA0CpC,KAA1C,KAAoD,KAAKG,YAAhE;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB;AACI,qBAAOX,OAAO,CAACC,OAAR,CAAiBwB,UAAjB,CAA4Bc,aAA5B,CAA0CpC,KAA1C,KAAoD,KAAKG,YAAhE;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV0E,O,GAa/E;AACA;AACA;AACA;;;iDACahB,+B,GAAN,MAAMA,+BAAN,SAA8C7B,oBAA9C,CAAmE,CACtE;AADsE,O;;6CAI7D8B,2B,GAAN,MAAMA,2BAAN,SAA0C9B,oBAA1C,CAA+D;AAAA;AAAA;AAAA,eAClE+E,UADkE,GAC/C7C,IAAI,CAAC8C,IAD0C;AAAA;;AAG3D1C,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED1C,UAAAA,OAAO,CAAC0C,WAAR,CAAoBC,IAApB,CAAyBC,WAAzB,CAAqC,KAAKJ,UAA1C;AACA,iBAAO;AAAA;AAAA,oCAASpC,OAAT,CAAiB,KAAKoC,UAAL,CAAgBK,CAAjC,EAAoC,KAAKvC,YAAzC,EAAuD,KAAKC,IAAL,CAAUC,SAAjE,CAAP;AACH;;AAViE,O;;6CAazDhB,2B,GAAN,MAAMA,2BAAN,SAA0CD,2BAA1C,CAAsE;AAClEQ,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED1C,UAAAA,OAAO,CAAC0C,WAAR,CAAoBC,IAApB,CAAyBC,WAAzB,CAAqC,KAAKJ,UAA1C;AACA,iBAAO;AAAA;AAAA,oCAASpC,OAAT,CAAiB,KAAKoC,UAAL,CAAgBM,CAAjC,EAAoC,KAAKxC,YAAzC,EAAuD,KAAKC,IAAL,CAAUC,SAAjE,CAAP;AACH;;AARwE,O;;oDAWhEf,kC,GAAN,MAAMA,kCAAN,SAAiDhC,oBAAjD,CAAsE;AAClEsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED,gBAAMK,QAAQ,GAAG/C,OAAO,CAAC0C,WAAR,CAAoBM,KAApB,GAA4BhD,OAAO,CAAC0C,WAAR,CAAoBO,KAAhD,GAAwD,GAAzE;AACA,iBAAO;AAAA;AAAA,oCAAS7C,OAAT,CAAiB2C,QAAjB,EAA2B,KAAKzC,YAAhC,EAA8C,KAAKC,IAAL,CAAUC,SAAxD,CAAP;AACH;;AARwE,O;;iDAWhEd,+B,GAAN,MAAMA,+BAAN,SAA8CjC,oBAA9C,CAAmE;AAC/DsC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AAAA;;AACjD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED,0CAAO1C,OAAO,CAAC0C,WAAR,CAAoBQ,QAA3B,qBAAO,sBAA8BC,OAA9B,CAAsC,KAAK7C,YAA3C,CAAP;AACH;;AAPqE,O", "sourcesContent": ["import { _decorator, Vec3 } from \"cc\";\r\nimport { EventConditionBase } from \"./IEventCondition\";\r\nimport { EventGroupContext, Comparer } from \"../EventGroup\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class EmitterConditionBase extends EventConditionBase {\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 以下是发射器相关参数\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 发射器是否启用\r\nexport class EmitterCondition_Active extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // Custom evaluation logic for active condition\r\n        return context.emitter!.isActive.value;\r\n    }\r\n}\r\n\r\n// 发射器初始延迟时间\r\nexport class EmitterCondition_InitialDelay extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.initialDelay.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Prewarm extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.isPreWarm.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.isPreWarm.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PrewarmDuration extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.preWarmDuration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n// 发射器持续时间\r\nexport class EmitterCondition_Duration extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.emitDuration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n// 发射器已运行时间\r\nexport class EmitterCondition_ElapsedTime extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.totalElapsedTime.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Loop extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.isLoop.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.isLoop.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_LoopInterval extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.loopInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_EmitInterval extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.emitInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitCount extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitCount.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitInterval extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitOffsetX extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitOffsetX.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Angle extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.angle.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Count extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.count.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 以下是发射器配置的子弹相关参数\r\n/////////////////////////////////////////////////////////////////////////////////\r\nexport class EmitterCondition_BulletDuration extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.duration.value;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletSpeed extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.speed.value;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletAcceleration extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.acceleration.value;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletAccelerationAngle extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.accelerationAngle.value;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletFacingMoveDir extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletTrackingTarget extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isTrackingTarget.value === (this._targetValue === 1) ? true : false;\r\n                case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isTrackingTarget.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDestructive extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isDestructive.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isDestructive.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDestructiveOnHit extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletHitEffect extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                // return context.emitter!.bulletData.hitEffect === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                // return context.emitter!.bulletData.hitEffect !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletScale extends EmitterConditionBase {\r\n    private _evalValue: number = 1;\r\n\r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.scale.value;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorR extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.r;\r\n    }\r\n    \r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorG extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.g;\r\n    }\r\n    \r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorB extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.b;\r\n    }\r\n    \r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDefaultFacing extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.defaultFacing.value === this._targetValue;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.defaultFacing.value !== this._targetValue;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// Player\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 玩家account等级\r\nexport class EmitterCondition_PlayerActLevel extends EmitterConditionBase {\r\n    // TODO:\r\n}\r\n\r\nexport class EmitterCondition_PlayerPosX extends EmitterConditionBase {\r\n    _playerPos: Vec3 = Vec3.ZERO;\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        context.playerPlane.node.getPosition(this._playerPos);\r\n        return Comparer.compare(this._playerPos.x, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerPosY extends EmitterCondition_PlayerPosX {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        context.playerPlane.node.getPosition(this._playerPos);\r\n        return Comparer.compare(this._playerPos.y, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerLifePercent extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        const hp_ratio = context.playerPlane.curHp / context.playerPlane.maxHp * 100;\r\n        return Comparer.compare(hp_ratio, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerGainBuff extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        return context.playerPlane.buffComp?.HasBuff(this._targetValue);\r\n    }\r\n}"]}