{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/IEventAction.ts"], "names": ["EventActionBase", "eTargetValueType", "Easing", "constructor", "data", "_isCompleted", "_elapsedTime", "_startValue", "_targetValue", "_duration", "isCompleted", "canLerp", "onLoad", "context", "duration", "eval", "resetStartValue", "resetTargetValue", "onExecute", "dt", "executeInternal", "lerp<PERSON><PERSON>ue", "startValue", "targetValue", "lerp", "easing", "Math", "min", "targetValueType", "Relative", "value"], "mappings": ";;;6CAgBaA,e;;;;;;;;;;;;;;;;;;;;;;;;AAfJC,MAAAA,gB,iBAAAA,gB;;AAEAC,MAAAA,M,iBAAAA,M;;;;;iCAaIF,e,GAAN,MAAMA,eAAN,CAA8C;AASjDG,QAAAA,WAAW,CAACC,IAAD,EAAyB;AAAA,eAR3BA,IAQ2B;AAAA,eAN1BC,YAM0B,GANF,KAME;AAAA,eAL1BC,YAK0B,GALH,CAKG;AAAA,eAJ1BC,WAI0B,GAJJ,CAII;AAAA,eAH1BC,YAG0B,GAHH,CAGG;AAAA,eAF1BC,SAE0B,GAFN,CAEM;AAChC,eAAKL,IAAL,GAAYA,IAAZ;AACH;;AAEDM,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKL,YAAZ;AACH;;AAEDM,QAAAA,OAAO,GAAY;AACf,iBAAO,IAAP;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,eAAKR,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKG,SAAL,GAAiB,KAAKL,IAAL,CAAUU,QAAV,CAAmBC,IAAnB,EAAjB;AACA,eAAKC,eAAL,CAAqBH,OAArB;AACA,eAAKI,gBAAL,CAAsBJ,OAAtB;AACH;;AAEDK,QAAAA,SAAS,CAACL,OAAD,EAA6BM,EAA7B,EAA+C;AACpD,eAAKb,YAAL,IAAqBa,EAArB;;AACA,cAAI,KAAKb,YAAL,IAAqB,KAAKG,SAA9B,EAAyC;AACrC,iBAAKW,eAAL,CAAqBP,OAArB,EAA8B,KAAKL,YAAnC;AACA,iBAAKH,YAAL,GAAoB,IAApB;AACH,WAHD,MAIK,IAAI,KAAKM,OAAL,EAAJ,EAAoB;AACrB,iBAAKS,eAAL,CAAqBP,OAArB,EAA8B,KAAKQ,SAAL,CAAe,KAAKd,WAApB,EAAiC,KAAKC,YAAtC,CAA9B;AACH;AACJ;;AAEDa,QAAAA,SAAS,CAACC,UAAD,EAAqBC,WAArB,EAAkD;AACvD,iBAAO;AAAA;AAAA,gCAAOC,IAAP,CAAY,KAAKpB,IAAL,CAAUqB,MAAtB,EAA8BH,UAA9B,EAA0CC,WAA1C,EAAuDG,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKrB,YAAL,GAAoB,KAAKG,SAAvC,CAAvD,CAAP;AACH,SA1CgD,CA4CjD;;;AACUO,QAAAA,eAAe,CAACH,OAAD,EAAmC;AACxD,eAAKN,WAAL,GAAmB,CAAnB;AACH;;AAESU,QAAAA,gBAAgB,CAACJ,OAAD,EAAmC;AACzD,kBAAQ,KAAKT,IAAL,CAAUwB,eAAlB;AAEI,iBAAK;AAAA;AAAA,sDAAiBC,QAAtB;AACI,mBAAKrB,YAAL,GAAoB,KAAKJ,IAAL,CAAUmB,WAAV,CAAsBR,IAAtB,KAA+B,KAAKR,WAAxD;AACA;;AACJ;AACI,mBAAKC,YAAL,GAAoB,KAAKJ,IAAL,CAAUmB,WAAV,CAAsBR,IAAtB,EAApB;AACA;AAPR;AASH;;AAESK,QAAAA,eAAe,CAACP,OAAD,EAA6BiB,KAA7B,EAAkD,CACvE;AACH;;AA/DgD,O", "sourcesContent": ["\r\nimport { eTargetValueType, IEventActionData } from \"../../data/bullet/EventGroupData\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\nimport { Easing} from \"../Easing\";\r\n\r\nexport interface IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    isCompleted(): boolean;\r\n\r\n    onLoad(context: EventGroupContext): void;\r\n    onExecute(context: EventGroupContext, dt: number): void;\r\n\r\n    // onCancel? onComplete?\r\n}\r\n\r\nexport class EventActionBase implements IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    protected _isCompleted: boolean = false;\r\n    protected _elapsedTime: number = 0;\r\n    protected _startValue: number = 0;\r\n    protected _targetValue: number = 0;\r\n    protected _duration: number = 0;\r\n\r\n    constructor(data: IEventActionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n\r\n    canLerp(): boolean {\r\n        return true;\r\n    }\r\n\r\n    onLoad(context: EventGroupContext): void {\r\n        this._isCompleted = false;\r\n        this._elapsedTime = 0;\r\n        this._duration = this.data.duration.eval();\r\n        this.resetStartValue(context);\r\n        this.resetTargetValue(context);\r\n    }\r\n\r\n    onExecute(context: EventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        if (this._elapsedTime >= this._duration) {\r\n            this.executeInternal(context, this._targetValue);\r\n            this._isCompleted = true;\r\n        }\r\n        else if (this.canLerp()) {\r\n            this.executeInternal(context, this.lerpValue(this._startValue, this._targetValue));\r\n        }\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        return Easing.lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this._duration));\r\n    }\r\n\r\n    // override this to get the correct start value\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = 0;\r\n    }\r\n\r\n    protected resetTargetValue(context: EventGroupContext): void {\r\n        switch (this.data.targetValueType)\r\n        {\r\n            case eTargetValueType.Relative:\r\n                this._targetValue = this.data.targetValue.eval() + this._startValue;\r\n                break;\r\n            default:\r\n                this._targetValue = this.data.targetValue.eval();\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // Default implementation does nothing\r\n    }\r\n}\r\n"]}