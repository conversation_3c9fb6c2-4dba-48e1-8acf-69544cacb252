{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts"], "names": ["_decorator", "ccclass", "UIAnimMethods", "fromTo", "from", "to", "frameToTime"], "mappings": ";;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcD,U;;yBAGCE,a,WADpBD,OAAO,CAAC,eAAD,C,2BAAR,MACqBC,aADrB,CACmC;AACF;;AAE7B;AACJ;AACA;AACA;AACA;AACA;AACiB,eAANC,MAAM,CAACC,IAAD,EAAcC,EAAd,EAAyB;AAClC,iBAAOA,EAAE,GAAGD,IAAL,GAAY,CAAZ,GAAgB,CAACC,EAAE,GAAGD,IAAN,IAAc,KAAKE,WAA1C;AACH;;AAX8B,O,UACxBA,W,GAAc,IAAI,E", "sourcesContent": ["import { _decorator, Sprite<PERSON><PERSON><PERSON>, tween, Node} from 'cc';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('UIAnimMethods')\r\nexport default class UIAnimMethods {\r\n    static frameToTime = 1 / 30; // 帧数转换为时间\r\n\r\n    /**\r\n     * 根据帧数计算时间\r\n     * @param {number} from 起始帧\r\n     * @param {number} to 结束帧\r\n     * @returns {number} 时间（秒）\r\n     */\r\n    static fromTo(from:number, to:number) {\r\n        return to < from ? 0 : (to - from) * this.frameToTime;\r\n    }\r\n}"]}