import { ResEquip } from 'db://assets/scripts/autogen/luban/schema';
import csproto from 'db://assets/scripts/autogen/pb/cs_proto.js';
import { MyApp } from 'db://assets/scripts/MyApp';
import Long from 'long';


export class EquipCombine {
    private _onceCombines: csproto.cs.ICSItem[] = [];
    private _onceCombineEquipConfig: ResEquip | null = null;
    private _allPossibleCombineEquipConfigs: ResEquip[] = []; //所有可能的装备合成配置(不考虑材料数量)
    private _allCombineFixedResults: csproto.cs.ICSEquipCombineOne[] = []; //所有可能的装备合成配置(考虑材料数量)

    private _matType2ListMap: Map<number, number[]> = new Map();

    //构建特殊逻辑数据，前6位数字表示某类装备,全量读取装备配置，建立装备类型到装备id的映射
    init() {
        const tbEquip = MyApp.lubanTables.TbResEquip
        tbEquip.getDataList().forEach(v => {
            const numStr = v.id.toString();
            const firstSixStr = Number(numStr.slice(0, 6));
            let list = this._matType2ListMap.get(firstSixStr)
            if (!list) {
                list = []
                this._matType2ListMap.set(firstSixStr, list)
            }
            list.push(v.id)
        })
    }

    getCombineItemsByMatType(matType: number) {
        return this._matType2ListMap.get(matType)
    }

    isSameMatType(typeItemID: number, itemID: number): boolean {
        if (typeItemID == itemID) {
            return true
        }
        return typeItemID == Number(itemID.toString().slice(0, 6));
    }

    reset(num: number = 0) {
        this._onceCombineEquipConfig = null;
        this._onceCombines = [];
        for (let i = 0; i < num; i++) {
            this._onceCombines.push(<csproto.cs.ICSItem>{ item_id: 0, count: 0, guid: Long.fromNumber(0) })
        }
    }

    isFull(): boolean {
        if (this._onceCombineEquipConfig == null) {
            return false
        }
        const consumeNum = this._onceCombineEquipConfig.consumeItems.reduce((pre, cur) => {
            return pre + cur.num
        }, 0)
        const num = this._onceCombines.reduce((pre, cur) => {
            if (!cur.guid!.eq(0)) {
                return pre + 1
            }
            return pre
        }, 0)
        return consumeNum == num
    }

    add(item: csproto.cs.ICSItem): boolean {
        if (this.isFull()) return false;
        const mainMat = this.getByPos(0)
        if (!mainMat) {
            const cfg = this._allPossibleCombineEquipConfigs.find(v => this.isSameMatType(v.consumeItems[0]?.id, item.item_id!))
            if (!cfg) {
                return false
            }
            this.reset(cfg.consumeItems.reduce((pre, cur) => {
                return pre + cur.num
            }, 0))
            this._onceCombineEquipConfig = cfg
            this._onceCombines[0] = <csproto.cs.ICSItem>{
                item_id: item.item_id,
                count: 1,
                guid: item.guid,
            }
        } else {
            if (!this._onceCombineEquipConfig) {
                return false
            }
            if (this._onceCombineEquipConfig.consumeItems.findIndex(v => this.isSameMatType(v.id, item.item_id!)) < 0) {
                return false
            }
            const idx = this._onceCombines.findIndex(v => v.guid!.eq(0))
            if (idx < 0) {
                return false
            }
            this._onceCombines[idx] = <csproto.cs.ICSItem>{
                item_id: item.item_id,
                count: 1,
                guid: item.guid,
            }
        }
        return true
    }

    size(): number {
        return this._onceCombines.length
    }

    currentNum(): number {
        return this._onceCombines.reduce((pre, cur) => {
            if (!cur.guid!.eq(0)) {
                return pre + 1
            }
            return pre
        }, 0)
    }

    deleteByPos(pos: number) {
        if (pos == 0) {
            this.reset();
        } else {
            this._onceCombines[pos] = <csproto.cs.ICSItem>{ item_id: 0, count: 0, guid: Long.fromNumber(0) }
        }
    }

    getByGuid(guid: Long): { pos: number, item: csproto.cs.ICSItem } | null {
        const pos = this._onceCombines.findIndex(v => v.guid!.eq(guid))
        if (pos < 0) {
            return null
        }
        return { pos, item: this._onceCombines[pos] }
    }

    getByItemID(itemID: number): { pos: number, item: csproto.cs.ICSItem } | null {
        const pos = this._onceCombines.findIndex(v => v.item_id == itemID)
        if (pos < 0) {
            return null
        }
        return { pos, item: this._onceCombines[pos] }
    }

    getByPos(pos: number): { pos: number, item: csproto.cs.ICSItem } | null {
        const info = this._onceCombines[pos]
        if (!info || info.guid!.eq(0)) {
            return null
        }
        return { pos, item: info }
    }

    isCanCombine(item: csproto.cs.ICSItem): boolean {
        if (this._onceCombineEquipConfig == null) {
            if (this._allPossibleCombineEquipConfigs.length == 0) {
                return false
            }
            return this._allPossibleCombineEquipConfigs.filter(v => v.consumeItems.some(v => this.isSameMatType(v.id, item.item_id!))) != null
        }
        return this._onceCombineEquipConfig.consumeItems.some(v => this.isSameMatType(v.id, item.item_id!))
    }

    getCombineResult(): ResEquip | null {
        return this._onceCombineEquipConfig
    }

    combine(): boolean {
        const newEquip = this.getCombineResult()
        if (!newEquip) {
            return false
        }
        const materials = <csproto.cs.ICSEquipCombineMaterial[]>[]
        this._onceCombines.forEach(v => {
            materials.push({
                id: v.item_id,
                num: v.count,
                guid: v.guid
            })
        })
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {
            equip_combine: {
                combines: [{
                    equip_id: newEquip.id,
                    num: 1,
                    materials: materials
                }]
            }
        })
        return true
    }

    combineAll(): boolean {
        if (this._allCombineFixedResults.length == 0) {
            return false
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {
            equip_combine: { combines: this._allCombineFixedResults }
        })
        return true
    }

    /**
     * 计算装备合成所有可能组合
     * @param items 装备和材料道具集合
     * @return 所有可能的装备合成配置
     */
    calculateAllCombinePossible(items: csproto.cs.ICSItem[]): ResEquip[] {
        this._allPossibleCombineEquipConfigs = []
        const itemCountMap = new Map<number, number>();
        items.forEach(v => {
            let count = itemCountMap.get(v.item_id!) || 0
            itemCountMap.set(v.item_id!, count + v.count!)
            //特殊逻辑通用配置材料id
            const firstSixStr = Number(v.item_id!.toString().slice(0, 6));
            let matTypeCount = itemCountMap.get(firstSixStr) || 0
            itemCountMap.set(firstSixStr, matTypeCount + v.count!)
        })
        const combineEquipConfigs = MyApp.lubanTables.TbResEquip.getDataList().filter(v => v.consumeItems.length > 0);
        combineEquipConfigs.forEach(equipConfig => {
            if (equipConfig.consumeItems.length == 0) return;
            const mainMaterial = equipConfig.consumeItems[0];
            const subMaterials = equipConfig.consumeItems.slice(1);
            const count = itemCountMap.get(mainMaterial.id) ?? 0;
            if (count < mainMaterial.num) {
                return
            }
            for (let index = 0; index < subMaterials.length; index++) {
                const subMat = subMaterials[index];
                const subMatCount = itemCountMap.get(subMat.id) ?? 0;
                if (subMatCount < subMat.num) {
                    return
                }
            }
            this._allPossibleCombineEquipConfigs.push(equipConfig)
        });
        this.batchCombinePreprocessing(items)
        return this._allPossibleCombineEquipConfigs
    }

    /**
     * 装备批量合成预处理方法
     * 功能：检查背包材料是否足够合成多件相同装备，收集合成信息
     * 规则：
     * 1. 主材料不存在不记录缺失，只跳过该装备
     * 2. 副材料不够要记录缺失
     * 3. 不同主材料的装备共享副材料，如果副材料不够分配，整个批量合成失败
     */
    private batchCombinePreprocessing(items: csproto.cs.ICSItem[]) {
        this._allCombineFixedResults = []
        const singleMatEquipConfigs = this._allPossibleCombineEquipConfigs.filter(v => v.consumeItems.length == 1)
        let itemMap: Map<number, csproto.cs.ICSItem[]> = new Map()
        items.forEach(e => {
            let items = itemMap.get(e.item_id!) ?? []
            items.push({ ...e })
            itemMap.set(e.item_id!, items)
        })
        let isAllMatEnough = true
        let combines: csproto.cs.ICSEquipCombineOne[] = []
        singleMatEquipConfigs.forEach(cfg => {
            if (!isAllMatEnough) {
                return
            }
            const mat = cfg.consumeItems[0]
            const itemArray = itemMap.get(mat.id) ?? []
            if (itemArray.length == 0) {
                isAllMatEnough = false;
                return
            }
            let remaining = 0
            while (remaining == 0) {
                remaining = mat.num
                let combineInfo: csproto.cs.ICSEquipCombineOne = {
                    equip_id: cfg.id,
                    num: 1,
                    materials: []
                }

                for (const item of itemArray) {
                    if (remaining <= 0) {
                        break;
                    }
                    // 跳过数量为0的元素
                    if (item.count! <= 0) {
                        continue;
                    }
                    const takeAmount = Math.min(item.count!, remaining);
                    if (item.count! < takeAmount) {
                        continue;
                    }
                    combineInfo.materials!.push({
                        id: item.item_id,
                        num: takeAmount,
                        guid: item.guid
                    })
                    remaining -= takeAmount;
                    item.count! -= takeAmount
                }
                if (remaining == 0) {
                    combines.push(combineInfo)
                }
            }
        })
        if (!isAllMatEnough) {
            return
        }
        this._allCombineFixedResults = combines
    }
}