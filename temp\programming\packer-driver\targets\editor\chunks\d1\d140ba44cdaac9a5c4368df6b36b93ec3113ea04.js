System.register([], function (_export, _context) {
  "use strict";

  var Easing, _crd, eEasing;

  _export("Easing", void 0);

  return {
    setters: [],
    execute: function () {
      _crd = true;

      _export("eEasing", eEasing = /*#__PURE__*/function (eEasing) {
        eEasing[eEasing["Linear"] = 0] = "Linear";
        eEasing[eEasing["InSine"] = 1] = "InSine";
        eEasing[eEasing["OutSine"] = 2] = "OutSine";
        eEasing[eEasing["InOutSine"] = 3] = "InOutSine";
        eEasing[eEasing["InQuad"] = 4] = "InQuad";
        eEasing[eEasing["OutQuad"] = 5] = "OutQuad";
        eEasing[eEasing["InOutQuad"] = 6] = "InOutQuad";
        return eEasing;
      }({}));

      _export("Easing", Easing = class Easing {
        static lerp(easing, start, end, t) {
          switch (easing) {
            case eEasing.Linear:
              return start + (end - start) * t;

            case eEasing.InSine:
              return start + (end - start) * (1 - Math.cos(t * Math.PI / 2));

            case eEasing.OutSine:
              return start + (end - start) * Math.sin(t * Math.PI / 2);

            case eEasing.InOutSine:
              return start + (end - start) * (t < 0.5 ? (1 - Math.cos(t * Math.PI)) / 2 : (1 + Math.sin(t * Math.PI)) / 2);

            case eEasing.InQuad:
              return start + (end - start) * t * t;

            case eEasing.OutQuad:
              return start + (end - start) * (1 - (1 - t) * (1 - t));

            case eEasing.InOutQuad:
              return start + (end - start) * (t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2);

            default:
              return start + (end - start) * t;
          }
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=d140ba44cdaac9a5c4368df6b36b93ec3113ea04.js.map