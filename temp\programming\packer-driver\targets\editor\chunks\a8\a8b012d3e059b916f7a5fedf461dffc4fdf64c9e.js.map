{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/BossData.ts"], "names": ["BossBaseData", "BossData", "BossAttackPointData", "BossAttackActionData", "v2", "Tools", "TrackGroup", "id", "atlas", "exp", "collideArr", "attack", "collide<PERSON><PERSON><PERSON>", "transformAudio", "blastParam", "blastShake", "<PERSON><PERSON><PERSON><PERSON>", "onlyLoot", "lootArr", "lootParam0", "lootParam1", "loadJson", "data", "hasOwnProperty", "parseInt", "split", "ta", "cs", "csArray", "stringToNumber", "bla", "blaArray", "skArray", "sk", "push", "stringToPoint", "atk", "col", "app", "fl", "loot", "lp0", "lp1", "subId", "units", "unitsOrder", "hpParam", "blastType", "bombHurt", "leave", "nextBoss", "wayPointXs", "wayPointYs", "wayPointIntervals", "speeds", "attackIntervals", "snakePara<PERSON>", "trackGroups", "attackActions", "attackPoints", "dieFall<PERSON>elay", "blastCount", "va", "dashTrack", "freeTrackArr", "enemyId", "enemyRotate", "enemyPos", "enemyTrackGroup1", "enemyTrackGroup2", "bId", "sId", "us", "rid", "uaArray", "ua", "hpp", "Number", "bh", "dhA<PERSON>y", "dh", "ea", "way", "wayA<PERSON>y", "point", "x", "y", "wi", "sp", "ai", "ra", "raArray", "i", "length", "attackAction", "attackPointIndex", "attackPointKey", "attackPointData", "blp", "blpArray", "ft", "eid", "erotate", "eposArray", "epos", "position", "etrack1Array", "etrack1", "trackGroup", "etrack2Array", "etrack2", "bAvailable", "atkType", "atkUnitId", "atkAnim", "shootInterval", "bulletIDs", "bulletNums", "bulletIntervals", "bulletAttackRates", "attackOverDelay", "waveIds", "parts", "error", "animParts", "anim", "attackParts", "attackData", "waveParts", "wavePosition", "bAtkMove", "atkActId", "atkPointId"], "mappings": ";;;2FAOaA,Y,EA4DAC,Q,EAoNAC,mB,EA+FAC,oB;;;;;;;;;;;;;;;;;;;;;;;AAtXUC,MAAAA,E,OAAAA,E;;AACdC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAET;AACA;AACA;8BACaN,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACtBO,EADsB,GACT,CADS;AAAA,eAEtBC,KAFsB,GAEJ,EAFI;AAAA,eAGtBC,GAHsB,GAGR,CAHQ;AAAA,eAItBC,UAJsB,GAIC,EAJD;AAAA,eAKtBC,MALsB,GAKL,CALK;AAAA,eAMtBC,aANsB,GAME,CANF;AAAA,eAOtBC,cAPsB,GAOG,EAPH;AAAA,eAQtBC,UARsB,GAQC,EARD;AAAA,eAStBC,UATsB,GASD,EATC;AAAA,eAUtBC,WAVsB,GAUE,EAVF;AAAA,eAWtBC,QAXsB,GAWD,EAXC;AAAA,eAYtBC,OAZsB,GAYF,EAZE;AAAA,eAatBC,UAbsB,GAaC,EAbD;AAAA,eActBC,UAdsB,GAcC,EAdD;AAAA;;AAgBtB;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACf,EAAN,CAAlB;AAC/B,cAAIe,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC,KAAKf,KAAL,GAAac,IAAI,CAACd,KAAL,CAAWiB,KAAX,CAAiB,GAAjB,CAAb;AAClC,cAAIH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKd,GAAL,GAAWe,QAAQ,CAACF,IAAI,CAACb,GAAN,CAAnB;AAChC,cAAIa,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKV,cAAL,GAAsBS,IAAI,CAACI,EAA3B;;AAE/B,cAAIJ,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACK,EAAL,KAAY,EAA7C,EAAiD;AAC7C,kBAAMC,OAAO,GAAGN,IAAI,CAACK,EAAL,CAAQF,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAME,EAAX,IAAiBC,OAAjB,EAA0B;AACtB,kBAAID,EAAE,KAAK,EAAX,EAAe,KAAKjB,UAAL,GAAkB;AAAA;AAAA,kCAAMmB,cAAN,CAAqBF,EAArB,EAAyB,GAAzB,CAAlB;AAClB;AACJ;;AAED,cAAIL,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACQ,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAGT,IAAI,CAACQ,GAAL,CAASL,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMK,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB,KAAKhB,UAAL,GAAkB;AAAA;AAAA,kCAAMe,cAAN,CAAqBC,GAArB,EAA0B,GAA1B,CAAlB;AACnB;AACJ;;AAED,cAAIR,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMS,OAAO,GAAGV,IAAI,CAACW,EAAL,CAAQR,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMQ,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAKlB,UAAL,CAAgBmB,IAAhB,CAAqB;AAAA;AAAA,kCAAMC,aAAN,CAAoBF,EAApB,EAAwB,GAAxB,CAArB;AAClB;AACJ;;AAED,cAAIX,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKZ,MAAL,GAAca,QAAQ,CAACF,IAAI,CAACc,GAAN,CAAtB;AAChC,cAAId,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKX,aAAL,GAAqBY,QAAQ,CAACF,IAAI,CAACe,GAAN,CAA7B;AAChC,cAAIf,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKP,WAAL,GAAmB;AAAA;AAAA,8BAAMa,cAAN,CAAqBP,IAAI,CAACgB,GAA1B,EAA+B,GAA/B,CAAnB;AAChC,cAAIhB,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACiB,EAAL,KAAY,EAA7C,EAAiD,KAAKtB,QAAL,GAAgB;AAAA;AAAA,8BAAMY,cAAN,CAAqBP,IAAI,CAACiB,EAA1B,EAA8B,GAA9B,CAAhB;AACjD,cAAIjB,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC,KAAKL,OAAL,GAAe;AAAA;AAAA,8BAAMW,cAAN,CAAqBP,IAAI,CAACkB,IAA1B,EAAgC,GAAhC,CAAf;AACjC,cAAIlB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKJ,UAAL,GAAkB;AAAA;AAAA,8BAAMU,cAAN,CAAqBP,IAAI,CAACmB,GAA1B,EAA+B,GAA/B,CAAlB;AAChC,cAAInB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKH,UAAL,GAAkB;AAAA;AAAA,8BAAMS,cAAN,CAAqBP,IAAI,CAACoB,GAA1B,EAA+B,GAA/B,CAAlB;AACnC;;AAtDqB,O;AAyD1B;AACA;AACA;;;0BACazC,Q,GAAN,MAAMA,QAAN,SAAuBD,YAAvB,CAAoC;AAAA;AAAA;AAAA,eACvC2C,KADuC,GACvB,CADuB;AAAA,eAEvCC,KAFuC,GAErB,EAFqB;AAAA,eAGvCC,UAHuC,GAGhB,EAHgB;AAAA,eAIvCC,OAJuC,GAInB,EAJmB;AAAA,eAKvC9B,WALuC,GAKf,EALe;AAAA,eAMvCH,cANuC,GAMd,EANc;AAAA,eAOvCkC,SAPuC,GAOnB,CAPmB;AAAA,eAQvCC,QARuC,GAQpB,CARoB;AAAA,eASvCC,KATuC,GASvB,CATuB;AAAA,eAUvCC,QAVuC,GAUlB,EAVkB;AAAA,eAWvCC,UAXuC,GAWhB,EAXgB;AAAA,eAYvCC,UAZuC,GAYhB,EAZgB;AAAA,eAavCC,iBAbuC,GAaT,EAbS;AAAA,eAcvCC,MAduC,GAcpB,EAdoB;AAAA,eAevCC,eAfuC,GAeX,EAfW;AAAA,eAgBvCC,UAhBuC,GAgBnB,EAhBmB;AAAA,eAiBvCC,WAjBuC,GAiBX,EAjBW;AAAA,eAkBvCC,aAlBuC,GAkBhB,EAlBgB;AAAA,eAmBvCC,YAnBuC,GAmBjB,EAnBiB;AAAA,eAoBvCC,YApBuC,GAoBhB,CApBgB;AAAA,eAqBvCC,UArBuC,GAqBlB,CArBkB;AAAA,eAsBvCC,EAtBuC,GAsBxB,EAtBwB;AAAA,eAuBvCC,SAvBuC,GAuBjB,EAvBiB;AAAA,eAwBvCC,YAxBuC,GAwBd,EAxBc;AAAA,eAyBvCC,OAzBuC,GAyBrB,CAzBqB;AAAA,eA0BvCC,WA1BuC,GA0BjB,CA1BiB;AAAA,eA2BvCC,QA3BuC,GA2BpB,EA3BoB;AAAA,eA4BvCC,gBA5BuC,GA4BN,EA5BM;AAAA,eA6BvCC,gBA7BuC,GA6BN,EA7BM;AAAA;;AA+BvC;AACJ;AACA;AACA;AACIhD,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACgD,GAAN,CAAlB;AAChC,cAAIhD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKoB,KAAL,GAAanB,QAAQ,CAACF,IAAI,CAACiD,GAAN,CAArB;AAChC,cAAIjD,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKqB,KAAL,GAAa;AAAA;AAAA,8BAAMf,cAAN,CAAqBP,IAAI,CAACkD,EAA1B,EAA8B,GAA9B,CAAb;AAC/B,cAAIlD,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACmD,GAAL,KAAa,EAA/C,EAAmD,KAAKvB,QAAL,GAAgB;AAAA;AAAA,8BAAMrB,cAAN,CAAqBP,IAAI,CAACmD,GAA1B,EAA+B,GAA/B,CAAhB;AACnD,cAAInD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKd,GAAL,GAAWe,QAAQ,CAACF,IAAI,CAACb,GAAN,CAAnB;AAChC,cAAIa,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC,KAAK0B,KAAL,GAAazB,QAAQ,CAACF,IAAI,CAAC2B,KAAN,CAArB;AAClC,cAAI3B,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKuC,EAAL,GAAU;AAAA;AAAA,8BAAMjC,cAAN,CAAqBP,IAAI,CAACwC,EAA1B,EAA8B,GAA9B,CAAV;;AAE/B,cAAIxC,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMmD,OAAO,GAAGpD,IAAI,CAACqD,EAAL,CAAQlD,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMkD,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAK9B,UAAL,GAAkB;AAAA;AAAA,kCAAMhB,cAAN,CAAqB8C,EAArB,EAAyB,GAAzB,CAAlB;AAClB;AACJ;;AAED,cAAIrD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKuB,OAAL,GAAe;AAAA;AAAA,8BAAMjB,cAAN,CAAqBP,IAAI,CAACsD,GAA1B,EAA+B,GAA/B,CAAf;AAChC,cAAItD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKP,WAAL,GAAmB;AAAA;AAAA,8BAAMa,cAAN,CAAqBP,IAAI,CAACgB,GAA1B,EAA+B,GAA/B,CAAnB;AAChC,cAAIhB,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKV,cAAL,GAAsBS,IAAI,CAACI,EAA3B;AAC/B,cAAIJ,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKwB,SAAL,GAAiB8B,MAAM,CAACvD,IAAI,CAACQ,GAAN,CAAvB;AAChC,cAAIR,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKyB,QAAL,GAAgB6B,MAAM,CAACvD,IAAI,CAACwD,EAAN,CAAtB;AAC/B,cAAIxD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKZ,MAAL,GAAca,QAAQ,CAACF,IAAI,CAACc,GAAN,CAAtB;AAChC,cAAId,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKX,aAAL,GAAqBY,QAAQ,CAACF,IAAI,CAACe,GAAN,CAA7B;;AAEhC,cAAIf,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMwD,OAAO,GAAGzD,IAAI,CAAC0D,EAAL,CAAQvD,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMuD,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAKjB,SAAL,GAAiB;AAAA;AAAA,kCAAMlC,cAAN,CAAqBmD,EAArB,EAAyB,GAAzB,CAAjB;AAClB;AACJ;;AAED,cAAI1D,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAAC2D,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKjB,YAAL,GAAoB;AAAA;AAAA,gCAAMnC,cAAN,CAAqBP,IAAI,CAAC2D,EAA1B,EAA8B,GAA9B,CAApB;AACH;;AAED,cAAI3D,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAAC4D,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAG7D,IAAI,CAAC4D,GAAL,CAASzD,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMyD,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB;AACZ,sBAAME,KAAK,GAAG;AAAA;AAAA,oCAAMjD,aAAN,CAAoB+C,GAApB,EAAyB,GAAzB,CAAd;AACA,qBAAK/B,UAAL,CAAgBjB,IAAhB,CAAqBkD,KAAK,CAACC,CAA3B;AACA,qBAAKjC,UAAL,CAAgBlB,IAAhB,CAAqBkD,KAAK,CAACE,CAA3B;AACH;AACJ;AACJ;;AAED,cAAIhE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACiE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKlC,iBAAL,GAAyB;AAAA;AAAA,gCAAMxB,cAAN,CAAqBP,IAAI,CAACiE,EAA1B,EAA8B,GAA9B,CAAzB;AACH;;AAED,cAAIjE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACkE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKlC,MAAL,GAAc;AAAA;AAAA,gCAAMzB,cAAN,CAAqBP,IAAI,CAACkE,EAA1B,EAA8B,GAA9B,CAAd;AACH;;AAED,cAAIlE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACmE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKlC,eAAL,GAAuB;AAAA;AAAA,gCAAM1B,cAAN,CAAqBP,IAAI,CAACmE,EAA1B,EAA8B,GAA9B,CAAvB;AACH;;AAED,cAAInE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACoE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,kBAAMC,OAAO,GAAGrE,IAAI,CAACoE,EAAL,CAAQjE,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,OAAO,CAACE,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,kBAAID,OAAO,CAACC,CAAD,CAAP,KAAe,EAAnB,EAAuB;AACnB,sBAAME,YAAY,GAAG,IAAI3F,oBAAJ,EAArB;AACA2F,gBAAAA,YAAY,CAACzE,QAAb,CAAsBsE,OAAO,CAACC,CAAD,CAA7B;AACA,qBAAKlC,aAAL,CAAmBxB,IAAnB,CAAwB4D,YAAxB;AACH;AACJ;AACJ,WAnEqB,CAqEtB;;;AACA,cAAIC,gBAAgB,GAAG,CAAvB;;AACA,iBAAO,IAAP,EAAa;AACT,kBAAMC,cAAc,GAAG,MAAMD,gBAAgB,EAA7C;AACA,gBAAI,CAACzE,IAAI,CAACC,cAAL,CAAoByE,cAApB,CAAD,IAAwC1E,IAAI,CAAC0E,cAAD,CAAJ,KAAyB,EAArE,EAAyE;AAEzE,kBAAMC,eAAe,GAAG,IAAI/F,mBAAJ,EAAxB;AACA+F,YAAAA,eAAe,CAAC5E,QAAhB,CAAyBC,IAAI,CAAC0E,cAAD,CAA7B;AACA,iBAAKrC,YAAL,CAAkBzB,IAAlB,CAAuB+D,eAAvB;AACH,WA9EqB,CAgFtB;;;AACA,cAAI3E,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAAC4E,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAG7E,IAAI,CAAC4E,GAAL,CAASzE,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,QAAQ,CAACN,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACtC,kBAAIO,QAAQ,CAACP,CAAD,CAAR,KAAgB,EAApB,EAAwB;AACpB,qBAAK9E,UAAL,GAAkB;AAAA;AAAA,oCAAMe,cAAN,CAAqBsE,QAAQ,CAACP,CAAD,CAA7B,EAAkC,GAAlC,CAAlB;AACA,qBAAK/B,UAAL;AACH;AACJ;AACJ,WAzFqB,CA2FtB;;;AACA,cAAIvC,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMS,OAAO,GAAGV,IAAI,CAACW,EAAL,CAAQR,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5D,OAAO,CAAC6D,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,kBAAI5D,OAAO,CAAC4D,CAAD,CAAP,KAAe,EAAnB,EAAuB;AACnB,qBAAK7E,UAAL,CAAgBmB,IAAhB,CAAqB;AAAA;AAAA,oCAAMC,aAAN,CAAoBH,OAAO,CAAC4D,CAAD,CAA3B,EAAgC,GAAhC,CAArB;AACH;AACJ;AACJ,WAnGqB,CAqGtB;;;AACA,cAAItE,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,iBAAKqC,YAAL,GAAoBiB,MAAM,CAACvD,IAAI,CAAC8E,EAAN,CAA1B;AACH,WAxGqB,CA0GtB;;;AACA,cAAI9E,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACiB,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKtB,QAAL,GAAgB;AAAA;AAAA,gCAAMY,cAAN,CAAqBP,IAAI,CAACiB,EAA1B,EAA8B,GAA9B,CAAhB;AACH,WA7GqB,CA+GtB;;;AACA,cAAIjB,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,iBAAKL,OAAL,GAAe;AAAA;AAAA,gCAAMW,cAAN,CAAqBP,IAAI,CAACkB,IAA1B,EAAgC,GAAhC,CAAf;AACH,WAlHqB,CAoHtB;;;AACA,cAAIlB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKJ,UAAL,GAAkB;AAAA;AAAA,gCAAMU,cAAN,CAAqBP,IAAI,CAACmB,GAA1B,EAA+B,GAA/B,CAAlB;AACH,WAvHqB,CAyHtB;;;AACA,cAAInB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKH,UAAL,GAAkB;AAAA;AAAA,gCAAMS,cAAN,CAAqBP,IAAI,CAACoB,GAA1B,EAA+B,GAA/B,CAAlB;AACH,WA5HqB,CA8HtB;;;AACA,cAAIpB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAK0C,OAAL,GAAeY,MAAM,CAACvD,IAAI,CAAC+E,GAAN,CAArB;AACH,WAjIqB,CAmItB;;;AACA,cAAI/E,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,iBAAK2C,WAAL,GAAmBW,MAAM,CAACvD,IAAI,CAACgF,OAAN,CAAzB;AACH,WAtIqB,CAwItB;;;AACA,cAAIhF,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,kBAAMgF,SAAS,GAAGjF,IAAI,CAACkF,IAAL,CAAU/E,KAAV,CAAgB,GAAhB,CAAlB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,SAAS,CAACV,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;AACvC,oBAAMa,QAAQ,GAAG;AAAA;AAAA,kCAAM5E,cAAN,CAAqB0E,SAAS,CAACX,CAAD,CAA9B,EAAmC,GAAnC,CAAjB;;AACA,kBAAIa,QAAQ,CAACZ,MAAT,KAAoB,CAAxB,EAA2B;AACvB,qBAAK1B,QAAL,CAAcjC,IAAd,CAAmB9B,EAAE,CAACqG,QAAQ,CAAC,CAAD,CAAT,EAAcA,QAAQ,CAAC,CAAD,CAAtB,CAArB;AACH;AACJ;AACJ,WAjJqB,CAmJtB;;;AACA,cAAInF,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,kBAAMmF,YAAY,GAAGpF,IAAI,CAACqF,OAAL,CAAalF,KAAb,CAAmB,GAAnB,CAArB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGc,YAAY,CAACb,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,kBAAIc,YAAY,CAACd,CAAD,CAAZ,KAAoB,EAApB,IAA0Bc,YAAY,CAACd,CAAD,CAAZ,CAAgBnE,KAAhB,CAAsB,GAAtB,EAA2BoE,MAA3B,GAAoC,CAAlE,EAAqE;AACjE,sBAAMe,UAAU,GAAG;AAAA;AAAA,+CAAnB;AACAA,gBAAAA,UAAU,CAACvF,QAAX,CAAoBqF,YAAY,CAACd,CAAD,CAAhC;AACA,qBAAKxB,gBAAL,CAAsBlC,IAAtB,CAA2B0E,UAA3B;AACH;AACJ;AACJ,WA7JqB,CA+JtB;;;AACA,cAAItF,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,kBAAMsF,YAAY,GAAGvF,IAAI,CAACwF,OAAL,CAAarF,KAAb,CAAmB,GAAnB,CAArB;;AACA,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,YAAY,CAAChB,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,kBAAIiB,YAAY,CAACjB,CAAD,CAAZ,KAAoB,EAApB,IAA0BiB,YAAY,CAACjB,CAAD,CAAZ,CAAgBnE,KAAhB,CAAsB,GAAtB,EAA2BoE,MAA3B,GAAoC,CAAlE,EAAqE;AACjE,sBAAMe,UAAU,GAAG;AAAA;AAAA,+CAAnB;AACAA,gBAAAA,UAAU,CAACvF,QAAX,CAAoBwF,YAAY,CAACjB,CAAD,CAAhC;AACA,qBAAKvB,gBAAL,CAAsBnC,IAAtB,CAA2B0E,UAA3B;AACH;AACJ;AACJ;AACJ;;AA7MsC,O;AAiN3C;AACA;AACA;;;qCACa1G,mB,GAAN,MAAMA,mBAAN,CAA0B;AAAA;AAAA,eAC7B6G,UAD6B,GACP,IADO;AAAA,eAE7BC,OAF6B,GAEX,CAFW;AAAA,eAG7BC,SAH6B,GAGT,CAHS;AAAA,eAI7BC,OAJ6B,GAIT,EAJS;AAAA,eAK7B7B,CAL6B,GAKjB,CALiB;AAAA,eAM7BC,CAN6B,GAMjB,CANiB;AAAA,eAO7B6B,aAP6B,GAOH,EAPG;AAAA,eAQ7BC,SAR6B,GAQP,EARO;AAAA,eAS7BC,UAT6B,GASN,EATM;AAAA,eAU7BC,eAV6B,GAUD,EAVC;AAAA,eAW7BC,iBAX6B,GAWC,EAXD;AAAA,eAY7BC,eAZ6B,GAYD,EAZC;AAAA,eAa7BC,OAb6B,GAaT,EAbS;AAAA;;AAe7B;AACJ;AACA;AACA;AACIpG,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAMoG,KAAK,GAAGpG,IAAI,CAACG,KAAL,CAAW,GAAX,CAAd;;AACA,cAAIiG,KAAK,CAAC7B,MAAN,GAAe,CAAnB,EAAsB;AAClB;AAAA;AAAA,gCAAM8B,KAAN,CAAY,4BAAZ,EAA0CrG,IAA1C;AACA;AACH;;AAED,eAAK0F,OAAL,GAAexF,QAAQ,CAACkG,KAAK,CAAC,CAAD,CAAN,CAAvB;AACA,eAAKT,SAAL,GAAiBzF,QAAQ,CAACkG,KAAK,CAAC,CAAD,CAAN,CAAzB;AAEA,gBAAME,SAAS,GAAGF,KAAK,CAAC,CAAD,CAAL,CAASjG,KAAT,CAAe,GAAf,CAAlB;;AACA,eAAK,MAAMoG,IAAX,IAAmBD,SAAnB,EAA8B;AAC1B,gBAAIC,IAAI,KAAK,EAAb,EAAiB;AACb,mBAAKX,OAAL,GAAe;AAAA;AAAA,kCAAMrF,cAAN,CAAqBgG,IAArB,EAA2B,GAA3B,CAAf;AACH;AACJ;;AAED,kBAAQ,KAAKb,OAAb;AACI,iBAAK,CAAL;AAAQ;AACJ,oBAAMc,WAAW,GAAGJ,KAAK,CAAC,CAAD,CAAL,CAASjG,KAAT,CAAe,GAAf,CAApB;;AACA,kBAAI;AACA,oBAAIqG,WAAW,CAACjC,MAAZ,IAAsB,CAA1B,EAA6B;AACzB,uBAAKkB,UAAL,GAAkB,KAAlB;AACA;AACH;;AAED,sBAAMN,QAAQ,GAAG;AAAA;AAAA,oCAAMtE,aAAN,CAAoB2F,WAAW,CAAC,CAAD,CAA/B,EAAoC,GAApC,CAAjB;AACA,qBAAKzC,CAAL,GAASoB,QAAQ,CAACpB,CAAlB;AACA,qBAAKC,CAAL,GAASmB,QAAQ,CAACnB,CAAlB;;AAEA,qBAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkC,WAAW,CAACjC,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AACzC,sBAAIkC,WAAW,CAAClC,CAAD,CAAX,KAAmB,EAAvB,EAA2B;AACvB,0BAAMmC,UAAU,GAAG;AAAA;AAAA,wCAAMlG,cAAN,CAAqBiG,WAAW,CAAClC,CAAD,CAAhC,EAAqC,GAArC,CAAnB;AACA,yBAAKuB,aAAL,CAAmBjF,IAAnB,CAAwB6F,UAAU,CAAC,CAAD,CAAlC;AACA,yBAAKX,SAAL,CAAelF,IAAf,CAAoB6F,UAAU,CAAC,CAAD,CAA9B;AACA,yBAAKV,UAAL,CAAgBnF,IAAhB,CAAqB6F,UAAU,CAAC,CAAD,CAA/B;AACA,yBAAKT,eAAL,CAAqBpF,IAArB,CAA0B6F,UAAU,CAAC,CAAD,CAApC;AACA,yBAAKR,iBAAL,CAAuBrF,IAAvB,CAA4B6F,UAAU,CAAC,CAAD,CAAV,GAAgB,GAA5C;AACA,yBAAKP,eAAL,CAAqBtF,IAArB,CAA0B6F,UAAU,CAAC,CAAD,CAApC;AACH;AACJ;AACJ,eArBD,CAqBE,OAAOJ,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAMA,KAAN,CAAY,4BAAZ,EAA0CrG,IAA1C;AACH;;AACD;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,oBAAM0G,SAAS,GAAGN,KAAK,CAAC,CAAD,CAAL,CAASjG,KAAT,CAAe,GAAf,CAAlB;;AACA,kBAAI;AACA,oBAAIuG,SAAS,CAACnC,MAAV,IAAoB,CAAxB,EAA2B;AACvB,uBAAKkB,UAAL,GAAkB,KAAlB;AACA;AACH;;AAED,sBAAMkB,YAAY,GAAG;AAAA;AAAA,oCAAM9F,aAAN,CAAoB6F,SAAS,CAAC,CAAD,CAA7B,EAAkC,GAAlC,CAArB;AACA,qBAAK3C,CAAL,GAAS4C,YAAY,CAAC5C,CAAtB;AACA,qBAAKC,CAAL,GAAS2C,YAAY,CAAC3C,CAAtB;AAEA,qBAAKmC,OAAL,GAAe;AAAA;AAAA,oCAAM5F,cAAN,CAAqBmG,SAAS,CAAC,CAAD,CAA9B,EAAmC,GAAnC,CAAf;;AACA,oBAAIA,SAAS,CAACnC,MAAV,GAAmB,CAAvB,EAA0B;AACtB,uBAAK2B,eAAL,CAAqBtF,IAArB,CAA0BV,QAAQ,CAACwG,SAAS,CAAC,CAAD,CAAV,CAAlC;AACH;AACJ,eAdD,CAcE,OAAOL,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAMA,KAAN,CAAY,4BAAZ,EAA0CrG,IAA1C;AACH;;AACD;;AAEJ;AACI;AAAA;AAAA,kCAAMqG,KAAN,CAAY,sBAAZ,EAAoC,KAAKX,OAAzC;AACA;AApDR;AAsDH;;AA1F4B,O;AA4FjC;AACA;AACA;;;sCACa7G,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eAC9B+H,QAD8B,GACV,KADU;AACH;AADG,eAE9BC,QAF8B,GAEX,CAFW;AAER;AAFQ,eAG9BC,UAH8B,GAGP,EAHO;AAAA;;AAGH;;AAE3B;AACJ;AACA;AACA;AACI/G,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAMoG,KAAK,GAAG;AAAA;AAAA,8BAAM7F,cAAN,CAAqBP,IAArB,EAA2B,GAA3B,CAAd;;AACA,cAAI;AACA,gBAAIoG,KAAK,CAAC7B,MAAN,GAAe,CAAnB,EAAsB;AAClB,mBAAKqC,QAAL,GAAgBR,KAAK,CAAC,CAAD,CAAL,KAAa,CAA7B;AACA,mBAAKS,QAAL,GAAgBT,KAAK,CAAC,CAAD,CAArB;;AACA,mBAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAAK,CAAC7B,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,qBAAKwC,UAAL,CAAgBlG,IAAhB,CAAqBwF,KAAK,CAAC9B,CAAD,CAA1B;AACH;AACJ;AACJ,WARD,CAQE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAY,6BAAZ,EAA2CrG,IAA3C;AACH;AACJ;;AAtB6B,O", "sourcesContent": ["import { color, Color, v2, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { TrackGroup } from \"./EnemyWave\";\r\n\r\n/**\r\n * Boss 基础数据类\r\n */\r\nexport class BossBaseData {\r\n    id: number = 0;\r\n    atlas: string[] = [];\r\n    exp: number = 0;\r\n    collideArr: number[] = [];\r\n    attack: number = 0;\r\n    collideAttack: number = 0;\r\n    transformAudio: string = \"\";\r\n    blastParam: number[] = [];\r\n    blastShake: Vec2[] = [];\r\n    appearParam: number[] = [];\r\n    onlyLoot: number[] = [];\r\n    lootArr: number[] = [];\r\n    lootParam0: number[] = [];\r\n    lootParam1: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载 Boss 基础数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"id\")) this.id = parseInt(data.id);\r\n        if (data.hasOwnProperty(\"atlas\")) this.atlas = data.atlas.split(\";\");\r\n        if (data.hasOwnProperty(\"exp\")) this.exp = parseInt(data.exp);\r\n        if (data.hasOwnProperty(\"ta\")) this.transformAudio = data.ta;\r\n\r\n        if (data.hasOwnProperty(\"cs\") && data.cs !== \"\") {\r\n            const csArray = data.cs.split(\";\");\r\n            for (const cs of csArray) {\r\n                if (cs !== \"\") this.collideArr = Tools.stringToNumber(cs, \",\");\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"bla\") && data.bla !== \"\") {\r\n            const blaArray = data.bla.split(\";\");\r\n            for (const bla of blaArray) {\r\n                if (bla !== \"\") this.blastParam = Tools.stringToNumber(bla, \",\");\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"sk\")) {\r\n            const skArray = data.sk.split(\";\");\r\n            for (const sk of skArray) {\r\n                if (sk !== \"\") this.blastShake.push(Tools.stringToPoint(sk, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"atk\")) this.attack = parseInt(data.atk);\r\n        if (data.hasOwnProperty(\"col\")) this.collideAttack = parseInt(data.col);\r\n        if (data.hasOwnProperty(\"app\")) this.appearParam = Tools.stringToNumber(data.app, \",\");\r\n        if (data.hasOwnProperty(\"fl\") && data.fl !== \"\") this.onlyLoot = Tools.stringToNumber(data.fl, \",\");\r\n        if (data.hasOwnProperty(\"loot\")) this.lootArr = Tools.stringToNumber(data.loot, \",\");\r\n        if (data.hasOwnProperty(\"lp0\")) this.lootParam0 = Tools.stringToNumber(data.lp0, \",\");\r\n        if (data.hasOwnProperty(\"lp1\")) this.lootParam1 = Tools.stringToNumber(data.lp1, \",\");\r\n    }\r\n}\r\n\r\n/**\r\n * Boss 数据类\r\n */\r\nexport class BossData extends BossBaseData {\r\n    subId: number = 0;\r\n    units: number[] = [];\r\n    unitsOrder: number[] = [];\r\n    hpParam: number[] = [];\r\n    appearParam: number[] = [];\r\n    transformAudio: string = \"\";\r\n    blastType: number = 0;\r\n    bombHurt: number = 0;\r\n    leave: number = 0;\r\n    nextBoss: number[] = [];\r\n    wayPointXs: number[] = [];\r\n    wayPointYs: number[] = [];\r\n    wayPointIntervals: number[] = [];\r\n    speeds: number[] = [];\r\n    attackIntervals: number[] = [];\r\n    snakeParam: any[] = [];\r\n    trackGroups: TrackGroup[] = [];\r\n    attackActions: any[] = [];\r\n    attackPoints: any[] = [];\r\n    dieFallDelay: number = 0;\r\n    blastCount: number = 0;\r\n    va: number[] = [];\r\n    dashTrack: number[] = [];\r\n    freeTrackArr: number[] = [];\r\n    enemyId: number = 0;\r\n    enemyRotate: number = 0;\r\n    enemyPos: Vec2[] = [];\r\n    enemyTrackGroup1: TrackGroup[] = [];\r\n    enemyTrackGroup2: TrackGroup[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载 Boss 数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"bId\")) this.id = parseInt(data.bId);\r\n        if (data.hasOwnProperty(\"sId\")) this.subId = parseInt(data.sId);\r\n        if (data.hasOwnProperty(\"us\")) this.units = Tools.stringToNumber(data.us, \",\");\r\n        if (data.hasOwnProperty(\"rid\") && data.rid !== \"\") this.nextBoss = Tools.stringToNumber(data.rid, \",\");\r\n        if (data.hasOwnProperty(\"exp\")) this.exp = parseInt(data.exp);\r\n        if (data.hasOwnProperty(\"leave\")) this.leave = parseInt(data.leave);\r\n        if (data.hasOwnProperty(\"va\")) this.va = Tools.stringToNumber(data.va, \",\");\r\n\r\n        if (data.hasOwnProperty(\"ua\")) {\r\n            const uaArray = data.ua.split(\";\");\r\n            for (const ua of uaArray) {\r\n                if (ua !== \"\") this.unitsOrder = Tools.stringToNumber(ua, \",\");\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"hpp\")) this.hpParam = Tools.stringToNumber(data.hpp, \",\");\r\n        if (data.hasOwnProperty(\"app\")) this.appearParam = Tools.stringToNumber(data.app, \",\");\r\n        if (data.hasOwnProperty(\"ta\")) this.transformAudio = data.ta;\r\n        if (data.hasOwnProperty(\"bla\")) this.blastType = Number(data.bla);\r\n        if (data.hasOwnProperty(\"bh\")) this.bombHurt = Number(data.bh);\r\n        if (data.hasOwnProperty(\"atk\")) this.attack = parseInt(data.atk);\r\n        if (data.hasOwnProperty(\"col\")) this.collideAttack = parseInt(data.col);\r\n\r\n        if (data.hasOwnProperty(\"dh\")) {\r\n            const dhArray = data.dh.split(\";\");\r\n            for (const dh of dhArray) {\r\n                if (dh !== \"\") this.dashTrack = Tools.stringToNumber(dh, \",\");\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ea\") && data.ea !== \"\") {\r\n            this.freeTrackArr = Tools.stringToNumber(data.ea, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"way\") && data.way !== \"\") {\r\n            const wayArray = data.way.split(\";\");\r\n            for (const way of wayArray) {\r\n                if (way !== \"\") {\r\n                    const point = Tools.stringToPoint(way, \",\");\r\n                    this.wayPointXs.push(point.x);\r\n                    this.wayPointYs.push(point.y);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"wi\") && data.wi !== \"\") {\r\n            this.wayPointIntervals = Tools.stringToNumber(data.wi, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"sp\") && data.sp !== \"\") {\r\n            this.speeds = Tools.stringToNumber(data.sp, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ai\") && data.ai !== \"\") {\r\n            this.attackIntervals = Tools.stringToNumber(data.ai, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ra\") && data.ra !== \"\") {\r\n            const raArray = data.ra.split(\";\");\r\n            for (let i = 0; i < raArray.length; i++) {\r\n                if (raArray[i] !== \"\") {\r\n                    const attackAction = new BossAttackActionData();\r\n                    attackAction.loadJson(raArray[i]);\r\n                    this.attackActions.push(attackAction);\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析攻击点数据\r\n        let attackPointIndex = 0;\r\n        while (true) {\r\n            const attackPointKey = \"a\" + attackPointIndex++;\r\n            if (!data.hasOwnProperty(attackPointKey) || data[attackPointKey] === \"\") break;\r\n        \r\n            const attackPointData = new BossAttackPointData();\r\n            attackPointData.loadJson(data[attackPointKey]);\r\n            this.attackPoints.push(attackPointData);\r\n        }\r\n        \r\n        // 解析爆炸参数\r\n        if (data.hasOwnProperty(\"blp\") && data.blp !== \"\") {\r\n            const blpArray = data.blp.split(\";\");\r\n            for (let i = 0; i < blpArray.length; i++) {\r\n                if (blpArray[i] !== \"\") {\r\n                    this.blastParam = Tools.stringToNumber(blpArray[i], \",\");\r\n                    this.blastCount++;\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析爆炸震动参数\r\n        if (data.hasOwnProperty(\"sk\")) {\r\n            const skArray = data.sk.split(\";\");\r\n            for (let i = 0; i < skArray.length; i++) {\r\n                if (skArray[i] !== \"\") {\r\n                    this.blastShake.push(Tools.stringToPoint(skArray[i], \",\"));\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析死亡掉落延迟\r\n        if (data.hasOwnProperty(\"ft\")) {\r\n            this.dieFallDelay = Number(data.ft);\r\n        }\r\n        \r\n        // 解析唯一掉落\r\n        if (data.hasOwnProperty(\"fl\") && data.fl !== \"\") {\r\n            this.onlyLoot = Tools.stringToNumber(data.fl, \",\");\r\n        }\r\n        \r\n        // 解析掉落数组\r\n        if (data.hasOwnProperty(\"loot\")) {\r\n            this.lootArr = Tools.stringToNumber(data.loot, \",\");\r\n        }\r\n        \r\n        // 解析掉落参数 0\r\n        if (data.hasOwnProperty(\"lp0\")) {\r\n            this.lootParam0 = Tools.stringToNumber(data.lp0, \",\");\r\n        }\r\n        \r\n        // 解析掉落参数 1\r\n        if (data.hasOwnProperty(\"lp1\")) {\r\n            this.lootParam1 = Tools.stringToNumber(data.lp1, \",\");\r\n        }\r\n        \r\n        // 解析敌人 ID\r\n        if (data.hasOwnProperty(\"eid\")) {\r\n            this.enemyId = Number(data.eid);\r\n        }\r\n        \r\n        // 解析敌人旋转角度\r\n        if (data.hasOwnProperty(\"erotate\")) {\r\n            this.enemyRotate = Number(data.erotate);\r\n        }\r\n        \r\n        // 解析敌人位置\r\n        if (data.hasOwnProperty(\"epos\")) {\r\n            const eposArray = data.epos.split(\"#\");\r\n            for (let i = 0; i < eposArray.length; i++) {\r\n                const position = Tools.stringToNumber(eposArray[i], \",\");\r\n                if (position.length === 2) {\r\n                    this.enemyPos.push(v2(position[0], position[1]));\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析敌人轨迹组 1\r\n        if (data.hasOwnProperty(\"etrack1\")) {\r\n            const etrack1Array = data.etrack1.split(\"#\");\r\n            for (let i = 0; i < etrack1Array.length; i++) {\r\n                if (etrack1Array[i] !== \"\" && etrack1Array[i].split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(etrack1Array[i]);\r\n                    this.enemyTrackGroup1.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析敌人轨迹组 2\r\n        if (data.hasOwnProperty(\"etrack2\")) {\r\n            const etrack2Array = data.etrack2.split(\"#\");\r\n            for (let i = 0; i < etrack2Array.length; i++) {\r\n                if (etrack2Array[i] !== \"\" && etrack2Array[i].split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(etrack2Array[i]);\r\n                    this.enemyTrackGroup2.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n}\r\n\r\n/**\r\n * Boss 攻击点数据类\r\n */\r\nexport class BossAttackPointData {\r\n    bAvailable: boolean = true;\r\n    atkType: number = 0;\r\n    atkUnitId: number = 0;\r\n    atkAnim: number[] = [];\r\n    x: number = 0;\r\n    y: number = 0;\r\n    shootInterval: number[] = [];\r\n    bulletIDs: number[] = [];\r\n    bulletNums: number[] = [];\r\n    bulletIntervals: number[] = [];\r\n    bulletAttackRates: number[] = [];\r\n    attackOverDelay: number[] = [];\r\n    waveIds: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载攻击点数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\"#\");\r\n        if (parts.length < 3) {\r\n            Tools.error(\"BossAttackPointData error:\", data);\r\n            return;\r\n        }\r\n\r\n        this.atkType = parseInt(parts[0]);\r\n        this.atkUnitId = parseInt(parts[1]);\r\n\r\n        const animParts = parts[2].split(\";\");\r\n        for (const anim of animParts) {\r\n            if (anim !== \"\") {\r\n                this.atkAnim = Tools.stringToNumber(anim, \",\");\r\n            }\r\n        }\r\n\r\n        switch (this.atkType) {\r\n            case 0: // 普通攻击\r\n                const attackParts = parts[3].split(\";\");\r\n                try {\r\n                    if (attackParts.length <= 1) {\r\n                        this.bAvailable = false;\r\n                        return;\r\n                    }\r\n\r\n                    const position = Tools.stringToPoint(attackParts[0], \",\");\r\n                    this.x = position.x;\r\n                    this.y = position.y;\r\n\r\n                    for (let i = 1; i < attackParts.length; i++) {\r\n                        if (attackParts[i] !== \"\") {\r\n                            const attackData = Tools.stringToNumber(attackParts[i], \",\");\r\n                            this.shootInterval.push(attackData[0]);\r\n                            this.bulletIDs.push(attackData[1]);\r\n                            this.bulletNums.push(attackData[2]);\r\n                            this.bulletIntervals.push(attackData[3]);\r\n                            this.bulletAttackRates.push(attackData[4] / 100);\r\n                            this.attackOverDelay.push(attackData[5]);\r\n                        }\r\n                    }\r\n                } catch (error) {\r\n                    Tools.error(\"BossAttackPointData error:\", data);\r\n                }\r\n                break;\r\n\r\n            case 1: // 波次攻击\r\n                const waveParts = parts[3].split(\";\");\r\n                try {\r\n                    if (waveParts.length <= 1) {\r\n                        this.bAvailable = false;\r\n                        return;\r\n                    }\r\n\r\n                    const wavePosition = Tools.stringToPoint(waveParts[0], \",\");\r\n                    this.x = wavePosition.x;\r\n                    this.y = wavePosition.y;\r\n\r\n                    this.waveIds = Tools.stringToNumber(waveParts[1], \",\");\r\n                    if (waveParts.length > 2) {\r\n                        this.attackOverDelay.push(parseInt(waveParts[2]));\r\n                    }\r\n                } catch (error) {\r\n                    Tools.error(\"BossAttackPointData error:\", data);\r\n                }\r\n                break;\r\n\r\n            default:\r\n                Tools.error(\"Unknown attack type:\", this.atkType);\r\n                break;\r\n        }\r\n    }\r\n}\r\n/**\r\n * Boss 攻击动作数据类\r\n */\r\nexport class BossAttackActionData {\r\n    bAtkMove: boolean = false; // 是否移动攻击\r\n    atkActId: number = 0; // 攻击动作 ID\r\n    atkPointId: number[] = []; // 攻击点 ID 列表\r\n\r\n    /**\r\n     * 从 JSON 数据加载攻击动作数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = Tools.stringToNumber(data, \",\");\r\n        try {\r\n            if (parts.length > 1) {\r\n                this.bAtkMove = parts[0] === 1;\r\n                this.atkActId = parts[1];\r\n                for (let i = 2; i < parts.length; i++) {\r\n                    this.atkPointId.push(parts[i]);\r\n                }\r\n            }\r\n        } catch (error) {\r\n            Tools.error(\"BossAttackActionData error:\", data);\r\n        }\r\n    }\r\n}\r\n"]}