System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, __checkObsolete__, __checkObsoleteInNamespace__, Prefab, instantiate, SingletonBase, GameIns, Tools, BossData, BattleLayer, GameFunc, MyApp, GameResourceList, BossPlane, BossManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossData(extras) {
    _reporterNs.report("BossData", "../data/BossData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossPlane(extras) {
    _reporterNs.report("BossPlane", "../ui/plane/boss/BossPlane", _context.meta, extras);
  }

  _export("BossManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      BossData = _unresolved_5.BossData;
    }, function (_unresolved_6) {
      BattleLayer = _unresolved_6.default;
    }, function (_unresolved_7) {
      GameFunc = _unresolved_7.GameFunc;
    }, function (_unresolved_8) {
      MyApp = _unresolved_8.MyApp;
    }, function (_unresolved_9) {
      GameResourceList = _unresolved_9.default;
    }, function (_unresolved_10) {
      BossPlane = _unresolved_10.default;
    }],
    execute: function () {
      _crd = true;

      __checkObsolete__(['JsonAsset', 'NodePool', 'resources', 'sp', 'Sprite', 'SpriteAtlas', 'Node', 'Prefab', 'instantiate']);

      _export("BossManager", BossManager = class BossManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super();
          this._bossDatas = new Map();
          this._bossArr = [];
          this._pfBoss = null;
          this.initConfig();
        }

        initConfig() {
          let bossTbDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbBoss.getDataList();

          for (let bossTbData of bossTbDatas) {
            const bossData = new (_crd && BossData === void 0 ? (_reportPossibleCrUseOfBossData({
              error: Error()
            }), BossData) : BossData)();
            bossData.loadJson(bossTbData);

            let bossList = this._bossDatas.get(bossData.id);

            if (!bossList) {
              bossList = [];

              this._bossDatas.set(bossData.id, bossList);
            }

            bossList.push(bossData);
          }
        }

        async preLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).PrefabBoss, Prefab, (error, prefab) => {
            this._pfBoss = prefab;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }

        mainReset() {
          this.subReset();
        }
        /**
         * 重置子关卡
         */


        subReset() {
          for (const boss of this._bossArr) {
            boss.node.parent = null;
            setTimeout(() => {
              boss.node.destroy();
            }, 1000);
          }

          this._bossArr = [];
        }
        /**
        * 添加 Boss
        * @param bossType Boss 类型
        * @param bossId Boss ID
        */


        addBoss(bossId) {
          const bossDatas = this.getBossDatas(bossId);

          if (!this._pfBoss) {
            throw new Error("Boss prefab is not initialized. Call preLoad() first.");
          }

          const node = instantiate(this._pfBoss);
          node.name = "boss";
          (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).me.addEnemy(node);
          const boss = node.getComponent(_crd && BossPlane === void 0 ? (_reportPossibleCrUseOfBossPlane({
            error: Error()
          }), BossPlane) : BossPlane);
          boss.initBoss(bossDatas);
          boss.new_uuid = (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).uuid;

          this._bossArr.push(boss);

          return boss;
        }
        /**
         * 移除 Boss
         * @param boss 要移除的 Boss
         */


        removeBoss(boss) {
          boss.node.y = 1000;
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this._bossArr, boss);
          boss.node.parent = null;
          boss.node.destroy();
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (let i = 0; i < this._bossArr.length; i++) {
            const boss = this._bossArr[i];

            if (boss.removeAble) {
              this.removeBoss(boss);
              i--;
            } else {
              boss.updateGameLogic(deltaTime);
            }
          }
        }
        /**
         * 开始 Boss 战斗
         */


        bossFightStart() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              boss.startBattle();
              break;
            }
          }
        }
        /**
         * 获取所有 Boss
         */


        get bosses() {
          return this._bossArr;
        }
        /**
         * 检查是否所有 Boss 已结束
         */


        isBossOver() {
          return this._bossArr.length === 0;
        }
        /**
         * 检查是否所有 Boss 已死亡
         */


        isBossDead() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              return false;
            }
          }

          return true;
        }
        /**
         * 获取 Boss 数据
         * @param bossId Boss ID
         */


        getBossDatas(bossId) {
          return this._bossDatas.get(bossId);
        }

      });

      _crd = false;
    }
  };
});
//# sourceMappingURL=801f3855dadb7927fbe58a803c46364f0079289b.js.map