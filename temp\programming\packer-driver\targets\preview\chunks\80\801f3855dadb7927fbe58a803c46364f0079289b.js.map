{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts"], "names": ["BossManager", "Prefab", "instantiate", "SingletonBase", "GameIns", "Tools", "BossData", "BattleLayer", "GameFunc", "MyApp", "GameResourceList", "BossPlane", "constructor", "_boss<PERSON><PERSON>s", "Map", "_bossArr", "_pfBoss", "initConfig", "bossTbDatas", "lubanTables", "TbBoss", "getDataList", "bossTbData", "bossData", "loadJson", "bossList", "get", "id", "set", "push", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "PrefabBoss", "error", "prefab", "checkLoadFinish", "mainReset", "subReset", "boss", "node", "parent", "setTimeout", "destroy", "addBoss", "bossId", "bossDatas", "getBossDatas", "Error", "name", "me", "addEnemy", "getComponent", "initBoss", "new_uuid", "uuid", "remove<PERSON>oss", "y", "arr<PERSON><PERSON><PERSON>", "updateGameLogic", "deltaTime", "i", "length", "removeAble", "bossFightStart", "isDead", "startBattle", "bosses", "isBossOver", "isBossDead"], "mappings": ";;;6LAYaA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZ2DC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACvEC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,W;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,gB;;AACAC,MAAAA,S;;;;;;;6BAGMX,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AAKxDY,QAAAA,WAAW,GAAG;AACV;AADU,eAJdC,UAIc,GAJwB,IAAIC,GAAJ,EAIxB;AAAA,eAHdC,QAGc,GAHU,EAGV;AAAA,eAFdC,OAEc,GAFW,IAEX;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT,cAAIC,WAAW,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,WAAzB,EAAlB;;AACA,eAAK,IAAIC,UAAT,IAAuBJ,WAAvB,EAAoC;AAChC,gBAAMK,QAAQ,GAAG;AAAA;AAAA,uCAAjB;AACAA,YAAAA,QAAQ,CAACC,QAAT,CAAkBF,UAAlB;;AACA,gBAAIG,QAAQ,GAAG,KAAKZ,UAAL,CAAgBa,GAAhB,CAAoBH,QAAQ,CAACI,EAA7B,CAAf;;AACA,gBAAI,CAACF,QAAL,EAAe;AACXA,cAAAA,QAAQ,GAAG,EAAX;;AACA,mBAAKZ,UAAL,CAAgBe,GAAhB,CAAoBL,QAAQ,CAACI,EAA7B,EAAiCF,QAAjC;AACH;;AACDA,YAAAA,QAAQ,CAACI,IAAT,CAAcN,QAAd;AACH;AACJ;;AAGKO,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBC,UAAnC,EAA+ClC,MAA/C,EAAuD,CAACmC,KAAD,EAAaC,MAAb,KAAgC;AACnF,cAAA,KAAI,CAACrB,OAAL,GAAeqB,MAAf;AACA;AAAA;AAAA,sCAAQN,aAAR,CAAsBO,eAAtB;AACH,aAHD;AAFY;AAMf;;AAGDC,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,QAAQ,GAAG;AAAA,2CAC2B;AAC9BC,YAAAA,IAAI,CAACC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAC,YAAAA,UAAU,CAAC,MAAM;AACbH,cAAAA,IAAI,CAACC,IAAL,CAAUG,OAAV;AACH,aAFS,EAEP,IAFO,CAAV;AAGH,WANM;;AACP,eAAK,IAAMJ,IAAX,IAAmB,KAAK1B,QAAxB;AAAA;AAAA;;AAMA,eAAKA,QAAL,GAAgB,EAAhB;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACI+B,QAAAA,OAAO,CAACC,MAAD,EAAmC;AACtC,cAAMC,SAAS,GAAG,KAAKC,YAAL,CAAkBF,MAAlB,CAAlB;;AACA,cAAI,CAAC,KAAK/B,OAAV,EAAmB;AACf,kBAAM,IAAIkC,KAAJ,CAAU,uDAAV,CAAN;AACH;;AACD,cAAMR,IAAU,GAAGxC,WAAW,CAAC,KAAKc,OAAN,CAA9B;AACA0B,UAAAA,IAAI,CAACS,IAAL,GAAY,MAAZ;AACA;AAAA;AAAA,0CAAYC,EAAZ,CAAeC,QAAf,CAAwBX,IAAxB;AAEA,cAAMD,IAAI,GAAGC,IAAI,CAACY,YAAL;AAAA;AAAA,qCAAb;AACAb,UAAAA,IAAI,CAACc,QAAL,CAAcP,SAAd;AACAP,UAAAA,IAAI,CAACe,QAAL,GAAgB;AAAA;AAAA,oCAASC,IAAzB;;AACA,eAAK1C,QAAL,CAAcc,IAAd,CAAmBY,IAAnB;;AAEA,iBAAOA,IAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACIiB,QAAAA,UAAU,CAACjB,IAAD,EAAkB;AACxBA,UAAAA,IAAI,CAACC,IAAL,CAAUiB,CAAV,GAAc,IAAd;AACA;AAAA;AAAA,8BAAMC,SAAN,CAAgB,KAAK7C,QAArB,EAA+B0B,IAA/B;AACAA,UAAAA,IAAI,CAACC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAF,UAAAA,IAAI,CAACC,IAAL,CAAUG,OAAV;AACH;AAED;AACJ;AACA;AACA;;;AACIgB,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhD,QAAL,CAAciD,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC3C,gBAAMtB,IAAI,GAAG,KAAK1B,QAAL,CAAcgD,CAAd,CAAb;;AACA,gBAAItB,IAAI,CAACwB,UAAT,EAAqB;AACjB,mBAAKP,UAAL,CAAgBjB,IAAhB;AACAsB,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHtB,cAAAA,IAAI,CAACoB,eAAL,CAAqBC,SAArB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,cAAc,GAAG;AACb,eAAK,IAAMzB,IAAX,IAAmB,KAAK1B,QAAxB,EAAkC;AAC9B,gBAAI,CAAC0B,IAAI,CAAC0B,MAAV,EAAkB;AACd1B,cAAAA,IAAI,CAAC2B,WAAL;AACA;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACc,YAANC,MAAM,GAAgB;AACtB,iBAAO,KAAKtD,QAAZ;AACH;AAED;AACJ;AACA;;;AACIuD,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKvD,QAAL,CAAciD,MAAd,KAAyB,CAAhC;AACH;AAED;AACJ;AACA;;;AACIO,QAAAA,UAAU,GAAY;AAClB,eAAK,IAAM9B,IAAX,IAAmB,KAAK1B,QAAxB,EAAkC;AAC9B,gBAAI,CAAC0B,IAAI,CAAC0B,MAAV,EAAkB;AACd,qBAAO,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACIlB,QAAAA,YAAY,CAACF,MAAD,EAAyC;AACjD,iBAAO,KAAKlC,UAAL,CAAgBa,GAAhB,CAAoBqB,MAApB,CAAP;AACH;;AAnJuD,O", "sourcesContent": ["import { JsonAsset, NodePool, resources, sp, Sprite, SpriteAtlas, Node, Prefab, instantiate } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { BossData} from \"../data/BossData\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { GameFunc } from \"../GameFunc\";\r\nimport { MyApp } from 'db://assets/scripts/MyApp';\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport BossPlane from \"../ui/plane/boss/BossPlane\";\r\n\r\n\r\nexport class BossManager extends SingletonBase<BossManager> {\r\n    _bossDatas: Map<number, BossData[]> = new Map();\r\n    _bossArr: BossPlane[] = [];\r\n    _pfBoss: Prefab | null = null;\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig() {\r\n        let bossTbDatas = MyApp.lubanTables.TbBoss.getDataList();\r\n        for (let bossTbData of bossTbDatas) {\r\n            const bossData = new BossData();\r\n            bossData.loadJson(bossTbData);\r\n            let bossList = this._bossDatas.get(bossData.id);\r\n            if (!bossList) {\r\n                bossList = [];\r\n                this._bossDatas.set(bossData.id, bossList);\r\n            }\r\n            bossList.push(bossData);\r\n        }\r\n    }\r\n\r\n\r\n    async preLoad() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.PrefabBoss, Prefab, (error: any, prefab: Prefab) => {\r\n            this._pfBoss = prefab;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n\r\n    mainReset() {\r\n        this.subReset();\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        for (const boss of this._bossArr) {\r\n            boss.node.parent = null;\r\n            setTimeout(() => {\r\n                boss.node.destroy();\r\n            }, 1000);\r\n        }\r\n        this._bossArr = [];\r\n    }\r\n\r\n\r\n    /**\r\n * 添加 Boss\r\n * @param bossType Boss 类型\r\n * @param bossId Boss ID\r\n */\r\n    addBoss(bossId: number): BossPlane | null {\r\n        const bossDatas = this.getBossDatas(bossId)!;\r\n        if (!this._pfBoss) {\r\n            throw new Error(\"Boss prefab is not initialized. Call preLoad() first.\");\r\n        }\r\n        const node: Node = instantiate(this._pfBoss);\r\n        node.name = \"boss\";\r\n        BattleLayer.me.addEnemy(node);\r\n\r\n        const boss = node.getComponent(BossPlane)!;\r\n        boss.initBoss(bossDatas);\r\n        boss.new_uuid = GameFunc.uuid;\r\n        this._bossArr.push(boss);\r\n\r\n        return boss;\r\n    }\r\n\r\n\r\n    /**\r\n     * 移除 Boss\r\n     * @param boss 要移除的 Boss\r\n     */\r\n    removeBoss(boss: BossPlane) {\r\n        boss.node.y = 1000;\r\n        Tools.arrRemove(this._bossArr, boss);\r\n        boss.node.parent = null;\r\n        boss.node.destroy();\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._bossArr.length; i++) {\r\n            const boss = this._bossArr[i];\r\n            if (boss.removeAble) {\r\n                this.removeBoss(boss);\r\n                i--;\r\n            } else {\r\n                boss.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始 Boss 战斗\r\n     */\r\n    bossFightStart() {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                boss.startBattle();\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取所有 Boss\r\n     */\r\n    get bosses(): BossPlane[] {\r\n        return this._bossArr;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已结束\r\n     */\r\n    isBossOver(): boolean {\r\n        return this._bossArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已死亡\r\n     */\r\n    isBossDead(): boolean {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n\r\n    /**\r\n     * 获取 Boss 数据\r\n     * @param bossId Boss ID\r\n     */\r\n    getBossDatas(bossId: number): BossData[] | undefined {\r\n        return this._bossDatas.get(bossId);\r\n    }\r\n}"]}