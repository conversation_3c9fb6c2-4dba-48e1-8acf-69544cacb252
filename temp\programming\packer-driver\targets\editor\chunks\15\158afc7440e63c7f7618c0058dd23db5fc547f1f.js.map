{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/game/bullet/EventGroup.ts"], "names": ["EventGroupContext", "Condition<PERSON><PERSON><PERSON>", "EventGroup", "Comparer", "ConditionFactory", "ActionFactory", "eEmitterCondition", "eBulletCondition", "eBulletAction", "eEmitterAction", "eConditionOp", "eCompareOp", "emitter_cond", "bullet_cond", "emitter_act", "bullet_act", "BulletSystem", "emitter", "bullet", "<PERSON><PERSON><PERSON>", "reset", "conditions", "evaluate", "context", "length", "result", "i", "condition", "conditionResult", "data", "op", "And", "Or", "eEventGroupStatus", "status", "_status", "constructor", "ctx", "<PERSON><PERSON><PERSON><PERSON>", "actions", "_isStarted", "_triggerCount", "Idle", "buildConditionChain", "map", "actionData", "action", "create", "changeStatus", "tryStart", "onCreateEventGroup", "Waiting", "tryStop", "onDestroyEventGroup", "Stopped", "canExecute", "tick", "dt", "Active", "tickActive", "newStatus", "for<PERSON>ach", "onLoad", "chain", "condData", "index", "push", "isAllFinished", "isCompleted", "onExecute", "triggerCount", "compare", "a", "b", "Equal", "NotEqual", "Greater", "GreaterEqual", "Less", "LessEqual", "Error", "type", "Emitter_Active", "EmitterCondition_Active", "Emitter_InitialDelay", "EmitterCondition_InitialDelay", "Emitter_Prewarm", "EmitterCondition_Prewarm", "Emitter_PrewarmDuration", "EmitterCondition_PrewarmDuration", "Emitter_Duration", "EmitterCondition_Duration", "Emitter_ElapsedTime", "EmitterCondition_ElapsedTime", "Emitter_Loop", "EmitterCondition_Loop", "Emitter_LoopInterval", "EmitterCondition_LoopInterval", "Emitter_EmitInterval", "EmitterCondition_EmitInterval", "Emitter_PerEmitCount", "EmitterCondition_PerEmitCount", "Emitter_PerEmitInterval", "EmitterCondition_PerEmitInterval", "Emitter_PerEmitOffsetX", "EmitterCondition_PerEmitOffsetX", "Emitter_Angle", "EmitterCondition_Angle", "Emitter_Count", "EmitterCondition_Count", "Bullet_Duration", "EmitterCondition_BulletDuration", "Bullet_Speed", "EmitterCondition_BulletSpeed", "Bullet_Acceleration", "EmitterCondition_BulletAcceleration", "Bullet_AccelerationAngle", "EmitterCondition_BulletAccelerationAngle", "Bullet_FacingMoveDir", "EmitterCondition_BulletFacingMoveDir", "Bullet_TrackingTarget", "EmitterCondition_BulletTrackingTarget", "Bullet_Destructive", "EmitterCondition_BulletDestructive", "Bullet_DestructiveOnHit", "EmitterCondition_BulletDestructiveOnHit", "Bullet_Scale", "EmitterCondition_BulletScale", "Bullet_ColorR", "EmitterCondition_BulletColorR", "Bullet_ColorG", "EmitterCondition_BulletColorG", "Bullet_ColorB", "EmitterCondition_BulletColorB", "Bullet_DefaultFacing", "EmitterCondition_BulletDefaultFacing", "Player_ActLevel", "EmitterCondition_PlayerActLevel", "Player_PosX", "EmitterCondition_PlayerPosX", "Player_PosY", "EmitterCondition_PlayerPosY", "Player_LifePercent", "EmitterCondition_PlayerLifePercent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterCondition_PlayerGainBuff", "BulletCondition_Duration", "Bullet_ElapsedTime", "BulletCondition_ElapsedTime", "Bullet_PosX", "BulletCondition_PosX", "Bullet_PosY", "BulletCondition_PosY", "BulletCondition_Speed", "Bullet_SpeedAngle", "BulletCondition_SpeedAngle", "BulletCondition_Acceleration", "BulletCondition_AccelerationAngle", "BulletCondition_Scale", "BulletCondition_ColorR", "BulletCondition_ColorG", "BulletCondition_ColorB", "BulletCondition_FacingMoveDir", "BulletCondition_Destructive", "BulletCondition_DestructiveOnHit", "EmitterAction_Active", "EmitterAction_InitialDelay", "EmitterAction_Prewarm", "EmitterAction_PrewarmDuration", "EmitterAction_Duration", "EmitterAction_ElapsedTime", "EmitterAction_Loop", "EmitterAction_LoopInterval", "EmitterAction_EmitInterval", "EmitterAction_PerEmitCount", "EmitterAction_PerEmitInterval", "EmitterAction_PerEmitOffsetX", "EmitterAction_Angle", "EmitterAction_Count", "EmitterAction_BulletDuration", "Bullet_Damage", "EmitterAction_BulletDamage", "EmitterAction_BulletSpeed", "EmitterAction_BulletSpeedAngle", "EmitterAction_BulletAcceleration", "EmitterAction_BulletAccelerationAngle", "EmitterAction_BulletScale", "EmitterAction_BulletColorR", "EmitterAction_BulletColorG", "EmitterAction_BulletColorB", "EmitterAction_BulletFacingMoveDir", "EmitterAction_BulletTrackingTarget", "EmitterAction_BulletDestructive", "EmitterAction_BulletDestructiveOnHit", "BulletAction_Duration", "BulletAction_ElapsedTime", "BulletAction_PosX", "BulletAction_PosY", "BulletAction_Speed", "BulletAction_SpeedAngle", "BulletAction_Acceleration", "BulletAction_AccelerationAngle", "BulletAction_Scale", "BulletAction_ColorR", "BulletAction_ColorG", "BulletAction_ColorB", "BulletAction_FacingMoveDir", "BulletAction_Destructive", "BulletAction_DestructiveOnHit"], "mappings": ";;;8LAeaA,iB,EAaPC,c,EA8BOC,U,EAkIAC,Q,EAsBPC,gB,EA2GAC,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3TGC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;AACnBC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,c,iBAAAA,c;;AAGfC,MAAAA,Y,iBAAAA,Y;AAAcC,MAAAA,U,iBAAAA,U;;AACXC,MAAAA,Y;;AACAC,MAAAA,W;;AACAC,MAAAA,W;;AACAC,MAAAA,U;;AACHC,MAAAA,Y,iBAAAA,Y;;;;;;;AAGT;mCACahB,iB,GAAN,MAAMA,iBAAN,CAAwB;AAAA;AAAA,eAC3BiB,OAD2B,GACD,IADC;AAAA,eAE3BC,MAF2B,GAEH,IAFG;AAAA,eAG3BC,WAH2B,GAGK,IAHL;AAAA;;AAI3B;AAEAC,QAAAA,KAAK,GAAS;AACV,eAAKH,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACH;;AAT0B,O,GAY/B;;;AACMjB,MAAAA,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjBoB,UADiB,GACoB,EADpB;AAAA;;AAGjBC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AAC1C,cAAI,KAAKF,UAAL,CAAgBG,MAAhB,KAA2B,CAA/B,EAAkC,OAAO,IAAP;AAClC,cAAIC,MAAM,GAAG,KAAKJ,UAAL,CAAgB,CAAhB,EAAmBC,QAAnB,CAA4BC,OAA5B,CAAb;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKL,UAAL,CAAgBG,MAApC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,kBAAMC,SAAS,GAAG,KAAKN,UAAL,CAAgBK,CAAhB,CAAlB;AACA,kBAAME,eAAe,GAAGD,SAAS,CAACL,QAAV,CAAmBC,OAAnB,CAAxB;;AAEA,gBAAII,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaC,GAAvC,EAA4C;AACxCN,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH,aAFD,MAEO,IAAID,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaE,EAAvC,EAA2C;AAC9CP,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH;AACJ;;AAED,iBAAOH,MAAP;AACH;;AAnBgB,O,EAsBrB;;mCACYQ,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;4BAOC/B,U,GAAN,MAAMA,UAAN,CAAiB;AAUV,YAANgC,MAAM,GAAsB;AAC5B,iBAAO,KAAKC,OAAZ;AACH;;AAEDC,QAAAA,WAAW,CAACC,GAAD,EAAyBR,IAAzB,EAA+C;AAAA,eAbjDA,IAaiD;AAAA,eAX1DN,OAW0D;AAAA,eAV1De,cAU0D;AAAA,eAT1DC,OAS0D;AAAA,eAPlDC,UAOkD,GAP5B,KAO4B;AAAA,eANlDC,aAMkD,GAN1B,CAM0B;AAAA,eALlDN,OAKkD,GALrBF,iBAAiB,CAACS,IAKG;AACtD,eAAKnB,OAAL,GAAec,GAAf;AACA,eAAKR,IAAL,GAAYA,IAAZ;AACA,eAAKS,cAAL,GAAsB,KAAKK,mBAAL,CAAyBd,IAAI,CAACR,UAA9B,CAAtB;AACA,eAAKkB,OAAL,GAAeV,IAAI,CAACU,OAAL,CAAaK,GAAb,CAAiBC,UAAU,IAAI;AAC1C,gBAAIC,MAAM,GAAGzC,aAAa,CAAC0C,MAAd,CAAqBF,UAArB,CAAb;AACA,mBAAOC,MAAP;AACH,WAHc,CAAf;AAIA,eAAKL,aAAL,GAAqB,CAArB;AACA,eAAKO,YAAL,CAAkBf,iBAAiB,CAACS,IAApC;AACH;;AAEDO,QAAAA,QAAQ,GAAY;AAChB,cAAI,KAAKT,UAAT,EAAqB,OAAO,KAAP;AAErB;AAAA;AAAA,4CAAaU,kBAAb,CAAgC,IAAhC;AACA,eAAKF,YAAL,CAAkBf,iBAAiB,CAACkB,OAApC;AACA,eAAKX,UAAL,GAAkB,IAAlB;AACA,iBAAO,IAAP;AACH;;AAEDY,QAAAA,OAAO,GAAY;AACf,cAAI,CAAC,KAAKZ,UAAV,EAAsB,OAAO,KAAP;AAEtB;AAAA;AAAA,4CAAaa,mBAAb,CAAiC,IAAjC;AACA,eAAKL,YAAL,CAAkBf,iBAAiB,CAACqB,OAApC;AACA,eAAKd,UAAL,GAAkB,KAAlB;AACA,iBAAO,IAAP;AACH;;AAEDpB,QAAAA,KAAK,GAAS;AACV,eAAKqB,aAAL,GAAqB,CAArB;AACA,eAAKO,YAAL,CAAkBf,iBAAiB,CAACS,IAApC;AACH;;AAEDa,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKjB,cAAL,CAAoBhB,QAApB,CAA6B,KAAKC,OAAlC,CAAP;AACH;;AAEDiC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAEnB,kBAAQ,KAAKtB,OAAb;AACI,iBAAKF,iBAAiB,CAACS,IAAvB;AACI;AACA;;AACJ,iBAAKT,iBAAiB,CAACkB,OAAvB;AACI;AACA,kBAAI,KAAKI,UAAL,EAAJ,EAAuB;AACnB;AACA,qBAAKP,YAAL,CAAkBf,iBAAiB,CAACyB,MAApC;AACH;;AACD;;AACJ,iBAAKzB,iBAAiB,CAACyB,MAAvB;AACI;AACA,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAKxB,iBAAiB,CAACqB,OAAvB;AACI;AACA;AAjBR;AAmBH;;AAEON,QAAAA,YAAY,CAACY,SAAD,EAA+B;AAC/C,cAAI,KAAKzB,OAAL,KAAiByB,SAArB,EAAgC;AAEhC,eAAKzB,OAAL,GAAeyB,SAAf;;AAEA,kBAAQ,KAAKzB,OAAb;AACI,iBAAKF,iBAAiB,CAACkB,OAAvB;AACI;AACA;;AACJ,iBAAKlB,iBAAiB,CAACyB,MAAvB;AACI;AACA,mBAAKnB,OAAL,CAAasB,OAAb,CAAqBf,MAAM,IAAIA,MAAM,CAACgB,MAAP,CAAc,KAAKvC,OAAnB,CAA/B;AACA;;AACJ,iBAAKU,iBAAiB,CAACqB,OAAvB;AACI;;AACJ;AAAS;AAVb;AAYH;;AAEOX,QAAAA,mBAAmB,CAACtB,UAAD,EAAmD;AAC1E,gBAAM0C,KAAK,GAAG,IAAI9D,cAAJ,EAAd;AACAoB,UAAAA,UAAU,CAACwC,OAAX,CAAmB,CAACG,QAAD,EAAWC,KAAX,KAAqB;AACpC,kBAAMtC,SAAS,GAAGvB,gBAAgB,CAAC2C,MAAjB,CAAwBiB,QAAxB,CAAlB;;AACA,gBAAIrC,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACmC,MAAV,CAAiB,KAAKvC,OAAtB;AACAwC,cAAAA,KAAK,CAAC1C,UAAN,CAAiB6C,IAAjB,CAAsBvC,SAAtB;AACH;AACJ,WAND;AAOA,iBAAOoC,KAAP;AACH;;AAEOJ,QAAAA,UAAU,CAACF,EAAD,EAAmB;AACjC,cAAIU,aAAa,GAAG,IAApB;;AAEA,eAAK,MAAMrB,MAAX,IAAqB,KAAKP,OAA1B,EAAmC;AAC/B,gBAAIO,MAAM,CAACsB,WAAP,EAAJ,EAA0B;AAC1BtB,YAAAA,MAAM,CAACuB,SAAP,CAAiB,KAAK9C,OAAtB,EAA+BkC,EAA/B;AACAU,YAAAA,aAAa,GAAG,KAAhB;AACH;;AAED,cAAIA,aAAJ,EAAmB;AACf,iBAAK1B,aAAL;;AACA,gBAAI,KAAKZ,IAAL,CAAUyC,YAAV,GAAyB,CAAzB,IAA8B,KAAK7B,aAAL,GAAqB,KAAKZ,IAAL,CAAUyC,YAAjE,EAA+E;AAC3E;AACA,mBAAKtB,YAAL,CAAkBf,iBAAiB,CAACkB,OAApC;AACH,aAHD,MAIK;AACD,mBAAKC,OAAL;AACH;AACJ;AACJ;;AA9HmB,O,GAiIxB;;;0BACajD,Q,GAAN,MAAMA,QAAN,CAAe;AACJ,eAAPoE,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB3C,EAAvB,EAAgD;AAC1D,kBAAQA,EAAR;AACI,iBAAK;AAAA;AAAA,0CAAW4C,KAAhB;AACI,qBAAOF,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,QAAhB;AACI,qBAAOH,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWG,OAAhB;AACI,qBAAOJ,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,YAAhB;AACI,qBAAOL,CAAC,IAAIC,CAAZ;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,IAAhB;AACI,qBAAON,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWM,SAAhB;AACI,qBAAOP,CAAC,IAAIC,CAAZ;;AACJ;AACI,oBAAM,IAAIO,KAAJ,CAAW,6BAA4BlD,EAAG,EAA1C,CAAN;AAdR;AAgBH;;AAlBiB,O,GAqBtB;;;AACM1B,MAAAA,gB,GAAN,MAAMA,gBAAN,CAAuB;AACN,eAAN2C,MAAM,CAAClB,IAAD,EAA4C;AACrD,kBAAQA,IAAI,CAACoD,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBC,cAAvB;AACI,qBAAO,IAAItE,YAAY,CAACuE,uBAAjB,CAAyCtD,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBuD,oBAAvB;AACI,qBAAO,IAAIxE,YAAY,CAACyE,6BAAjB,CAA+CxD,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkByD,eAAvB;AACI,qBAAO,IAAI1E,YAAY,CAAC2E,wBAAjB,CAA0C1D,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB2D,uBAAvB;AACI,qBAAO,IAAI5E,YAAY,CAAC6E,gCAAjB,CAAkD5D,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB6D,gBAAvB;AACI,qBAAO,IAAI9E,YAAY,CAAC+E,yBAAjB,CAA2C9D,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB+D,mBAAvB;AACI,qBAAO,IAAIhF,YAAY,CAACiF,4BAAjB,CAA8ChE,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBiE,YAAvB;AACI,qBAAO,IAAIlF,YAAY,CAACmF,qBAAjB,CAAuClE,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBmE,oBAAvB;AACI,qBAAO,IAAIpF,YAAY,CAACqF,6BAAjB,CAA+CpE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBqE,oBAAvB;AACI,qBAAO,IAAItF,YAAY,CAACuF,6BAAjB,CAA+CtE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBuE,oBAAvB;AACI,qBAAO,IAAIxF,YAAY,CAACyF,6BAAjB,CAA+CxE,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkByE,uBAAvB;AACI,qBAAO,IAAI1F,YAAY,CAAC2F,gCAAjB,CAAkD1E,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB2E,sBAAvB;AACI,qBAAO,IAAI5F,YAAY,CAAC6F,+BAAjB,CAAiD5E,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB6E,aAAvB;AACI,qBAAO,IAAI9F,YAAY,CAAC+F,sBAAjB,CAAwC9E,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB+E,aAAvB;AACI,qBAAO,IAAIhG,YAAY,CAACiG,sBAAjB,CAAwChF,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBiF,eAAvB;AACI,qBAAO,IAAIlG,YAAY,CAACmG,+BAAjB,CAAiDlF,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBmF,YAAvB;AACI,qBAAO,IAAIpG,YAAY,CAACqG,4BAAjB,CAA8CpF,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBqF,mBAAvB;AACI,qBAAO,IAAItG,YAAY,CAACuG,mCAAjB,CAAqDtF,IAArD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBuF,wBAAvB;AACI,qBAAO,IAAIxG,YAAY,CAACyG,wCAAjB,CAA0DxF,IAA1D,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkByF,oBAAvB;AACI,qBAAO,IAAI1G,YAAY,CAAC2G,oCAAjB,CAAsD1F,IAAtD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB2F,qBAAvB;AACI,qBAAO,IAAI5G,YAAY,CAAC6G,qCAAjB,CAAuD5F,IAAvD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB6F,kBAAvB;AACI,qBAAO,IAAI9G,YAAY,CAAC+G,kCAAjB,CAAoD9F,IAApD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB+F,uBAAvB;AACI,qBAAO,IAAIhH,YAAY,CAACiH,uCAAjB,CAAyDhG,IAAzD,CAAP;AACJ;AACA;;AACA,iBAAK;AAAA;AAAA,wDAAkBiG,YAAvB;AACI,qBAAO,IAAIlH,YAAY,CAACmH,4BAAjB,CAA8ClG,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBmG,aAAvB;AACI,qBAAO,IAAIpH,YAAY,CAACqH,6BAAjB,CAA+CpG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBqG,aAAvB;AACI,qBAAO,IAAItH,YAAY,CAACuH,6BAAjB,CAA+CtG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBuG,aAAvB;AACI,qBAAO,IAAIxH,YAAY,CAACyH,6BAAjB,CAA+CxG,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkByG,oBAAvB;AACI,qBAAO,IAAI1H,YAAY,CAAC2H,oCAAjB,CAAsD1G,IAAtD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB2G,eAAvB;AACI,qBAAO,IAAI5H,YAAY,CAAC6H,+BAAjB,CAAiD5G,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB6G,WAAvB;AACI,qBAAO,IAAI9H,YAAY,CAAC+H,2BAAjB,CAA6C9G,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB+G,WAAvB;AACI,qBAAO,IAAIhI,YAAY,CAACiI,2BAAjB,CAA6ChH,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBiH,kBAAvB;AACI,qBAAO,IAAIlI,YAAY,CAACmI,kCAAjB,CAAoDlH,IAApD,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBmH,eAAvB;AACI,qBAAO,IAAIpI,YAAY,CAACqI,+BAAjB,CAAiDpH,IAAjD,CAAP;AAEJ;;AACA,iBAAK;AAAA;AAAA,sDAAiBiF,eAAtB;AACI,qBAAO,IAAIjG,WAAW,CAACqI,wBAAhB,CAAyCrH,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBsH,kBAAtB;AACI,qBAAO,IAAItI,WAAW,CAACuI,2BAAhB,CAA4CvH,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBwH,WAAtB;AACI,qBAAO,IAAIxI,WAAW,CAACyI,oBAAhB,CAAqCzH,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB0H,WAAtB;AACI,qBAAO,IAAI1I,WAAW,CAAC2I,oBAAhB,CAAqC3H,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBmF,YAAtB;AACI,qBAAO,IAAInG,WAAW,CAAC4I,qBAAhB,CAAsC5H,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB6H,iBAAtB;AACI,qBAAO,IAAI7I,WAAW,CAAC8I,0BAAhB,CAA2C9H,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBqF,mBAAtB;AACI,qBAAO,IAAIrG,WAAW,CAAC+I,4BAAhB,CAA6C/H,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBuF,wBAAtB;AACI,qBAAO,IAAIvG,WAAW,CAACgJ,iCAAhB,CAAkDhI,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBiG,YAAtB;AACI,qBAAO,IAAIjH,WAAW,CAACiJ,qBAAhB,CAAsCjI,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBmG,aAAtB;AACI,qBAAO,IAAInH,WAAW,CAACkJ,sBAAhB,CAAuClI,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBqG,aAAtB;AACI,qBAAO,IAAIrH,WAAW,CAACmJ,sBAAhB,CAAuCnI,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiBuG,aAAtB;AACI,qBAAO,IAAIvH,WAAW,CAACoJ,sBAAhB,CAAuCpI,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiByF,oBAAtB;AACI,qBAAO,IAAIzG,WAAW,CAACqJ,6BAAhB,CAA8CrI,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB6F,kBAAtB;AACI,qBAAO,IAAI7G,WAAW,CAACsJ,2BAAhB,CAA4CtI,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,sDAAiB+F,uBAAtB;AACI,qBAAO,IAAI/G,WAAW,CAACuJ,gCAAhB,CAAiDvI,IAAjD,CAAP;;AACJ;AACI,oBAAM,IAAImD,KAAJ,CAAW,2BAA0BnD,IAAI,CAACoD,IAAK,EAA/C,CAAN;AApGR;AAsGH;;AAxGkB,O;AA2GjB5E,MAAAA,a,GAAN,MAAMA,aAAN,CAAoB;AACH,eAAN0C,MAAM,CAAClB,IAAD,EAAsC;AAC/C,kBAAQA,IAAI,CAACoD,IAAb;AACI,iBAAK;AAAA;AAAA,kDAAeC,cAApB;AACI,qBAAO,IAAIpE,WAAW,CAACuJ,oBAAhB,CAAqCxI,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeuD,oBAApB;AACI,qBAAO,IAAItE,WAAW,CAACwJ,0BAAhB,CAA2CzI,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeyD,eAApB;AACI,qBAAO,IAAIxE,WAAW,CAACyJ,qBAAhB,CAAsC1I,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe2D,uBAApB;AACI,qBAAO,IAAI1E,WAAW,CAAC0J,6BAAhB,CAA8C3I,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe6D,gBAApB;AACI,qBAAO,IAAI5E,WAAW,CAAC2J,sBAAhB,CAAuC5I,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe+D,mBAApB;AACI,qBAAO,IAAI9E,WAAW,CAAC4J,yBAAhB,CAA0C7I,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeiE,YAApB;AACI,qBAAO,IAAIhF,WAAW,CAAC6J,kBAAhB,CAAmC9I,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAemE,oBAApB;AACI,qBAAO,IAAIlF,WAAW,CAAC8J,0BAAhB,CAA2C/I,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeqE,oBAApB;AACI,qBAAO,IAAIpF,WAAW,CAAC+J,0BAAhB,CAA2ChJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeuE,oBAApB;AACI,qBAAO,IAAItF,WAAW,CAACgK,0BAAhB,CAA2CjJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeyE,uBAApB;AACI,qBAAO,IAAIxF,WAAW,CAACiK,6BAAhB,CAA8ClJ,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe2E,sBAApB;AACI,qBAAO,IAAI1F,WAAW,CAACkK,4BAAhB,CAA6CnJ,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe6E,aAApB;AACI,qBAAO,IAAI5F,WAAW,CAACmK,mBAAhB,CAAoCpJ,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe+E,aAApB;AACI,qBAAO,IAAI9F,WAAW,CAACoK,mBAAhB,CAAoCrJ,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeiF,eAApB;AACI,qBAAO,IAAIhG,WAAW,CAACqK,4BAAhB,CAA6CtJ,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeuJ,aAApB;AACI,qBAAO,IAAItK,WAAW,CAACuK,0BAAhB,CAA2CxJ,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAemF,YAApB;AACI,qBAAO,IAAIlG,WAAW,CAACwK,yBAAhB,CAA0CzJ,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe6H,iBAApB;AACI,qBAAO,IAAI5I,WAAW,CAACyK,8BAAhB,CAA+C1J,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeqF,mBAApB;AACI,qBAAO,IAAIpG,WAAW,CAAC0K,gCAAhB,CAAiD3J,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeuF,wBAApB;AACI,qBAAO,IAAItG,WAAW,CAAC2K,qCAAhB,CAAsD5J,IAAtD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeiG,YAApB;AACI,qBAAO,IAAIhH,WAAW,CAAC4K,yBAAhB,CAA0C7J,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAemG,aAApB;AACI,qBAAO,IAAIlH,WAAW,CAAC6K,0BAAhB,CAA2C9J,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeqG,aAApB;AACI,qBAAO,IAAIpH,WAAW,CAAC8K,0BAAhB,CAA2C/J,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeuG,aAApB;AACI,qBAAO,IAAItH,WAAW,CAAC+K,0BAAhB,CAA2ChK,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAeyF,oBAApB;AACI,qBAAO,IAAIxG,WAAW,CAACgL,iCAAhB,CAAkDjK,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe2F,qBAApB;AACI,qBAAO,IAAI1G,WAAW,CAACiL,kCAAhB,CAAmDlK,IAAnD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe6F,kBAApB;AACI,qBAAO,IAAI5G,WAAW,CAACkL,+BAAhB,CAAgDnK,IAAhD,CAAP;;AACJ,iBAAK;AAAA;AAAA,kDAAe+F,uBAApB;AACI,qBAAO,IAAI9G,WAAW,CAACmL,oCAAhB,CAAqDpK,IAArD,CAAP;AACJ;;AACA,iBAAK;AAAA;AAAA,gDAAciF,eAAnB;AACI,qBAAO,IAAI/F,UAAU,CAACmL,qBAAf,CAAqCrK,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcsH,kBAAnB;AACI,qBAAO,IAAIpI,UAAU,CAACoL,wBAAf,CAAwCtK,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcwH,WAAnB;AACI,qBAAO,IAAItI,UAAU,CAACqL,iBAAf,CAAiCvK,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc0H,WAAnB;AACI,qBAAO,IAAIxI,UAAU,CAACsL,iBAAf,CAAiCxK,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcmF,YAAnB;AACI,qBAAO,IAAIjG,UAAU,CAACuL,kBAAf,CAAkCzK,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc6H,iBAAnB;AACI,qBAAO,IAAI3I,UAAU,CAACwL,uBAAf,CAAuC1K,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcqF,mBAAnB;AACI,qBAAO,IAAInG,UAAU,CAACyL,yBAAf,CAAyC3K,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcuF,wBAAnB;AACI,qBAAO,IAAIrG,UAAU,CAAC0L,8BAAf,CAA8C5K,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAciG,YAAnB;AACI,qBAAO,IAAI/G,UAAU,CAAC2L,kBAAf,CAAkC7K,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcmG,aAAnB;AACI,qBAAO,IAAIjH,UAAU,CAAC4L,mBAAf,CAAmC9K,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcqG,aAAnB;AACI,qBAAO,IAAInH,UAAU,CAAC6L,mBAAf,CAAmC/K,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcuG,aAAnB;AACI,qBAAO,IAAIrH,UAAU,CAAC8L,mBAAf,CAAmChL,IAAnC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAcyF,oBAAnB;AACI,qBAAO,IAAIvG,UAAU,CAAC+L,0BAAf,CAA0CjL,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc6F,kBAAnB;AACI,qBAAO,IAAI3G,UAAU,CAACgM,wBAAf,CAAwClL,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,gDAAc+F,uBAAnB;AACI,qBAAO,IAAI7G,UAAU,CAACiM,6BAAf,CAA6CnL,IAA7C,CAAP;;AACJ;AACI,oBAAM,IAAImD,KAAJ,CAAW,wBAAuBnD,IAAI,CAACoD,IAAK,EAA5C,CAAN;AAzFR;AA2FH;;AA7Fe,O", "sourcesContent": ["import { Emitter } from \"./Emitter\";\r\nimport { Bullet } from \"./Bullet\";\r\nimport { eEmitterCondition, eBulletCondition } from \"../data/bullet/EventConditionType\";\r\nimport { eBulletAction, eEmitterAction } from \"../data/bullet/EventActionType\";\r\nimport { IEventCondition } from \"./conditions/IEventCondition\";\r\nimport { IEventAction } from \"./actions/IEventAction\";\r\nimport { eConditionOp, eCompareOp, EventGroupData, EventActionData, EventConditionData } from \"../data/bullet/EventGroupData\";\r\nimport * as emitter_cond from \"./conditions/EmitterEventConditions\";\r\nimport * as bullet_cond from \"./conditions/BulletEventConditions\";\r\nimport * as emitter_act from \"./actions/EmitterEventActions\";\r\nimport * as bullet_act from \"./actions/BulletEventActions\";\r\nimport { BulletSystem } from \"./BulletSystem\";\r\nimport PlaneBase from \"db://assets/scripts/game/ui/plane/PlaneBase\";\r\n\r\n// context for running condition & action\r\nexport class EventGroupContext {\r\n    emitter: Emitter | null = null;\r\n    bullet: Bullet | null = null;\r\n    playerPlane: PlaneBase | null = null;\r\n    // TODO: add level \r\n\r\n    reset(): void {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n    }\r\n}\r\n\r\n// Condition chain with operators\r\nclass ConditionChain {\r\n    conditions: Array<IEventCondition> = [];\r\n\r\n    evaluate(context: EventGroupContext): boolean {\r\n        if (this.conditions.length === 0) return true;\r\n        let result = this.conditions[0].evaluate(context);\r\n        \r\n        for (let i = 1; i < this.conditions.length; i++) {\r\n            const condition = this.conditions[i];\r\n            const conditionResult = condition.evaluate(context);\r\n            \r\n            if (condition.data.op === eConditionOp.And) {\r\n                result = result && conditionResult;\r\n            } else if (condition.data.op === eConditionOp.Or) {\r\n                result = result || conditionResult;\r\n            }\r\n        }\r\n        \r\n        return result;\r\n    }\r\n}\r\n\r\n// Updated EventGroup\r\nexport enum eEventGroupStatus {\r\n    Idle,       // not active\r\n    Waiting,    // waiting for conditions to be met\r\n    Active,     // conditions are met, now ticking actions\r\n    Stopped     // stopped\r\n}\r\n\r\nexport class EventGroup {\r\n    readonly data: EventGroupData;\r\n\r\n    context: EventGroupContext;\r\n    conditionChain: ConditionChain;\r\n    actions: IEventAction[];\r\n\r\n    private _isStarted: boolean = false;\r\n    private _triggerCount: number = 0;\r\n    private _status: eEventGroupStatus = eEventGroupStatus.Idle;\r\n    get status(): eEventGroupStatus {\r\n        return this._status;\r\n    }\r\n    \r\n    constructor(ctx: EventGroupContext, data: EventGroupData) {\r\n        this.context = ctx;\r\n        this.data = data;\r\n        this.conditionChain = this.buildConditionChain(data.conditions);\r\n        this.actions = data.actions.map(actionData => {\r\n            let action = ActionFactory.create(actionData);\r\n            return action;\r\n        });\r\n        this._triggerCount = 0;\r\n        this.changeStatus(eEventGroupStatus.Idle);\r\n    }\r\n\r\n    tryStart(): boolean {\r\n        if (this._isStarted) return false;\r\n        \r\n        BulletSystem.onCreateEventGroup(this);\r\n        this.changeStatus(eEventGroupStatus.Waiting);\r\n        this._isStarted = true;\r\n        return true;\r\n    }\r\n\r\n    tryStop(): boolean {\r\n        if (!this._isStarted) return false;\r\n        \r\n        BulletSystem.onDestroyEventGroup(this);\r\n        this.changeStatus(eEventGroupStatus.Stopped); \r\n        this._isStarted = false;\r\n        return true;\r\n    }\r\n\r\n    reset(): void {\r\n        this._triggerCount = 0;\r\n        this.changeStatus(eEventGroupStatus.Idle);\r\n    }\r\n\r\n    canExecute(): boolean {\r\n        return this.conditionChain.evaluate(this.context);\r\n    }\r\n    \r\n    tick(dt: number): void {\r\n        \r\n        switch (this._status) {\r\n            case eEventGroupStatus.Idle:\r\n                // not active\r\n                break;\r\n            case eEventGroupStatus.Waiting:\r\n                // waiting for conditions to be met\r\n                if (this.canExecute()) {\r\n                    // TODO: 考虑这里检测增加时间间隔来减少消耗\r\n                    this.changeStatus(eEventGroupStatus.Active);\r\n                }\r\n                break;\r\n            case eEventGroupStatus.Active:\r\n                // conditions are met, now ticking actions\r\n                this.tickActive(dt);\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                // stopped\r\n                break;\r\n        }\r\n    }\r\n\r\n    private changeStatus(newStatus: eEventGroupStatus) {\r\n        if (this._status === newStatus) return;\r\n\r\n        this._status = newStatus;\r\n    \r\n        switch (this._status) {\r\n            case eEventGroupStatus.Waiting:\r\n                // reset actions by onLoad\r\n                break;\r\n            case eEventGroupStatus.Active:\r\n                // 启用时，重置action的初始参数\r\n                this.actions.forEach(action => action.onLoad(this.context));\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                break;\r\n            default: break;\r\n        }\r\n    }\r\n\r\n    private buildConditionChain(conditions: EventConditionData[]): ConditionChain {\r\n        const chain = new ConditionChain();\r\n        conditions.forEach((condData, index) => {\r\n            const condition = ConditionFactory.create(condData);\r\n            if (condition) {\r\n                condition.onLoad(this.context);\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n\r\n    private tickActive(dt: number): void {\r\n        let isAllFinished = true;\r\n\r\n        for (const action of this.actions) {\r\n            if (action.isCompleted()) continue;\r\n            action.onExecute(this.context, dt);\r\n            isAllFinished = false;\r\n        }\r\n        \r\n        if (isAllFinished) {\r\n            this._triggerCount++;\r\n            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {\r\n                // restart\r\n                this.changeStatus(eEventGroupStatus.Waiting);\r\n            }\r\n            else {\r\n                this.tryStop();\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// 提供一个静态函数帮助比较value\r\nexport class Comparer {\r\n    static compare(a: number, b: number, op: eCompareOp): boolean {\r\n        switch (op) {\r\n            case eCompareOp.Equal:\r\n                return a === b;\r\n            case eCompareOp.NotEqual:\r\n                return a !== b;\r\n            case eCompareOp.Greater:\r\n                return a > b;\r\n            case eCompareOp.GreaterEqual:\r\n                return a >= b;\r\n            case eCompareOp.Less:\r\n                return a < b;\r\n            case eCompareOp.LessEqual:\r\n                return a <= b;\r\n            default:\r\n                throw new Error(`Unknown compare operator: ${op}`);\r\n        }\r\n    }\r\n}\r\n\r\n// Factory pattern for conditions & actions\r\nclass ConditionFactory {\r\n    static create(data: EventConditionData): IEventCondition {\r\n        switch (data.type) {\r\n            case eEmitterCondition.Emitter_Active:\r\n                return new emitter_cond.EmitterCondition_Active(data);\r\n            case eEmitterCondition.Emitter_InitialDelay:\r\n                return new emitter_cond.EmitterCondition_InitialDelay(data);\r\n            case eEmitterCondition.Emitter_Prewarm:\r\n                return new emitter_cond.EmitterCondition_Prewarm(data);\r\n            case eEmitterCondition.Emitter_PrewarmDuration:\r\n                return new emitter_cond.EmitterCondition_PrewarmDuration(data);\r\n            case eEmitterCondition.Emitter_Duration:\r\n                return new emitter_cond.EmitterCondition_Duration(data);\r\n            case eEmitterCondition.Emitter_ElapsedTime:\r\n                return new emitter_cond.EmitterCondition_ElapsedTime(data);\r\n            case eEmitterCondition.Emitter_Loop:\r\n                return new emitter_cond.EmitterCondition_Loop(data);\r\n            case eEmitterCondition.Emitter_LoopInterval:\r\n                return new emitter_cond.EmitterCondition_LoopInterval(data);\r\n            case eEmitterCondition.Emitter_EmitInterval:\r\n                return new emitter_cond.EmitterCondition_EmitInterval(data);\r\n            case eEmitterCondition.Emitter_PerEmitCount:\r\n                return new emitter_cond.EmitterCondition_PerEmitCount(data);\r\n            case eEmitterCondition.Emitter_PerEmitInterval:\r\n                return new emitter_cond.EmitterCondition_PerEmitInterval(data);\r\n            case eEmitterCondition.Emitter_PerEmitOffsetX:\r\n                return new emitter_cond.EmitterCondition_PerEmitOffsetX(data);\r\n            case eEmitterCondition.Emitter_Angle:\r\n                return new emitter_cond.EmitterCondition_Angle(data);\r\n            case eEmitterCondition.Emitter_Count:\r\n                return new emitter_cond.EmitterCondition_Count(data);\r\n            case eEmitterCondition.Bullet_Duration:\r\n                return new emitter_cond.EmitterCondition_BulletDuration(data);\r\n            case eEmitterCondition.Bullet_Speed:\r\n                return new emitter_cond.EmitterCondition_BulletSpeed(data);\r\n            case eEmitterCondition.Bullet_Acceleration:\r\n                return new emitter_cond.EmitterCondition_BulletAcceleration(data);\r\n            case eEmitterCondition.Bullet_AccelerationAngle:\r\n                return new emitter_cond.EmitterCondition_BulletAccelerationAngle(data);\r\n            case eEmitterCondition.Bullet_FacingMoveDir:\r\n                return new emitter_cond.EmitterCondition_BulletFacingMoveDir(data);\r\n            case eEmitterCondition.Bullet_TrackingTarget:\r\n                return new emitter_cond.EmitterCondition_BulletTrackingTarget(data);\r\n            case eEmitterCondition.Bullet_Destructive:\r\n                return new emitter_cond.EmitterCondition_BulletDestructive(data);\r\n            case eEmitterCondition.Bullet_DestructiveOnHit:\r\n                return new emitter_cond.EmitterCondition_BulletDestructiveOnHit(data);\r\n            // case eEmitterCondition.Bullet_Sprite:\r\n            //     return new emitter_cond.EmitterCondition_BulletSprite(data);\r\n            case eEmitterCondition.Bullet_Scale:\r\n                return new emitter_cond.EmitterCondition_BulletScale(data);\r\n            case eEmitterCondition.Bullet_ColorR:\r\n                return new emitter_cond.EmitterCondition_BulletColorR(data);\r\n            case eEmitterCondition.Bullet_ColorG:\r\n                return new emitter_cond.EmitterCondition_BulletColorG(data);\r\n            case eEmitterCondition.Bullet_ColorB:\r\n                return new emitter_cond.EmitterCondition_BulletColorB(data);\r\n            case eEmitterCondition.Bullet_DefaultFacing:\r\n                return new emitter_cond.EmitterCondition_BulletDefaultFacing(data);\r\n            case eEmitterCondition.Player_ActLevel:\r\n                return new emitter_cond.EmitterCondition_PlayerActLevel(data);  \r\n            case eEmitterCondition.Player_PosX:\r\n                return new emitter_cond.EmitterCondition_PlayerPosX(data);  \r\n            case eEmitterCondition.Player_PosY:\r\n                return new emitter_cond.EmitterCondition_PlayerPosY(data);\r\n            case eEmitterCondition.Player_LifePercent:\r\n                return new emitter_cond.EmitterCondition_PlayerLifePercent(data);\r\n            case eEmitterCondition.Player_GainBuff:\r\n                return new emitter_cond.EmitterCondition_PlayerGainBuff(data);\r\n            \r\n            // ... bullet cases\r\n            case eBulletCondition.Bullet_Duration:\r\n                return new bullet_cond.BulletCondition_Duration(data);\r\n            case eBulletCondition.Bullet_ElapsedTime:\r\n                return new bullet_cond.BulletCondition_ElapsedTime(data);\r\n            case eBulletCondition.Bullet_PosX:\r\n                return new bullet_cond.BulletCondition_PosX(data);\r\n            case eBulletCondition.Bullet_PosY:\r\n                return new bullet_cond.BulletCondition_PosY(data);\r\n            case eBulletCondition.Bullet_Speed:\r\n                return new bullet_cond.BulletCondition_Speed(data);\r\n            case eBulletCondition.Bullet_SpeedAngle:\r\n                return new bullet_cond.BulletCondition_SpeedAngle(data);\r\n            case eBulletCondition.Bullet_Acceleration:\r\n                return new bullet_cond.BulletCondition_Acceleration(data);\r\n            case eBulletCondition.Bullet_AccelerationAngle:\r\n                return new bullet_cond.BulletCondition_AccelerationAngle(data);\r\n            case eBulletCondition.Bullet_Scale:\r\n                return new bullet_cond.BulletCondition_Scale(data);\r\n            case eBulletCondition.Bullet_ColorR:\r\n                return new bullet_cond.BulletCondition_ColorR(data);\r\n            case eBulletCondition.Bullet_ColorG:\r\n                return new bullet_cond.BulletCondition_ColorG(data);\r\n            case eBulletCondition.Bullet_ColorB:\r\n                return new bullet_cond.BulletCondition_ColorB(data);\r\n            case eBulletCondition.Bullet_FacingMoveDir:\r\n                return new bullet_cond.BulletCondition_FacingMoveDir(data);\r\n            case eBulletCondition.Bullet_Destructive:\r\n                return new bullet_cond.BulletCondition_Destructive(data);\r\n            case eBulletCondition.Bullet_DestructiveOnHit:\r\n                return new bullet_cond.BulletCondition_DestructiveOnHit(data);\r\n            default:\r\n                throw new Error(`Unknown condition type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\nclass ActionFactory {\r\n    static create(data: EventActionData): IEventAction {\r\n        switch (data.type) {\r\n            case eEmitterAction.Emitter_Active:\r\n                return new emitter_act.EmitterAction_Active(data);\r\n            case eEmitterAction.Emitter_InitialDelay:\r\n                return new emitter_act.EmitterAction_InitialDelay(data);\r\n            case eEmitterAction.Emitter_Prewarm:\r\n                return new emitter_act.EmitterAction_Prewarm(data);\r\n            case eEmitterAction.Emitter_PrewarmDuration:\r\n                return new emitter_act.EmitterAction_PrewarmDuration(data);\r\n            case eEmitterAction.Emitter_Duration:\r\n                return new emitter_act.EmitterAction_Duration(data);\r\n            case eEmitterAction.Emitter_ElapsedTime:\r\n                return new emitter_act.EmitterAction_ElapsedTime(data);\r\n            case eEmitterAction.Emitter_Loop:\r\n                return new emitter_act.EmitterAction_Loop(data);\r\n            case eEmitterAction.Emitter_LoopInterval:\r\n                return new emitter_act.EmitterAction_LoopInterval(data);\r\n            case eEmitterAction.Emitter_EmitInterval:\r\n                return new emitter_act.EmitterAction_EmitInterval(data);\r\n            case eEmitterAction.Emitter_PerEmitCount:\r\n                return new emitter_act.EmitterAction_PerEmitCount(data);\r\n            case eEmitterAction.Emitter_PerEmitInterval:\r\n                return new emitter_act.EmitterAction_PerEmitInterval(data);\r\n            case eEmitterAction.Emitter_PerEmitOffsetX:\r\n                return new emitter_act.EmitterAction_PerEmitOffsetX(data);\r\n            case eEmitterAction.Emitter_Angle:\r\n                return new emitter_act.EmitterAction_Angle(data);\r\n            case eEmitterAction.Emitter_Count:\r\n                return new emitter_act.EmitterAction_Count(data);\r\n            case eEmitterAction.Bullet_Duration:\r\n                return new emitter_act.EmitterAction_BulletDuration(data);\r\n            case eEmitterAction.Bullet_Damage:\r\n                return new emitter_act.EmitterAction_BulletDamage(data);\r\n            case eEmitterAction.Bullet_Speed:\r\n                return new emitter_act.EmitterAction_BulletSpeed(data);\r\n            case eEmitterAction.Bullet_SpeedAngle:\r\n                return new emitter_act.EmitterAction_BulletSpeedAngle(data);\r\n            case eEmitterAction.Bullet_Acceleration:\r\n                return new emitter_act.EmitterAction_BulletAcceleration(data);\r\n            case eEmitterAction.Bullet_AccelerationAngle:\r\n                return new emitter_act.EmitterAction_BulletAccelerationAngle(data);\r\n            case eEmitterAction.Bullet_Scale:\r\n                return new emitter_act.EmitterAction_BulletScale(data);\r\n            case eEmitterAction.Bullet_ColorR:\r\n                return new emitter_act.EmitterAction_BulletColorR(data);\r\n            case eEmitterAction.Bullet_ColorG:\r\n                return new emitter_act.EmitterAction_BulletColorG(data);\r\n            case eEmitterAction.Bullet_ColorB:\r\n                return new emitter_act.EmitterAction_BulletColorB(data);\r\n            case eEmitterAction.Bullet_FacingMoveDir:\r\n                return new emitter_act.EmitterAction_BulletFacingMoveDir(data);\r\n            case eEmitterAction.Bullet_TrackingTarget:\r\n                return new emitter_act.EmitterAction_BulletTrackingTarget(data);\r\n            case eEmitterAction.Bullet_Destructive:\r\n                return new emitter_act.EmitterAction_BulletDestructive(data);\r\n            case eEmitterAction.Bullet_DestructiveOnHit:\r\n                return new emitter_act.EmitterAction_BulletDestructiveOnHit(data);\r\n            // ... bullet cases\r\n            case eBulletAction.Bullet_Duration:\r\n                return new bullet_act.BulletAction_Duration(data);\r\n            case eBulletAction.Bullet_ElapsedTime:\r\n                return new bullet_act.BulletAction_ElapsedTime(data);\r\n            case eBulletAction.Bullet_PosX:\r\n                return new bullet_act.BulletAction_PosX(data);\r\n            case eBulletAction.Bullet_PosY:\r\n                return new bullet_act.BulletAction_PosY(data);\r\n            case eBulletAction.Bullet_Speed:\r\n                return new bullet_act.BulletAction_Speed(data);\r\n            case eBulletAction.Bullet_SpeedAngle:\r\n                return new bullet_act.BulletAction_SpeedAngle(data);\r\n            case eBulletAction.Bullet_Acceleration:\r\n                return new bullet_act.BulletAction_Acceleration(data);\r\n            case eBulletAction.Bullet_AccelerationAngle:\r\n                return new bullet_act.BulletAction_AccelerationAngle(data);\r\n            case eBulletAction.Bullet_Scale:\r\n                return new bullet_act.BulletAction_Scale(data);\r\n            case eBulletAction.Bullet_ColorR:\r\n                return new bullet_act.BulletAction_ColorR(data);\r\n            case eBulletAction.Bullet_ColorG:\r\n                return new bullet_act.BulletAction_ColorG(data);\r\n            case eBulletAction.Bullet_ColorB:\r\n                return new bullet_act.BulletAction_ColorB(data);\r\n            case eBulletAction.Bullet_FacingMoveDir:\r\n                return new bullet_act.BulletAction_FacingMoveDir(data);\r\n            case eBulletAction.Bullet_Destructive:\r\n                return new bullet_act.BulletAction_Destructive(data);\r\n            case eBulletAction.Bullet_DestructiveOnHit:\r\n                return new bullet_act.BulletAction_DestructiveOnHit(data);\r\n            default:\r\n                throw new Error(`Unknown action type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n"]}